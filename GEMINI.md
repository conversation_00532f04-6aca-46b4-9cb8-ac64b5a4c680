# Gemini Project Configuration

This document provides a comprehensive overview of the project structure, conventions, and commands for the Gemini AI assistant.

## 1. Project Overview

This project is a full-stack web application with a Rust backend and a React frontend.

- **Backend:** A robust API built with Rust using the Axum framework. It handles business logic, database interactions, and authentication.
- **Frontend:** A modern, responsive user interface built with React, TypeScript, and Vite. It communicates with the backend via a RESTful API.
- **Database:** The application uses PostgreSQL for data storage. Database migrations are managed with `sqlx-cli`.

## 2. Directory Structure

```
/
├── backend/         # Rust backend application
│   ├── src/
│   ├── Cargo.toml
│   └── migrations/
├── frontend/        # React frontend application
│   ├── src/
│   ├── package.json
│   └── vite.config.ts
├── docs/            # Project documentation
└── .kiro/           # Specifications and design documents
```

## 3. Backend (Rust)

The backend is a multi-tenant system built on Axum and SQLx.

### Key Technologies

- **Framework:** [Axum](https://github.com/tokio-rs/axum)
- **Database ORM:** [SQLx](https://github.com/launchbadge/sqlx) (with PostgreSQL)
- **Async Runtime:** [Tokio](https://tokio.rs/)
- **Authentication:** JWT (JSON Web Tokens)

### Conventions

- **Code Structure:** The code is organized into `controller`, `service`, and `model` layers.
- **Error Handling:** Centralized error handling is implemented in `utils/error.rs`.
- **Database Migrations:** Migrations are located in `backend/migrations/`. They are raw SQL files and are applied sequentially.

### Common Commands

- **Run Development Server:**
  ```bash
  cd backend
  cargo run
  ```
- **Run Tests:**
  ```bash
  cd backend
  cargo test
  ```
- **Manage Database Migrations (requires `sqlx-cli`):**
  ```bash
  # Install sqlx-cli if you haven't already
  cargo install sqlx-cli

  # Apply migrations
  cd backend
  sqlx migrate run --database-url <YOUR_DATABASE_URL>
  ```

## 4. Frontend (React)

The frontend is a single-page application (SPA) built with React and Vite.

### Key Technologies

- **Framework:** [React](https://react.dev/)
- **Build Tool:** [Vite](https://vitejs.dev/)
- **Language:** [TypeScript](https://www.typescriptlang.org/)
- **Styling:** [Tailwind CSS](https://tailwindcss.com/)
- **Routing:** [React Router](https://reactrouter.com/)
- **State Management:** [Zustand](https://github.com/pmndrs/zustand)
- **HTTP Client:** [Axios](https://axios-http.com/)

### Common Commands

- **Install Dependencies:**
  ```bash
  cd frontend
  npm install
  ```
- **Run Development Server:**
  ```bash
  cd frontend
  npm run dev
  ```
- **Build for Production:**
  ```bash
  cd frontend
  npm run build
  ```
- **Run Linter:**
  ```bash
  cd frontend
  npm run lint
  ```
- **Run Type Checking:**
  ```bash
  cd frontend
  npm run typecheck
  ```

## 5. General Workflow

1.  **Start the Backend:** Navigate to the `backend` directory and run `cargo run`.
2.  **Start the Frontend:** In a new terminal, navigate to the `frontend` directory and run `npm run dev`.
3.  Access the application in your browser at the URL provided by Vite (usually `http://localhost:5173`).
