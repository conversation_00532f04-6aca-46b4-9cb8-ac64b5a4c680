# 菜单权限迁移到Casbin策略设计方案

## 1. 当前架构分析

### 1.1 现有菜单权限结构
```sql
-- menu_permissions 表中的权限字段
required_permissions TEXT[]  -- 如: ['student:read', 'class:manage']
data_scopes TEXT[]          -- 如: ['class:*', 'school:own']
```

### 1.2 Casbin策略表结构
```sql
-- casbin_policies 表结构
ptype VARCHAR(100)    -- 策略类型: p(权限), g(角色), g2(继承)
v0 VARCHAR(256)       -- 主体 (subject)
v1 VARCHAR(256)       -- 域 (domain/tenant)
v2 VARCHAR(256)       -- 对象 (object)
v3 VARCHAR(256)       -- 动作 (action)
v4 VARCHAR(256)       -- 效果 (effect: allow/deny)
v5 VARCHAR(256)       -- 扩展字段
tenant_id VARCHAR(100) -- 租户标识
```

## 2. 映射规则设计

### 2.1 菜单权限策略映射
将 `required_permissions` 转换为菜单访问策略：

```
原始: required_permissions = ['student:read', 'class:manage']
转换为:
ptype='p', v0='menu', v1='tenant_*', v2='menu:student_management', v3='access', v4='allow'
```

**映射规则:**
- `ptype`: 固定为 'p' (权限策略)
- `v0`: 'menu' (表示这是菜单权限)
- `v1`: 'tenant_*' (模板策略，实际使用时替换为具体租户ID)
- `v2`: 'menu:{menu_id}' (菜单对象标识)
- `v3`: 'access' (访问动作)
- `v4`: 'allow' (允许效果)
- `v5`: JSON格式存储原始权限要求: `{"required_permissions": ["student:read", "class:manage"]}`

### 2.2 数据范围策略映射
将 `data_scopes` 转换为数据范围策略：

```
原始: data_scopes = ['class:*', 'school:own']
转换为:
ptype='p', v0='menu', v1='tenant_*', v2='data:menu:student_management', v3='scope', v4='allow', v5='{"scopes": ["class:*", "school:own"]}'
```

**映射规则:**
- `ptype`: 固定为 'p'
- `v0`: 'menu'
- `v1`: 'tenant_*'
- `v2`: 'data:menu:{menu_id}' (数据范围对象)
- `v3`: 'scope' (范围动作)
- `v4`: 'allow'
- `v5`: JSON格式存储数据范围: `{"scopes": ["class:*", "school:own"]}`

### 2.3 角色-菜单关联策略
为每个角色创建菜单访问策略：

```
ptype='p', v0='{role_code}', v1='tenant_*', v2='menu:{menu_id}', v3='access', v4='allow'
```

## 3. 迁移策略

### 3.1 数据迁移步骤
1. 读取所有 `menu_permissions` 记录
2. 为每个菜单创建基础访问策略
3. 根据权限要求创建角色-菜单关联策略
4. 创建数据范围策略
5. 验证迁移结果
6. 删除原有字段

### 3.2 策略生成逻辑
```rust
// 伪代码示例
fn migrate_menu_permission(menu: &MenuPermission) -> Vec<CasbinPolicy> {
    let mut policies = Vec::new();
    
    // 1. 基础菜单访问策略
    policies.push(CasbinPolicy {
        ptype: "p".to_string(),
        v0: "menu".to_string(),
        v1: "tenant_*".to_string(),
        v2: format!("menu:{}", menu.menu_id),
        v3: "access".to_string(),
        v4: "allow".to_string(),
        v5: serde_json::to_string(&json!({
            "required_permissions": menu.required_permissions
        })).unwrap(),
        tenant_id: "template".to_string(),
    });
    
    // 2. 数据范围策略
    if let Some(data_scopes) = &menu.data_scopes {
        policies.push(CasbinPolicy {
            ptype: "p".to_string(),
            v0: "menu".to_string(),
            v1: "tenant_*".to_string(),
            v2: format!("data:menu:{}", menu.menu_id),
            v3: "scope".to_string(),
            v4: "allow".to_string(),
            v5: serde_json::to_string(&json!({
                "scopes": data_scopes
            })).unwrap(),
            tenant_id: "template".to_string(),
        });
    }
    
    policies
}
```

## 4. 代码修改方案

### 4.1 后端修改
1. **CasbinService扩展**: 添加菜单权限查询方法
2. **MenuService修改**: 移除直接字段操作，改为策略操作
3. **权限检查逻辑**: 使用Casbin策略进行验证
4. **迁移服务**: 创建专门的迁移服务

### 4.2 前端修改
1. **PermissionContext**: 更新菜单权限获取逻辑
2. **API接口**: 保持接口不变，后端适配
3. **类型定义**: 保持现有接口，确保兼容性

## 5. 兼容性保证

### 5.1 API兼容性
- 保持现有API接口不变
- 后端内部使用Casbin策略
- 前端无感知迁移

### 5.2 数据兼容性
- 迁移过程中保留原有数据
- 验证迁移正确性后再删除
- 提供回滚机制

## 6. 实施计划

### 阶段1: 准备阶段
- [ ] 创建迁移服务
- [ ] 设计策略模板
- [ ] 编写迁移脚本

### 阶段2: 迁移阶段
- [ ] 执行数据迁移
- [ ] 修改后端代码
- [ ] 更新权限检查逻辑

### 阶段3: 验证阶段
- [ ] 功能测试
- [ ] 性能测试
- [ ] 兼容性验证

### 阶段4: 清理阶段
- [ ] 删除废弃字段
- [ ] 更新文档
- [ ] 代码优化
