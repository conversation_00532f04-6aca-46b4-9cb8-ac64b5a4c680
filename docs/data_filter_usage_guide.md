# 通用数据过滤器使用指南

## 概述

通用数据过滤器是基于Casbin权限模型的数据访问控制系统，能够根据用户角色和身份自动过滤数据范围，确保用户只能访问其权限范围内的数据。

## 核心概念

### 1. 数据范围权限

数据范围权限定义了用户可以访问的数据范围，格式为：`{resource}:{scope_type}:{scope_value}`

- **resource**: 资源类型（如 student, administrative_class, grade 等）
- **scope_type**: 范围类型（如 class, grade, subject_group, school 等）
- **scope_value**: 范围值（具体的ID或*表示所有）

### 2. 权限层级

权限按照以下层级组织，高级别权限自动包含低级别权限：
- **school** > **grade** > **class**
- **manage** > **create/write/delete** > **read**

### 3. 支持的范围类型

- **class**: 班级范围（通过administrative_class_id关联）
- **grade**: 年级范围（通过grade_level_code关联）
- **subject_group**: 学科组范围（通过学科组ID关联）
- **school**: 学校范围（租户级别）

## 使用方法

### 1. 基本使用

```rust
use crate::service::permission::{
    DataFilterManager, StudentDataFilter, FilterContext
};
use std::sync::Arc;

// 创建数据过滤器管理器
let mut filter_manager = DataFilterManager::new();

// 注册学生数据过滤器
let student_filter = Arc::new(StudentDataFilter::new(db_pool.clone()));
filter_manager.register_filter("student".to_string(), student_filter);

// 创建过滤上下文
let filter_context = FilterContext {
    user_id: user_id,
    tenant_id: tenant_id.to_string(),
    user_identity: format!("user:{}", user_id),
    resource: "student".to_string(),
    action: "read".to_string(),
    schema_name: schema_name.clone(),
};

// 应用数据过滤
let mut query_builder = sqlx::QueryBuilder::new("SELECT * FROM students WHERE 1=1");
let mut count_builder = sqlx::QueryBuilder::new("SELECT COUNT(*) FROM students WHERE 1=1");

filter_manager.apply_data_filter(
    &filter_context,
    &mut query_builder,
    &mut count_builder,
    casbin_service,
).await?;
```

### 2. 在StudentService中使用

```rust
use crate::service::student::StudentServiceFactory;

// 创建带有数据过滤器的学生服务
let student_service = StudentServiceFactory::create_with_data_filter(db_pool);

// 使用新的API调用
let (students, total) = student_service.page_all_student(
    &schema_name,
    &params,
    Some(user_id),
    Some(tenant_id),
    Some(casbin_service.as_ref()),
).await?;
```

### 3. 权限配置

#### 班主任权限配置

```sql
-- 为班主任角色添加学生数据的班级范围读取权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
VALUES ('p', 'role:class_teacher', '{tenant_id}', 'student:class:*', 'read', 'allow', '', '{tenant_id}', NOW(), NOW());

-- 为具体用户添加具体班级权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
VALUES ('p', 'user:{user_id}', '{tenant_id}', 'student:class:{class_id}', 'read', 'allow', '', '{tenant_id}', NOW(), NOW());
```

#### 年级主任权限配置

```sql
-- 为年级主任角色添加学生数据的年级范围读取权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
VALUES ('p', 'role:grade_director', '{tenant_id}', 'student:grade:*', 'read', 'allow', '', '{tenant_id}', NOW(), NOW());
```

#### 校长权限配置

```sql
-- 为校长角色添加学生数据的学校范围管理权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
VALUES ('p', 'role:principal', '{tenant_id}', 'student:school:*', 'manage', 'allow', '', '{tenant_id}', NOW(), NOW());
```

### 4. 权限同步

使用数据范围权限同步服务自动同步权限：

```rust
use crate::service::permission::{
    DataScopePermissionSyncService, DataScopeSyncConfig
};

let sync_service = DataScopePermissionSyncService::new(db_pool);

let config = DataScopeSyncConfig {
    sync_class_teacher_permissions: true,
    sync_grade_director_permissions: true,
    sync_subject_leader_permissions: false,
    clear_existing_data_scope_policies: false,
    dry_run: false,
};

let result = sync_service.sync_tenant_data_scope_permissions(
    &tenant_id,
    &schema_name,
    casbin_service,
    &config,
).await?;

println!("Synced {} permissions", result.synced_permissions);
```

## 扩展指南

### 1. 创建自定义数据过滤器

```rust
use async_trait::async_trait;
use crate::service::permission::{DataFilter, FilterContext, FilterCondition};

pub struct CustomDataFilter {
    db_pool: PgPool,
}

#[async_trait]
impl DataFilter for CustomDataFilter {
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        // 实现自定义过滤逻辑
        // 1. 检查是否为系统管理员
        // 2. 获取用户的数据权限范围
        // 3. 构建过滤条件
        todo!()
    }
    
    fn apply_filter_to_query<'q>(
        &self,
        query_builder: &mut QueryBuilder<'q, Postgres>,
        condition: &FilterCondition,
    ) -> Result<()> {
        // 应用过滤条件到查询
        query_builder.push(" AND (").push(&condition.sql_condition).push(")");
        
        for param in &condition.bind_params {
            match param {
                FilterParam::Uuid(uuid) => query_builder.push_bind(uuid),
                FilterParam::String(s) => query_builder.push_bind(s),
                FilterParam::Int(i) => query_builder.push_bind(i),
                FilterParam::Bool(b) => query_builder.push_bind(b),
            };
        }
        
        Ok(())
    }
    
    fn apply_filter_to_count_query<'q>(
        &self,
        count_builder: &mut QueryBuilder<'q, Postgres>,
        condition: &FilterCondition,
    ) -> Result<()> {
        // 应用过滤条件到计数查询
        // 通常与apply_filter_to_query相同
        self.apply_filter_to_query(count_builder, condition)
    }
}
```

### 2. 注册自定义过滤器

```rust
let custom_filter = Arc::new(CustomDataFilter::new(db_pool.clone()));
filter_manager.register_filter("custom_resource".to_string(), custom_filter);
```

## 最佳实践

### 1. 权限设计原则

- **最小权限原则**: 用户只能访问完成工作所需的最小数据范围
- **权限继承**: 利用角色继承减少权限配置复杂度
- **明确边界**: 清晰定义不同角色的数据访问边界

### 2. 性能优化

- **索引优化**: 确保过滤字段（如administrative_class_id）有适当的索引
- **缓存策略**: 对频繁查询的权限信息进行缓存
- **批量操作**: 避免在循环中进行权限检查

### 3. 安全考虑

- **输入验证**: 验证所有用户输入，防止SQL注入
- **权限审计**: 记录权限变更和访问日志
- **定期同步**: 定期同步权限数据，确保一致性

## 故障排除

### 1. 常见问题

**问题**: 用户无法看到应该有权限的数据
**解决**: 
1. 检查Casbin权限策略是否正确配置
2. 验证用户身份标识格式是否正确
3. 确认数据范围权限是否已同步

**问题**: 查询性能较差
**解决**:
1. 检查过滤字段的索引
2. 优化SQL查询条件
3. 考虑使用缓存

**问题**: 权限同步失败
**解决**:
1. 检查数据库连接
2. 验证租户和schema配置
3. 查看错误日志

### 2. 调试技巧

- 启用详细日志记录
- 使用dry_run模式测试权限同步
- 检查生成的SQL条件是否正确

## 示例场景

### 班主任查看学生列表

1. 班主任登录系统
2. 系统识别用户身份为班主任角色
3. 数据过滤器查询班主任负责的班级ID
4. 自动在学生查询中添加班级过滤条件
5. 返回该班主任负责班级的学生列表

### 年级主任查看年级数据

1. 年级主任访问年级管理页面
2. 系统根据权限配置识别年级范围
3. 数据过滤器添加年级过滤条件
4. 返回该年级主任负责年级的所有数据

这个通用数据过滤器系统提供了灵活、安全、高性能的数据访问控制能力，能够满足复杂的教育管理系统权限需求。
