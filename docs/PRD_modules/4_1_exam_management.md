# 4. 功能模块详述

## 4.1 考试管理系统
### 4.1.1 功能描述
支持线下纸质考试的完整管理流程，包括单校考试和多校联考。考试采用传统线下答题模式，考试过程不在本系统管理范围内，主要负责考试后的试卷扫描识别和线上智能评阅。

### 4.1.2 核心功能
- **考试创建**：考试创建、科目安排、时间管理
- **多校联考**：跨租户考试协调、数据同步
- **成绩管理**：成绩录入、统计分析、排名计算

### 4.1.3 考试组成要素
```mermaid
mindmap
    Root{{考试}}
        _[学科&年级]
            
        _(试卷题提卡&标注)
            试卷
                试题 题型 分数 评分标准
            题卡
                定位区 页码 题块 （与试卷中试题一一对应 / 调整对应关系）
        _)学生(
            关联学生
            是否允许动态添加
                扫描识别中自动添加学生
            考生处理结果
                关联扫描纸张/缺考
                
        _(老师)
            修改学生/作答/题卡信息
            阅卷
            查看成绩统计
        _(扫描纸张)
            按班级扫描，可追踪每张纸张的处理结果
            白纸、重复、已识别、已关联
        _(评分)
            AI辅助评分
                依赖评分标准
            分配老师评分任务
                手机端
                电脑端
            扫描登记
        _(任务分发)
            控制评分任务分发与重评
            各个老师的任务量
            AI评分进度控制
```

### 4.1.4 联考协作机制
- 主办方发起联考邀请
- 参与方确认考试安排
- 考试后阅卷状态同步

### 4.1.5 考试管理详细流程

#### 4.1.5.1 流程概述
考试管理系统基于线下纸质考试模式，涵盖从考试创建到成绩分析的完整生命周期，支持单校考试和多校联考两种模式。

**考试类型定义：**
- **单校考试**：单个租户内部组织的线下纸质考试
- **多校联考**：跨租户协作的联合线下考试，支持统一命题、统一时间、统一评分标准

**流程六大阶段：**
1. **考试创建**
2. **考试前准备阶段**：考试创建、参数设置、试卷准备
3. **考试进行阶段**：线下考试执行（不在系统管理范围）
4. **试卷收集扫描阶段**：试卷收集、扫描识别、数字化处理
5. **在线阅卷评分阶段**：智能评阅、人工复核、成绩录入
6. **成绩处理与分析阶段**：成绩审核、发布、数据分析、报告生成
```mermaid
flowchart TD
    subgraph 创建考试
        A[创建考试] --> B{判断}
        B -->|手动| C[手动创建]
        B -->|选题| B1[系统选题生成答题卡]
        B -->|模板| B2[使用模板直接复制试题和题卡]
    end
    subgraph 手动设置
        C-->C1[录入试卷试题]
        C-->C2[标记答题卡]
        C-->C3[设置评分规则]
    end
    subgraph 设置学生/班级
        A --> D{是否支撑扫描中添加}
        D --> |是| D1
        D --> |否| D2[选择班级,学生]
    end
    subgraph 扫描 
        E{扫描方式} --> |启辰配置扫描仪| E1(批次扫描)
        E1 --> E3(上传批次管理)
        E --> |图片上传| E2(合并正反页)
        E2 --> E3
        E3 --> E4(纸张管理)
    end
    subgraph 题卡识别 
        E4 --> F(识别提取)
        F --> F1(空白页检测,图片hasher计算)
        F1 --> F2(定位点和页面提取,二维码比对)
        F2 --> F3(客观题识别)
        F3 --> F4(区块切割)
    end
    F4 --> F6(重复提卡判断)
    F6 --> F7(学生ID匹配 短号或考号关联学生)

    subgraph 评分控制
        G(启动评分) --> G1[客观题分数登记]
        G1 --> GIF{判断}
        GIF --> |AI阅卷| G2
        GIF --> |卷面批改录入| G3
        GIF --> |系统阅卷任务| G4
        G2 --> G5(打分明细, 记录时间 批阅人/方式ID/工作流ID 区块设置版本号)
        G5 --> G6(录入考生分数)
        GR{{试题重新分发}}
    end
```

#### 4.1.5.2 阶段一：考试前准备阶段

**考试创建流程：**

*单校线下考试创建流程：*
```mermaid
graph TD
    A[考试管理员登录] --> B[创建考试]
    B --> C[设置考试基本信息]
    C --> D[选择试卷模板]
    D --> E[选择考试班级]
    E --> E1[可选: 精确选择班级下的学生]
    E1 --> F[设置考试科目]
    F --> G[配置考试时间和地点]
    G --> H[设置评分标准]
    H --> I[分配阅卷员]
    I --> J[发布考试通知]
    J --> K[等待线下考试开始]
```

*多校联考创��流程：*
```mermaid
graph TD
    A[主办方考试管理员] --> B[创建联考]
    B --> C[设置联考基本信息]
    C --> D[邀请参与学校]
    D --> E[等待学校确认]
    E --> F{所有学校确认?}
    F -->|否| G[继续等待/催促]
    F -->|是| H[配置联考参数]
    H --> I[统一试卷制作分发]
    I --> J[同步考试安排]
    J --> K[分配各校阅卷员]
    K --> L[发布联考通知]
    L --> M[等待各校线下考试]
    G --> E
```

**考试参数配置：**

*基本信息设置：*

| 参数   | 必填 | 说明       | 示例                 |
|------|----|----------|--------------------|
| 考试名称 | 是  | 考试标题     | "2024年春季期末考试"      |
| 考试类型 | 是  | 单校/联考    | "single" / "joint" |
| 考试级别 | 是  | 年级范围     | "高三"               |
| 考试性质 | 是  | 正式/模拟/练习 | "formal"           |
| 考试说明 | 否  | 考试备注     | "注意事项..."          |
| 学生选择 | 否  | 班级下学生精确选择 | "可选特定学生参加"      |

*时间和地点设置：*

| 参数     | 必填 | 说明       | 限制            |
|--------|----|----------|---------------|
| 考试开始时间 | 是  | 线下考试开始时间 | 不能早于当前时间+24小时 |
| 考试结束时间 | 是  | 线下考试结束时间 | 必须晚于开始时间      |
| 考试地点   | 否  | 线下考试场地   | 具体教室或考场安排     |
| 预计收卷时间 | 否  | 试卷收集完成时间 | 默认考试结束后2小时    |
| 扫描开始时间 | 否  | 试卷扫描开始时间 | 默认收卷完成后开始     |

*评阅模式和质量控制设置：*

| 参数        | 必填 | 说明           | 默认值/选项 |
|-----------|----|--------------|--------|
| 评阅模式      | 是  | 智能评阅/先阅后扫    | "智能评阅" |
| 质量控制      | 是  | 评阅质量控制机制     | "单次评阅" |
| AI评阅置信度阈值 | 否  | AI自动评阅的置信度要求 | 95%    |
| 人工复核比例    | 否  | 需要人工复核的试卷比例  | 5%     |

**评阅模式说明：**
- **智能评阅**：先扫描试卷数字化，再通过AI系统和人工结合进行��阅
- **先阅后扫**：人工在原始试卷上评阅打分，再扫描识别手写分数

**评分标准管理**：
- **动态更改**：评分标准在阅卷过程中可以进行更改和调整
- **版本控制**：每次评分标准修改都保留版本号，支持版本回溯
- **重阅权限**：是否重新评阅由有权限的用户（阅卷管理员、教导主任）控制
- **权限分级**：不同角色对评分标准的修改权限分级管理
- **变更记录**：完整记录评分标准的修改历史和修改人
- **自动重评**：评分标准重大修改后可选择自动重新评阅已评试卷

**重阅控制机制**：
- **权限验证**：仅阅卷管理员和教导主任可以决定是否重阅
- **重阅范围**：支持按题目、按阅卷员、按分数段进行重阅
- **重阅原因**：必须填写重阅原因和预期效果
- **重阅审批**：重要考试的重阅需要上级审批
- **重阅记录**：完整记录重阅决策过程和结果

**质量控制机制：**
- **单次评阅**：每题由一位阅卷员评阅（默认）
- **双评机制**：主观题由两位阅卷员独立评阅，分数差异超过��值时启动仲裁

*权限控制和审批：*

**考试创建权限验证流程：**
```mermaid
graph TD
    A[用户申请创建考试] --> B[获取用户角色信息]
    B --> C{角色权限检查}
    C -->|校长| D[允许创建任何考试]
    C -->|教导主任| D
    C -->|学科组长| E[检查学科权限范围]
    C -->|年级长| F[检查年级权限范围]
    C -->|班主任| G[检查班级权限范围]
    C -->|任课老师| H[检查任教关系]
    C -->|其他角色| I[拒绝创建]
    
    E --> J{是否为本学科考试?}
    F --> K{是否为本年级考试?}
    G --> L{是否为本班级考试?}
    H --> M{是否为任教班级考试?}
    
    J -->|是| N[允许创建]
    J -->|否| I
    K -->|是| N
    K -->|否| I
    L -->|是| N
    L -->|否| I
    M -->|是| N
    M -->|否| I
    
    D --> N
    N --> O[生成考试记录]
    I --> P[返回权限不足错误]
```

#### 4.1.4.3 阶段二：考试进行阶段

**说明：此阶段不在系统管理范围内**

线下��试进行阶段由学校自行组织管理，包括：
- 考场安排和监考安排
- 学生入场和身份核验
- 考试纪律监督
- 试卷分发和收集
- 考试异常情况处理

系统在此阶段无需参与，待考试结束、试卷收集完成后，进入试卷收集扫描阶段。

#### 4.1.4.4 阶段三：试卷收集扫描阶段

**试卷收集和扫描流程：**
```mermaid
graph TD
    A[考试结束] --> B[监考员收集试卷]
    B --> C[试卷清点核验]
    C --> D[提交试卷扫描员]
    D --> E[试卷整理分类]
    E --> F[扫描设备准备]
    F --> G[试卷扫描上传]
    G --> H[图像质量检查]
    H --> I{质量合格?}
    I -->|否| J[重新扫描]
    I -->|是| K[图像预处理]
    K --> L[试卷区域识别]
    L --> M[学生信息识别]
    M --> N[答题区域定位]
    N --> O[OCR文字识别]
    O --> P[数据结构化]
    P --> Q[质量验证]
    Q --> R[扫描结果保存]
    J --> G
```

#### 4.1.4.5 阶段四：在线阅卷评分阶段

**智能评阅系统：**
```mermaid
graph TD
    A[扫描完成] --> B[评阅任务生成]
    B --> C[题目类型分析]
    C --> D{题目类型}
    D -->|客观题| E[AI自动评阅]
    D -->|主观题| F[人工评阅分配]
    E --> G[置信度检查]
    G --> H{置信度>95%?}
    H -->|是| I[自动评分]
    H -->|否| J[人工复核]
    F --> K[阅卷员分配]
    K --> L[评阅任务推送]
    I --> M[评分结果保存]
    J --> L
    L --> M
```

**评分质量控制（双评机制）：**
```mermaid
graph TD
    A[主观题评阅] --> B[第一评阅员评分]
    B --> C[第二评阅员评分]
    C --> D[分数差异检查]
    D --> E{分数差异<10%?}
    E -->|是| F[取平均分]
    E -->|否| G[第三评阅员仲裁]
    G --> H[仲裁结果]
    F --> I[最终分数确定]
    H --> I
    I --> J[质量标记]
    J --> K[评分完成]
```

#### 4.1.4.6 阶段五：成绩处理与分析阶段

**成绩汇总计算：**
```mermaid
graph TD
    A[评阅完成] --> B[成绩数据收集]
    B --> C[题目分数汇总]
    C --> D[科目总分计算]
    D --> E[权重分配计算]
    E --> F[等级评定]
    F --> G[排名计算]
    G --> H[异常分数检查]
    H --> I{异常检查}
    I -->|发现异常| J[异常分数标记]
    I -->|无异常| K[成绩数据保存]
    J --> L[人工审核]
    L --> M[异常处理]
    M --> K
```

**成绩审核工作流：**
```mermaid
graph TD
    A[成绩计算完成] --> B[自动异常检测]
    B --> C[生成审核任务]
    C --> D[任课老师初审]
    D --> E{初审结果}
    E -->|通过| F[班主任复审]
    E -->|异常| G[异常处理]
    F --> H{复审结果}
    H -->|通过| I[教导主任终审]
    H -->|异常| G
    I --> J{终审结果}
    J -->|通过| K[成绩发布]
    J -->|异常| G
    G --> L[异常调查]
    L --> M[处理结果]
    M --> D
```

**成绩发布流程控制（简化版）**：
```mermaid
graph TD
    A[成绩发布申请] --> B[权限验证]
    B --> C{权限检查}
    C -->|无权限| D[拒绝发布]
    C -->|有权限| E[快速检查]
    E --> F{基本检查通过?}
    F -->|否| G[返回处理]
    F -->|是| H[一键发布]
    H --> I[发送通知]
    I --> J[记录日志]
```

**简化发布特点**：
- **一键发布**：减少审核层级，支持一键快速发布
- **智能检查**：自动执行基本检查，无需人工干预
- **快速通知**：发布后立即通知相关人员
- **权限简化**：阅卷管理员和教导主任均可直接发布
- **批量操作**：支持多个考试科目的批量发布
- **状态实时**：发布状态实时更新，无需等待

#### 4.1.4.7 联考特殊流程

**报告生成流程：**
```mermaid
graph TD
    A[成绩发布] --> B[报告生成任务]
    B --> C[数据预处理]
    C --> D[分析计算]
    D --> E[报告模板选择]
    E --> F[内容生成]
    F --> G[图表生成]
    G --> H[报告格式化]
    H --> I[质量检查]
    I --> J{质量合格?}
    J -->|否| K[重新生成]
    J -->|是| L[报告发布]
    K --> F
    L --> M[知相关用户]
```

**报告类型定义：**

| 报告类型   | 目标用户    | 主要内容         | 生成周期  |
|--------|---------|--------------|-------|
| 学生个人报告 | 学生、家长   | 个人成绩、排名、分析建议 | 考试后3天 |
| 班级报告   | 班主任     | 班级整体表现、学生对比  | 考试后2天 |
| 学科报告   | 任课老师    | 学科成绩分析、教学建议  | 考试后2天 |
| 年级报告   | 年级长     | 年级整体分析、班级对比  | 考试后1天 |
| 学校报告   | 校长、教导主任 | 全校分析、决策建议    | 考试后1天 |

#### 4.1.4.7 联考特殊流程

**联考邀请流程：**
```mermaid
graph TD
    A[主办方创建联考] --> B[设置联考参数]
    B --> C[选择邀请学校]
    C --> D[发送邀请函]
    D --> E[等待学校响应]
    E --> F{响应情况}
    F -->|接受| G[确认参与]
    F -->|拒绝| H[记录拒绝原因]
    F -->|超时| I[催促或移除]
    G --> J[同步考试设置]
    J --> K[准备联考]
    H --> L[更新参与名单]
    I --> L
    L --> M{所有学校确认?}
    M -->|否| E
    M -->|是| N[联考正式确认]
```

**跨租户成绩汇总：**
```mermaid
graph TD
    A[各校考试完成] --> B[成绩数据上报]
    B --> C[数据格式验证]
    C --> D[数据完整性检查]
    D --> E[跨校排名计算]
    E --> F[统计分析]
    F --> G[联考报告生成]
    G --> H[结果分发]
    H --> I[各校本地化]
```

#### 4.1.4.8 异常处理机制

**异常分类处理：**

| 异常类型   | 严重程度 | 处理时限 | 处理方式      |
|--------|------|------|-----------|
| 系统宕机   | 紧急   | 5分钟  | 自动切换+人工介入 |
| 数据丢失   | 严重   | 30分钟 | 数据恢复+通知用户 |
| 扫描设备故障 | 重要   | 15分钟 | 设备修复+备用设备 |
| 识别错误   | 一般   | 2小时  | 重新识别+人工校验 |


#### 4.1.4.9 权限控制详细说明

**考试创建权限矩阵：**

| 角色   | 本班考试 | 本年级考试 | 本校考试 | 跨校联考 |
|------|------|-------|------|------|
| 校长   | ✓    | ✓     | ✓    | ✓    |
| 教导主任 | ✓    | ✓     | ✓    | ✓    |
| 年级长  | ✓    | ✓     | ✗    | ✗    |
| 班主任  | ✓    | ✗     | ✗    | ✗    |
| 任课老师 | ✓    | ✗     | ✗    | ✗    |

**数据过滤规则：**
```sql
-- 成绩查看权限过滤
CREATE OR REPLACE FUNCTION filter_score_data(
  user_id INTEGER,
  exam_id INTEGER
) RETURNS TABLE(
  student_id INTEGER,
  subject VARCHAR(50),
  score DECIMAL(5,2),
  rank INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ss.student_id,
    ss.subject,
    ss.raw_score,
    ss.class_rank
  FROM tenant_001.student_scores ss
  JOIN tenant_001.students s ON ss.student_id = s.id
  WHERE ss.exam_id = filter_score_data.exam_id
  AND (
    -- 校长和教导主任可查看全部
    EXISTS (
      SELECT 1 FROM public.users u JOIN public.roles r ON u.role_id = r.id
      WHERE u.id = user_id AND r.name IN ('principal', 'dean')
    )
    OR
    -- 年级长可查看本年级
    EXISTS (
      SELECT 1 FROM tenant_001.teacher_assignments ta
      WHERE ta.teacher_user_id = user_id AND ta.role = 'grade_leader'
      AND s.grade_level = (SELECT grade_level FROM tenant_001.classes WHERE id = ta.class_id)
    )
    OR
    -- 班主任可查看本班
    EXISTS (
      SELECT 1 FROM tenant_001.teacher_assignments ta
      WHERE ta.teacher_user_id = user_id AND ta.role = 'head_teacher'
      AND s.administrative_class_id = ta.class_id
    )
    OR
    -- 任课老师可查看任教学科
    EXISTS (
      SELECT 1 FROM tenant_001.teacher_assignments ta
      WHERE ta.teacher_user_id = user_id AND ta.role = 'subject_teacher'
      AND ta.subject = ss.subject
      AND s.administrative_class_id = ta.class_id
    )
  );
END;
$$ LANGUAGE plpgsql;
```

#### ******** 性能优化策略

**数据库优化：**
```sql
-- 考试相关索引
CREATE INDEX idx_exams_type_status ON tenant_001.exams(type, status);
CREATE INDEX idx_student_scores_exam_subject ON tenant_001.student_scores(exam_id, subject);
CREATE INDEX idx_student_scores_student_exam ON tenant_001.student_scores(student_id, exam_id);
CREATE INDEX idx_grading_tasks_assigned_status ON tenant_001.grading_tasks(assigned_to, status);
CREATE INDEX idx_paper_scans_exam_status ON tenant_001.paper_scans(exam_id, status);

-- 成绩统计查询优化
CREATE MATERIALIZED VIEW tenant_001.exam_statistics AS
SELECT 
  exam_id,
  subject,
  COUNT(*) as student_count,
  AVG(raw_score) as avg_score,
  MAX(raw_score) as max_score,
  MIN(raw_score) as min_score,
  STDDEV(raw_score) as stddev_score
FROM tenant_001.student_scores
GROUP BY exam_id, subject;
```

**缓存策略：**

| 缓存项  | 缓存键格式                         | 过期时间 | 更新策略    |
|------|-------------------------------|------|---------|
| 考试信息 | exam:{exam_id}                | 1小时  | 考试修改时失效 |
| 学生成绩 | scores:{exam_id}:{student_id} | 30分钟 | 成绩更新时失效 |
| 阅卷进度 | progress:{exam_id}            | 5分钟  | 实时更新    |
| 用户权限 | perms:{user_id}               | 30分钟 | 权限变更时失效 |
| 考试统计 | stats:{exam_id}               | 2小时  | 成绩发布时失效 |

#### 4.1.4.11 监控和日志

**关键指标监控：**

| 指标名称    | 监控周期 | 正常范围   | 告警阈值     |
|---------|------|--------|----------|
| 考试创建成功率 | 1分钟  | >99%   | <95%     |
| 成绩计算准确率 | 实时   | >99.9% | <99.5%   |
| 阅卷进度完成率 | 5分钟  | 按计划    | 延迟>20%   |
| 系统并发用户数 | 1分钟  | -      | >设计容量80% |
| API响应时间 | 30秒  | <2秒    | >5秒      |
| 缓存命中率   | 5分钟  | >80%   | <60%     |

**操作日志记录：**
```sql
-- 操作日志表
CREATE TABLE tenant_001.operation_logs (
  id BIGSERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES public.users(id),
  operation_type VARCHAR(50) NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  resource_id INTEGER,
  operation_details JSONB,
  ip_address INET,
  user_agent TEXT,
  request_id VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 需要记录的关键操作示例
INSERT INTO tenant_001.operation_logs (user_id, operation_type, resource_type, resource_id, operation_details)
VALUES 
  (1, 'create_exam', 'exam', 123, '{"exam_name": "期末考试", "subjects": ["数学", "语文"]}'),
  (1, 'publish_scores', 'exam', 123, '{"affected_students": 150}'),
  (2, 'modify_score', 'student_score', 456, '{"old_score": 85, "new_score": 87, "reason": "阅卷错误"}');
```
