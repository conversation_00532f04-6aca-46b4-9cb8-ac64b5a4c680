# 4.0 试卷与题库管理

## 4.0.1 模块概述

试卷与题库管理模块提供平台级的共享题库和试卷管理中心，支持从公共教辅资源导入题目，手动创建题目，并基于题库灵活组织生成标准化试卷模板。教辅中的混合内容（知识讲解、例题、试题）通过引用题库中的题目实现标准化管理，用户可以通过教辅结构化的方式选择题库中的题目进行组卷，并生成题卡合一答题卡。教辅的知识讲解内容为题目提供理论基础，例题为试题提供解题示范。

### 4.0.1.1 模块价值
- **资源复用**：建立标准化的题库资源池，避免重复建设
- **质量统一**：统一题目标准和试卷格式，提升考试质量
- **协作共享**：支持跨租户的题库共享和试卷协作
- **智能组卷**：基于知识点和难度的智能试卷生成
- **混合内容整合**：教辅中的知识讲解、例题和试题通过引用题库实现标准化管理
- **结构化选题**：支持按教辅混合内容结构（知识讲解→例题→试题）进行题目选择
- **题卡一体**：生成题目内容与答题区域一体化的答题卡，提升答题体验

### 4.0.1.2 模块边界
- **包含功能**：题库管理、试卷模板创建、教辅混合内容引用管理、题卡合一答题卡生成、资源授权、版本控制
- **不包含功能**：具体考试实施、成绩录入、学生答题、答题卡扫描识别、教辅内容管理、知识讲解内容创建
- **上游依赖**：公共教辅资源（包含知识讲解、例题、试题的混合内容）、用户权限系统
- **下游服务**：考试管理、阅卷系统、成绩分析、答题卡扫描系统

## 4.0.2 核心功能详述

### 4.0.2.1 中央题库管理 (Question Bank)

**题目导入功能**：
- **批量导入**：支持从公共教辅 (`public.textbooks`) 的混合内容中批量导入题目（例题和试题）
- **数据验证**：导入过程中自动验证题目完整性和格式正确性
- **内容类型识别**：智能识别教辅中的知识讲解、例题和试题内容
- **重复检测**：智能识别和处理重复题目，避免资源冗余
- **关联维护**：保持题目与原始教辅资源的关联关系，包括在混合内容中的上下文信息

**题目创建功能**：
- **多题型支持**：基于题型组合字典，支持选择题、填空题、解答题、计算题等多种题型，确保题型与学科年级的匹配性
- **富文本编辑**：提供专业的题目编辑器，支持数学公式、图片插入
- **模板复用**：提供常用题目模板，加速题目创建过程
- **预览功能**：实时预览题目显示效果，确保格式正确

**题型组合管理功能**：
- **题型字典管理**：维护系统支持的标准题型库，包括题型编码、名称和描述
- **学科年级适配**：定义不同学科和年级下可用的题型组合，确保题型选择的合法性
- **组合验证机制**：创建题目时自动验证所选题型是否在当前学科年级下可用
- **题型扩展支持**：支持根据教学需要添加新的题型和题型组合规则

**版本控制机制**：
- **版本追踪**：题目的任何修改都会产生新版本，确保历史试卷的准确性
- **变更记录**：详细记录每次修改的内容、时间和操作人
- **回滚功能**：支持回滚到任意历史版本
- **影响分析**：分析题目修改对现有试卷的影响

**元数据管理**：
- **分类标签**：学科、知识点、难度等级、题型、分值建议
- **多维度属性**：支持一题多解、多种难度系数、多个知识点关联
- **质量标记**：题目质量评级、使用频次、正确率统计
- **关联信息**：与教辅章节、考试大纲的关联关系
- **使用统计**：题目使用历史、反馈收集、效果评估
- **版本追踪**：支持题目内容、答案、解析的版本化管理

### 4.0.2.1.1 增强的题目属性管理

**多答案支持**：
- **标准答案**：权威的官方标准答案
- **参考答案**：多种可接受的答案变体
- **评分标准**：分步骤的评分细则和要点
- **答案类型**：支持文本、数学公式、图形、代码等多种答案格式

**多解析体系**：
- **基础解析**：面向基础学习者的详细步骤解析
- **进阶解析**：面向高水平学习者的简化解析
- **思路解析**：解题思路和方法指导
- **易错提醒**：常见错误点和注意事项
- **拓展解析**：相关知识点和变式题目

**多难度等级**：
```yaml
difficulty_levels:
  cognitive_level:     # 认知难度
    - "记忆理解"       # 1级：基础概念记忆
    - "简单应用"       # 2级：直接公式应用
    - "综合应用"       # 3级：多知识点综合
    - "分析评价"       # 4级：分析推理能力
    - "创新创造"       # 5级：创新性解决问题
  
  computational_level: # 计算难度
    - "基础计算"       # 简单数值计算
    - "中等计算"       # 多步骤计算
    - "复杂计算"       # 复杂公式推导
  
  time_consumption:    # 时间消耗
    - "快速"           # <2分钟
    - "中等"           # 2-5分钟
    - "较长"           # 5-10分钟
    - "很长"           # >10分钟
```

**多知识点关联**：
- **主要知识点**：题目主要考查的核心知识点
- **次要知识点**：题目涉及的辅助知识点
- **前置知识**：解题所需的前序知识要求
- **知识点权重**：各知识点在题目中的重要程度
- **知识图谱**：知识点之间的关联关系

**题目标签系统**：
```yaml
question_tags:
  content_tags:        # 内容标签
    - "概念理解"
    - "公式应用"
    - "实际应用"
    - "数据分析"
  
  skill_tags:          # 能力标签
    - "逻辑推理"
    - "计算能力"
    - "图形理解"
    - "文字表达"
  
  scenario_tags:       # 情境标签
    - "生活实际"
    - "科学实验"
    - "历史背景"
    - "跨学科"
```

### 4.0.2.2 试卷模板管理 (Paper Templates)

**试卷组装功能**：
- **题目筛选**：基于学科、难度、知识点等条件筛选题目
- **拖拽组卷**：直观的拖拽界面，快速组织试卷结构
- **分数配置**：灵活设置各题目和章节的分值分配
- **预览检查**：实时预览试卷效果，检查格式和内容

**结构定义功能**：
- **章节管理**：支持定义试卷的章节、大题（如"第一部分：选择题"）
- **分值设置**：为每个部分设置总分和分值分布
- **排列顺序**：灵活调整题目和章节的显示顺序
- **格式控制**：定义试卷的页面布局和显示样式

**智能组卷功能**：
- **约束条件**：根据知识点、难度分布、总分等约束条件
- **算法优化**：使用智能算法自动生成符合要求的试卷
- **多方案生成**：生成多个候选方案供用户选择
- **手动调整**：支持在自动生成基础上进行手动调整

**版本与状态管理**：
- **状态流转**：草稿 → 已发布 → 已归档的状态管理
- **版本控制**：支持试卷模板的版本管理和历史追溯
- **权限控制**：不同状态下的访问和修改权限控制
- **审核流程**：试卷发布前的质量审核机制

### 4.0.2.3 共享与权限管理

**公共模板管理**：
- **全局可见**：系统管理员可以创建对所有租户可见的公共试卷模板
- **质量控制**：公共模板需要经过严格的质量审核
- **更新推送**：公共模板更新时自动通知相关租户
- **使用统计**：跟踪公共模板的使用情况和效果

**授权使用机制**：
- **精细授权**：可将特定试卷模板的使用权授权给一个或多个租户
- **权限层级**：只读权限、修改权限、复制权限等不同权限级别
- **时间限制**：支持设置授权的有效期限
- **审核机制**：授权申请的审核和批准流程

### 4.0.2.4 题卡合一答题卡生成 (Integrated Answer Sheet Generation)

**智能版式布局**：
- **自适应布局**：根据题目内容和类型自动计算最优版式布局
- **空间优化**：智能优化题目展示区域和答题区域的空间分配
- **多页分割**：支持大试卷的智能分页和跨页题目处理
- **美观设计**：确保答题卡的视觉美观和使用便利性

**题型对应答题区**：
- **选择题区域**：标准化涂卡圆圈，支持A/B/C/D等选项
- **填空题区域**：根据填空数量和长度生成标准化横线区域
- **主观题区域**：提供横线纸、网格纸等不同类型的答题空间
- **特殊题型区域**：支持作图题、计算题等特殊答题区域设计

**扫描识别适配**：
- **定位标记**：在页面关键位置添加扫描定位标记
- **识别码生成**：为每个答题区域生成唯一的识别码
- **质量检测**：自动检测生成的答题卡质量，确保扫描识别率
- **兼容性保证**：确保与现有扫描识别系统的完全兼容

**个性化定制**：
- **学校标识**：支持添加学校logo和个性化页眉页脚
- **考试信息**：自动填充考试名称、科目、时间等基本信息
- **样式模板**：提供多种答题卡样式模板供选择
- **打印优化**：针对不同打印设备优化输出格式

## 4.0.3 业务流程

### 4.0.3.1 组卷流程

```mermaid
graph TD
    A[教辅练习题] --> B[题目质量审核]
    C[手动创建题目] --> B
    D[导入外部题目] --> B
    
    B --> E[题目入库]
    E --> F{中央题库}
    
    G[教辅引用题库题目] --> F
    
    F --> H[基于教辅结构化选题]
    F --> I[直接题库选题]
    
    H --> J[题目整合]
    I --> J
    
    J --> K[组装试卷模板]
    K --> L[设置试卷结构和分数]
    L --> M[生成题卡合一答题卡]
    M --> N[预览和检查]
    N --> O[发布试卷模板]
    O --> P[租户创建考试时选用]
    P --> Q[反馈和优化]
    Q --> F
```

### 4.0.3.2 题卡合一答题卡生成流程

```mermaid
graph TD
    A[接收试卷题目列表] --> B[分析题目类型和内容]
    B --> C[计算版式布局]
    C --> D[优化空间分配]
    
    D --> E[生成题目展示区域]
    E --> F[生成对应答题区域]
    
    F --> G{题目类型}
    G -->|选择题| H[生成涂卡圆圈]
    G -->|填空题| I[生成填空横线]
    G -->|主观题| J[生成答题网格/横线]
    
    H --> K[添加识别标记]
    I --> K
    J --> K
    
    K --> L[页面分割处理]
    L --> M[添加页眉页脚信息]
    M --> N[质量检测]
    N --> O{检测通过}
    
    O -->|否| P[调整布局参数]
    O -->|是| Q[生成最终PDF]
    
    P --> C
    Q --> R[输出题卡合一答题卡]
```

### 4.0.3.3 权限管理流程

```mermaid
graph TD
    A[创建试卷模板] --> B[设置访问权限]
    B --> C{权限类型}
    C -->|公共模板| D[全租户可见]
    C -->|私有模板| E[仅本租户可见]
    C -->|授权模板| F[指定租户可见]
    D --> G[质量审核]
    E --> H[直接使用]
    F --> I[授权审批]
    G --> J[发布使用]
    I --> J
    H --> J
    J --> K[使用统计]
    K --> L[效果评估]
```

## 4.0.4 数据模型

### 4.0.4.1 核心数据表

**中央题库表**：
- `public.question_bank`：存储所有标准化的题目信息，支持多答案、多解析、多难度、多知识点
- 包含题目内容、多维度答案、分层解析、版本信息等
- 引用题型字典，确保题型的标准化和一致性

**题型管理表**：
- `public.question_types`：题型字典表，存储系统支持的标准题型定义
- 包含题型编码、名称、描述等基本信息
- `public.compose_question_types`：题型组合表，定义学科-年级-题型的有效组合
- 确保题型选择的合法性，防止不合理的题型使用

**试卷模板表**：
- `public.exam_papers`：存储试卷模板的基本信息
- 包含标题、学科、年级、总分、结构定义等

**关联关系表**：
- `public.exam_paper_questions`：存储试卷模板与题库中题目的关联关系
- 支持题目在试卷中的位置、分值、显示顺序等信息

**题卡合一答题卡表**：
- `tenant_001.integrated_answer_sheets`：存储题卡合一答题卡模板信息
- 包含布局配置、识别配置、页面数量等信息

**题卡合一题目映射表**：
- `tenant_001.integrated_sheet_questions`：存储答题卡与题目的映射关系
- 支持题目来源标识（题库）、位置配置、答题区域配置等

**教辅混合内容引用表**：
- `public.textbook_mixed_contents`：存储教辅四层结构与混合内容的引用关系
- 包含内容类型（知识讲解、例题、试题）、模块类型、显示顺序、阅读序列等信息
- 支持知识讲解的富文本内容存储和例题、试题的题库引用

**混合内容组织表**：
- `public.content_reading_orders`：存储混合内容的阅读顺序控制，支持个性化学习

**题目多维度属性表**：
- `public.question_answers`：存储题目的多个答案（标准答案、参考答案、评分标准）
- `public.question_explanations`：存储题目的多层次解析（基础、进阶、思路、易错、拓展）
- `public.question_difficulties`：存储题目的多维度难度（认知、计算、时间）
- `public.question_knowledge_points`：存储题目与知识点的多对多关联关系

### 4.0.4.2 权限控制表

**教辅授权表**：
- `public.textbook_tenant_access`：控制教辅资源的租户访问权限
- 支持授权人、授权时间、权限范围等信息

### ******* 增强题目数据模型

**题目核心表结构**：
```sql
-- 题型字典表
CREATE TABLE public.question_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(30) UNIQUE NOT NULL,              -- 题型编码 (SINGLE_CHOICE, MULTI_CHOICE, FILL_BLANK, etc.)
    type_name VARCHAR(100) NOT NULL,               -- 题型名称 (单选题、多选题、填空题等)
    description TEXT,                              -- 题型描述
    is_active BOOLEAN DEFAULT TRUE,               -- 是否启用
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 题型组合表 (定义学科-年级-题型的有效组合)
CREATE TABLE public.compose_question_types (
    question_type_code VARCHAR(30) REFERENCES public.question_types(code),
    subject_code VARCHAR(30) REFERENCES public.subjects(code),
    grade_level_code VARCHAR(30) REFERENCES public.grade_levels(code),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (question_type_code, subject_code, grade_level_code)
);

-- 中央题库表（增强版）
CREATE TABLE public.question_bank (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_content TEXT NOT NULL,
    question_type_code VARCHAR(30) REFERENCES public.question_types(code), -- 引用题型字典
    subject_code VARCHAR(30) REFERENCES public.subjects(code),             -- 引用学科字典
    grade_level_code VARCHAR(30) REFERENCES public.grade_levels(code),     -- 引用年级字典
    source_type VARCHAR(30) DEFAULT 'manual', -- manual, textbook, import
    source_reference VARCHAR(200),
    version INTEGER DEFAULT 1,
    status VARCHAR(20) DEFAULT 'active',
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- 确保题型-学科-年级组合的有效性
    CONSTRAINT fk_valid_question_type_composition 
        FOREIGN KEY (question_type_code, subject_code, grade_level_code) 
        REFERENCES public.compose_question_types(question_type_code, subject_code, grade_level_code)
);

-- 题目答案表（支持多答案）
CREATE TABLE public.question_answers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_id UUID REFERENCES public.question_bank(id) ON DELETE CASCADE,
    answer_type VARCHAR(30) NOT NULL, -- standard, reference, scoring_criteria
    answer_content TEXT NOT NULL,
    answer_format VARCHAR(20) DEFAULT 'text', -- text, formula, image, code
    is_primary BOOLEAN DEFAULT FALSE,
    score_weight DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 题目解析表（支持多层次解析）
CREATE TABLE public.question_explanations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_id UUID REFERENCES public.question_bank(id) ON DELETE CASCADE,
    explanation_type VARCHAR(30) NOT NULL, -- basic, advanced, approach, common_errors, extension
    explanation_content TEXT NOT NULL,
    target_audience VARCHAR(30), -- beginner, intermediate, advanced
    display_order INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 题目难度表（支持多维度难度）
CREATE TABLE public.question_difficulties (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_id UUID REFERENCES public.question_bank(id) ON DELETE CASCADE,
    difficulty_type VARCHAR(30) NOT NULL, -- cognitive, computational, time_consumption
    difficulty_level VARCHAR(20) NOT NULL,
    difficulty_value DECIMAL(3,2), -- 数值化难度系数
    assessment_basis TEXT, -- 难度评定依据
    assessed_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 题目知识点关联表（支持多知识点）
CREATE TABLE public.question_knowledge_points (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    question_id UUID REFERENCES public.question_bank(id) ON DELETE CASCADE,
    knowledge_point_id UUID REFERENCES public.knowledge_points(id),
    relation_type VARCHAR(30) NOT NULL, -- primary, secondary, prerequisite
    weight DECIMAL(3,2) DEFAULT 1.0, -- 知识点在题目中的权重
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(question_id, knowledge_point_id, relation_type)
);

-- 教辅混合内容引用表（简化版，支持个性化）
CREATE TABLE public.textbook_mixed_contents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    textbook_id UUID REFERENCES public.textbooks(id) ON DELETE CASCADE,
    chapter_id UUID NOT NULL, -- 章节ID
    module_id UUID NOT NULL, -- 模块ID
    module_type VARCHAR(30) NOT NULL, -- knowledge_guide, knowledge_exploration, consolidation_training
    content_type VARCHAR(30) NOT NULL, -- knowledge_explanation, example_question, practice_question
    sequence_number INTEGER NOT NULL, -- 在模块中的序号
    title VARCHAR(200), -- 内容标题
    content_data JSONB, -- 内容数据（知识讲解的富文本内容）
    question_ref UUID REFERENCES public.question_bank(id), -- 题目引用（例题和试题使用）
    display_format VARCHAR(30) DEFAULT 'rich_text', -- 显示格式
    is_interactive BOOLEAN DEFAULT FALSE, -- 是否可交互
    solution_display BOOLEAN DEFAULT FALSE, -- 是否显示解答过程（例题使用）
    -- 个性化支持字段
    difficulty_level VARCHAR(20), -- 难度级别：beginner, intermediate, advanced
    is_optional BOOLEAN DEFAULT FALSE, -- 是否为可选内容
    learning_notes TEXT, -- 学习注释和建议
    prerequisites TEXT, -- 前置知识要求
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(textbook_id, chapter_id, module_id, sequence_number)
);
```

## 4.0.5 技术实现

### 4.0.5.1 性能要求
- **响应时间**：题目查询 < 500ms，试卷生成 < 2s，题卡合一答题卡生成 < 5s
- **并发支持**：支持100+ 用户同时编辑不同试卷模板，50+ 用户同时生成答题卡
- **存储容量**：支持10万+ 题目，1万+ 试卷模板，5万+ 题卡合一答题卡模板
- **数据一致性**：确保题目版本变更的数据一致性，答题卡布局的准确性
- **教辅引用性能**：支持基于教辅四层结构化选择500+ 题目，响应时间 < 2s
- **多维查询性能**：支持多答案、多解析、多难度、多知识点的复合查询，响应时间 < 1s

### 4.0.5.2 安全要求
- **数据加密**：敏感题目内容加密存储
- **访问控制**：严格的权限验证和审计日志
- **版本保护**：防止恶意修改和数据丢失
- **备份恢复**：完整的数据备份和恢复机制

### 4.0.5.3 集成接口
- **教辅系统接口**：支持从外部教辅系统导入题目，支持教辅题目引用关系管理
- **考试管理接口**：为考试管理模块提供试卷模板和题卡合一答题卡
- **数据分析接口**：提供题目使用统计和效果数据，包含教辅引用使用情况
- **第三方题库接口**：支持对接第三方题库资源
- **答题卡扫描接口**：为扫描识别系统提供题卡合一答题卡的布局和识别配置
- **打印服务接口**：支持题卡合一答题卡的批量打印输出

## 4.0.6 监控与运维

### 4.0.6.1 关键指标
- **题目质量指标**：题目正确率、使用频次、反馈评分
- **系统性能指标**：响应时间、并发用户数、存储使用率
- **业务指标**：试卷创建数量、题目使用分布、用户活跃度
- **教辅引用指标**：教辅引用频次、基于教辅选题数量、用户满意度、模块类型使用分布
- **题库质量指标**：多维度难度分布、知识点覆盖度、解析完整性、答案准确性
- **题卡合一指标**：答题卡生成成功率、布局质量得分、扫描识别准确率

### 4.0.6.2 告警机制
- **性能告警**：系统响应时间超过阈值时自动告警
- **质量告警**：发现低质量题目时推送给管理员
- **容量告警**：存储空间使用超过80%时预警

### 4.0.6.3 日志记录
- **操作日志**：记录所有题目和试卷的创建、修改、删除操作
- **访问日志**：记录用户访问题库和试卷模板的详细信息
- **性能日志**：记录系统性能指标和资源使用情况

> **相关章节**：
> - [4.1 考试管理系统](./4_1_exam_management.md) - 试卷模板在考试中的应用
> - [4.4 教辅管理系统](./4_4_teaching_aids_management.md) - 题目来源的教辅资源管理
> - [6. 技术架构](./6_technical_architecture.md) - 系统架构和数据模型详情