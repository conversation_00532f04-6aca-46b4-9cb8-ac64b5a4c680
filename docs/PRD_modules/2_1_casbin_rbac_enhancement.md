# 2.1 Casbin RBAC 增强需求文档

## 2.1.1 需求背景

基于原始PRD中的用户角色与权限设计，为了实现更加灵活、高效、可扩展的多租户权限控制系统，引入 casbin-rs 作为权限引擎的核心组件。本文档详细说明了Casbin RBAC系统的设计方案，简化了过于复杂的特殊授权流程，并明确了前端菜单控制和数据权限的实现方式。

## 2.1.2 需求改进说明

### 原始PRD问题分析：

1. **特殊授权系统过于复杂**：原始设计中的多级审批、复杂权限类型分类过于繁重，不利于初期实现
2. **缺少Casbin集成规范**：虽然提到使用casbin-rs，但缺少具体的模型设计和集成方案
3. **前端菜单控制规范不明确**：没有明确说明如何基于权限控制前端功能菜单
4. **数据权限实现方案模糊**：组织级数据访问控制缺少具体实现指导

### 改进方案：

1. **简化特殊授权**：初期实现基础的临时权限授权，复杂审批流程后续迭代实现
2. **明确Casbin模型**：设计清晰的多租户RBAC模型文件
3. **规范菜单控制**：定义基于权限的前端菜单控制API和数据结构
4. **细化数据权限**：明确数据级权限的实现方式和casbin策略设计

## 2.1.3 Casbin RBAC 模型设计

### 模型结构（model.conf）

```ini
[request_definition]
r = sub, dom, obj, act

[policy_definition]
p = sub, dom, obj, act, eft

[role_definition]
g = _, _, _
g2 = _, _, _

[policy_effect]
e = some(where (p.eft == allow)) && !some(where (p.eft == deny))

[matchers]
m = g(r.sub, p.sub, r.dom) && r.dom == p.dom && r.obj == p.obj && r.act == p.act
```

### 模型说明：

- **sub**: 用户身份标识 (user_id:role_id:target_type:target_id)
- **dom**: 租户域 (tenant_id)
- **obj**: 资源对象 (resource:scope)
- **act**: 操作动作 (read, write, create, delete, manage)
- **eft**: 效果 (allow/deny)

### 权限层级设计：

```
租户域(tenant_001)
├── 学校级权限
│   ├── 校长: school:all -> read,write,manage
│   └── 教导主任: school:teaching -> read,write,manage
├── 学科组权限  
│   └── 学科组长: subject_group:math -> read,write,manage
├── 年级权限
│   └── 年级长: grade:grade_1 -> read,write,manage
├── 班级权限
│   ├── 班主任: class:class_1a -> read,write,manage
│   └── 任课老师: class:class_1a,subject:math -> read,write
└── 学生权限
    └── 学生: student:self -> read
```

## 2.1.4 简化的特殊授权设计

### 基础临时权限类型：

| 权限类型 | 描述 | 最长期限 | 审批级别 |
|---------|------|----------|----------|
| 临时查看 | 临时访问特定数据的只读权限 | 7天 | 直属上级 |
| 临时管理 | 临时获得特定范围的管理权限 | 3天 | 跨级审批 |
| 代理职务 | 临时代理他人职务权限 | 30天 | 高级管理员 |

### 审批流程简化：

```mermaid
graph TD
    A[用户申请特殊权限] --> B[系统风险评估]
    B --> C{权限类型}
    C -->|临时查看| D[直属上级审批]
    C -->|临时管理| E[跨级审批]
    C -->|代理职务| F[高级管理员审批]
    D --> G[权限授予]
    E --> G
    F --> G
    G --> H[自动到期回收]
```

## 2.1.5 前端菜单控制规范

### 菜单权限数据结构：

```typescript
interface MenuPermission {
  menu_id: string;           // 菜单标识
  name: string;              // 菜单名称
  path: string;              // 路由路径
  icon?: string;             // 图标
  parent_id?: string;        // 父菜单ID
  required_permissions: string[]; // 所需权限列表
  data_scopes?: string[];    // 数据范围限制
  children?: MenuPermission[]; // 子菜单
}
```

### 菜单控制API设计：

```yaml
# 获取用户可访问菜单
GET /api/v1/permissions/menus
Response:
  data:
    - menu_id: "student_management"
      name: "学生管理"
      path: "/students"
      required_permissions: ["student:read"]
      data_scopes: ["class:1a", "class:1b"]
      children: [...]
```

### 前端菜单渲染逻辑：

```typescript
// 基于权限过滤菜单
function filterMenusByPermissions(
  menus: MenuPermission[], 
  userPermissions: string[]
): MenuPermission[] {
  return menus.filter(menu => 
    menu.required_permissions.every(perm => 
      userPermissions.includes(perm)
    )
  ).map(menu => ({
    ...menu,
    children: menu.children ? 
      filterMenusByPermissions(menu.children, userPermissions) : []
  }));
}
```

## 2.1.6 数据级权限实现方案

### Casbin策略设计：

```
# 数据范围策略格式
# p, user_identity, tenant_id, resource:scope, action, allow

# 示例策略
p, user_123:teacher:class:1a, tenant_001, student:class_1a, read, allow
p, user_123:teacher:class:1a, tenant_001, exam:class_1a, write, allow
p, user_456:grade_leader:grade:1, tenant_001, student:grade_1, read, allow
p, user_789:principal:school:all, tenant_001, *, *, allow
```

### 数据查询中间件：

```rust
// 数据查询权限装饰器
pub async fn enforce_data_permission<T>(
    enforcer: &Enforcer,
    user_identity: &str,
    tenant_id: &str,
    resource: &str,
    action: &str,
    query_builder: QueryBuilder<T>
) -> Result<QueryBuilder<T>, PermissionError> {
    // 1. 检查基础权限
    let has_permission = enforcer.enforce((
        user_identity, 
        tenant_id, 
        resource, 
        action
    ))?;
    
    if !has_permission {
        return Err(PermissionError::Denied);
    }
    
    // 2. 获取数据范围限制
    let data_scopes = get_user_data_scopes(
        enforcer, user_identity, tenant_id, resource
    ).await?;
    
    // 3. 应用数据范围过滤
    Ok(apply_data_scope_filter(query_builder, data_scopes))
}
```

## 2.1.7 租户隔离策略

### 多租户Casbin策略管理：

```rust
pub struct MultiTenantEnforcer {
    enforcers: HashMap<String, Arc<Enforcer>>,
    pool: PgPool,
}

impl MultiTenantEnforcer {
    // 获取租户专用的enforcer
    pub async fn get_tenant_enforcer(&self, tenant_id: &str) -> Result<Arc<Enforcer>> {
        if let Some(enforcer) = self.enforcers.get(tenant_id) {
            Ok(enforcer.clone())
        } else {
            let enforcer = self.create_tenant_enforcer(tenant_id).await?;
            self.enforcers.insert(tenant_id.to_string(), enforcer.clone());
            Ok(enforcer)
        }
    }
    
    // 创建租户专用enforcer
    async fn create_tenant_enforcer(&self, tenant_id: &str) -> Result<Arc<Enforcer>> {
        let adapter = SqlxAdapter::new(&self.pool, tenant_id).await?;
        let enforcer = Enforcer::new("model.conf", adapter).await?;
        Ok(Arc::new(enforcer))
    }
}
```

## 2.1.8 系统集成要求

### API中间件增强：

```rust
// 权限验证中间件
pub async fn permission_middleware(
    Extension(enforcer): Extension<Arc<MultiTenantEnforcer>>,
    Extension(user_context): Extension<UserContext>,
    req: Request<Body>,
    next: Next<Body>,
) -> Result<Response, StatusCode> {
    let tenant_id = user_context.tenant_id;
    let user_identity = user_context.identity_string();
    let resource = extract_resource_from_path(req.uri().path());
    let action = http_method_to_action(req.method());
    
    let tenant_enforcer = enforcer.get_tenant_enforcer(&tenant_id).await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
    
    let allowed = tenant_enforcer.enforce((
        &user_identity, 
        &tenant_id, 
        &resource, 
        &action
    )).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
    
    if !allowed {
        return Err(StatusCode::FORBIDDEN);
    }
    
    Ok(next.run(req).await)
}
```

### 事务同步要求：

所有角色分配、权限变更、身份绑定操作必须在数据库事务中完成，确保：

1. **角色数据与Casbin策略同步**：角色变更时同步更新casbin策略
2. **身份绑定与权限同步**：用户身份绑定时同步创建相应的casbin角色关系
3. **组织架构变更同步**：组织架构调整时同步更新相关的数据权限策略
4. **审计日志一致性**：所有权限操作的审计日志与业务数据变更保持事务一致性

## 2.1.9 实施优先级

### 第一阶段（核心功能）：
1. Casbin模型设计和基础策略管理
2. 多租户enforcer实现
3. API权限中间件
4. 基础菜单控制API

### 第二阶段（增强功能）：
1. 数据级权限精细控制
2. 临时权限授权功能
3. 权限审计和监控
4. 前端权限组件库

### 第三阶段（高级功能）：
1. 复杂审批流程
2. 权限分析和报告
3. 自动化权限推荐
4. 权限异常检测

## 2.1.10 技术约束

1. **性能要求**：权限检查响应时间 < 50ms
2. **并发支持**：支持10000+并发权限检查
3. **策略数量**：单租户支持100万+权限策略
4. **内存使用**：单个enforcer内存占用 < 100MB
5. **数据一致性**：权限策略与业务数据强一致性

本增强文档将作为Casbin RBAC系统实施的标准规范，确保实现满足实际业务需求且具备良好的可维护性和扩展性。