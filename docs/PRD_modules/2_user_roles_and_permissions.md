# 2. 用户角色与权限

## 2.1 用户角色层次

### 2.1.1 角色分类概述

Deep-Mate平台按照管理层级和业务职能划分了六个主要角色类别，形成了从系统级到终端用户的完整权限体系。
1. **系统级用户**
   - 系统超级管理员：平台运维、租户管理
   - 系统代理商：租户销售、技术支持
   - 运维人员：系统监控、技术维护

2. **租户级用户**
   - 租户管理员：租户配置、用户管理

3. **学校管理用户**
   - 校长：学校整体管理决策、考试策略制定
   - 教导主任：教学管理、考试安排、成绩审核

4. **业务操作用户**
   - 学科组长：学科教学管理、跨年级学科协调、学科考试统筹
   - 考试管理员：考试创建、试卷管理、评分标准管理
   - 阅卷管理员：阅卷任务分配、质量控制、评分标准管理
   - 试卷扫描员：扫描试卷、异常处理
   - 阅卷员：试卷评阅、成绩录入

5. **班级/年级用户**
   - 年级长：年级考试管理、成绩分析
   - 班主任：学生成绩跟踪、波动分析、学习态度评估
   - 任课老师：学科考试参与、成绩查看

6. **终端用户**
   - 学生：成绩查看、学情分析查看
   - 家长：学生成绩查看、学情关注

### 2.1.2 角色权限特点

**权限继承性**：上级角色自动继承下级角色的基础权限
**功能专业性**：不同角色根据业务职能设置专业化权限
**数据隔离性**：确保用户只能访问权限范围内的数据
**跨租户性**：部分角色支持跨租户数据访问（如家长角色）

## 2.2 权限矩阵
| 角色    | 数据权限      | 功能权限             | 跨租户权限     |
|-------|-----------|------------------|-----------|
| 系统管理员 | 全局        | 全部               | 是         |
| 租户管理员 | 租户内       | 管理功能             | 否         |
| 校长    | 本校全部      | 决策分析、策略制定        | 联考协作      |
| 教导主任  | 本校教学相关    | 考试安排、成绩审核        | 否         |
| 学科组长  | 本学科跨年级    | 学科教学管理、学科考试统筹    | 学科联考协作    |
| 考试管理员 | 考试相关      | 考试管理             | 联考协作      |
| 阅卷管理员 | 阅卷相关      | 阅卷管理             | 否         |
| 试卷扫描员 | 扫描试卷      | 扫描识别             | 否         |
| 阅卷员   | 分配试卷      | 评阅功能             | 否         |
| 年级长   | 本年级跨学科    | 年级管理、年级考试管理、成绩分析 | 否         |
| 班主任   | 本班级       | 成绩跟踪             | 否         |
| 任课老师  | 任教学科班级    | 成绩查看、成绩录入        | 否         |
| 学生    | 个人相关      | 查看功能             | 否         |
| 家长    | 子女相关（跨租户） | 查看功能             | 授权验证后可跨租户 |
