
评分流程
```mermaid
graph TD
    A[创建考试] --> B[设置评分规则]
    B --> C{评分类型}
    C -->|扫描登分| D[识别分数]
    C -->|AI评分| E[ocr+评分]
    C -->|线上评分| F[在线阅卷任务]
    D --> G
    E --> G
    F --> G[扫描试卷]
    G --> K[待评分条目生成]
    K --> L[任务分发控制]
    L --> M[评分完成]
    M --> N[核查+反馈+重阅]
```

现有工作流缺点：
1. 无法绑定等价模型
2. 模型效率检测，失效剔除及替换

未来可能处理方案：
1. 原子化每一个模型，记录执行时间与执行结果
2. 将等效模型关联为组，内部自动分发，可以通过参数（时效、费用、失败率等）调节并发及优先级
3. 工作流为模组的组合，工作流类别可以分为：OCR、评分，每个题型可已设置默认的工作流；考试中可创建并绑定新的工作流。

评分任务分发
```rust
// 任务状态
pub enum TaskState {
    NotReady,
    Ready,
    Dispatched,
    Completed,
}
```

```mermaid
mindmap
    Root{{评分}}
        任务生成
            工作流绑定
                每个题型默认需要绑定OCR和评分工作流
                学科+年级+学校绑定了更具体的工作流，则优先选定
                考试可以绑定具体的工作流
                工作流ID记录到考试评分表中（模板只用记录到题型）
            任务列表
                题卡识别后生成，每个打分点对应的答题块获得则状态为Ready，跨页未就绪的题需要绑定学生后才能从NotReady到Ready
            分发（状态改为Dispatched）
                每个ready的题可以分配打分
                学生都对应上后分配
                条件选择重新分发(学生、班级、第n题、状态筛选、版本范围)
        优先级
            分发后进入优先级队列，原始列表改为Dispatched，暂时分为三个队列（slow,normal,urgent）
            设置优先级有配额，比如每个学校每天有1万个urgent，10万个normal处理额度，用完会自动转为slow
            三个队列的取数据概率为1：5：20
        执行
            启动时将dispatched阅卷表加入到优先级队列
            优先级队列启动触发条件
                有内容加入优先级队列时检测是否在处理
                一批内容处理完成后
            查询进度
                队列位置 start end
            类别
                AI评阅：每个题需要完成OCR识别与评分
                扫描登分：OCR识别对错或分数
```

试卷试题结构
```mermaid
erDiagram
    questions {
        bigserial id PK
        varchar(20) question_type_code
        varchar(20) knowledge_point_code
        int difficulty
        varchar(20) cognitive_level
        time create_time
        varchar(20) origin_code
        int origin_id
    }
    %% 包含文本 插图等类别
    %% 枚举：选择、判断、填空、问答、语文作文、英语作文
    question_items {
        bigserial id PK
        int question_id FK
        int order
        varchar(20) subject_code
        varchar(20) question_item_type
        jsonb data
    }
    answer_analysis {
        bigserial id PK
        int question_item_id FK
        text answer
        text analysis
    }
    %% =====================================
    %% Relationships
    %% =====================================
    question_items ||--o{ questions: is_accessed_by
    question_items ||--o{ answer_analysis: "配置"
```