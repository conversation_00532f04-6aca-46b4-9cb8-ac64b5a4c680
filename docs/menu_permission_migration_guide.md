# 菜单权限迁移方案

## 概述

将菜单权限从 `menu_permissions` 表的 `required_permissions` 和 `data_scopes` 字段迁移到 `casbin_policies` 表，实现统一的权限管理架构。

## 权限架构设计

### 1. 菜单访问权限
```sql
-- 格式: ('p', 'role:角色名', 'menu', 'access', '菜单ID')
('p', 'role:班主任', 'menu', 'access', 'student_management')
('p', 'role:教导主任', 'menu', 'access', 'student_management')
```

### 2. 数据范围权限（独立于菜单）
```sql
-- 格式: ('p', 'role:角色名', '资源', '动作', '范围定义')
('p', 'role:班主任', 'student', 'read', 'scope:class:own')
('p', 'role:教导主任', 'student', 'read', 'scope:school:*')
('p', 'role:班主任', 'class', 'read', 'scope:class:own')
```

## 核心组件

### 1. MenuMigrationService
**功能**: 执行权限迁移
- `migrate_permissions_to_casbin()` - 完整迁移
- `validate_migration()` - 验证迁移结果
- `cleanup_menu_permission_fields()` - 清理旧字段

**特点**:
- 支持dry-run模式
- 分离菜单权限和数据权限
- 完整的验证和报告机制

### 2. UnifiedMenuPermissionService
**功能**: 统一菜单权限检查
- `can_access_menu()` - 检查菜单访问权限
- `get_accessible_menus()` - 获取可访问菜单列表
- `check_menu_with_data_access()` - 综合权限检查

**优势**:
- 完全基于Casbin策略
- 复用现有CasbinPermissionService
- 最小化代码重复

### 3. MenuMigrationController
**功能**: 提供迁移管理API
- `POST /migrate` - 执行迁移
- `GET /validate` - 验证结果  
- `POST /cleanup` - 清理旧字段
- `GET /status` - 获取迁移状态

## 使用方式

### 1. CLI工具（推荐）
```bash
# 试运行迁移
cargo run --bin menu_migration -- --tenant-id tenant_001 --dry-run

# 执行实际迁移
cargo run --bin menu_migration -- --tenant-id tenant_001 --execute

# 验证迁移结果
cargo run --bin menu_migration -- --tenant-id tenant_001 --validate

# 清理旧字段
cargo run --bin menu_migration -- --cleanup --confirm
```

### 2. API调用
```bash
# 试运行迁移
curl -X POST "/api/admin/permissions/migrate?dry_run=true&target_tenant_id=tenant_001" \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# 执行迁移
curl -X POST "/api/admin/permissions/migrate?dry_run=false&target_tenant_id=tenant_001" \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# 验证结果
curl -X GET "/api/admin/permissions/validate?target_tenant_id=tenant_001" \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

### 3. 程序化使用
```rust
use deep_mate_backend::service::permission::{
    MenuMigrationService, 
    UnifiedMenuPermissionService
};

// 执行迁移
let migration_service = MenuMigrationService::new(pool);
let report = migration_service
    .migrate_permissions_to_casbin(&casbin_service, "tenant_001", false)
    .await?;

// 使用新的权限检查
let menu_service = UnifiedMenuPermissionService::new(&casbin_service);
let can_access = menu_service
    .can_access_menu("role:班主任", "tenant_001", "student_management")
    .await?;
```

## 迁移步骤

### 阶段一：准备和验证
1. **备份数据**
   ```sql
   -- 备份现有菜单权限配置
   CREATE TABLE menu_permissions_backup AS 
   SELECT * FROM menu_permissions;
   ```

2. **试运行迁移**
   ```bash
   cargo run --bin menu_migration -- --tenant-id tenant_001 --dry-run
   ```

3. **检查生成的策略**
   - 验证策略格式正确
   - 确认权限映射准确

### 阶段二：执行迁移
1. **执行实际迁移**
   ```bash
   cargo run --bin menu_migration -- --tenant-id tenant_001 --execute
   ```

2. **验证迁移结果**
   ```bash
   cargo run --bin menu_migration -- --tenant-id tenant_001 --validate
   ```

3. **功能测试**
   - 测试菜单访问权限
   - 测试数据过滤功能
   - 验证用户体验

### 阶段三：代码更新
1. **更新权限检查代码**
   ```rust
   // 替换原有的菜单权限检查
   let menu_service = UnifiedMenuPermissionService::new(&casbin_service);
   let can_access = menu_service.can_access_menu(user_identity, tenant_id, menu_id).await?;
   ```

2. **更新前端代码**
   ```typescript
   // 前端权限检查逻辑保持不变
   // 后端API自动使用新的权限架构
   const canAccessMenu = await permissionApi.checkMenuAccess(menuId);
   ```

### 阶段四：清理和优化
1. **清理旧字段**（在确认一切正常后）
   ```bash
   cargo run --bin menu_migration -- --cleanup --confirm
   ```

2. **性能优化**
   - 监控权限检查性能
   - 优化Casbin策略查询
   - 调整缓存策略

## 优势总结

### 1. 架构统一
- 所有权限集中在Casbin管理
- 菜单权限和数据权限清晰分离
- 权限检查逻辑统一

### 2. 维护简化
- 权限变更只需修改Casbin策略
- 减少双重维护的复杂性
- 统一的权限审计入口

### 3. 功能增强
- 支持更复杂的权限模型
- 更好的权限组合和继承
- 灵活的数据范围控制

### 4. 代码优化
- 最大限度复用现有代码
- 遵循现有的编码规范
- 最小化代码重复

## 风险控制

### 1. 数据备份
- 迁移前完整备份数据
- 支持回滚机制

### 2. 渐进式迁移
- 支持dry-run模式
- 分租户迁移
- 完整的验证机制

### 3. 功能兼容
- 保持API接口不变
- 前端代码无需修改
- 向后兼容性保证

### 4. 监控和日志
- 完整的迁移日志
- 权限检查监控
- 性能指标跟踪

这个方案确保了权限系统的统一化，同时最大限度地减少了重复代码和迁移风险。