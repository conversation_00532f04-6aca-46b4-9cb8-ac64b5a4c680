-- 更新 gzxxzz 租户测试用户的密码
-- 设置统一的测试密码: Test123!

UPDATE public.users 
SET password_hash = '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jg/L7G',
    salt = 'default_salt'
WHERE phone_number LIKE '1380000%';

-- 验证更新结果
SELECT 
    '=== 密码更新完成 ===' AS status,
    COUNT(*) AS updated_users
FROM public.users 
WHERE phone_number LIKE '1380000%' AND salt = 'default_salt';

-- 显示测试账号登录信息
SELECT 
    '账号信息' as 类型,
    username as 用户名,
    phone_number as 手机号,
    'admin' as 统一测试密码,
    CASE 
        WHEN username LIKE '%principal%' THEN '校长'
        WHEN username LIKE '%director%' THEN '教导主任' 
        WHEN username LIKE '%leader%' THEN '学科组长'
        WHEN username LIKE '%class_teacher%' THEN '班主任'
        WHEN username LIKE '%teacher_%' THEN '任课老师'
        WHEN username LIKE '%student_%' THEN '学生'
        ELSE '其他'
    END as 角色
FROM public.users 
WHERE phone_number LIKE '1380000%'
ORDER BY phone_number
LIMIT 15;