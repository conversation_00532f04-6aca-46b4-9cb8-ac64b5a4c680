-- 修复学生页面访问权限问题 - 添加 student:list 和相关权限
-- 解决 class_teacher_2301 无法访问 students 页面的问题

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
    added_count INT;
BEGIN
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 为 class_teacher 角色添加 student:list 权限（学生列表访问）
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
    VALUES ('p', 'role:class_teacher', tenant_id_var, 'student:list', 'read', 'allow', '', tenant_id_var, NOW(), NOW())
    ON CONFLICT DO NOTHING;
    
    GET DIAGNOSTICS added_count = ROW_COUNT;
    RAISE NOTICE 'Added % student:list permission for class_teacher role', added_count;
    
    -- 为 class_teacher 角色添加 menu:student_list 特定菜单权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
    VALUES ('p', 'role:class_teacher', tenant_id_var, 'menu:student_list', 'access', 'allow', '', tenant_id_var, NOW(), NOW())
    ON CONFLICT DO NOTHING;
    
    GET DIAGNOSTICS added_count = ROW_COUNT;
    RAISE NOTICE 'Added % menu:student_list permission for class_teacher role', added_count;
    
    -- 为 class_teacher 角色添加 student:* 通配符权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
    VALUES ('p', 'role:class_teacher', tenant_id_var, 'student:*', 'read', 'allow', '', tenant_id_var, NOW(), NOW())
    ON CONFLICT DO NOTHING;
    
    GET DIAGNOSTICS added_count = ROW_COUNT;
    RAISE NOTICE 'Added % student:* wildcard permission for class_teacher role', added_count;
    
    RAISE NOTICE '学生页面访问权限已修复！班主任现在应该可以访问学生列表页面了。';
END $$;

-- 验证修复后的权限配置
SELECT '=== 学生相关权限验证 ===' as section;

SELECT 
    'Class Teacher Student Permissions:' as type,
    v0 as role,
    v1 as domain,
    v2 as object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
  AND v0 = 'role:class_teacher'
  AND v2 LIKE '%student%'
ORDER BY v2, v3;

SELECT '=== 权限说明 ===' as section;
SELECT '班主任现在应该拥有以下学生相关权限:' as info;
SELECT '- student:class,manage (班级学生管理权限)' as permission;
SELECT '- student:read,read (学生信息读取权限)' as permission;
SELECT '- student:list,read (学生列表访问权限)' as permission;
SELECT '- student:*,read (学生通配符读取权限)' as permission;
SELECT '- menu:student_management (学生管理菜单权限)' as permission;
SELECT '- menu:student_list (学生列表菜单权限)' as permission;