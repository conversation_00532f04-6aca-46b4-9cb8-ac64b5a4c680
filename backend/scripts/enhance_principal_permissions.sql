-- 为校长角色添加通用菜单访问权限
-- 确保校长能访问所有菜单

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
BEGIN
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 清理现有的校长菜单权限策略
    DELETE FROM public.casbin_policies 
    WHERE tenant_id = tenant_id_var 
      AND ptype = 'p' 
      AND v0 = 'role:principal' 
      AND v2 LIKE 'menu:%';
    
    -- 添加校长的通用菜单访问权限
    -- 为不同格式的权限要求都添加策略
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    -- 基础菜单权限
    ('p', 'role:principal', tenant_id_var, 'menu:access', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'menu:*', 'allow', tenant_id_var),
    
    -- 具体功能权限（确保校长有所有权限）
    ('p', 'role:principal', tenant_id_var, 'teaching_aids:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'homework:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'administrative_class:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'teaching_class:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'grading:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'statistics:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'subject:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'education_stage:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'subject_group:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'grade_level:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'user:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'personal:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'tenant:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'role:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'casbin:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'permission:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'monitor:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'performance:*', 'allow', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'health:*', 'allow', tenant_id_var)
    
    ON CONFLICT DO NOTHING;
    
    -- 同时简化所有菜单的权限要求，使用更通用的权限模式
    UPDATE public.menu_permissions 
    SET required_permissions = ARRAY['menu:access']
    WHERE required_permissions IS NOT NULL AND required_permissions != ARRAY['menu:access'];
    
    RAISE NOTICE '校长权限已增强，菜单权限已简化！';
END $$;

-- 验证修复结果
SELECT 
    '=== 权限修复验证 ===' as section,
    COUNT(*) as principal_menu_policies
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p' 
  AND v0 = 'role:principal'
  AND v2 LIKE 'menu:%';

-- 显示校长所有权限策略
SELECT 
    '=== 校长完整权限列表 ===' as section,
    v2 as resource_object,
    v3 as action
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
  AND v0 = 'role:principal'
ORDER BY v2;

-- 验证菜单配置
SELECT 
    '=== 菜单权限配置确认 ===' as section,
    COUNT(*) as total_menus,
    COUNT(CASE WHEN required_permissions = ARRAY['menu:access'] THEN 1 END) as simple_permission_menus
FROM public.menu_permissions
WHERE is_active = true;