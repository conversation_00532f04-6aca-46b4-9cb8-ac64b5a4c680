-- 为 gzxxzz 租户创建模拟教师和学生数据测试脚本
-- 用于测试教师权限系统（包括菜单权限）
-- Migration: create_gzxxzz_test_data.sql

-- ================================================================
-- 1. 创建用户账号 (public.users)
-- ================================================================

-- 插入校长用户
INSERT INTO public.users (id, phone_number, username, password_hash, salt, phone_verified, is_active) VALUES 
('11111111-1111-1111-1111-111111111111', '13800000001', 'principal_gzxxzz', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt001', TRUE, TRUE);

-- 插入教导主任用户
INSERT INTO public.users (id, phone_number, username, password_hash, salt, phone_verified, is_active) VALUES 
('22222222-2222-2222-2222-222222222222', '13800000002', 'director_gzxxzz', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt002', TRUE, TRUE);

-- 插入学科组长用户（语文、数学、英语）
INSERT INTO public.users (id, phone_number, username, password_hash, salt, phone_verified, is_active) VALUES 
('33333333-3333-3333-3333-333333333333', '13800000003', 'chinese_leader_gzxxzz', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt003', TRUE, TRUE),
('44444444-4444-4444-4444-444444444444', '13800000004', 'math_leader_gzxxzz', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt004', TRUE, TRUE),
('55555555-5555-5555-5555-555555555555', '13800000005', 'english_leader_gzxxzz', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt005', TRUE, TRUE);

-- 插入班主任用户（5个班级）
INSERT INTO public.users (id, phone_number, username, password_hash, salt, phone_verified, is_active) VALUES 
('66666666-6666-6666-6666-666666666666', '13800000006', 'class_teacher_2301', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt006', TRUE, TRUE),
('77777777-7777-7777-7777-777777777777', '13800000007', 'class_teacher_2302', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt007', TRUE, TRUE),
('88888888-8888-8888-8888-888888888888', '13800000008', 'class_teacher_2303', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt008', TRUE, TRUE),
('*************-9999-9999-************', '13800000009', 'class_teacher_2401', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt009', TRUE, TRUE),
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '13800000010', 'class_teacher_2402', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt010', TRUE, TRUE);

-- 插入任课老师用户（10个）
INSERT INTO public.users (id, phone_number, username, password_hash, salt, phone_verified, is_active) VALUES 
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '13800000011', 'teacher_wang', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt011', TRUE, TRUE),
('cccccccc-cccc-cccc-cccc-cccccccccccc', '13800000012', 'teacher_li', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt012', TRUE, TRUE),
('dddddddd-dddd-dddd-dddd-dddddddddddd', '13800000013', 'teacher_zhang', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt013', TRUE, TRUE),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '13800000014', 'teacher_liu', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt014', TRUE, TRUE),
('ffffffff-ffff-ffff-ffff-ffffffffffff', '13800000015', 'teacher_chen', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt015', TRUE, TRUE),
('10101010-1010-1010-1010-101010101010', '13800000016', 'teacher_huang', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt016', TRUE, TRUE),
('*************-2020-2020-************', '13800000017', 'teacher_wu', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt017', TRUE, TRUE),
('*************-3030-3030-************', '13800000018', 'teacher_zhao', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt018', TRUE, TRUE),
('*************-4040-4040-************', '13800000019', 'teacher_sun', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt019', TRUE, TRUE),
('*************-5050-5050-************', '13800000020', 'teacher_zhou', '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456', 'salt020', TRUE, TRUE);

-- 插入学生用户（每个班级20个学生，共100个）
INSERT INTO public.users (id, phone_number, username, password_hash, salt, phone_verified, is_active) 
SELECT 
    ('*************-0000-0000-' || LPAD(s.id::text, 12, '0'))::uuid,
    '1380000' || LPAD((s.id + 100)::text, 4, '0'),
    'student_' || LPAD(s.id::text, 3, '0') || '_gzxxzz',
    '$argon2id$v=19$m=19456,t=2,p=1$abc123$def456',
    'salt' || LPAD((s.id + 100)::text, 3, '0'),
    TRUE,
    TRUE
FROM generate_series(1, 100) AS s(id)
ON CONFLICT (phone_number) DO NOTHING;

-- ================================================================
-- 2. 创建学科组 (tenant_gzxxzz.subject_groups)
-- ================================================================

-- 先获取 gzxxzz 租户ID
DO $$ 
DECLARE
    tenant_uuid UUID;
BEGIN
    SELECT id INTO tenant_uuid FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 创建学科组
    INSERT INTO tenant_gzxxzz.subject_groups (id, group_name, subject_code, description, leader_teacher_id, is_active) VALUES 
    ('a0000000-0000-0000-0000-000000000001', '语文组', 'CHINESE', '负责语文学科教学和教研工作', NULL, TRUE),
    ('a0000000-0000-0000-0000-000000000002', '数学组', 'MATH', '负责数学学科教学和教研工作', NULL, TRUE),
    ('a0000000-0000-0000-0000-000000000003', '英语组', 'ENGLISH', '负责英语学科教学和教研工作', NULL, TRUE),
    ('a0000000-0000-0000-0000-000000000004', '物理组', 'PHYSICS', '负责物理学科教学和教研工作', NULL, TRUE),
    ('a0000000-0000-0000-0000-000000000005', '化学组', 'CHEMISTRY', '负责化学学科教学和教研工作', NULL, TRUE);
END $$;

-- ================================================================
-- 3. 创建教师记录 (tenant_gzxxzz.teachers)
-- ================================================================

-- 校长
INSERT INTO tenant_gzxxzz.teachers (id, user_id, employee_id, teacher_name, phone, email, gender, title, employment_status, is_active) VALUES 
('b0000000-0000-0000-0000-000000000001', '11111111-1111-1111-1111-111111111111', 'EMP001', '李校长', '13800000001', '<EMAIL>', '男', '高级教师', '在职', TRUE);

-- 教导主任
INSERT INTO tenant_gzxxzz.teachers (id, user_id, employee_id, teacher_name, phone, email, gender, title, employment_status, is_active) VALUES 
('b0000000-0000-0000-0000-000000000002', '22222222-2222-2222-2222-222222222222', 'EMP002', '王主任', '13800000002', '<EMAIL>', '女', '高级教师', '在职', TRUE);

-- 学科组长
INSERT INTO tenant_gzxxzz.teachers (id, user_id, employee_id, teacher_name, phone, email, gender, title, employment_status, is_active) VALUES 
('b0000000-0000-0000-0000-000000000003', '33333333-3333-3333-3333-333333333333', 'EMP003', '张老师', '13800000003', '<EMAIL>', '女', '一级教师', '在职', TRUE),
('b0000000-0000-0000-0000-000000000004', '44444444-4444-4444-4444-444444444444', 'EMP004', '刘老师', '13800000004', '<EMAIL>', '男', '一级教师', '在职', TRUE),
('b0000000-0000-0000-0000-000000000005', '55555555-5555-5555-5555-555555555555', 'EMP005', '陈老师', '13800000005', '<EMAIL>', '女', '一级教师', '在职', TRUE);

-- 班主任
INSERT INTO tenant_gzxxzz.teachers (id, user_id, employee_id, teacher_name, phone, email, gender, title, employment_status, is_active) VALUES 
('b0000000-0000-0000-0000-000000000006', '66666666-6666-6666-6666-666666666666', 'EMP006', '黄老师', '13800000006', '<EMAIL>', '女', '二级教师', '在职', TRUE),
('b0000000-0000-0000-0000-000000000007', '77777777-7777-7777-7777-777777777777', 'EMP007', '吴老师', '13800000007', '<EMAIL>', '男', '二级教师', '在职', TRUE),
('b0000000-0000-0000-0000-000000000008', '88888888-8888-8888-8888-888888888888', 'EMP008', '赵老师', '13800000008', '<EMAIL>', '女', '二级教师', '在职', TRUE),
('b0000000-0000-0000-0000-000000000009', '*************-9999-9999-************', 'EMP009', '孙老师', '13800000009', '<EMAIL>', '男', '二级教师', '在职', TRUE),
('b0000000-0000-0000-0000-000000000010', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'EMP010', '周老师', '13800000010', '<EMAIL>', '女', '二级教师', '在职', TRUE);

-- 任课老师
INSERT INTO tenant_gzxxzz.teachers (id, user_id, employee_id, teacher_name, phone, email, gender, title, employment_status, is_active) 
SELECT 
    ('b0000000-0000-0000-0000-00000000' || LPAD((s.id + 10)::text, 4, '0'))::uuid,
    CASE s.id 
        WHEN 1 THEN 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'::uuid
        WHEN 2 THEN 'cccccccc-cccc-cccc-cccc-cccccccccccc'::uuid
        WHEN 3 THEN 'dddddddd-dddd-dddd-dddd-dddddddddddd'::uuid
        WHEN 4 THEN 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee'::uuid
        WHEN 5 THEN 'ffffffff-ffff-ffff-ffff-ffffffffffff'::uuid
        WHEN 6 THEN '10101010-1010-1010-1010-101010101010'::uuid
        WHEN 7 THEN '*************-2020-2020-************'::uuid
        WHEN 8 THEN '*************-3030-3030-************'::uuid
        WHEN 9 THEN '*************-4040-4040-************'::uuid
        WHEN 10 THEN '*************-5050-5050-************'::uuid
    END,
    'EMP' || LPAD((s.id + 10)::text, 3, '0'),
    CASE s.id 
        WHEN 1 THEN '王老师'
        WHEN 2 THEN '李老师'
        WHEN 3 THEN '张老师'
        WHEN 4 THEN '刘老师'
        WHEN 5 THEN '陈老师'
        WHEN 6 THEN '黄老师'
        WHEN 7 THEN '吴老师'
        WHEN 8 THEN '赵老师'
        WHEN 9 THEN '孙老师'
        WHEN 10 THEN '周老师'
    END,
    '1380000' || LPAD((s.id + 10)::text, 4, '0'),
    CASE s.id 
        WHEN 1 THEN 'wang'
        WHEN 2 THEN 'li'
        WHEN 3 THEN 'zhang'
        WHEN 4 THEN 'liu'
        WHEN 5 THEN 'chen'
        WHEN 6 THEN 'huang'
        WHEN 7 THEN 'wu'
        WHEN 8 THEN 'zhao'
        WHEN 9 THEN 'sun'
        WHEN 10 THEN 'zhou'
    END || '@gzxxzz.edu.cn',
    CASE s.id % 2 WHEN 0 THEN '女' ELSE '男' END,
    '二级教师',
    '在职',
    TRUE
FROM generate_series(1, 10) AS s(id);

-- 更新学科组的组长
UPDATE tenant_gzxxzz.subject_groups SET leader_teacher_id = 'b0000000-0000-0000-0000-000000000003' WHERE subject_code = 'CHINESE';
UPDATE tenant_gzxxzz.subject_groups SET leader_teacher_id = 'b0000000-0000-0000-0000-000000000004' WHERE subject_code = 'MATH';
UPDATE tenant_gzxxzz.subject_groups SET leader_teacher_id = 'b0000000-0000-0000-0000-000000000005' WHERE subject_code = 'ENGLISH';

-- ================================================================
-- 4. 创建管理班级 (tenant_gzxxzz.administrative_classes)
-- ================================================================

INSERT INTO tenant_gzxxzz.administrative_classes (id, class_name, code, academic_year, grade_level_code, teacher_id, is_active) VALUES 
('c0000000-0000-0000-0000-000000000001', '2023级计算机应用1班', '2301', '2023-2024', 'G2023', 'b0000000-0000-0000-0000-000000000006', TRUE),
('c0000000-0000-0000-0000-000000000002', '2023级计算机应用2班', '2302', '2023-2024', 'G2023', 'b0000000-0000-0000-0000-000000000007', TRUE),
('c0000000-0000-0000-0000-000000000003', '2023级电子商务班', '2303', '2023-2024', 'G2023', 'b0000000-0000-0000-0000-000000000008', TRUE),
('c0000000-0000-0000-0000-000000000004', '2024级计算机应用1班', '2401', '2024-2025', 'G2024', 'b0000000-0000-0000-0000-000000000009', TRUE),
('c0000000-0000-0000-0000-000000000005', '2024级计算机应用2班', '2402', '2024-2025', 'G2024', 'b0000000-0000-0000-0000-000000000010', TRUE);

-- ================================================================
-- 5. 创建教学班级 (tenant_gzxxzz.teaching_classes)
-- ================================================================

INSERT INTO tenant_gzxxzz.teaching_classes (id, class_name, code, academic_year, subject_group_id, teacher_id, is_active) VALUES 
-- 语文课
('d0000000-0000-0000-0000-000000000001', '2023级语文1班', 'CHI2301', '2023-2024', 'a0000000-0000-0000-0000-000000000001', 'b0000000-0000-0000-0000-000000000011', TRUE),
('d0000000-0000-0000-0000-000000000002', '2023级语文2班', 'CHI2302', '2023-2024', 'a0000000-0000-0000-0000-000000000001', 'b0000000-0000-0000-0000-000000000012', TRUE),
('d0000000-0000-0000-0000-000000000003', '2024级语文1班', 'CHI2401', '2024-2025', 'a0000000-0000-0000-0000-000000000001', 'b0000000-0000-0000-0000-000000000013', TRUE),

-- 数学课
('d0000000-0000-0000-0000-000000000004', '2023级数学1班', 'MATH2301', '2023-2024', 'a0000000-0000-0000-0000-000000000002', 'b0000000-0000-0000-0000-000000000014', TRUE),
('d0000000-0000-0000-0000-000000000005', '2023级数学2班', 'MATH2302', '2023-2024', 'a0000000-0000-0000-0000-000000000002', 'b0000000-0000-0000-0000-000000000015', TRUE),
('d0000000-0000-0000-0000-000000000006', '2024级数学1班', 'MATH2401', '2024-2025', 'a0000000-0000-0000-0000-000000000002', 'b0000000-0000-0000-0000-000000000016', TRUE),

-- 英语课
('d0000000-0000-0000-0000-000000000007', '2023级英语1班', 'ENG2301', '2023-2024', 'a0000000-0000-0000-0000-000000000003', 'b0000000-0000-0000-0000-000000000017', TRUE),
('d0000000-0000-0000-0000-000000000008', '2023级英语2班', 'ENG2302', '2023-2024', 'a0000000-0000-0000-0000-000000000003', 'b0000000-0000-0000-0000-000000000018', TRUE),
('d0000000-0000-0000-0000-000000000009', '2024级英语1班', 'ENG2401', '2024-2025', 'a0000000-0000-0000-0000-000000000003', 'b0000000-0000-0000-0000-000000000019', TRUE),

-- 专业课
('d0000000-0000-0000-0000-000000000010', '2023级计算机基础', 'COMP2301', '2023-2024', 'a0000000-0000-0000-0000-000000000004', 'b0000000-0000-0000-0000-000000000020', TRUE);

-- ================================================================
-- 6. 创建学生记录 (tenant_gzxxzz.students)
-- ================================================================

-- 创建学生记录（每班20名学生）
INSERT INTO tenant_gzxxzz.students (id, user_id, student_number, student_name, gender, administrative_class_id, enrollment_date, status)
SELECT 
    ('e0000000-0000-0000-0000-' || LPAD(s.id::text, 12, '0'))::uuid,
    ('*************-0000-0000-' || LPAD(s.id::text, 12, '0'))::uuid,
    CASE 
        WHEN s.id <= 20 THEN '2301' || LPAD((s.id)::text, 3, '0')
        WHEN s.id <= 40 THEN '2302' || LPAD((s.id - 20)::text, 3, '0')
        WHEN s.id <= 60 THEN '2303' || LPAD((s.id - 40)::text, 3, '0')
        WHEN s.id <= 80 THEN '2401' || LPAD((s.id - 60)::text, 3, '0')
        ELSE '2402' || LPAD((s.id - 80)::text, 3, '0')
    END,
    CASE s.id % 10
        WHEN 1 THEN '张' WHEN 2 THEN '王' WHEN 3 THEN '李' WHEN 4 THEN '刘' WHEN 5 THEN '陈'
        WHEN 6 THEN '杨' WHEN 7 THEN '赵' WHEN 8 THEN '黄' WHEN 9 THEN '周' ELSE '吴'
    END || 
    CASE (s.id + 3) % 10
        WHEN 1 THEN '明' WHEN 2 THEN '华' WHEN 3 THEN '强' WHEN 4 THEN '军' WHEN 5 THEN '敏'
        WHEN 6 THEN '静' WHEN 7 THEN '丽' WHEN 8 THEN '伟' WHEN 9 THEN '芳' ELSE '刚'
    END,
    CASE s.id % 2 WHEN 0 THEN '女' ELSE '男' END,
    CASE 
        WHEN s.id <= 20 THEN 'c0000000-0000-0000-0000-000000000001'::uuid
        WHEN s.id <= 40 THEN 'c0000000-0000-0000-0000-000000000002'::uuid
        WHEN s.id <= 60 THEN 'c0000000-0000-0000-0000-000000000003'::uuid
        WHEN s.id <= 80 THEN 'c0000000-0000-0000-0000-000000000004'::uuid
        ELSE 'c0000000-0000-0000-0000-000000000005'::uuid
    END,
    CASE 
        WHEN s.id <= 60 THEN '2023-09-01'::date
        ELSE '2024-09-01'::date
    END,
    'active'
FROM generate_series(1, 100) AS s(id);

-- ================================================================
-- 7. 创建用户身份绑定 (tenant_gzxxzz.user_identities)
-- ================================================================

-- 获取角色ID
DO $$ 
DECLARE
    principal_role_id UUID;
    director_role_id UUID;
    subject_leader_role_id UUID;
    class_teacher_role_id UUID;
    teacher_role_id UUID;
    student_role_id UUID;
BEGIN
    -- 获取各角色ID
    SELECT id INTO principal_role_id FROM public.roles WHERE code = 'principal' AND is_system = TRUE;
    SELECT id INTO director_role_id FROM public.roles WHERE code = 'academic_director' AND is_system = TRUE;
    SELECT id INTO subject_leader_role_id FROM public.roles WHERE code = 'subject_leader' AND is_system = TRUE;
    SELECT id INTO class_teacher_role_id FROM public.roles WHERE code = 'class_teacher' AND is_system = TRUE;
    SELECT id INTO teacher_role_id FROM public.roles WHERE code = 'teacher' AND is_system = TRUE;
    SELECT id INTO student_role_id FROM public.roles WHERE code = 'student' AND is_system = TRUE;

    -- 校长身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id) VALUES 
    ('11111111-1111-1111-1111-111111111111', principal_role_id, 'school', NULL);

    -- 教导主任身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id) VALUES 
    ('22222222-2222-2222-2222-222222222222', director_role_id, 'school', NULL);

    -- 学科组长身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id, subject) VALUES 
    ('33333333-3333-3333-3333-333333333333', subject_leader_role_id, 'subject_group', 'a0000000-0000-0000-0000-000000000001', 'CHINESE'),
    ('44444444-4444-4444-4444-444444444444', subject_leader_role_id, 'subject_group', 'a0000000-0000-0000-0000-000000000002', 'MATH'),
    ('55555555-5555-5555-5555-555555555555', subject_leader_role_id, 'subject_group', 'a0000000-0000-0000-0000-000000000003', 'ENGLISH');

    -- 班主任身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id) VALUES 
    ('66666666-6666-6666-6666-666666666666', class_teacher_role_id, 'class', 'c0000000-0000-0000-0000-000000000001'),
    ('77777777-7777-7777-7777-777777777777', class_teacher_role_id, 'class', 'c0000000-0000-0000-0000-000000000002'),
    ('88888888-8888-8888-8888-888888888888', class_teacher_role_id, 'class', 'c0000000-0000-0000-0000-000000000003'),
    ('*************-9999-9999-************', class_teacher_role_id, 'class', 'c0000000-0000-0000-0000-000000000004'),
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', class_teacher_role_id, 'class', 'c0000000-0000-0000-0000-000000000005');

    -- 任课老师身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id, subject) 
    SELECT 
        CASE s.id 
            WHEN 1 THEN 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'::uuid
            WHEN 2 THEN 'cccccccc-cccc-cccc-cccc-cccccccccccc'::uuid
            WHEN 3 THEN 'dddddddd-dddd-dddd-dddd-dddddddddddd'::uuid
            WHEN 4 THEN 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee'::uuid
            WHEN 5 THEN 'ffffffff-ffff-ffff-ffff-ffffffffffff'::uuid
            WHEN 6 THEN '10101010-1010-1010-1010-101010101010'::uuid
            WHEN 7 THEN '*************-2020-2020-************'::uuid
            WHEN 8 THEN '*************-3030-3030-************'::uuid
            WHEN 9 THEN '*************-4040-4040-************'::uuid
            WHEN 10 THEN '*************-5050-5050-************'::uuid
        END,
        teacher_role_id,
        'class',
        ('d0000000-0000-0000-0000-00000000000' || s.id::text)::uuid,
        CASE 
            WHEN s.id IN (1,2,3) THEN 'CHINESE'
            WHEN s.id IN (4,5,6) THEN 'MATH'
            WHEN s.id IN (7,8,9) THEN 'ENGLISH'
            ELSE 'COMPUTER'
        END
    FROM generate_series(1, 10) AS s(id);

    -- 学生身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id) 
    SELECT 
        ('*************-0000-0000-' || LPAD(s.id::text, 12, '0'))::uuid,
        student_role_id,
        'class',
        CASE 
            WHEN s.id <= 20 THEN 'c0000000-0000-0000-0000-000000000001'::uuid
            WHEN s.id <= 40 THEN 'c0000000-0000-0000-0000-000000000002'::uuid
            WHEN s.id <= 60 THEN 'c0000000-0000-0000-0000-000000000003'::uuid
            WHEN s.id <= 80 THEN 'c0000000-0000-0000-0000-000000000004'::uuid
            ELSE 'c0000000-0000-0000-0000-000000000005'::uuid
        END
    FROM generate_series(1, 100) AS s(id);

END $$;

-- ================================================================
-- 8. 完成提示
-- ================================================================

-- 显示创建的数据统计
SELECT '=== gzxxzz 租户测试数据创建完成 ===' AS message;
SELECT COUNT(*) AS total_users FROM public.users WHERE phone_number LIKE '1380000%';
SELECT COUNT(*) AS total_teachers FROM tenant_gzxxzz.teachers;
SELECT COUNT(*) AS total_students FROM tenant_gzxxzz.students;
SELECT COUNT(*) AS total_admin_classes FROM tenant_gzxxzz.administrative_classes;
SELECT COUNT(*) AS total_teaching_classes FROM tenant_gzxxzz.teaching_classes;
SELECT COUNT(*) AS total_subject_groups FROM tenant_gzxxzz.subject_groups;
SELECT COUNT(*) AS total_identities FROM tenant_gzxxzz.user_identities;

-- 显示教师角色分布
SELECT r.name AS role_name, COUNT(*) AS teacher_count 
FROM tenant_gzxxzz.user_identities ui
JOIN public.roles r ON ui.role_id = r.id
JOIN tenant_gzxxzz.teachers t ON ui.user_id = t.user_id
GROUP BY r.name, r.level
ORDER BY r.level;

COMMIT;