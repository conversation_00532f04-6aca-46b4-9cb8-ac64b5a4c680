-- 为班主任添加缺失的 grade:write 权限
-- 解决 class_teacher_2301 无法访问学生管理菜单的问题

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
    added_count INT;
BEGIN
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 为 class_teacher 角色添加 grade:write 权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
    VALUES ('p', 'role:class_teacher', tenant_id_var, 'grade:write', 'write', 'allow', '', tenant_id_var, NOW(), NOW())
    ON CONFLICT DO NOTHING;
    
    GET DIAGNOSTICS added_count = ROW_COUNT;
    RAISE NOTICE 'Added % grade:write permission for class_teacher role', added_count;
    
    -- 为 class_teacher 角色添加 grade:class:write 权限（班级成绩写入）
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
    VALUES ('p', 'role:class_teacher', tenant_id_var, 'grade:class', 'write', 'allow', '', tenant_id_var, NOW(), NOW())
    ON CONFLICT DO NOTHING;
    
    GET DIAGNOSTICS added_count = ROW_COUNT;
    RAISE NOTICE 'Added % grade:class:write permission for class_teacher role', added_count;
    
    -- 为 class_teacher 角色添加 exam:write 权限（考试写入）
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
    VALUES ('p', 'role:class_teacher', tenant_id_var, 'exam:write', 'write', 'allow', '', tenant_id_var, NOW(), NOW())
    ON CONFLICT DO NOTHING;
    
    GET DIAGNOSTICS added_count = ROW_COUNT;
    RAISE NOTICE 'Added % exam:write permission for class_teacher role', added_count;
    
    RAISE NOTICE '班主任成绩权限已修复！现在应该可以访问学生管理菜单了。';
END $$;

-- 验证修复后的权限配置
SELECT '=== 班主任成绩相关权限验证 ===' as section;

SELECT 
    'Class Teacher Grade Permissions:' as type,
    v0 as role,
    v1 as domain,
    v2 as object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
  AND v0 = 'role:class_teacher'
  AND v2 LIKE '%grade%'
ORDER BY v2, v3;

-- 验证学生管理菜单所需权限
SELECT '=== 学生管理菜单权限要求 ===' as section;

SELECT 
    menu_id as 菜单ID,
    name as 菜单名称,
    required_permissions as 所需权限
FROM public.menu_permissions 
WHERE menu_id = 'student_management';

-- 验证班主任是否拥有所有所需权限
SELECT '=== 权限匹配检查 ===' as section;

WITH menu_perms AS (
    SELECT unnest(required_permissions) as perm
    FROM public.menu_permissions 
    WHERE menu_id = 'student_management'
),
user_perms AS (
    SELECT DISTINCT v2 || ':' || v3 as perm
    FROM public.casbin_policies 
    WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
      AND ptype = 'p'
      AND v0 = 'role:class_teacher'
      AND v4 = 'allow'
)
SELECT 
    mp.perm as 菜单要求权限,
    CASE WHEN up.perm IS NOT NULL THEN '✅ 已拥有' ELSE '❌ 缺失' END as 权限状态
FROM menu_perms mp
LEFT JOIN user_perms up ON mp.perm = up.perm
ORDER BY mp.perm; 