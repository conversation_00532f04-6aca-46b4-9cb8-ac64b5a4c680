-- 修复菜单API问题
-- 问题诊断：menu controller构建的用户身份格式与Casbin策略不匹配
-- 
-- 当前问题：
-- Menu Controller构建: "{user_id}:{role_type}:tenant:{tenant_id}"
-- C<PERSON>bin期望格式: "user:{user_id}" 
--
-- 解决方案：验证并确保用户身份格式与Casbin策略一致

-- ================================================================
-- 1. 验证当前Casbin策略格式
-- ================================================================

SELECT '=== 1. 当前权限策略分析 ===' as section;

-- 检查用户-角色映射格式
SELECT 
    'Step 1.1: 用户-角色映射格式验证' as step,
    v0 as user_subject_format,
    v1 as role_object_format,
    COUNT(*) as count
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'g'
GROUP BY v0, v1;

-- 检查角色权限策略格式
SELECT 
    'Step 1.2: 角色权限策略格式验证' as step,
    v0 as role_subject_format,
    v2 as object_format,
    v3 as action,
    COUNT(*) as count
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
  AND v2 LIKE 'menu:%'
GROUP BY v0, v2, v3;

-- ================================================================
-- 2. 问题根因分析
-- ================================================================

SELECT '=== 2. 问题根因分析 ===' as section;

SELECT 
    'Step 2.1: 用户身份构建分析' as step,
    'Menu Controller错误地构建用户身份为复合格式' as issue,
    '应该使用简单的 user:{user_id} 格式匹配Casbin策略' as solution;

-- ================================================================
-- 3. 验证修复所需的代码更改
-- ================================================================

SELECT '=== 3. 所需代码修复确认 ===' as section;

SELECT 
    'Step 3.1: Menu Controller修复' as step,
    '需要修改 get_user_menus 中的用户身份构建逻辑' as change_needed,
    '从复合格式改为 format!("user:{}", auth_context.user_id)' as fix_details;

-- ================================================================
-- 4. 测试用户身份格式正确性
-- ================================================================

SELECT '=== 4. 用户身份格式测试 ===' as section;

-- 测试正确的用户身份格式是否能匹配权限策略
DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
    user_identity VARCHAR(255);
    has_user_role BOOLEAN;
    has_role_permission BOOLEAN;
BEGIN
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    user_identity := 'user:11111111-1111-1111-1111-111111111111';
    
    -- 检查用户-角色映射
    SELECT EXISTS (
        SELECT 1 FROM public.casbin_policies 
        WHERE tenant_id = tenant_id_var 
          AND ptype = 'g'
          AND v0 = user_identity
          AND v1 = 'role:principal'
    ) INTO has_user_role;
    
    -- 检查角色权限策略
    SELECT EXISTS (
        SELECT 1 FROM public.casbin_policies 
        WHERE tenant_id = tenant_id_var 
          AND ptype = 'p'
          AND v0 = 'role:principal'
          AND v2 = 'menu:access'
          AND v3 = 'allow'
    ) INTO has_role_permission;
    
    RAISE NOTICE 'Step 4.1: 正确用户身份格式测试';
    RAISE NOTICE '用户身份: %', user_identity;
    RAISE NOTICE '用户-角色映射存在: %', has_user_role;
    RAISE NOTICE '角色权限策略存在: %', has_role_permission;
    RAISE NOTICE '权限链路完整: %', (has_user_role AND has_role_permission);
END $$;

-- ================================================================
-- 5. 代码修复指导
-- ================================================================

SELECT '=== 5. 代码修复指导 ===' as section;

SELECT 
    'Step 5.1: 修复文件' as step,
    'backend/src/controller/permission/menu_controller.rs' as file_path,
    '第156-164行 - get_user_menus方法中的用户身份构建' as location;

SELECT 
    'Step 5.2: 当前错误代码' as step,
    'format!("{}:{}:tenant:{}", auth_context.user_id, first_role.identity_type, tenant_id)' as current_code;

SELECT 
    'Step 5.3: 正确修复代码' as step,
    'format!("user:{}", auth_context.user_id)' as fixed_code;

SELECT 
    'Step 5.4: 其他需要修复的位置' as step,
    '所有构建用户身份的地方都需要使用统一格式：user:{user_id}' as guidance;

-- ================================================================
-- 6. 修复验证计划
-- ================================================================

SELECT '=== 6. 修复验证计划 ===' as section;

SELECT 
    'Step 6.1: 修复步骤' as step,
    '1. 修改menu_controller.rs中的用户身份构建逻辑' as action_1,
    '2. 重新编译并启动后端服务' as action_2,
    '3. 使用正确JWT token测试菜单API' as action_3,
    '4. 验证返回非空菜单数据' as action_4;

SELECT 
    'Step 6.2: 测试命令' as step,
    'curl -H "Authorization: Bearer JWT_TOKEN" http://localhost:8080/api/v1/permissions/menus?tenant_id=7ff2e111-1ca4-4402-bc02-af69c1a7283c' as test_command;

-- ================================================================
-- 7. 总结
-- ================================================================

SELECT '=== 7. 问题总结 ===' as section;

SELECT 
    'ISSUE IDENTIFIED' as status,
    '菜单API返回空结果是因为用户身份格式不匹配' as root_cause,
    'Menu Controller需要修改用户身份构建逻辑' as solution,
    '修复后应该能够正常返回校长可访问的菜单列表' as expected_result;