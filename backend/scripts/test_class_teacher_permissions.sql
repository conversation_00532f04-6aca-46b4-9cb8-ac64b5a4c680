-- 测试班主任权限是否正常工作
-- 验证 class_teacher_2301 的权限配置

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
    user_id_var VARCHAR(100);
    test_result BOOLEAN;
BEGIN
    -- 获取 gzxxzz 租户ID
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 获取 class_teacher_2301 用户ID
    SELECT id::text INTO user_id_var FROM public.users WHERE username = 'class_teacher_2301';
    
    RAISE NOTICE '=== 班主任权限测试开始 ===';
    RAISE NOTICE '租户ID: %', tenant_id_var;
    RAISE NOTICE '用户ID: %', user_id_var;
    
    -- 1. 检查用户身份绑定
    RAISE NOTICE '1. 检查用户身份绑定:';
    SELECT COUNT(*) INTO test_result FROM tenant_gzxxzz.user_identities 
    WHERE user_id = user_id_var::uuid;
    
    IF test_result > 0 THEN
        RAISE NOTICE '✅ 用户身份绑定存在';
    ELSE
        RAISE NOTICE '❌ 用户身份绑定不存在';
    END IF;
    
    -- 2. 检查角色分配
    RAISE NOTICE '2. 检查角色分配:';
    SELECT COUNT(*) INTO test_result FROM public.casbin_policies 
    WHERE tenant_id = tenant_id_var 
      AND ptype = 'g' 
      AND v0 = 'user:' || user_id_var;
    
    IF test_result > 0 THEN
        RAISE NOTICE '✅ 角色分配存在 (% 个)', test_result;
    ELSE
        RAISE NOTICE '❌ 角色分配不存在';
    END IF;
    
    -- 3. 检查学生读取权限
    RAISE NOTICE '3. 检查学生读取权限:';
    SELECT COUNT(*) INTO test_result FROM public.casbin_policies 
    WHERE tenant_id = tenant_id_var 
      AND ptype = 'p' 
      AND v0 = 'role:class_teacher'
      AND v2 = 'student:read';
    
    IF test_result > 0 THEN
        RAISE NOTICE '✅ 学生读取权限存在';
    ELSE
        RAISE NOTICE '❌ 学生读取权限不存在';
    END IF;
    
    -- 4. 检查菜单访问权限
    RAISE NOTICE '4. 检查菜单访问权限:';
    SELECT COUNT(*) INTO test_result FROM public.casbin_policies 
    WHERE tenant_id = tenant_id_var 
      AND ptype = 'p' 
      AND v0 = 'role:class_teacher'
      AND v2 LIKE 'menu:%';
    
    IF test_result > 0 THEN
        RAISE NOTICE '✅ 菜单访问权限存在 (% 个)', test_result;
    ELSE
        RAISE NOTICE '❌ 菜单访问权限不存在';
    END IF;
    
    -- 5. 检查学生管理菜单权限
    RAISE NOTICE '5. 检查学生管理菜单权限:';
    SELECT COUNT(*) INTO test_result FROM public.casbin_policies 
    WHERE tenant_id = tenant_id_var 
      AND ptype = 'p' 
      AND v0 = 'role:class_teacher'
      AND v2 = 'menu:student_management';
    
    IF test_result > 0 THEN
        RAISE NOTICE '✅ 学生管理菜单权限存在';
    ELSE
        RAISE NOTICE '❌ 学生管理菜单权限不存在';
    END IF;
    
    RAISE NOTICE '=== 班主任权限测试完成 ===';
END $$;

-- 显示班主任的所有权限
SELECT '=== 班主任所有权限 ===' as section;

SELECT 
    ptype as 策略类型,
    v0 as 角色,
    v1 as 租户,
    v2 as 对象,
    v3 as 动作,
    v4 as 效果
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
  AND v0 = 'role:class_teacher'
ORDER BY v2, v3;

-- 显示班主任的角色绑定
SELECT '=== 班主任角色绑定 ===' as section;

SELECT 
    ptype as 策略类型,
    v0 as 用户,
    v1 as 角色,
    v2 as 租户
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'g'
  AND v1 = 'role:class_teacher';

-- 显示菜单权限配置
SELECT '=== 菜单权限配置 ===' as section;

SELECT 
    menu_id as 菜单ID,
    name as 菜单名称,
    required_permissions as 所需权限
FROM public.menu_permissions 
WHERE menu_id IN ('student_management', 'administrative_classes_management', 'teacher_management')
ORDER BY menu_id; 