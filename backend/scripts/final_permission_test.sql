-- 最终权限测试
-- 验证 class_teacher_2301 的所有权限配置

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
    user_id_var VARCHAR(100);
    user_identity VARCHAR(200);
    test_result BOOLEAN;
    total_tests INT := 0;
    passed_tests INT := 0;
BEGIN
    -- 获取 gzxxzz 租户ID
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 获取 class_teacher_2301 用户ID
    SELECT id::text INTO user_id_var FROM public.users WHERE username = 'class_teacher_2301';
    
    -- 构建用户身份标识
    user_identity := 'user:' || user_id_var;
    
    RAISE NOTICE '=== 最终权限测试 ===';
    RAISE NOTICE '用户: class_teacher_2301';
    RAISE NOTICE '用户ID: %', user_id_var;
    RAISE NOTICE '租户ID: %', tenant_id_var;
    RAISE NOTICE '用户身份: %', user_identity;
    RAISE NOTICE '';
    
    -- 测试1: 用户存在性检查
    total_tests := total_tests + 1;
    SELECT EXISTS(SELECT 1 FROM public.users WHERE id = user_id_var::uuid) INTO test_result;
    IF test_result THEN
        RAISE NOTICE '✅ 测试1: 用户存在性检查 - 通过';
        passed_tests := passed_tests + 1;
    ELSE
        RAISE NOTICE '❌ 测试1: 用户存在性检查 - 失败';
    END IF;
    
    -- 测试2: 用户身份绑定检查
    total_tests := total_tests + 1;
    SELECT EXISTS(SELECT 1 FROM tenant_gzxxzz.user_identities WHERE user_id = user_id_var::uuid) INTO test_result;
    IF test_result THEN
        RAISE NOTICE '✅ 测试2: 用户身份绑定检查 - 通过';
        passed_tests := passed_tests + 1;
    ELSE
        RAISE NOTICE '❌ 测试2: 用户身份绑定检查 - 失败';
    END IF;
    
    -- 测试3: 角色分配检查
    total_tests := total_tests + 1;
    SELECT EXISTS(SELECT 1 FROM public.casbin_policies WHERE tenant_id = tenant_id_var AND ptype = 'g' AND v0 = user_identity AND v1 = 'role:class_teacher') INTO test_result;
    IF test_result THEN
        RAISE NOTICE '✅ 测试3: 角色分配检查 - 通过';
        passed_tests := passed_tests + 1;
    ELSE
        RAISE NOTICE '❌ 测试3: 角色分配检查 - 失败';
    END IF;
    
    -- 测试4: student:read 权限检查
    total_tests := total_tests + 1;
    SELECT EXISTS(SELECT 1 FROM public.casbin_policies WHERE tenant_id = tenant_id_var AND ptype = 'p' AND v0 = 'role:class_teacher' AND v2 = 'student:read' AND v3 = 'read' AND v4 = 'allow') INTO test_result;
    IF test_result THEN
        RAISE NOTICE '✅ 测试4: student:read 权限检查 - 通过';
        passed_tests := passed_tests + 1;
    ELSE
        RAISE NOTICE '❌ 测试4: student:read 权限检查 - 失败';
    END IF;
    
    -- 测试5: grade:read 权限检查
    total_tests := total_tests + 1;
    SELECT EXISTS(SELECT 1 FROM public.casbin_policies WHERE tenant_id = tenant_id_var AND ptype = 'p' AND v0 = 'role:class_teacher' AND v2 = 'grade:read' AND v3 = 'read' AND v4 = 'allow') INTO test_result;
    IF test_result THEN
        RAISE NOTICE '✅ 测试5: grade:read 权限检查 - 通过';
        passed_tests := passed_tests + 1;
    ELSE
        RAISE NOTICE '❌ 测试5: grade:read 权限检查 - 失败';
    END IF;
    
    -- 测试6: grade:write 权限检查
    total_tests := total_tests + 1;
    SELECT EXISTS(SELECT 1 FROM public.casbin_policies WHERE tenant_id = tenant_id_var AND ptype = 'p' AND v0 = 'role:class_teacher' AND v2 = 'grade:write' AND v3 = 'write' AND v4 = 'allow') INTO test_result;
    IF test_result THEN
        RAISE NOTICE '✅ 测试6: grade:write 权限检查 - 通过';
        passed_tests := passed_tests + 1;
    ELSE
        RAISE NOTICE '❌ 测试6: grade:write 权限检查 - 失败';
    END IF;
    
    -- 测试7: exam:read 权限检查
    total_tests := total_tests + 1;
    SELECT EXISTS(SELECT 1 FROM public.casbin_policies WHERE tenant_id = tenant_id_var AND ptype = 'p' AND v0 = 'role:class_teacher' AND v2 = 'exam:read' AND v3 = 'read' AND v4 = 'allow') INTO test_result;
    IF test_result THEN
        RAISE NOTICE '✅ 测试7: exam:read 权限检查 - 通过';
        passed_tests := passed_tests + 1;
    ELSE
        RAISE NOTICE '❌ 测试7: exam:read 权限检查 - 失败';
    END IF;
    
    -- 测试8: 学生管理菜单存在性检查
    total_tests := total_tests + 1;
    SELECT EXISTS(SELECT 1 FROM public.menu_permissions WHERE menu_id = 'student_management') INTO test_result;
    IF test_result THEN
        RAISE NOTICE '✅ 测试8: 学生管理菜单存在性检查 - 通过';
        passed_tests := passed_tests + 1;
    ELSE
        RAISE NOTICE '❌ 测试8: 学生管理菜单存在性检查 - 失败';
    END IF;
    
    -- 测试9: 菜单权限要求检查
    total_tests := total_tests + 1;
    SELECT EXISTS(SELECT 1 FROM public.menu_permissions WHERE menu_id = 'student_management' AND required_permissions @> ARRAY['student:read', 'grade:read', 'grade:write', 'exam:read']) INTO test_result;
    IF test_result THEN
        RAISE NOTICE '✅ 测试9: 菜单权限要求检查 - 通过';
        passed_tests := passed_tests + 1;
    ELSE
        RAISE NOTICE '❌ 测试9: 菜单权限要求检查 - 失败';
    END IF;
    
    -- 测试10: 菜单访问权限检查
    total_tests := total_tests + 1;
    SELECT EXISTS(SELECT 1 FROM public.casbin_policies WHERE tenant_id = tenant_id_var AND ptype = 'p' AND v0 = 'role:class_teacher' AND v2 = 'menu:student_management' AND v3 = 'access' AND v4 = 'allow') INTO test_result;
    IF test_result THEN
        RAISE NOTICE '✅ 测试10: 菜单访问权限检查 - 通过';
        passed_tests := passed_tests + 1;
    ELSE
        RAISE NOTICE '❌ 测试10: 菜单访问权限检查 - 失败';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '=== 测试结果汇总 ===';
    RAISE NOTICE '总测试数: %', total_tests;
    RAISE NOTICE '通过测试数: %', passed_tests;
    RAISE NOTICE '失败测试数: %', total_tests - passed_tests;
    RAISE NOTICE '通过率: %%%', ROUND((passed_tests::float / total_tests * 100)::numeric, 2);
    
    IF passed_tests = total_tests THEN
        RAISE NOTICE '🎉 所有测试通过！class_teacher_2301 应该能够正常访问学生管理菜单。';
    ELSE
        RAISE NOTICE '⚠️ 部分测试失败，需要进一步检查。';
    END IF;
    
END $$;

-- 显示详细的权限配置
SELECT '=== 详细权限配置 ===' as section;

-- 用户信息
SELECT 
    '用户信息' as 类型,
    username as 用户名,
    phone_number as 手机号,
    is_active as 是否激活
FROM public.users 
WHERE username = 'class_teacher_2301';

-- 用户身份绑定
SELECT 
    '用户身份绑定' as 类型,
    ui.user_id as 用户ID,
    r.name as 角色名称,
    ui.target_type as 目标类型,
    ui.subject as 目标对象
FROM tenant_gzxxzz.user_identities ui
JOIN public.roles r ON ui.role_id = r.id
WHERE ui.user_id = (SELECT id FROM public.users WHERE username = 'class_teacher_2301');

-- 角色权限策略
SELECT 
    '角色权限策略' as 类型,
    v0 as 角色,
    v2 as 对象,
    v3 as 动作,
    v4 as 效果
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
  AND v0 = 'role:class_teacher'
ORDER BY v2, v3;

-- 菜单配置
SELECT 
    '菜单配置' as 类型,
    menu_id as 菜单ID,
    name as 菜单名称,
    required_permissions as 所需权限
FROM public.menu_permissions 
WHERE menu_id = 'student_management'; 