-- 为 gzxxzz 租户配置 Casbin 权限策略和菜单权限测试数据
-- 用于测试教师角色的菜单权限系统

-- ================================================================
-- 1. 配置 Casbin 权限策略 (public.casbin_policies)
-- ================================================================

-- 获取租户ID
DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
BEGIN
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 清理之前的策略
    DELETE FROM public.casbin_policies WHERE tenant_id = tenant_id_var;
    
    -- 1. 用户-角色策略 (g 策略)
    -- 校长
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, tenant_id) VALUES 
    ('g', 'user:11111111-1111-1111-1111-111111111111', 'role:principal', tenant_id_var, tenant_id_var);
    
    -- 教导主任
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, tenant_id) VALUES 
    ('g', 'user:22222222-2222-2222-2222-222222222222', 'role:academic_director', tenant_id_var, tenant_id_var);
    
    -- 学科组长
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, tenant_id) VALUES 
    ('g', 'user:33333333-3333-3333-3333-333333333333', 'role:subject_leader', tenant_id_var, tenant_id_var),
    ('g', 'user:44444444-4444-4444-4444-444444444444', 'role:subject_leader', tenant_id_var, tenant_id_var),
    ('g', 'user:55555555-5555-5555-5555-555555555555', 'role:subject_leader', tenant_id_var, tenant_id_var);
    
    -- 班主任
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, tenant_id) VALUES 
    ('g', 'user:66666666-6666-6666-6666-666666666666', 'role:class_teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:77777777-7777-7777-7777-777777777777', 'role:class_teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:88888888-8888-8888-8888-888888888888', 'role:class_teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:99999999-9999-9999-9999-999999999999', 'role:class_teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'role:class_teacher', tenant_id_var, tenant_id_var);
    
    -- 任课老师
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, tenant_id) VALUES 
    ('g', 'user:bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'role:teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:cccccccc-cccc-cccc-cccc-cccccccccccc', 'role:teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:dddddddd-dddd-dddd-dddd-dddddddddddd', 'role:teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'role:teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:ffffffff-ffff-ffff-ffff-ffffffffffff', 'role:teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:10101010-1010-1010-1010-101010101010', 'role:teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:20202020-2020-2020-2020-202020202020', 'role:teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:30303030-3030-3030-3030-303030303030', 'role:teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:40404040-4040-4040-4040-404040404040', 'role:teacher', tenant_id_var, tenant_id_var),
    ('g', 'user:50505050-5050-5050-5050-505050505050', 'role:teacher', tenant_id_var, tenant_id_var);
    
    -- 部分学生 (前10名学生作为测试)
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, tenant_id) 
    SELECT 
        'g',
        'user:' || ('60000000-0000-0000-0000-' || LPAD(s.id::text, 12, '0')),
        'role:student',
        tenant_id_var,
        tenant_id_var
    FROM generate_series(1, 10) AS s(id);
    
    -- 2. 角色权限策略 (p 策略)
    
    -- 校长权限 - 全部菜单
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:principal', tenant_id_var, 'menu:*', 'access', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'student:*', 'manage', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'teacher:*', 'manage', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'class:*', 'manage', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'exam:*', 'manage', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'grade:*', 'manage', tenant_id_var),
    ('p', 'role:principal', tenant_id_var, 'system:*', 'manage', tenant_id_var);
    
    -- 教导主任权限 - 大部分菜单
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:academic_director', tenant_id_var, 'menu:student_management', 'access', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'menu:exam_management', 'access', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'menu:grade_management', 'access', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'menu:class_management', 'access', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'menu:teacher_management', 'access', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'student:*', 'read', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'teacher:*', 'read', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'class:*', 'read', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'exam:*', 'manage', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'grade:*', 'read', tenant_id_var);
    
    -- 学科组长权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:subject_leader', tenant_id_var, 'menu:student_management', 'access', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'menu:exam_management', 'access', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'menu:grade_management', 'access', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'menu:teacher_management', 'access', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'student:subject_group', 'read', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'teacher:subject_group', 'read', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'exam:subject_group', 'create', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'grade:subject_group', 'read', tenant_id_var);
    
    -- 班主任权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:class_teacher', tenant_id_var, 'menu:student_management', 'access', tenant_id_var),
    ('p', 'role:class_teacher', tenant_id_var, 'menu:grade_management', 'access', tenant_id_var),
    ('p', 'role:class_teacher', tenant_id_var, 'menu:class_management', 'access', tenant_id_var),
    ('p', 'role:class_teacher', tenant_id_var, 'student:class', 'manage', tenant_id_var),
    ('p', 'role:class_teacher', tenant_id_var, 'grade:class', 'read', tenant_id_var),
    ('p', 'role:class_teacher', tenant_id_var, 'exam:class', 'read', tenant_id_var);
    
    -- 任课老师权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:teacher', tenant_id_var, 'menu:student_management', 'access', tenant_id_var),
    ('p', 'role:teacher', tenant_id_var, 'menu:grade_management', 'access', tenant_id_var),
    ('p', 'role:teacher', tenant_id_var, 'menu:personal_center', 'access', tenant_id_var),
    ('p', 'role:teacher', tenant_id_var, 'student:teaching_class', 'read', tenant_id_var),
    ('p', 'role:teacher', tenant_id_var, 'grade:teaching_class', 'write', tenant_id_var);
    
    -- 学生权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:student', tenant_id_var, 'menu:personal_center', 'access', tenant_id_var),
    ('p', 'role:student', tenant_id_var, 'grade:self', 'read', tenant_id_var),
    ('p', 'role:student', tenant_id_var, 'profile:self', 'read', tenant_id_var);
    
    -- 3. 角色继承策略 (g2 策略) - 建立角色层次
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, tenant_id) VALUES 
    ('g2', 'role:academic_director', 'role:subject_leader', tenant_id_var, tenant_id_var),
    ('g2', 'role:subject_leader', 'role:class_teacher', tenant_id_var, tenant_id_var),
    ('g2', 'role:class_teacher', 'role:teacher', tenant_id_var, tenant_id_var);
    
    RAISE NOTICE 'Casbin 权限策略配置完成!';
END $$;

-- ================================================================
-- 2. 创建菜单权限测试数据 (扩展现有菜单)
-- ================================================================

-- 添加更多教师相关的菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes) VALUES
-- 成绩管理扩展菜单
('grade_input_my_classes', '我的班级成绩', '/grades/my-classes', 'edit-2', 'grade_management', 304, ARRAY['grade:class'], ARRAY['class:*']),
('grade_subject_analysis', '学科成绩分析', '/grades/subject-analysis', 'trending-up', 'grade_management', 305, ARRAY['grade:subject_group'], ARRAY['subject_group:*']),

-- 教师工作台
('teacher_dashboard', '教师工作台', '/teacher', 'layout-dashboard', NULL, 50, ARRAY['teacher:dashboard'], ARRAY['class:*']),
('my_teaching_classes', '我的教学班', '/teacher/classes', 'users', 'teacher_dashboard', 51, ARRAY['teacher:teaching_class'], ARRAY['class:*']),
('my_grade_input', '成绩录入', '/teacher/grades', 'edit', 'teacher_dashboard', 52, ARRAY['teacher:grade_input'], ARRAY['class:*']),
('class_student_management', '班级学生管理', '/teacher/students', 'user-plus', 'teacher_dashboard', 53, ARRAY['teacher:student_manage'], ARRAY['class:*']),

-- 学科组管理
('subject_group_dashboard', '学科组工作台', '/subject-group', 'layers', NULL, 150, ARRAY['subject_leader:dashboard'], ARRAY['subject_group:*']),
('subject_teachers', '学科教师管理', '/subject-group/teachers', 'users', 'subject_group_dashboard', 151, ARRAY['subject_leader:teacher_manage'], ARRAY['subject_group:*']),
('subject_exams', '学科考试管理', '/subject-group/exams', 'file-text', 'subject_group_dashboard', 152, ARRAY['subject_leader:exam_manage'], ARRAY['subject_group:*']),

-- 班主任专用菜单
('homeroom_dashboard', '班主任工作台', '/homeroom', 'home', NULL, 250, ARRAY['class_teacher:dashboard'], ARRAY['class:*']),
('class_daily_management', '班级日常管理', '/homeroom/daily', 'calendar', 'homeroom_dashboard', 251, ARRAY['class_teacher:daily_manage'], ARRAY['class:*']),
('student_performance', '学生表现管理', '/homeroom/performance', 'award', 'homeroom_dashboard', 252, ARRAY['class_teacher:performance'], ARRAY['class:*'])

ON CONFLICT (menu_id) DO NOTHING;

-- ================================================================
-- 3. 显示测试数据统计
-- ================================================================

SELECT '=== gzxxzz 租户权限配置完成 ===' AS message;

-- Casbin 策略统计
SELECT 
    ptype,
    COUNT(*) as policy_count,
    STRING_AGG(DISTINCT SPLIT_PART(v0, ':', 2), ', ') as subjects
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
GROUP BY ptype
ORDER BY ptype;

-- 菜单权限统计
SELECT COUNT(*) as total_menus FROM public.menu_permissions;

-- 测试用户账号信息
SELECT 
    '=== 测试账号信息 (用户名/密码都是账号名) ===' as info,
    username as 登录用户名,
    phone_number as 手机号,
    CASE 
        WHEN username LIKE '%principal%' THEN '校长'
        WHEN username LIKE '%director%' THEN '教导主任' 
        WHEN username LIKE '%leader%' THEN '学科组长'
        WHEN username LIKE '%class_teacher%' THEN '班主任'
        WHEN username LIKE '%teacher_%' THEN '任课老师'
        ELSE '其他'
    END as 角色类型
FROM public.users 
WHERE phone_number LIKE '1380000%' AND username NOT LIKE '%student%'
ORDER BY phone_number
LIMIT 15;