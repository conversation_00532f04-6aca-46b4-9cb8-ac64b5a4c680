-- 修复 gzxxzz 租户测试数据中的用户身份绑定
-- 修复 UUID 格式错误并完成身份绑定

-- ================================================================
-- 修复并创建用户身份绑定 (tenant_gzxxzz.user_identities)
-- ================================================================

DO $$ 
DECLARE
    principal_role_id UUID;
    director_role_id UUID;
    subject_leader_role_id UUID;
    class_teacher_role_id UUID;
    teacher_role_id UUID;
    student_role_id UUID;
BEGIN
    -- 获取各角色ID
    SELECT id INTO principal_role_id FROM public.roles WHERE code = 'principal' AND is_system = TRUE;
    SELECT id INTO director_role_id FROM public.roles WHERE code = 'academic_director' AND is_system = TRUE;
    SELECT id INTO subject_leader_role_id FROM public.roles WHERE code = 'subject_leader' AND is_system = TRUE;
    SELECT id INTO class_teacher_role_id FROM public.roles WHERE code = 'class_teacher' AND is_system = TRUE;
    SELECT id INTO teacher_role_id FROM public.roles WHERE code = 'teacher' AND is_system = TRUE;
    SELECT id INTO student_role_id FROM public.roles WHERE code = 'student' AND is_system = TRUE;

    -- 清理之前可能的错误数据
    DELETE FROM tenant_gzxxzz.user_identities;

    -- 校长身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id) VALUES 
    ('11111111-1111-1111-1111-111111111111', principal_role_id, 'school', NULL);

    -- 教导主任身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id) VALUES 
    ('22222222-2222-2222-2222-222222222222', director_role_id, 'school', NULL);

    -- 学科组长身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id, subject) VALUES 
    ('33333333-3333-3333-3333-333333333333', subject_leader_role_id, 'subject_group', 'a0000000-0000-0000-0000-000000000001', 'CHINESE'),
    ('44444444-4444-4444-4444-444444444444', subject_leader_role_id, 'subject_group', 'a0000000-0000-0000-0000-000000000002', 'MATH'),
    ('55555555-5555-5555-5555-555555555555', subject_leader_role_id, 'subject_group', 'a0000000-0000-0000-0000-000000000003', 'ENGLISH');

    -- 班主任身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id) VALUES 
    ('66666666-6666-6666-6666-666666666666', class_teacher_role_id, 'class', 'c0000000-0000-0000-0000-000000000001'),
    ('*************-7777-7777-************', class_teacher_role_id, 'class', 'c0000000-0000-0000-0000-000000000002'),
    ('*************-8888-8888-************', class_teacher_role_id, 'class', 'c0000000-0000-0000-0000-000000000003'),
    ('*************-9999-9999-************', class_teacher_role_id, 'class', 'c0000000-0000-0000-0000-000000000004'),
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', class_teacher_role_id, 'class', 'c0000000-0000-0000-0000-000000000005');

    -- 任课老师身份 (修复UUID格式)
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id, subject) VALUES
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', teacher_role_id, 'class', 'd0000000-0000-0000-0000-000000000001', 'CHINESE'),
    ('cccccccc-cccc-cccc-cccc-cccccccccccc', teacher_role_id, 'class', 'd0000000-0000-0000-0000-000000000002', 'CHINESE'),
    ('dddddddd-dddd-dddd-dddd-dddddddddddd', teacher_role_id, 'class', 'd0000000-0000-0000-0000-000000000003', 'CHINESE'),
    ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', teacher_role_id, 'class', 'd0000000-0000-0000-0000-000000000004', 'MATH'),
    ('ffffffff-ffff-ffff-ffff-ffffffffffff', teacher_role_id, 'class', 'd0000000-0000-0000-0000-000000000005', 'MATH'),
    ('10101010-1010-1010-1010-101010101010', teacher_role_id, 'class', 'd0000000-0000-0000-0000-000000000006', 'MATH'),
    ('*************-2020-2020-************', teacher_role_id, 'class', 'd0000000-0000-0000-0000-000000000007', 'ENGLISH'),
    ('*************-3030-3030-303030303030', teacher_role_id, 'class', 'd0000000-0000-0000-0000-000000000008', 'ENGLISH'),
    ('*************-4040-4040-************', teacher_role_id, 'class', 'd0000000-0000-0000-0000-000000000009', 'ENGLISH'),
    ('*************-5050-5050-************', teacher_role_id, 'class', 'd0000000-0000-0000-0000-000000000010', 'COMPUTER');

    -- 学生身份
    INSERT INTO tenant_gzxxzz.user_identities (user_id, role_id, target_type, target_id) 
    SELECT 
        ('*************-0000-0000-' || LPAD(s.id::text, 12, '0'))::uuid,
        student_role_id,
        'class',
        CASE 
            WHEN s.id <= 20 THEN 'c0000000-0000-0000-0000-000000000001'::uuid
            WHEN s.id <= 40 THEN 'c0000000-0000-0000-0000-000000000002'::uuid
            WHEN s.id <= 60 THEN 'c0000000-0000-0000-0000-000000000003'::uuid
            WHEN s.id <= 80 THEN 'c0000000-0000-0000-0000-000000000004'::uuid
            ELSE 'c0000000-0000-0000-0000-000000000005'::uuid
        END
    FROM generate_series(1, 100) AS s(id);

    RAISE NOTICE '用户身份绑定修复完成!';
END $$;

-- 显示身份绑定结果
SELECT '=== gzxxzz 租户身份绑定修复完成 ===' AS message;
SELECT COUNT(*) AS total_identities FROM tenant_gzxxzz.user_identities;

-- 显示教师角色分布
SELECT r.name AS role_name, COUNT(*) AS teacher_count 
FROM tenant_gzxxzz.user_identities ui
JOIN public.roles r ON ui.role_id = r.id
JOIN tenant_gzxxzz.teachers t ON ui.user_id = t.user_id
GROUP BY r.name, r.level
ORDER BY r.level;

-- 显示学生身份统计
SELECT '学生身份' AS identity_type, COUNT(*) AS count
FROM tenant_gzxxzz.user_identities ui
JOIN public.roles r ON ui.role_id = r.id
WHERE r.code = 'student';