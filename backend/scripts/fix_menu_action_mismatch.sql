-- 修复Casbin策略中的动作参数不匹配问题
-- 菜单权限要求 menu:access，系统检查 (user, tenant, menu:access, access)
-- 但策略中的动作是 allow，需要改为 access

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
    updated_count INT;
BEGIN
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 更新所有 menu:access 策略的动作从 allow 改为 access
    UPDATE public.casbin_policies 
    SET v3 = 'access'
    WHERE tenant_id = tenant_id_var 
      AND ptype = 'p' 
      AND v2 = 'menu:access' 
      AND v3 = 'allow';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % menu:access policies from allow to access', updated_count;
    
    -- 更新所有 menu:* 策略的动作从 allow 改为 access  
    UPDATE public.casbin_policies 
    SET v3 = 'access'
    WHERE tenant_id = tenant_id_var 
      AND ptype = 'p' 
      AND v2 = 'menu:*' 
      AND v3 = 'allow';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % menu:* policies from allow to access', updated_count;
    
    RAISE NOTICE 'Casbin菜单权限动作参数已修复！';
END $$;

-- 验证修复结果
SELECT '=== 修复验证 ===' as section;

-- 检查修复后的菜单相关权限策略
SELECT 
    'Fixed Menu Permissions:' as type,
    v0 as role,
    v1 as domain,
    v2 as object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
  AND v2 LIKE 'menu:%'
ORDER BY v0, v2;

SELECT '=== 权限检查验证 ===' as section;
SELECT '现在 menu:access 权限的动作参数应该是 access，与系统检查保持一致' as confirmation;