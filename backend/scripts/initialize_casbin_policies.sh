#!/bin/bash

# Casbin Policies Initialization Script
# This script runs the casbin policies initialization migration

set -e

# Database connection settings
DB_URL=${DATABASE_URL:-"postgresql://username:password@localhost/deep_mate"}
MIGRATION_FILE="$(dirname "$0")/../migrations/20250802_casbin_policies_initialization.sql"

echo "🚀 Starting Casbin policies initialization..."

# Check if migration file exists
if [ ! -f "$MIGRATION_FILE" ]; then
    echo "❌ Migration file not found: $MIGRATION_FILE"
    exit 1
fi

# Run the migration
echo "📝 Running casbin policies initialization migration..."
psql "$DB_URL" -f "$MIGRATION_FILE"

if [ $? -eq 0 ]; then
    echo "✅ Casbin policies initialization completed successfully!"
    
    # Display some statistics
    echo ""
    echo "📊 Policy Statistics:"
    psql "$DB_URL" -c "
        SELECT 
            'System Policies' as category,
            COUNT(*) as count
        FROM public.casbin_policies 
        WHERE tenant_id = 'system'
        UNION ALL
        SELECT 
            'Template Policies' as category,
            COUNT(*) as count
        FROM public.casbin_policies 
        WHERE tenant_id = 'template'
        UNION ALL
        SELECT 
            'Tenant Policies' as category,
            COUNT(*) as count
        FROM public.casbin_policies 
        WHERE tenant_id NOT IN ('system', 'template')
        UNION ALL
        SELECT 
            'Menu Permissions' as category,
            COUNT(*) as count
        FROM public.menu_permissions;
    "
    
    echo ""
    echo "🔍 Sample Policies by Type:"
    psql "$DB_URL" -c "
        SELECT 
            ptype as policy_type,
            COUNT(*) as count,
            string_agg(DISTINCT v0, ', ') as sample_subjects
        FROM public.casbin_policies 
        WHERE tenant_id != 'template'
        GROUP BY ptype 
        ORDER BY policy_type;
    "
    
    echo ""
    echo "🏢 Tenant Policy Distribution:"
    psql "$DB_URL" -c "
        SELECT 
            tenant_id,
            COUNT(*) as total_policies,
            COUNT(*) FILTER (WHERE ptype = 'p') as permission_policies,
            COUNT(*) FILTER (WHERE ptype = 'g') as role_mappings,
            COUNT(*) FILTER (WHERE ptype = 'g2') as role_inheritance
        FROM public.casbin_policies 
        GROUP BY tenant_id 
        ORDER BY tenant_id;
    "
    
else
    echo "❌ Migration failed!"
    exit 1
fi

echo ""
echo "🎉 Casbin RBAC system is now initialized with:"
echo "   • System-level permissions for super_admin"
echo "   • Role-based permissions for all user types"
echo "   • Multi-tenant policy templates"
echo "   • Menu access control mappings"
echo "   • Role inheritance hierarchies"
echo "   • Temporary permission templates"
echo ""
echo "🔧 You can now use the Casbin Policy Management page at /casbin-policies to manage policies!"