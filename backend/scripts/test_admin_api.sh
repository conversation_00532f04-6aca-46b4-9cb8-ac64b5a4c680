#!/bin/bash

# 动态租户创建API测试脚本
# 使用方法: ./test_admin_api.sh [BASE_URL] [ADMIN_TOKEN]

BASE_URL=${1:-"http://localhost:8080"}
ADMIN_TOKEN=${2:-"YOUR_ADMIN_TOKEN_HERE"}

echo "=== 动态租户创建API测试 ==="
echo "Base URL: $BASE_URL"
echo "Admin Token: ${ADMIN_TOKEN:0:20}..."
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${YELLOW}测试: $description${NC}"
    echo "请求: $method $endpoint"
    
    if [ -n "$data" ]; then
        echo "数据: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Authorization: Bearer $ADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Authorization: Bearer $ADMIN_TOKEN" \
            "$BASE_URL$endpoint")
    fi
    
    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status_code=$(echo "$response" | tail -n 1)
    
    echo "状态码: $status_code"
    echo "响应: $body"
    
    if [ "$status_code" -ge 200 ] && [ "$status_code" -lt 300 ]; then
        echo -e "${GREEN}✓ 成功${NC}"
    else
        echo -e "${RED}✗ 失败${NC}"
    fi
    
    echo "----------------------------------------"
    echo
}

# 1. 测试创建租户
echo "=== 1. 创建租户测试 ==="
CREATE_DATA='{
  "name": "测试公司",
  "tenantType": "standard",
  "domain": "test.example.com",
  "settings": {
    "max_users": 100,
    "features": ["basic", "analytics"]
  }
}'

test_api "POST" "/api/admin/tenants" "$CREATE_DATA" "创建新租户"

# 2. 测试获取租户列表
echo "=== 2. 获取租户列表测试 ==="
test_api "GET" "/api/admin/tenants" "" "获取所有租户"

# 3. 测试获取单个租户（需要先有租户ID）
echo "=== 3. 获取单个租户测试 ==="
echo "注意: 需要替换为实际的租户ID"
# test_api "GET" "/api/admin/tenants/123e4567-e89b-12d3-a456-426614174000" "" "获取单个租户"

# 4. 测试更新租户
echo "=== 4. 更新租户测试 ==="
UPDATE_DATA='{
  "name": "更新后的测试公司",
  "domain": "updated.example.com",
  "status": "active"
}'
echo "注意: 需要替换为实际的租户ID"
# test_api "PUT" "/api/admin/tenants/123e4567-e89b-12d3-a456-426614174000" "$UPDATE_DATA" "更新租户信息"

# 5. 测试删除租户
echo "=== 5. 删除租户测试 ==="
echo "注意: 需要替换为实际的租户ID"
# test_api "DELETE" "/api/admin/tenants/123e4567-e89b-12d3-a456-426614174000" "" "删除租户"

echo "=== 测试完成 ==="
echo
echo "使用说明:"
echo "1. 确保服务器正在运行: cargo run"
echo "2. 获取管理员JWT token"
echo "3. 运行测试: ./test_admin_api.sh http://localhost:8080 YOUR_ADMIN_TOKEN"
echo
echo "注意事项:"
echo "- 需要有效的管理员JWT token"
echo "- 数据库需要正确配置和迁移"
echo "- 某些测试需要实际的租户ID"
