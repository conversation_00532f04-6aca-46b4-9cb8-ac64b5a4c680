#!/bin/bash
# <PERSON><PERSON>t to fix migration issues

# Check if the migrations directory exists
if [ ! -d "migrations" ]; then
    echo "Error: migrations directory not found"
    exit 1
fi

# Create backup directory if it doesn't exist
mkdir -p migrations_backup_new

# Move the problematic migration to backup
echo "Moving problematic migration to backup..."
mv migrations/20250101_enhanced_auth_system.sql migrations_backup_new/

# Remove the update migration if it exists
if [ -f "migrations/20250106_enhanced_auth_system_updates.sql" ]; then
    mv migrations/20250106_enhanced_auth_system_updates.sql migrations_backup_new/
fi

# Copy the new complete migration to the migrations directory
echo "Using the new complete migration..."
cp migrations/20250720_enhanced_auth_system_complete.sql migrations/20250101_enhanced_auth_system.sql

echo "Migration files have been reorganized."
echo "Now you need to reset the migration history in the database."
echo "You can use the reset_migrations.sh script for this."