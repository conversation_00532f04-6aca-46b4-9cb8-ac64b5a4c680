-- 修复Casbin策略定义不匹配问题
-- 为所有权限策略添加缺失的 effect 字段 (v4 = "allow")

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
BEGIN
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 更新所有 p 类型策略，添加 effect 字段
    UPDATE public.casbin_policies 
    SET v4 = 'allow'
    WHERE tenant_id = tenant_id_var 
      AND ptype = 'p' 
      AND (v4 IS NULL OR v4 = '');
    
    RAISE NOTICE 'Updated % permission policies with effect field', 
        (SELECT COUNT(*) FROM public.casbin_policies 
         WHERE tenant_id = tenant_id_var AND ptype = 'p' AND v4 = 'allow');
         
    RAISE NOTICE 'Casbin策略定义已修复！所有权限策略现在都包含effect字段。';
END $$;

-- 验证修复结果
SELECT '=== 修复验证 ===' as section;

SELECT 
    'Permission策略字段验证' as check_type,
    ptype,
    COUNT(*) as total_policies,
    COUNT(CASE WHEN v4 = 'allow' THEN 1 END) as policies_with_effect,
    COUNT(CASE WHEN v4 IS NULL OR v4 = '' THEN 1 END) as policies_without_effect
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
GROUP BY ptype;

-- 显示几个示例修复后的策略
SELECT '=== 修复后的策略示例 ===' as section;
SELECT 
    'Policy示例' as type,
    ptype,
    v0 as subject,
    v1 as domain, 
    v2 as object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
ORDER BY v0, v2
LIMIT 10;

SELECT '=== 策略格式确认 ===' as section;
SELECT '现在所有权限策略都应该有5个字段: sub, dom, obj, act, eft' as confirmation;
SELECT '这与Casbin模型要求的 p = sub, dom, obj, act, eft 完全匹配' as model_match;