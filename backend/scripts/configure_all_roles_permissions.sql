-- 为其他角色配置菜单访问权限
-- 确保所有角色都能看到对应的菜单

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
BEGIN
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 为教导主任配置权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:academic_director', tenant_id_var, 'menu:access', 'allow', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'student:read', 'allow', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'teacher:read', 'allow', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'class:read', 'allow', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'exam:manage', 'allow', tenant_id_var),
    ('p', 'role:academic_director', tenant_id_var, 'grade:read', 'allow', tenant_id_var)
    ON CONFLICT DO NOTHING;
    
    -- 为学科组长配置权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:subject_leader', tenant_id_var, 'menu:access', 'allow', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'student:subject_group', 'read', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'teacher:subject_group', 'read', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'exam:subject_group', 'create', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'grade:subject_group', 'read', tenant_id_var)
    ON CONFLICT DO NOTHING;
    
    -- 为班主任配置权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:class_teacher', tenant_id_var, 'menu:access', 'allow', tenant_id_var),
    ('p', 'role:class_teacher', tenant_id_var, 'student:class', 'manage', tenant_id_var),
    ('p', 'role:class_teacher', tenant_id_var, 'grade:class', 'read', tenant_id_var),
    ('p', 'role:class_teacher', tenant_id_var, 'exam:class', 'read', tenant_id_var)
    ON CONFLICT DO NOTHING;
    
    -- 为任课老师配置权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:teacher', tenant_id_var, 'menu:access', 'allow', tenant_id_var),
    ('p', 'role:teacher', tenant_id_var, 'student:teaching_class', 'read', tenant_id_var),
    ('p', 'role:teacher', tenant_id_var, 'grade:teaching_class', 'write', tenant_id_var),
    ('p', 'role:teacher', tenant_id_var, 'personal:self', 'read', tenant_id_var)
    ON CONFLICT DO NOTHING;
    
    -- 为学生配置权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:student', tenant_id_var, 'menu:access', 'allow', tenant_id_var),
    ('p', 'role:student', tenant_id_var, 'grade:self', 'read', tenant_id_var),
    ('p', 'role:student', tenant_id_var, 'personal:self', 'read', tenant_id_var)
    ON CONFLICT DO NOTHING;
    
    RAISE NOTICE '所有角色权限配置完成！';
END $$;

-- 验证所有角色的菜单权限
SELECT 
    '=== 所有角色菜单权限验证 ===' as section,
    v0 as role,
    COUNT(*) as permission_count
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
  AND v2 = 'menu:access'
GROUP BY v0
ORDER BY v0;

-- 显示权限策略总数
SELECT 
    '=== gzxxzz 租户权限策略总览 ===' as section,
    ptype,
    COUNT(*) as count
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
GROUP BY ptype
ORDER BY ptype;

SELECT '=== 权限系统配置完成！===' as final_status;
SELECT '现在所有测试账号都应该能正常登录并看到对应菜单' as instruction;
SELECT 'principal_gzxxzz / admin123 = 校长账号（全部菜单）' as test_account_1;
SELECT 'class_teacher_2301 / admin123 = 班主任账号（部分菜单）' as test_account_2;
SELECT 'teacher_wang / admin123 = 任课老师账号（基础菜单）' as test_account_3;