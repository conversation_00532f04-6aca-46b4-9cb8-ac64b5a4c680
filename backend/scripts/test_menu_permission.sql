-- 测试菜单权限检查
-- 验证 class_teacher_2301 是否能访问学生管理菜单

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
    user_id_var VARCHAR(100);
    user_identity VARCHAR(200);
    permission_check BOOLEAN;
BEGIN
    -- 获取 gzxxzz 租户ID
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 获取 class_teacher_2301 用户ID
    SELECT id::text INTO user_id_var FROM public.users WHERE username = 'class_teacher_2301';
    
    -- 构建用户身份标识
    user_identity := 'user:' || user_id_var;
    
    RAISE NOTICE '=== 菜单权限测试 ===';
    RAISE NOTICE '用户身份: %', user_identity;
    RAISE NOTICE '租户ID: %', tenant_id_var;
    
    -- 检查学生管理菜单所需权限
    RAISE NOTICE '学生管理菜单所需权限: student:read, grade:read, grade:write, exam:read';
    
    -- 1. 检查 student:read 权限
    SELECT EXISTS(
        SELECT 1 FROM public.casbin_policies 
        WHERE tenant_id = tenant_id_var 
          AND ptype = 'p' 
          AND v0 = 'role:class_teacher'
          AND v2 = 'student:read'
          AND v3 = 'read'
          AND v4 = 'allow'
    ) INTO permission_check;
    
    IF permission_check THEN
        RAISE NOTICE '✅ student:read 权限检查通过';
    ELSE
        RAISE NOTICE '❌ student:read 权限检查失败';
    END IF;
    
    -- 2. 检查 grade:read 权限
    SELECT EXISTS(
        SELECT 1 FROM public.casbin_policies 
        WHERE tenant_id = tenant_id_var 
          AND ptype = 'p' 
          AND v0 = 'role:class_teacher'
          AND v2 = 'grade:read'
          AND v3 = 'read'
          AND v4 = 'allow'
    ) INTO permission_check;
    
    IF permission_check THEN
        RAISE NOTICE '✅ grade:read 权限检查通过';
    ELSE
        RAISE NOTICE '❌ grade:read 权限检查失败';
    END IF;
    
    -- 3. 检查 grade:write 权限
    SELECT EXISTS(
        SELECT 1 FROM public.casbin_policies 
        WHERE tenant_id = tenant_id_var 
          AND ptype = 'p' 
          AND v0 = 'role:class_teacher'
          AND v2 = 'grade:write'
          AND v3 = 'write'
          AND v4 = 'allow'
    ) INTO permission_check;
    
    IF permission_check THEN
        RAISE NOTICE '✅ grade:write 权限检查通过';
    ELSE
        RAISE NOTICE '❌ grade:write 权限检查失败';
    END IF;
    
    -- 4. 检查 exam:read 权限
    SELECT EXISTS(
        SELECT 1 FROM public.casbin_policies 
        WHERE tenant_id = tenant_id_var 
          AND ptype = 'p' 
          AND v0 = 'role:class_teacher'
          AND v2 = 'exam:read'
          AND v3 = 'read'
          AND v4 = 'allow'
    ) INTO permission_check;
    
    IF permission_check THEN
        RAISE NOTICE '✅ exam:read 权限检查通过';
    ELSE
        RAISE NOTICE '❌ exam:read 权限检查失败';
    END IF;
    
    -- 5. 检查用户角色绑定
    SELECT EXISTS(
        SELECT 1 FROM public.casbin_policies 
        WHERE tenant_id = tenant_id_var 
          AND ptype = 'g' 
          AND v0 = user_identity
          AND v1 = 'role:class_teacher'
    ) INTO permission_check;
    
    IF permission_check THEN
        RAISE NOTICE '✅ 用户角色绑定检查通过';
    ELSE
        RAISE NOTICE '❌ 用户角色绑定检查失败';
    END IF;
    
    RAISE NOTICE '=== 菜单权限测试完成 ===';
END $$;

-- 显示完整的权限检查结果
SELECT '=== 完整权限检查结果 ===' as section;

-- 显示用户角色绑定
SELECT 
    '用户角色绑定' as 检查项,
    v0 as 用户,
    v1 as 角色,
    v2 as 租户
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'g' 
  AND v0 = 'user:66666666-6666-6666-6666-666666666666';

-- 显示所需权限检查
SELECT 
    '所需权限检查' as 检查项,
    v0 as 角色,
    v2 as 对象,
    v3 as 动作,
    v4 as 效果
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p' 
  AND v0 = 'role:class_teacher'
  AND v2 IN ('student:read', 'grade:read', 'grade:write', 'exam:read')
ORDER BY v2, v3; 