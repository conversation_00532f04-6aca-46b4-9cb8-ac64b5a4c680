-- 修复班主任权限问题 - 添加缺失的 student:read 权限
-- 解决 class_teacher_2301 无法访问行政班列表页面的问题

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
    added_count INT;
BEGIN
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 为 class_teacher 角色添加 student:read,read 权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
    VALUES ('p', 'role:class_teacher', tenant_id_var, 'student:read', 'read', 'allow', '', tenant_id_var, NOW(), NOW())
    ON CONFLICT DO NOTHING;
    
    GET DIAGNOSTICS added_count = ROW_COUNT;
    RAISE NOTICE 'Added % student:read permission for class_teacher role', added_count;
    
    -- 为 class_teacher 角色添加 grade:read,read 权限（可能也需要）
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
    VALUES ('p', 'role:class_teacher', tenant_id_var, 'grade:read', 'read', 'allow', '', tenant_id_var, NOW(), NOW())
    ON CONFLICT DO NOTHING;
    
    GET DIAGNOSTICS added_count = ROW_COUNT;
    RAISE NOTICE 'Added % grade:read permission for class_teacher role', added_count;
    
    -- 为 class_teacher 角色添加 exam:read,read 权限（可能也需要）
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
    VALUES ('p', 'role:class_teacher', tenant_id_var, 'exam:read', 'read', 'allow', '', tenant_id_var, NOW(), NOW())
    ON CONFLICT DO NOTHING;
    
    GET DIAGNOSTICS added_count = ROW_COUNT;
    RAISE NOTICE 'Added % exam:read permission for class_teacher role', added_count;
    
    RAISE NOTICE '班主任权限已修复！现在应该可以访问行政班列表页面了。';
END $$;

-- 验证权限修复结果
SELECT '=== 班主任权限验证 ===' as section;

SELECT 
    'Class Teacher Permissions:' as type,
    v0 as role,
    v1 as domain,
    v2 as object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
  AND v0 = 'role:class_teacher'
ORDER BY v2, v3;

SELECT '=== 权限检查说明 ===' as section;
SELECT '班主任现在应该有以下权限:' as info;
SELECT '- student:class,manage (班级学生管理)' as permission;
SELECT '- student:read,read (学生信息读取)' as permission;
SELECT '- grade:class,read (班级成绩查看)' as permission;
SELECT '- grade:read,read (成绩信息读取)' as permission;
SELECT '- exam:class,read (班级考试查看)' as permission;
SELECT '- exam:read,read (考试信息读取)' as permission;
SELECT '- menu:access (基础菜单访问)' as permission;
SELECT '- menu:class_management (班级管理菜单)' as permission;
SELECT '- menu:student_management (学生管理菜单)' as permission;
SELECT '- menu:grade_management (成绩管理菜单)' as permission;