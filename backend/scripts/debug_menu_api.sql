-- API调试脚本 - 模拟 /api/v1/permissions/menus 的逻辑
-- 用于诊断为什么返回空结果

-- ================================================================
-- 1. 模拟用户身份提取过程
-- ================================================================

SELECT '=== 1. 模拟用户身份提取过程 ===' as section;

-- 模拟从JWT token中提取的用户信息
SELECT 
    'Step 1.1: 用户基本信息' as step,
    u.id as user_id,
    u.username,
    u.is_active
FROM public.users u 
WHERE u.username = 'principal_gzxxzz';

-- 模拟从用户身份获取角色信息
SELECT 
    'Step 1.2: 用户角色信息' as step,
    ui.user_id,
    r.code as role_code,
    r.name as role_name,
    ui.target_type,
    ui.subject
FROM tenant_gzxxzz.user_identities ui
JOIN public.roles r ON ui.role_id = r.id
WHERE ui.user_id = '11111111-1111-1111-1111-111111111111';

-- ================================================================
-- 2. 模拟租户ID确定逻辑
-- ================================================================

SELECT '=== 2. 模拟租户ID确定逻辑 ===' as section;

-- API通常会尝试从以下方式获取tenant_id:
-- 1. 查询参数 
-- 2. 用户的第一个角色的租户ID
-- 3. 默认值

SELECT 
    'Step 2.1: 租户信息' as step,
    id as tenant_uuid,
    id::text as tenant_id_string,
    name as tenant_name,
    schema_name
FROM public.tenants 
WHERE schema_name = 'tenant_gzxxzz';

-- ================================================================
-- 3. 模拟用户身份标识构建
-- ================================================================

SELECT '=== 3. 模拟用户身份标识构建 ===' as section;

-- API会构建类似这样的用户身份标识：
-- format: "{}:{}:tenant:{}", user_id, role_type, tenant_id
SELECT 
    'Step 3.1: 用户身份标识' as step,
    '11111111-1111-1111-1111-111111111111' as user_id,
    'principal' as first_role_type,
    '7ff2e111-1ca4-4402-bc02-af69c1a7283c' as tenant_id,
    '11111111-1111-1111-1111-111111111111:principal:tenant:7ff2e111-1ca4-4402-bc02-af69c1a7283c' as constructed_identity;

-- ================================================================
-- 4. 模拟菜单数据获取
-- ================================================================

SELECT '=== 4. 模拟菜单数据获取 ===' as section;

-- API会从 menu_service.get_menu_tree() 获取菜单
SELECT 
    'Step 4.1: 菜单数据检查' as step,
    COUNT(*) as total_menus,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_menus,
    COUNT(CASE WHEN required_permissions IS NOT NULL THEN 1 END) as menus_with_permissions
FROM public.menu_permissions;

-- 显示几个示例菜单
SELECT 
    'Step 4.2: 菜单示例' as step,
    menu_id,
    name,
    required_permissions,
    is_active
FROM public.menu_permissions 
WHERE is_active = true
ORDER BY sort_order
LIMIT 5;

-- ================================================================
-- 5. 模拟权限过滤逻辑
-- ================================================================

SELECT '=== 5. 模拟权限过滤逻辑 ===' as section;

-- 对于每个菜单，API会检查用户是否有required_permissions
-- 例如菜单要求 'menu:access'，检查casbin中是否有对应策略

-- 检查校长的权限策略
SELECT 
    'Step 5.1: 校长权限策略' as step,
    v0 as subject,
    v1 as domain,
    v2 as object,
    v3 as action
FROM public.casbin_policies 
WHERE tenant_id = '7ff2e111-1ca4-4402-bc02-af69c1a7283c'
  AND ptype = 'p'
  AND v0 = 'role:principal'
  AND v2 LIKE 'menu:%';

-- 检查用户-角色映射
SELECT 
    'Step 5.2: 用户-角色映射' as step,
    v0 as user_subject,
    v1 as role_object,
    v2 as domain
FROM public.casbin_policies 
WHERE tenant_id = '7ff2e111-1ca4-4402-bc02-af69c1a7283c'
  AND ptype = 'g'
  AND v0 = 'user:11111111-1111-1111-1111-111111111111';

-- ================================================================
-- 6. 模拟Casbin enforce检查
-- ================================================================

SELECT '=== 6. 模拟Casbin enforce检查 ===' as section;

-- Casbin enforce 调用格式: enforcer.enforce((user_identity, tenant_id, object, action))
-- 例如: enforce(("user:11111111...", "tenant_id", "menu:access", "allow"))

-- 验证权限链路完整性
SELECT 
    'Step 6.1: 权限链路验证' as step,
    CASE 
        WHEN EXISTS (
            -- 检查用户-角色映射
            SELECT 1 FROM public.casbin_policies 
            WHERE tenant_id = '7ff2e111-1ca4-4402-bc02-af69c1a7283c'
              AND ptype = 'g'
              AND v0 = 'user:11111111-1111-1111-1111-111111111111'
              AND v1 = 'role:principal'
        ) AND EXISTS (
            -- 检查角色权限策略
            SELECT 1 FROM public.casbin_policies 
            WHERE tenant_id = '7ff2e111-1ca4-4402-bc02-af69c1a7283c'
              AND ptype = 'p'
              AND v0 = 'role:principal'
              AND v2 = 'menu:access'
              AND v3 = 'allow'
        ) THEN '✓ 权限链路完整'
        ELSE '✗ 权限链路不完整'
    END as chain_status;

-- ================================================================
-- 7. 诊断可能的问题
-- ================================================================

SELECT '=== 7. 诊断可能的问题 ===' as section;

-- 检查可能的问题点
SELECT 
    'Step 7.1: 潜在问题检查' as step,
    CASE 
        WHEN (SELECT COUNT(*) FROM public.menu_permissions WHERE is_active = true) = 0 
        THEN '问题: 没有激活的菜单'
        WHEN NOT EXISTS (
            SELECT 1 FROM tenant_gzxxzz.user_identities 
            WHERE user_id = '11111111-1111-1111-1111-111111111111'
        ) THEN '问题: 用户没有身份绑定'
        WHEN NOT EXISTS (
            SELECT 1 FROM public.casbin_policies 
            WHERE tenant_id = '7ff2e111-1ca4-4402-bc02-af69c1a7283c'
              AND ptype = 'g'
              AND v0 = 'user:11111111-1111-1111-1111-111111111111'
        ) THEN '问题: 用户没有角色映射'
        WHEN NOT EXISTS (
            SELECT 1 FROM public.casbin_policies 
            WHERE tenant_id = '7ff2e111-1ca4-4402-bc02-af69c1a7283c'
              AND ptype = 'p'
              AND v2 = 'menu:access'
        ) THEN '问题: 没有菜单访问权限策略'
        ELSE '配置正常，问题可能在后端逻辑'
    END as diagnosis,
    
    -- 提供具体的调试建议
    '建议检查: 1)用户认证中间件 2)租户ID解析 3)用户身份构建 4)Casbin权限验证' as debug_suggestions;

-- ================================================================
-- 8. 生成调试命令
-- ================================================================

SELECT '=== 8. 后端调试建议 ===' as section;

SELECT 
    'Debug Command' as type,
    'curl -H "Authorization: Bearer YOUR_JWT_TOKEN" -H "Content-Type: application/json" http://localhost:8080/api/v1/permissions/menus?tenant_id=7ff2e111-1ca4-4402-bc02-af69c1a7283c' as command,
    '使用实际JWT token测试API' as description;