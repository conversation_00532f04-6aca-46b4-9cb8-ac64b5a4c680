-- 权限调试 - 验证 gzxxzz 租户校长账号的完整权限链路
-- 这个脚本将逐步验证每个环节是否正确

-- ================================================================
-- 1. 基础数据验证
-- ================================================================

SELECT '=== 1. 校长基础数据验证 ===' as section;

-- 校长用户信息
SELECT 
    'Step 1.1: 用户信息' as step,
    id,
    username,
    is_active,
    (salt = 'default_salt') as correct_password_setup
FROM public.users 
WHERE username = 'principal_gzxxzz';

-- 租户信息
SELECT 
    'Step 1.2: 租户信息' as step,
    id,
    name,
    schema_name,
    status
FROM public.tenants 
WHERE schema_name = 'tenant_gzxxzz';

-- ================================================================
-- 2. 身份绑定验证
-- ================================================================

SELECT '=== 2. 身份绑定验证 ===' as section;

-- 用户身份绑定
SELECT 
    'Step 2.1: 身份绑定' as step,
    ui.id,
    ui.user_id,
    r.code as role_code,
    r.name as role_name,
    ui.target_type,
    ui.target_id
FROM tenant_gzxxzz.user_identities ui
JOIN public.roles r ON ui.role_id = r.id
WHERE ui.user_id = '11111111-1111-1111-1111-111111111111';

-- ================================================================
-- 3. Casbin 策略验证
-- ================================================================

SELECT '=== 3. Casbin 策略验证 ===' as section;

-- 用户-角色映射 (g 策略)
SELECT 
    'Step 3.1: 用户-角色映射' as step,
    ptype,
    v0 as user_subject,
    v1 as role_object,
    v2 as domain
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'g'
  AND v0 = 'user:11111111-1111-1111-1111-111111111111';

-- 角色权限策略 (p 策略)
SELECT 
    'Step 3.2: 角色权限策略' as step,
    ptype,
    v0 as role_subject,
    v1 as domain,
    v2 as resource_object,
    v3 as action
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p'
  AND v0 = 'role:principal';

-- ================================================================
-- 4. 菜单权限配置验证
-- ================================================================

SELECT '=== 4. 菜单权限配置验证 ===' as section;

-- 菜单权限配置
SELECT 
    'Step 4.1: 菜单权限配置' as step,
    menu_id,
    name,
    path,
    required_permissions,
    is_active
FROM public.menu_permissions 
WHERE is_active = true
ORDER BY sort_order
LIMIT 10;

-- ================================================================
-- 5. 权限匹配分析
-- ================================================================

SELECT '=== 5. 权限匹配分析 ===' as section;

-- 验证权限匹配逻辑
-- 菜单要求：['menu:access']
-- Casbin 策略：role:principal -> menu:access (allow)
-- 用户映射：user:11111111... -> role:principal

SELECT 
    'Step 5.1: 权限匹配检查' as step,
    'menu:access' as required_permission,
    '校长应该具有此权限' as expected_result;

-- ================================================================
-- 6. 潜在问题分析
-- ================================================================

SELECT '=== 6. 潜在问题分析 ===' as section;

-- 检查是否有重复或冲突的策略
SELECT 
    'Step 6.1: 策略一致性检查' as step,
    ptype,
    COUNT(*) as count,
    CASE 
        WHEN COUNT(*) > 1 AND ptype = 'g' THEN '可能的重复用户-角色映射'
        WHEN COUNT(*) > 10 AND ptype = 'p' THEN '可能的权限策略过多'
        ELSE '正常'
    END as analysis
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
GROUP BY ptype;

-- ================================================================
-- 7. 权限系统诊断建议
-- ================================================================

SELECT '=== 7. 权限系统诊断建议 ===' as section;

SELECT 
    'Step 7.1: 系统诊断' as step,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM public.casbin_policies cp
            WHERE cp.tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
              AND cp.ptype = 'g'
              AND cp.v0 = 'user:11111111-1111-1111-1111-111111111111'
              AND cp.v1 = 'role:principal'
        ) AND EXISTS (
            SELECT 1 FROM public.casbin_policies cp
            WHERE cp.tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
              AND cp.ptype = 'p'
              AND cp.v0 = 'role:principal'
              AND cp.v2 = 'menu:access'
              AND cp.v3 = 'allow'
        ) THEN '✓ 权限链路完整，问题可能在后端权限验证逻辑中'
        ELSE '✗ 权限链路不完整，需要修复 Casbin 策略'
    END as diagnosis,
    '建议检查后端 Casbin enforcer 的参数顺序和权限验证逻辑' as recommendation;