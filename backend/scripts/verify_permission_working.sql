-- 验证班主任权限是否真的工作
-- 模拟权限检查过程

DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
    user_id_var VARCHAR(100);
    user_identity VARCHAR(200);
    permission_check BOOLEAN;
BEGIN
    -- 获取 gzxxzz 租户ID
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 获取 class_teacher_2301 用户ID
    SELECT id::text INTO user_id_var FROM public.users WHERE username = 'class_teacher_2301';
    
    -- 构建用户身份标识
    user_identity := 'user:' || user_id_var;
    
    RAISE NOTICE '=== 权限验证测试 ===';
    RAISE NOTICE '用户身份: %', user_identity;
    RAISE NOTICE '租户ID: %', tenant_id_var;
    
    -- 1. 检查学生读取权限
    SELECT EXISTS(
        SELECT 1 FROM public.casbin_policies 
        WHERE tenant_id = tenant_id_var 
          AND ptype = 'p' 
          AND v0 = 'role:class_teacher'
          AND v2 = 'student:read'
          AND v3 = 'read'
          AND v4 = 'allow'
    ) INTO permission_check;
    
    IF permission_check THEN
        RAISE NOTICE '✅ student:read 权限检查通过';
    ELSE
        RAISE NOTICE '❌ student:read 权限检查失败';
    END IF;
    
    -- 2. 检查菜单访问权限
    SELECT EXISTS(
        SELECT 1 FROM public.casbin_policies 
        WHERE tenant_id = tenant_id_var 
          AND ptype = 'p' 
          AND v0 = 'role:class_teacher'
          AND v2 = 'menu:student_management'
          AND v3 = 'access'
          AND v4 = 'allow'
    ) INTO permission_check;
    
    IF permission_check THEN
        RAISE NOTICE '✅ menu:student_management 权限检查通过';
    ELSE
        RAISE NOTICE '❌ menu:student_management 权限检查失败';
    END IF;
    
    -- 3. 检查用户角色绑定
    SELECT EXISTS(
        SELECT 1 FROM public.casbin_policies 
        WHERE tenant_id = tenant_id_var 
          AND ptype = 'g' 
          AND v0 = user_identity
          AND v1 = 'role:class_teacher'
    ) INTO permission_check;
    
    IF permission_check THEN
        RAISE NOTICE '✅ 用户角色绑定检查通过';
    ELSE
        RAISE NOTICE '❌ 用户角色绑定检查失败';
    END IF;
    
    RAISE NOTICE '=== 权限验证完成 ===';
END $$;

-- 显示完整的权限检查路径
SELECT '=== 完整权限检查路径 ===' as section;

-- 1. 用户 -> 角色绑定
SELECT 
    '用户角色绑定' as step,
    v0 as user_identity,
    v1 as role,
    v2 as domain
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'g' 
  AND v0 = 'user:66666666-6666-6666-6666-666666666666';

-- 2. 角色 -> 权限绑定
SELECT 
    '角色权限绑定' as step,
    v0 as role,
    v2 as object,
    v3 as action,
    v4 as effect
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p' 
  AND v0 = 'role:class_teacher'
  AND (v2 = 'student:read' OR v2 = 'menu:student_management')
ORDER BY v2;

-- 3. 菜单配置
SELECT 
    '菜单配置' as step,
    menu_id,
    name,
    required_permissions
FROM public.menu_permissions 
WHERE menu_id = 'student_management'; 