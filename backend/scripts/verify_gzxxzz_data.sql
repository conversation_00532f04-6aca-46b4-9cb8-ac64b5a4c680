-- gzxxzz 租户测试数据验证脚本
-- 验证所有测试数据是否正确创建

-- ================================================================
-- 1. 基础数据统计验证
-- ================================================================

SELECT '=== 基础数据统计 ===' AS section;

SELECT 
    '用户总数' as 项目,
    COUNT(*) as 数量 
FROM public.users 
WHERE phone_number LIKE '1380000%'
UNION ALL
SELECT 
    '教师总数',
    COUNT(*) 
FROM tenant_gzxxzz.teachers
UNION ALL
SELECT 
    '学生总数',
    COUNT(*) 
FROM tenant_gzxxzz.students
UNION ALL
SELECT 
    '管理班级数',
    COUNT(*) 
FROM tenant_gzxxzz.administrative_classes
UNION ALL
SELECT 
    '教学班级数',
    COUNT(*) 
FROM tenant_gzxxzz.teaching_classes
UNION ALL
SELECT 
    '学科组数',
    COUNT(*) 
FROM tenant_gzxxzz.subject_groups;

-- ================================================================
-- 2. 角色分布验证
-- ================================================================

SELECT '=== 角色分布统计 ===' AS section;

SELECT 
    r.name as 角色名称,
    r.level as 权限级别,
    COUNT(ui.*) as 人数
FROM public.roles r
LEFT JOIN tenant_gzxxzz.user_identities ui ON r.id = ui.role_id
WHERE r.is_system = TRUE
GROUP BY r.id, r.name, r.level
HAVING COUNT(ui.*) > 0
ORDER BY r.level;

-- ================================================================
-- 3. 权限策略验证
-- ================================================================

SELECT '=== 权限策略统计 ===' AS section;

SELECT 
    ptype as 策略类型,
    CASE ptype
        WHEN 'g' THEN '用户角色映射'
        WHEN 'p' THEN '角色权限策略'
        WHEN 'g2' THEN '角色继承策略'
    END as 说明,
    COUNT(*) as 数量
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
GROUP BY ptype
ORDER BY ptype;

-- ================================================================
-- 4. 班级学生分布验证
-- ================================================================

SELECT '=== 班级学生分布 ===' AS section;

SELECT 
    ac.class_name as 班级名称,
    ac.code as 班级代码,
    COUNT(s.*) as 学生人数,
    t.teacher_name as 班主任
FROM tenant_gzxxzz.administrative_classes ac
LEFT JOIN tenant_gzxxzz.students s ON ac.id = s.administrative_class_id
LEFT JOIN tenant_gzxxzz.teachers t ON ac.teacher_id = t.id
GROUP BY ac.id, ac.class_name, ac.code, t.teacher_name
ORDER BY ac.code;

-- ================================================================
-- 5. 教学班级分布验证
-- ================================================================

SELECT '=== 教学班级分布 ===' AS section;

SELECT 
    tc.class_name as 教学班名称,
    tc.code as 班级代码,
    sg.group_name as 学科组,
    t.teacher_name as 任课老师
FROM tenant_gzxxzz.teaching_classes tc
LEFT JOIN tenant_gzxxzz.subject_groups sg ON tc.subject_group_id = sg.id
LEFT JOIN tenant_gzxxzz.teachers t ON tc.teacher_id = t.id
ORDER BY sg.group_name, tc.code;

-- ================================================================
-- 6. 用户登录信息验证
-- ================================================================

SELECT '=== 测试账号信息 (前15个) ===' AS section;

SELECT 
    u.username as 用户名,
    u.phone_number as 手机号,
    CASE 
        WHEN u.username LIKE '%principal%' THEN '校长'
        WHEN u.username LIKE '%director%' THEN '教导主任' 
        WHEN u.username LIKE '%leader%' THEN '学科组长'
        WHEN u.username LIKE '%class_teacher%' THEN '班主任'
        WHEN u.username LIKE '%teacher_%' THEN '任课老师'
        WHEN u.username LIKE '%student_%' THEN '学生'
        ELSE '其他'
    END as 角色类型,
    '密码与用户名相同' as 登录密码
FROM public.users u
WHERE u.phone_number LIKE '1380000%'
ORDER BY u.phone_number
LIMIT 15;

-- ================================================================
-- 7. 菜单权限验证
-- ================================================================

SELECT '=== 菜单权限配置 ===' AS section;

SELECT 
    COUNT(*) as 菜单总数,
    COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as 主菜单数,
    COUNT(CASE WHEN parent_id IS NOT NULL THEN 1 END) as 子菜单数
FROM public.menu_permissions;

-- ================================================================
-- 8. 数据完整性检查
-- ================================================================

SELECT '=== 数据完整性检查 ===' AS section;

-- 检查教师是否都有用户账号
SELECT 
    '教师用户绑定' as 检查项,
    CASE 
        WHEN COUNT(*) = (SELECT COUNT(*) FROM tenant_gzxxzz.teachers) 
        THEN '✓ 通过'
        ELSE '✗ 失败'
    END as 检查结果,
    COUNT(*) || '/' || (SELECT COUNT(*) FROM tenant_gzxxzz.teachers) as 绑定情况
FROM tenant_gzxxzz.teachers t
JOIN public.users u ON t.user_id = u.id

UNION ALL

-- 检查学生是否都有班级
SELECT 
    '学生班级绑定',
    CASE 
        WHEN COUNT(*) = (SELECT COUNT(*) FROM tenant_gzxxzz.students) 
        THEN '✓ 通过'
        ELSE '✗ 失败'
    END,
    COUNT(*) || '/' || (SELECT COUNT(*) FROM tenant_gzxxzz.students)
FROM tenant_gzxxzz.students s
WHERE s.administrative_class_id IS NOT NULL

UNION ALL

-- 检查身份绑定是否完整
SELECT 
    '身份绑定完整性',
    CASE 
        WHEN COUNT(*) >= 120 THEN '✓ 通过'
        ELSE '✗ 失败'
    END,
    COUNT(*)::text || ' 个身份绑定'
FROM tenant_gzxxzz.user_identities;

-- ================================================================
-- 9. 权限测试示例
-- ================================================================

SELECT '=== 权限测试示例 ===' AS section;

-- 显示校长的权限
SELECT 
    '校长权限示例' as 说明,
    COUNT(*) as 权限策略数
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p' 
  AND v0 = 'role:principal';

-- 显示任课老师的权限  
SELECT 
    '任课老师权限示例' as 说明,
    COUNT(*) as 权限策略数
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p' 
  AND v0 = 'role:teacher';

-- ================================================================
-- 10. 总结报告
-- ================================================================

SELECT '=== 测试数据创建完成总结 ===' AS section;

SELECT 
    '✓ gzxxzz 租户测试数据创建成功！' as 状态,
    '包含完整的教师权限测试数据' as 描述,
    '可以开始测试教师菜单权限功能' as 建议;