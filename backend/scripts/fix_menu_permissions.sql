-- 修复菜单权限配置，使其与 Casbin 策略格式匹配
-- 将菜单权限从具体权限改为通用匹配权限

-- 更新菜单权限配置，简化权限要求
UPDATE public.menu_permissions SET required_permissions = ARRAY['menu:access'] WHERE required_permissions IS NOT NULL;

-- 为校长创建更简单的权限验证
DO $$ 
DECLARE
    tenant_id_var VARCHAR(100);
BEGIN
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = 'tenant_gzxxzz';
    
    -- 删除现有的校长菜单权限策略，重新创建更简单的
    DELETE FROM public.casbin_policies 
    WHERE tenant_id = tenant_id_var AND ptype = 'p' AND v0 = 'role:principal' AND v2 LIKE 'menu:%';
    
    -- 添加校长菜单访问权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:principal', tenant_id_var, 'menu:access', 'allow', tenant_id_var);
    
    -- 为其他角色也添加基础菜单权限
    INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, tenant_id) VALUES 
    ('p', 'role:academic_director', tenant_id_var, 'menu:access', 'allow', tenant_id_var),
    ('p', 'role:subject_leader', tenant_id_var, 'menu:access', 'allow', tenant_id_var),
    ('p', 'role:class_teacher', tenant_id_var, 'menu:access', 'allow', tenant_id_var),
    ('p', 'role:teacher', tenant_id_var, 'menu:access', 'allow', tenant_id_var),
    ('p', 'role:student', tenant_id_var, 'menu:access', 'allow', tenant_id_var)
    ON CONFLICT DO NOTHING;
    
    RAISE NOTICE '菜单权限修复完成！';
END $$;

-- 验证修复结果
SELECT 
    '=== 校长权限验证 ===' as section,
    COUNT(*) as menu_access_policies
FROM public.casbin_policies 
WHERE tenant_id = (SELECT id::text FROM public.tenants WHERE schema_name = 'tenant_gzxxzz')
  AND ptype = 'p' 
  AND v0 = 'role:principal'
  AND v2 = 'menu:access';

-- 检查菜单权限更新结果
SELECT 
    '=== 菜单权限更新结果 ===' as section,
    COUNT(*) as total_menus,
    COUNT(CASE WHEN required_permissions = ARRAY['menu:access'] THEN 1 END) as updated_menus
FROM public.menu_permissions;