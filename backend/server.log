warning: unused imports: `Environment` and `WarmupStrategy`
 --> src/config/mod.rs:6:39
  |
6 | pub use schema_config::{SchemaConfig, WarmupStrategy, Environment, get_schema_config};
  |                                       ^^^^^^^^^^^^^^  ^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `str::FromStr`
 --> src/controller/administrative_classes/administrative_classes_controller.rs:3:5
  |
3 |     str::FromStr,
  |     ^^^^^^^^^^^^

warning: unused import: `HeaderValue`
 --> src/controller/administrative_classes/administrative_classes_controller.rs:8:23
  |
8 |     http::{HeaderMap, HeaderValue},
  |                       ^^^^^^^^^^^

warning: unused import: `axum::response::IntoResponse`
  --> src/controller/auth/auth_controller.rs:11:5
   |
11 | use axum::response::IntoResponse;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::middleware::auth_middleware::AuthExtractor`
  --> src/controller/auth/auth_controller.rs:13:5
   |
13 | use crate::middleware::auth_middleware::AuthExtractor;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `ApiResponse` and `responses`
  --> src/controller/auth/auth_controller.rs:14:34
   |
14 | use crate::utils::api_response::{responses, ApiResponse};
   |                                  ^^^^^^^^^  ^^^^^^^^^^^

warning: unused import: `crate::model::base::PageResult`
 --> src/controller/education_stage/education_stage_controller.rs:1:5
  |
1 | use crate::model::base::PageResult;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::model::education_stage::EducationStage`
 --> src/controller/education_stage/education_stage_controller.rs:2:5
  |
2 | use crate::model::education_stage::EducationStage;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `EducationStageStatistics`, `EducationStageSummary`, and `EducationStageVO`
 --> src/controller/education_stage/education_stage_controller.rs:4:61
  |
4 |     CreateEducationStageRequest, EducationStageQueryParams, EducationStageStatistics,
  |                                                             ^^^^^^^^^^^^^^^^^^^^^^^^
5 |     EducationStageSummary, EducationStageVO, UpdateEducationStageRequest,
  |     ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^

warning: unused import: `education_stage_controller::*`
 --> src/controller/education_stage/mod.rs:3:9
  |
3 | pub use education_stage_controller::*;
  |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `CheckCodeResponse`, `GradeLevelStatistics`, `GradeLevelSummary`, `GradeLevelVO`, and `GradeLevel`
 --> src/controller/grade/grade_controller.rs:2:5
  |
2 |     GradeLevel, GradeLevelVO, GradeLevelSummary, CreateGradeLevel, UpdateGradeLevel, 
  |     ^^^^^^^^^^  ^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^
3 |     GradeLevelQueryParams, GradeLevelStatistics, CheckCodeResponse
  |                            ^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^

warning: unused import: `crate::model::base::PageResult`
 --> src/controller/grade/grade_controller.rs:5:5
  |
5 | use crate::model::base::PageResult;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `put`
  --> src/controller/grade/grade_controller.rs:11:26
   |
11 |     routing::{get, post, put, patch},
   |                          ^^^

warning: unused import: `get`
  --> src/controller/homework/homework_controller.rs:10:15
   |
10 |     routing::{get, post},
   |               ^^^

warning: unused import: `Extension`
 --> src/controller/menu/menu_controller.rs:8:5
  |
8 |     Extension,
  |     ^^^^^^^^^

warning: unused import: `warn`
  --> src/controller/menu/menu_controller.rs:12:21
   |
12 | use tracing::{info, warn, error, debug};
   |                     ^^^^

warning: unused import: `AuthContext`
  --> src/controller/menu/menu_controller.rs:15:42
   |
15 | use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
   |                                          ^^^^^^^^^^^

warning: unused import: `PaginatedApiResponse`
  --> src/controller/menu/menu_controller.rs:16:47
   |
16 | use crate::utils::api_response::{ApiResponse, PaginatedApiResponse};
   |                                               ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
  --> src/controller/menu/menu_controller.rs:17:5
   |
17 | use crate::utils::error::AppError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `menu_controller::MenuController`
 --> src/controller/menu/mod.rs:3:9
  |
3 | pub use menu_controller::MenuController;
  |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Extension`
 --> src/controller/permission/menu_controller.rs:8:5
  |
8 |     Extension,
  |     ^^^^^^^^^

warning: unused import: `UserContextExtractor`
  --> src/controller/permission/menu_controller.rs:19:48
   |
19 | use crate::middleware::permission_middleware::{UserContextExtractor};
   |                                                ^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `AuthContext` and `ErrorResponse`
  --> src/controller/permission/casbin_policy_controller.rs:11:50
   |
11 |     middleware::auth_middleware::{AuthExtractor, AuthContext},
   |                                                  ^^^^^^^^^^^
...
18 |     utils::api_response::{ApiResponse, ErrorResponse, PaginatedApiResponse},
   |                                        ^^^^^^^^^^^^^

warning: unused imports: `delete` and `post`
   --> src/controller/permission/casbin_policy_controller.rs:559:30
    |
559 |     use axum::routing::{get, post, delete};
    |                              ^^^^  ^^^^^^

warning: unused import: `std::sync::Arc`
 --> src/controller/permission/permission_template_controller.rs:1:5
  |
1 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `AuthContext`
  --> src/controller/permission/permission_template_controller.rs:14:42
   |
14 | use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
   |                                          ^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
  --> src/controller/permission/permission_template_controller.rs:16:5
   |
16 | use crate::utils::error::AppError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `Path`
 --> src/controller/permission/permission_test_controller.rs:3:29
  |
3 |     extract::{State, Query, Path},
  |                             ^^^^

warning: unused imports: `MenuPermission` and `PermissionRequest`
  --> src/controller/permission/permission_test_controller.rs:16:5
   |
16 |     PermissionRequest,
   |     ^^^^^^^^^^^^^^^^^
17 |     MenuPermission,
   |     ^^^^^^^^^^^^^^

warning: unused import: `AuthContext`
  --> src/controller/permission/permission_test_controller.rs:19:42
   |
19 | use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
   |                                          ^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
  --> src/controller/permission/permission_test_controller.rs:21:5
   |
21 | use crate::utils::error::AppError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> src/controller/permission/permission_audit_controller.rs:1:5
  |
1 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `Path`
 --> src/controller/permission/permission_audit_controller.rs:3:29
  |
3 |     extract::{State, Query, Path},
  |                             ^^^^

warning: unused import: `AuthContext`
  --> src/controller/permission/permission_audit_controller.rs:14:42
   |
14 | use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
   |                                          ^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
  --> src/controller/permission/permission_audit_controller.rs:16:5
   |
16 | use crate::utils::error::AppError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `menu_controller::MenuPermissionController`
 --> src/controller/permission/mod.rs:7:9
  |
7 | pub use menu_controller::MenuPermissionController;
  |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `casbin_policy_controller::CasbinPolicyController`
 --> src/controller/permission/mod.rs:8:9
  |
8 | pub use casbin_policy_controller::CasbinPolicyController;
  |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `permission_template_controller::PermissionTemplateController`
 --> src/controller/permission/mod.rs:9:9
  |
9 | pub use permission_template_controller::PermissionTemplateController;
  |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `permission_test_controller::PermissionTestController`
  --> src/controller/permission/mod.rs:10:9
   |
10 | pub use permission_test_controller::PermissionTestController;
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `permission_audit_controller::PermissionAuditController`
  --> src/controller/permission/mod.rs:11:9
   |
11 | pub use permission_audit_controller::PermissionAuditController;
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `subject_controller::*`
 --> src/controller/subject/mod.rs:2:9
  |
2 | pub use subject_controller::*;
  |         ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::middleware::tenant_middleware::TenantExtractor`
 --> src/controller/teacher/teacher_controller.rs:2:5
  |
2 | use crate::middleware::tenant_middleware::TenantExtractor;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `TeacherQueryParams`
 --> src/controller/teacher/teacher_controller.rs:3:50
  |
3 | use crate::model::teacher::{CreateTeacherParams, TeacherQueryParams, UpdateTeacherParams};
  |                                                  ^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::service::teacher::teacher_service::TeacherService`
 --> src/controller/teacher/teacher_controller.rs:5:5
  |
5 | use crate::service::teacher::teacher_service::TeacherService;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
 --> src/controller/teacher/teacher_controller.rs:7:5
  |
7 | use crate::utils::error::AppError;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `Query`, `get`, and `patch`
  --> src/controller/teacher/teacher_controller.rs:12:21
   |
12 |     extract::{Path, Query, State},
   |                     ^^^^^
13 |     response::Json,
14 |     routing::{get, patch},
   |               ^^^  ^^^^^

warning: unused import: `crate::service::auth::auth_service::AuthService`
  --> src/controller/user/identity_controller.rs:15:5
   |
15 | use crate::service::auth::auth_service::AuthService;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> src/controller/grading/grading_controller.rs:1:5
  |
1 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused imports: `RecognizeRequest` and `SheetRequest`
 --> src/controller/grading/grading_controller.rs:7:587
  |
7 | ...rScanResponse, ProcessAIGradingRequest, RecognizeRequest, ScanExceptionResponse, SheetRequest, SubmitCardBlockGradingRequest, SubmitGr...
  |                                            ^^^^^^^^^^^^^^^^                         ^^^^^^^^^^^^

warning: unused import: `responses`
 --> src/controller/grading/grading_controller.rs:9:34
  |
9 | use crate::utils::api_response::{responses, ApiResponse};
  |                                  ^^^^^^^^^

warning: unused import: `delete`
 --> src/controller/question/question_type_controller.rs:6:21
  |
6 | use axum::routing::{delete, get, post, put};
  |                     ^^^^^^

warning: unused import: `crate::service::subject::SubjectService`
 --> src/controller/question/question_type_controller.rs:9:5
  |
9 | use crate::service::subject::SubjectService;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `HashSet`
 --> src/controller/subject_groups/subject_groups_controller.rs:1:33
  |
1 | use std::collections::{HashMap, HashSet};
  |                                 ^^^^^^^

warning: unused import: `get`
 --> src/controller/subject_groups/subject_groups_controller.rs:5:15
  |
5 |     routing::{get, post},
  |               ^^^

warning: unused import: `uuid`
 --> src/controller/subject_groups/subject_groups_controller.rs:8:12
  |
8 | use uuid::{uuid, Uuid};
  |            ^^^^

warning: unused imports: `delete` and `put`
 --> src/controller/teaching_aids/textbook_paper_controller.rs:9:21
  |
9 | use axum::routing::{delete, get, post, put};
  |                     ^^^^^^             ^^^

warning: unused import: `str::FromStr`
 --> src/controller/teaching_classes/teaching_classes_controller.rs:3:5
  |
3 |     str::FromStr,
  |     ^^^^^^^^^^^^

warning: unused imports: `delete` and `put`
 --> src/controller/paper/paper_controller.rs:7:21
  |
7 | use axum::routing::{delete, get, post, put};
  |                     ^^^^^^             ^^^

warning: unused import: `crate::model::PageParams`
 --> src/model/education_stage/education_stage.rs:5:5
  |
5 | use crate::model::PageParams;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::model::PageParams`
 --> src/model/grade/grade.rs:5:5
  |
5 | use crate::model::PageParams;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `question::*`
 --> src/model/question/mod.rs:4:9
  |
4 | pub use question::*;
  |         ^^^^^^^^^^^

warning: unused import: `crate::model::PageParams`
 --> src/model/subject/subject.rs:5:5
  |
5 | use crate::model::PageParams;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `NaiveDateTime`
 --> src/model/tenant/tenant.rs:1:24
  |
1 | use chrono::{DateTime, NaiveDateTime, Utc};
  |                        ^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/model/user/auth.rs:5:5
  |
5 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::model::UserIdentitySelectVO`
 --> src/model/user/auth.rs:6:5
  |
6 | use crate::model::UserIdentitySelectVO;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `grade::*`
  --> src/model/mod.rs:43:9
   |
43 | pub use grade::*;
   |         ^^^^^^^^

warning: unused import: `education_stage::*`
  --> src/model/mod.rs:46:9
   |
46 | pub use education_stage::*;
   |         ^^^^^^^^^^^^^^^^^^

warning: unused import: `api_response::ApiResponse`
 --> src/service/homework/homework_service.rs:7:13
  |
7 |     utils::{api_response::ApiResponse, schema::connect_with_schema},
  |             ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `tower::builder`
  --> src/service/homework/homework_service.rs:10:5
   |
10 | use tower::builder;
   |     ^^^^^^^^^^^^^^

warning: unused import: `anyhow::Result`
 --> src/service/tenant/tenant_data_service.rs:1:5
  |
1 | use anyhow::Result;
  |     ^^^^^^^^^^^^^^

warning: unused import: `PgPool`
 --> src/service/tenant/tenant_data_service.rs:2:12
  |
2 | use sqlx::{PgPool, Row};
  |            ^^^^^^

warning: unused import: `std::sync::Arc`
 --> src/service/tenant/tenant_data_service.rs:6:5
  |
6 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused imports: `HashMap` and `HashSet`
 --> src/service/tenant/member.rs:1:24
  |
1 | use std::collections::{HashMap, HashSet};
  |                        ^^^^^^^  ^^^^^^^

warning: unused import: `uuid::Uuid`
 --> src/service/tenant/member.rs:7:5
  |
7 | use uuid::Uuid;
  |     ^^^^^^^^^^

warning: unused import: `anyhow::Result`
  --> src/service/tenant/mod.rs:10:9
   |
10 |     use anyhow::Result;
   |         ^^^^^^^^^^^^^^

warning: unused import: `anyhow::Result`
  --> src/service/tenant/mod.rs:23:9
   |
23 |     use anyhow::Result;
   |         ^^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
 --> src/service/user/identity_service.rs:3:5
  |
3 | use crate::utils::error::AppError;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `warn`
 --> src/service/user/parent_service.rs:4:21
  |
4 | use tracing::{info, warn};
  |                     ^^^^

warning: unused import: `crate::model::auth::User`
 --> src/service/user/user_service.rs:1:5
  |
1 | use crate::model::auth::User;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `sqlx::postgres::PgRow`
 --> src/service/user/user_service.rs:7:5
  |
7 | use sqlx::postgres::PgRow;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `tower::builder`
 --> src/service/administrative_classes/administrative_classes_service.rs:4:5
  |
4 | use tower::builder;
  |     ^^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
 --> src/service/auth/auth_service.rs:5:5
  |
5 | use crate::utils::error::AppError;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `AnyhowResultExt`, `AppResult`, `SqlxResultExt`, and `errors`
 --> src/service/auth/auth_service.rs:6:35
  |
6 | use crate::utils::error_handler::{SqlxResultExt, AnyhowResultExt, errors, AppResult};
  |                                   ^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^  ^^^^^^  ^^^^^^^^^

warning: unused import: `DateTime`
 --> src/service/auth/auth_service.rs:7:14
  |
7 | use chrono::{DateTime, Duration, Utc};
  |              ^^^^^^^^

warning: unused import: `error`
  --> src/service/auth/auth_service.rs:13:15
   |
13 | use tracing::{error, info, warn};
   |               ^^^^^

warning: unused imports: `SubjectQueryParams` and `SubjectVO`
 --> src/service/education_stage/education_stage_service.rs:8:32
  |
8 | use crate::model::{PageParams, SubjectQueryParams, SubjectVO};
  |                                ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `CheckCodeResponse`
 --> src/service/grade/grade_service.rs:5:72
  |
5 |     GradeLevelQueryParams, GradeLevelStatistics, GradeLevelUsageStats, CheckCodeResponse
  |                                                                        ^^^^^^^^^^^^^^^^^

warning: unused imports: `SubjectQueryParams` and `SubjectVO`
  --> src/service/grade/grade_service.rs:11:32
   |
11 | use crate::model::{PageParams, SubjectQueryParams, SubjectVO};
   |                                ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `axum::Json`
 --> src/service/grading/grading_service.rs:5:5
  |
5 | use axum::Json;
  |     ^^^^^^^^^^

warning: unused imports: `HttpClientError` and `get_request`
  --> src/service/grading/grading_service.rs:10:33
   |
10 | use crate::utils::http_client::{get_request, post_json, HttpClientError,parse_json_response};
   |                                 ^^^^^^^^^^^             ^^^^^^^^^^^^^^^

warning: unused import: `crate::utils::api_response::ApiResponse`
  --> src/service/grading/grading_service.rs:14:5
   |
14 | use crate::utils::api_response::ApiResponse;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `auth::IdentitySwitchLog` and `middleware::auth_middleware::AuthContext`
 --> src/service/homework_students/homework_students_service.rs:7:5
  |
7 |     middleware::auth_middleware::AuthContext,
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
8 |     model::{
9 |         auth::IdentitySwitchLog,
  |         ^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `SubjectSummary`
 --> src/service/question/question_type_service.rs:2:44
  |
2 | use crate::model::{PageParams, PageResult, SubjectSummary};
  |                                            ^^^^^^^^^^^^^^

warning: unused import: `anyhow`
 --> src/service/permission/role_sync_service.rs:2:22
  |
2 | use anyhow::{Result, anyhow};
  |                      ^^^^^^

warning: unused import: `warn`
 --> src/service/permission/role_sync_service.rs:5:21
  |
5 | use tracing::{info, warn, error, debug};
  |                     ^^^^

warning: unused import: `crate::model::role::permission::Permission`
  --> src/service/permission/role_sync_service.rs:15:5
   |
15 | use crate::model::role::permission::Permission;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
  --> src/service/permission/role_sync_service.rs:16:5
   |
16 | use crate::utils::error::AppError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `DataScope`
  --> src/service/permission/mod.rs:12:5
   |
12 |     DataScope,
   |     ^^^^^^^^^

warning: unused imports: `RolePermissionSyncService`, `SyncConfig`, and `SyncResult`
  --> src/service/permission/mod.rs:16:5
   |
16 |     RolePermissionSyncService,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^
17 |     SyncConfig,
   |     ^^^^^^^^^^
18 |     SyncResult,
   |     ^^^^^^^^^^

warning: unused imports: `CasbinRule` and `PostgresAdapter`
  --> src/service/permission/mod.rs:22:5
   |
22 |     PostgresAdapter,
   |     ^^^^^^^^^^^^^^^
23 |     CasbinRule,
   |     ^^^^^^^^^^

warning: unused import: `SystemRoles`
 --> src/service/role/role_service.rs:5:17
  |
5 |     Permission, SystemRoles
  |                 ^^^^^^^^^^^

warning: unused import: `warn`
  --> src/service/role/role_service.rs:12:28
   |
12 | use tracing::{info, error, warn};
   |                            ^^^^

warning: unused import: `role_service::*`
 --> src/service/role/mod.rs:4:9
  |
4 | pub use role_service::*;
  |         ^^^^^^^^^^^^^^^

warning: unused import: `tracing::debug`
  --> src/service/storage/minio_storage.rs:14:5
   |
14 | use tracing::debug;
   |     ^^^^^^^^^^^^^^

warning: unused import: `tracing::log::info`
  --> src/service/storage/minio_storage.rs:15:5
   |
15 | use tracing::log::info;
   |     ^^^^^^^^^^^^^^^^^^

warning: unused imports: `StorageResult` and `UploadOptions as StorageUploadOptions`
 --> src/service/storage/mod.rs:4:43
  |
4 | pub use storage_service::{StorageService, StorageResult, FileInfo, UploadOptions as StorageUploadOptions};
  |                                           ^^^^^^^^^^^^^            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `HashSet`
 --> src/service/student/student_service.rs:1:33
  |
1 | use std::collections::{HashMap, HashSet};
  |                                 ^^^^^^^

warning: unused imports: `HashMap` and `HashSet`
 --> src/service/subject_groups/subject_groups_service.rs:1:24
  |
1 | use std::collections::{HashMap, HashSet};
  |                        ^^^^^^^  ^^^^^^^

warning: unused imports: `AdministrativeClassesStatistics`, `AdministrativeClasses`, and `CreateAdministrativeClassesParams`
 --> src/service/subject_groups/subject_groups_service.rs:8:9
  |
8 |         AdministrativeClasses, AdministrativeClassesStatistics, CreateAdministrativeClassesParams,
  |         ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `Postgres` and `Transaction`
  --> src/service/teaching_aids/teaching_aids_service.rs:10:20
   |
10 | use sqlx::{PgPool, Postgres, Transaction};
   |                    ^^^^^^^^  ^^^^^^^^^^^

warning: unused imports: `Json` and `http::StatusCode`
 --> src/utils/error_handler.rs:6:5
  |
6 |     http::StatusCode,
  |     ^^^^^^^^^^^^^^^^
7 |     response::{IntoResponse, Response},
8 |     Json,
  |     ^^^^

warning: unused import: `serde_json::json`
  --> src/utils/error_handler.rs:10:5
   |
10 | use serde_json::json;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `Claims`
 --> src/middleware/auth_middleware.rs:4:52
  |
4 | pub(crate) use crate::utils::jwt::{validate_token, Claims};
  |                                                    ^^^^^^

warning: unused import: `HeaderMap`
 --> src/middleware/tenant_middleware.rs:5:12
  |
5 |     http::{HeaderMap, StatusCode},
  |            ^^^^^^^^^

warning: unused import: `Path`
 --> src/middleware/permission_middleware.rs:4:31
  |
4 |     extract::{Request, State, Path},
  |                               ^^^^

warning: unused import: `AuthExtractor`
  --> src/middleware/permission_middleware.rs:19:55
   |
19 | use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
   |                                                       ^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
  --> src/middleware/permission_middleware.rs:20:5
   |
20 | use crate::utils::error::AppError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: use of deprecated struct `model::base::ApiResponse`: Please use utils::api_response::ApiResponse instead
  --> src/model/base.rs:49:12
   |
49 | pub struct ApiResponse<T> {
   |            ^^^^^^^^^^^
   |
   = note: `#[warn(deprecated)]` on by default

warning: use of deprecated struct `model::base::ApiResponse`: Please use utils::api_response::ApiResponse instead
  --> src/model/base.rs:49:12
   |
49 | pub struct ApiResponse<T> {
   |            ^^^^^^^^^^^

warning: unused variable: `context`
   --> src/controller/administrative_classes/administrative_classes_controller.rs:192:19
    |
192 |     AuthExtractor(context): AuthExtractor,
    |                   ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_context`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `token`
   --> src/controller/auth/auth_controller.rs:174:9
    |
174 |     let token = extract_bearer_token(&headers).ok_or_else(|| {
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_token`

warning: unused variable: `context`
  --> src/controller/homework/homework_controller.rs:24:19
   |
24 |     AuthExtractor(context): AuthExtractor,
   |                   ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_context`

warning: unused variable: `header_map`
  --> src/controller/homework/homework_controller.rs:25:5
   |
25 |     header_map: HeaderMap,
   |     ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_header_map`

warning: unused variable: `header_map`
  --> src/controller/homework/homework_controller.rs:39:5
   |
39 |     header_map: HeaderMap,
   |     ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_header_map`

warning: unused variable: `header_map`
  --> src/controller/homework/homework_controller.rs:54:5
   |
54 |     header_map: HeaderMap,
   |     ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_header_map`

warning: unused variable: `header_map`
  --> src/controller/homework/homework_controller.rs:69:5
   |
69 |     header_map: HeaderMap,
   |     ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_header_map`

warning: unused variable: `previous_data_scopes`
   --> src/controller/permission/permission_template_controller.rs:573:45
    |
573 |                 Some((previous_permissions, previous_data_scopes)) => {
    |                                             ^^^^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_previous_data_scopes`

warning: unused variable: `pool`
   --> src/controller/permission/permission_test_controller.rs:661:29
    |
661 |     async fn get_user_roles(pool: &PgPool, user_id: &Uuid, tenant_id: &str) -> Result<Vec<String>, StatusCode> {
    |                             ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `user_id`
   --> src/controller/permission/permission_test_controller.rs:661:44
    |
661 |     async fn get_user_roles(pool: &PgPool, user_id: &Uuid, tenant_id: &str) -> Result<Vec<String>, StatusCode> {
    |                                            ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `tenant_id`
   --> src/controller/permission/permission_test_controller.rs:661:60
    |
661 |     async fn get_user_roles(pool: &PgPool, user_id: &Uuid, tenant_id: &str) -> Result<Vec<String>, StatusCode> {
    |                                                            ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `tenant_id`
   --> src/controller/permission/permission_test_controller.rs:688:46
    |
688 |     async fn get_all_menu_ids(pool: &PgPool, tenant_id: &str) -> Result<Vec<String>, StatusCode> {
    |                                              ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `casbin_service`
   --> src/controller/permission/permission_test_controller.rs:701:9
    |
701 |         casbin_service: &Arc<MultiTenantCasbinService>,
    |         ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_casbin_service`

warning: unused variable: `user_identity`
   --> src/controller/permission/permission_test_controller.rs:702:9
    |
702 |         user_identity: &str,
    |         ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_identity`

warning: unused variable: `tenant_id`
   --> src/controller/permission/permission_test_controller.rs:703:9
    |
703 |         tenant_id: &str,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `pool`
   --> src/controller/permission/permission_audit_controller.rs:732:37
    |
732 |     async fn get_operations_by_menu(pool: &PgPool, time_condition: &str) -> Result<Vec<MenuStats>, StatusCode> {
    |                                     ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `time_condition`
   --> src/controller/permission/permission_audit_controller.rs:732:52
    |
732 |     async fn get_operations_by_menu(pool: &PgPool, time_condition: &str) -> Result<Vec<MenuStats>, StatusCode> {
    |                                                    ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_time_condition`

warning: unused variable: `pool`
   --> src/controller/permission/permission_audit_controller.rs:737:39
    |
737 |     async fn get_operations_by_tenant(pool: &PgPool, time_condition: &str) -> Result<Vec<TenantStats>, StatusCode> {
    |                                       ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `time_condition`
   --> src/controller/permission/permission_audit_controller.rs:737:54
    |
737 |     async fn get_operations_by_tenant(pool: &PgPool, time_condition: &str) -> Result<Vec<TenantStats>, StatusCode> {
    |                                                      ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_time_condition`

warning: unused variable: `pool`
   --> src/controller/permission/permission_audit_controller.rs:742:32
    |
742 |     async fn get_timeline_data(pool: &PgPool, time_condition: &str, time_range: &str) -> Result<Vec<TimelinePoint>, StatusCode> {
    |                                ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `time_condition`
   --> src/controller/permission/permission_audit_controller.rs:742:47
    |
742 |     async fn get_timeline_data(pool: &PgPool, time_condition: &str, time_range: &str) -> Result<Vec<TimelinePoint>, StatusCode> {
    |                                               ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_time_condition`

warning: unused variable: `time_range`
   --> src/controller/permission/permission_audit_controller.rs:742:69
    |
742 |     async fn get_timeline_data(pool: &PgPool, time_condition: &str, time_range: &str) -> Result<Vec<TimelinePoint>, StatusCode> {
    |                                                                     ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_time_range`

warning: unused variable: `pool`
   --> src/controller/permission/permission_audit_controller.rs:747:28
    |
747 |     async fn analyze_risks(pool: &PgPool, time_condition: &str) -> Result<RiskAnalysis, StatusCode> {
    |                            ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `time_condition`
   --> src/controller/permission/permission_audit_controller.rs:747:43
    |
747 |     async fn analyze_risks(pool: &PgPool, time_condition: &str) -> Result<RiskAnalysis, StatusCode> {
    |                                           ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_time_condition`

warning: unused variable: `pool`
   --> src/controller/permission/permission_audit_controller.rs:757:34
    |
757 |     async fn get_recent_activity(pool: &PgPool) -> Result<Vec<RecentActivity>, StatusCode> {
    |                                  ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `pool`
   --> src/controller/permission/permission_audit_controller.rs:762:32
    |
762 |     async fn get_top_operators(pool: &PgPool) -> Result<Vec<TopOperator>, StatusCode> {
    |                                ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `pool`
   --> src/controller/permission/permission_audit_controller.rs:767:38
    |
767 |     async fn get_frequent_operations(pool: &PgPool) -> Result<Vec<FrequentOperation>, StatusCode> {
    |                                      ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `pool`
   --> src/controller/permission/permission_audit_controller.rs:772:38
    |
772 |     async fn calculate_system_health(pool: &PgPool) -> Result<SystemHealthIndicator, StatusCode> {
    |                                      ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `pool`
   --> src/controller/permission/permission_audit_controller.rs:781:36
    |
781 |     async fn generate_audit_alerts(pool: &PgPool) -> Result<Vec<AuditAlert>, StatusCode> {
    |                                    ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `pool`
   --> src/controller/permission/permission_audit_controller.rs:786:34
    |
786 |     async fn get_audit_log_by_id(pool: &PgPool, audit_log_id: &Uuid) -> Result<AuditLogResponse, StatusCode> {
    |                                  ^^^^ help: if this is intentional, prefix it with an underscore: `_pool`

warning: unused variable: `audit_log_id`
   --> src/controller/permission/permission_audit_controller.rs:786:49
    |
786 |     async fn get_audit_log_by_id(pool: &PgPool, audit_log_id: &Uuid) -> Result<AuditLogResponse, StatusCode> {
    |                                                 ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_audit_log_id`

warning: unused variable: `tx`
   --> src/controller/permission/permission_audit_controller.rs:792:9
    |
792 |         tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
    |         ^^ help: if this is intentional, prefix it with an underscore: `_tx`

warning: unused variable: `audit_log`
   --> src/controller/permission/permission_audit_controller.rs:793:9
    |
793 |         audit_log: &AuditLogResponse,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_audit_log`

warning: unused variable: `rollback_data`
   --> src/controller/permission/permission_audit_controller.rs:794:9
    |
794 |         rollback_data: &serde_json::Value,
    |         ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_rollback_data`

warning: unused variable: `admin_context`
   --> src/controller/teacher/teacher_controller.rs:148:19
    |
148 |     AuthExtractor(admin_context): AuthExtractor,
    |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `error_msg`
  --> src/controller/tenant/tenant_controller.rs:79:31
   |
79 |             let (status_code, error_msg, user_msg) = if e.to_string().contains("not found") {
   |                               ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_error_msg`

warning: unused variable: `error_msg`
   --> src/controller/tenant/tenant_controller.rs:111:31
    |
111 |             let (status_code, error_msg, user_msg) = if e.to_string().contains("not found") {
    |                               ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_error_msg`

warning: unused variable: `error_msg`
   --> src/controller/tenant/tenant_controller.rs:142:31
    |
142 |             let (status_code, error_msg, user_msg) = if e.to_string().contains("not found") {
    |                               ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_error_msg`

warning: unused variable: `admin_context`
  --> src/controller/user/user_controller.rs:74:19
   |
74 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
  --> src/controller/user/user_controller.rs:91:19
   |
91 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `field_name`
  --> src/controller/grading/grading_controller.rs:57:21
   |
57 |                 let field_name = field.name().unwrap_or_default().to_string();
   |                     ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_field_name`

warning: unused variable: `content_type`
  --> src/controller/grading/grading_controller.rs:62:25
   |
62 |                     let content_type = field.content_type().unwrap_or("application/octet-stream").to_string();
   |                         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_content_type`

warning: unused variable: `scan_pages`
   --> src/controller/grading/grading_controller.rs:115:9
    |
115 |     let scan_pages = state.grading_service.save_upload_paper_scans(params.tenant_name,&request).await
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_scan_pages`

warning: unused variable: `admin_context`
  --> src/controller/subject_groups/subject_groups_controller.rs:31:19
   |
31 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
  --> src/controller/subject_groups/subject_groups_controller.rs:47:19
   |
47 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
  --> src/controller/subject_groups/subject_groups_controller.rs:63:19
   |
63 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
  --> src/controller/subject_groups/subject_groups_controller.rs:74:19
   |
74 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: variable `metadata` is assigned to, but never used
  --> src/controller/teaching_aids/teaching_aids_controller.rs:93:13
   |
93 |     let mut metadata: Option<ImportTeachingAidRequest> = None;
   |             ^^^^^^^^
   |
   = note: consider using `_metadata` instead

warning: value assigned to `metadata` is never read
   --> src/controller/teaching_aids/teaching_aids_controller.rs:101:13
    |
101 |             metadata = Some(serde_json::from_slice(&data)?);
    |             ^^^^^^^^
    |
    = help: maybe it is overwritten before being read?
    = note: `#[warn(unused_assignments)]` on by default

warning: unused variable: `page_params`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:406:9
    |
406 |     let page_params = PageParams {
    |         ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_page_params`

warning: unused variable: `state`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:532:11
    |
532 |     State(state): State<Arc<TeachingAidsRouteState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:578:11
    |
578 |     State(state): State<Arc<TeachingAidsRouteState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:648:11
    |
648 |     State(state): State<Arc<TeachingAidsRouteState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:684:11
    |
684 |     State(state): State<Arc<TeachingAidsRouteState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:702:11
    |
702 |     State(state): State<Arc<TeachingAidsRouteState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `page_params`
  --> src/controller/teaching_aids/textbook_paper_controller.rs:29:9
   |
29 |     let page_params = PageParams {
   |         ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_page_params`

warning: unused variable: `admin_context`
   --> src/controller/teaching_classes/teaching_classes_controller.rs:182:19
    |
182 |     AuthExtractor(admin_context): AuthExtractor,
    |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
   --> src/controller/teaching_classes/teaching_classes_controller.rs:196:19
    |
196 |     AuthExtractor(admin_context): AuthExtractor,
    |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `page_params`
  --> src/controller/paper/paper_controller.rs:24:9
   |
24 |     let page_params = PageParams {
   |         ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_page_params`

warning: unused variable: `context`
  --> src/service/homework/homework_service.rs:80:9
   |
80 |         context: &AuthContext,
   |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_context`

warning: unused variable: `context`
   --> src/service/homework/homework_service.rs:105:9
    |
105 |         context: &AuthContext,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_context`

warning: unused variable: `context`
   --> src/service/homework/homework_service.rs:134:9
    |
134 |         context: &AuthContext,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_context`

warning: unused variable: `user_id`
   --> src/service/user/identity_service.rs:203:9
    |
203 |         user_id: Uuid,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `user_id`
   --> src/service/user/identity_service.rs:314:9
    |
314 |         user_id: Uuid,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `session_id`
   --> src/service/user/identity_service.rs:315:9
    |
315 |         session_id: Uuid,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_session_id`

warning: unused variable: `request`
   --> src/service/user/identity_service.rs:316:9
    |
316 |         request: SwitchIdentityRequest,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_request`

warning: unused variable: `ip_address`
   --> src/service/user/identity_service.rs:317:9
    |
317 |         ip_address: Option<std::net::IpAddr>,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_ip_address`

warning: unused variable: `user_agent`
   --> src/service/user/identity_service.rs:318:9
    |
318 |         user_agent: Option<String>,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_agent`

warning: unused variable: `identity_id`
   --> src/service/user/identity_service.rs:400:41
    |
400 |     pub async fn verify_identity(&self, identity_id: Uuid, verified_by: Uuid) -> AuthResult<()> {
    |                                         ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_identity_id`

warning: unused variable: `verified_by`
   --> src/service/user/identity_service.rs:400:60
    |
400 |     pub async fn verify_identity(&self, identity_id: Uuid, verified_by: Uuid) -> AuthResult<()> {
    |                                                            ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_verified_by`

warning: unused variable: `assigned_by`
   --> src/service/user/user_service.rs:177:9
    |
177 |         assigned_by: Uuid,
    |         ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_assigned_by`

warning: value assigned to `password_hash` is never read
   --> src/service/user/user_service.rs:476:17
    |
476 |         let mut password_hash = String::new();
    |                 ^^^^^^^^^^^^^
    |
    = help: maybe it is overwritten before being read?

warning: value assigned to `salt` is never read
   --> src/service/user/user_service.rs:477:17
    |
477 |         let mut salt = String::new();
    |                 ^^^^
    |
    = help: maybe it is overwritten before being read?

warning: unused variable: `user_id`
   --> src/service/administrative_classes/administrative_classes_service.rs:178:9
    |
178 |         user_id: &Uuid,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `tenant_id`
  --> src/service/classes/classes_service.rs:39:9
   |
39 |         tenant_id: String,
   |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `tenant_id`
  --> src/service/classes/classes_service.rs:55:9
   |
55 |         tenant_id: String,
   |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `params`
  --> src/service/classes/classes_service.rs:56:9
   |
56 |         params: PageAdministrativeClassesParams,
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_params`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:210:9
    |
210 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `params`
   --> src/service/classes/classes_service.rs:211:9
    |
211 |         params: PageTeachingClassesParams,
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_params`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:366:9
    |
366 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `name`
   --> src/service/classes/classes_service.rs:370:13
    |
370 |             name,
    |             ^^^^ help: try ignoring the field: `name: _`

warning: unused variable: `code`
   --> src/service/classes/classes_service.rs:371:13
    |
371 |             code,
    |             ^^^^ help: try ignoring the field: `code: _`

warning: unused variable: `grade_level_code`
   --> src/service/classes/classes_service.rs:372:13
    |
372 |             grade_level_code,
    |             ^^^^^^^^^^^^^^^^ help: try ignoring the field: `grade_level_code: _`

warning: unused variable: `subject_code`
   --> src/service/classes/classes_service.rs:373:13
    |
373 |             subject_code,
    |             ^^^^^^^^^^^^ help: try ignoring the field: `subject_code: _`

warning: unused variable: `class_type`
   --> src/service/classes/classes_service.rs:374:13
    |
374 |             class_type,
    |             ^^^^^^^^^^ help: try ignoring the field: `class_type: _`

warning: unused variable: `school_year`
   --> src/service/classes/classes_service.rs:375:13
    |
375 |             school_year,
    |             ^^^^^^^^^^^ help: try ignoring the field: `school_year: _`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:474:9
    |
474 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `id`
   --> src/service/classes/classes_service.rs:478:13
    |
478 |             id,
    |             ^^ help: try ignoring the field: `id: _`

warning: unused variable: `name`
   --> src/service/classes/classes_service.rs:479:13
    |
479 |             name,
    |             ^^^^ help: try ignoring the field: `name: _`

warning: unused variable: `code`
   --> src/service/classes/classes_service.rs:480:13
    |
480 |             code,
    |             ^^^^ help: try ignoring the field: `code: _`

warning: unused variable: `grade_level_code`
   --> src/service/classes/classes_service.rs:481:13
    |
481 |             grade_level_code,
    |             ^^^^^^^^^^^^^^^^ help: try ignoring the field: `grade_level_code: _`

warning: unused variable: `subject_code`
   --> src/service/classes/classes_service.rs:482:13
    |
482 |             subject_code,
    |             ^^^^^^^^^^^^ help: try ignoring the field: `subject_code: _`

warning: unused variable: `class_type`
   --> src/service/classes/classes_service.rs:483:13
    |
483 |             class_type,
    |             ^^^^^^^^^^ help: try ignoring the field: `class_type: _`

warning: unused variable: `school_year`
   --> src/service/classes/classes_service.rs:484:13
    |
484 |             school_year,
    |             ^^^^^^^^^^^ help: try ignoring the field: `school_year: _`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:583:9
    |
583 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `id`
   --> src/service/classes/classes_service.rs:586:30
    |
586 |         let IdStringParams { id } = params;
    |                              ^^ help: try ignoring the field: `id: _`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:596:9
    |
596 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `id`
   --> src/service/classes/classes_service.rs:599:40
    |
599 |         let PageClassesStudentParams { id, pagination } = params;
    |                                        ^^ help: try ignoring the field: `id: _`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:712:9
    |
712 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `params`
   --> src/service/classes/classes_service.rs:713:9
    |
713 |         params: AddTeacherParams,
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_params`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:724:9
    |
724 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `params`
   --> src/service/classes/classes_service.rs:725:9
    |
725 |         params: RemoveTeacherParams,
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_params`

warning: variable does not need to be mutable
  --> src/service/grade/grade_service.rs:88:13
   |
88 |         let mut query = "
   |             ----^^^^^
   |             |
   |             help: remove this `mut`
   |
   = note: `#[warn(unused_mut)]` on by default

warning: unused variable: `content_type`
  --> src/service/grading/grading_service.rs:55:25
   |
55 |                     let content_type = field.content_type().unwrap_or("application/octet-stream").to_string();
   |                         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_content_type`

warning: unused variable: `paper_scan`
   --> src/service/grading/grading_service.rs:159:13
    |
159 |         let paper_scan = sqlx::query_as::<_, PaperScan>(&query)
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_paper_scan`

warning: variable does not need to be mutable
   --> src/service/grading/grading_service.rs:140:13
    |
140 |         let mut request = request;
    |             ----^^^^^^^
    |             |
    |             help: remove this `mut`

warning: unused variable: `statistics`
   --> src/service/grading/grading_service.rs:774:13
    |
774 |         let statistics = query
    |             ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_statistics`

warning: unused variable: `has_exceptions`
   --> src/service/grading/grading_service.rs:822:13
    |
822 |         let has_exceptions = sqlx::query_scalar::<_, bool>(&query)
    |             ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_has_exceptions`

warning: unused variable: `query`
   --> src/service/grading/grading_service.rs:855:13
    |
855 |         let query = format!(
    |             ^^^^^ help: if this is intentional, prefix it with an underscore: `_query`

warning: unused variable: `result`
   --> src/service/question/question_type_service.rs:408:13
    |
408 |         let result = sqlx::query!(
    |             ^^^^^^ help: if this is intentional, prefix it with an underscore: `_result`

warning: unused variable: `new_menu_id`
   --> src/service/menu/menu_service.rs:189:13
    |
189 |         let new_menu_id = sqlx::query_scalar::<_, Uuid>(
    |             ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_new_menu_id`

warning: unused variable: `config`
   --> src/service/permission/role_sync_service.rs:504:79
    |
504 |     async fn create_tenant_role_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
    |                                                                               ^^^^^^ help: if this is intentional, prefix it with an underscore: `_config`

warning: unused variable: `tenant_id`
   --> src/service/teacher/teacher_service.rs:472:9
    |
472 |         tenant_id: &Option<Uuid>,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `auth_context`
   --> src/middleware/auth_middleware.rs:405:9
    |
405 |     let auth_context = request
    |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_auth_context`

warning: unused variable: `auth_context`
   --> src/middleware/auth_middleware.rs:422:9
    |
422 |     let auth_context = request
    |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_auth_context`

warning: unused variable: `config`
   --> src/middleware/permission_middleware.rs:285:5
    |
285 |     config: &PermissionConfig,
    |     ^^^^^^ help: if this is intentional, prefix it with an underscore: `_config`

warning: unused import: `Row`
 --> src/model/role/role.rs:2:21
  |
2 | use sqlx::{FromRow, Row};
  |                     ^^^

warning: unused import: `Row`
 --> src/service/tenant/tenant_data_service.rs:2:20
  |
2 | use sqlx::{PgPool, Row};
  |                    ^^^

warning: unused import: `Row`
 --> src/utils/db.rs:2:20
  |
2 | use sqlx::{PgPool, Row, Transaction, Postgres};
  |                    ^^^

warning: unused variable: `old_config`
   --> src/controller/permission/permission_audit_controller.rs:628:9
    |
628 |         old_config: &Option<serde_json::Value>,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_old_config`

warning: unused variable: `new_config`
   --> src/controller/permission/permission_audit_controller.rs:629:9
    |
629 |         new_config: &Option<serde_json::Value>,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_new_config`

warning: unused variable: `headers`
   --> src/controller/user/identity_controller.rs:174:36
    |
174 | fn extract_session_id_from_headers(headers: &HeaderMap) -> Result<Uuid, (StatusCode, Json<ErrorResponse>)> {
    |                                    ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_headers`

warning: value assigned to `has_where` is never read
  --> src/service/education_stage/education_stage_service.rs:70:21
   |
70 |                     has_where = true;
   |                     ^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

warning: value assigned to `where_count` is never read
  --> src/service/question/question_type_service.rs:63:21
   |
63 |                     where_count += 1;
   |                     ^^^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

warning: unused variable: `menu_id`
   --> src/service/menu/menu_service.rs:511:14
    |
511 |         for (menu_id, mut menu) in all_menus {
    |              ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_menu_id`

warning: variable does not need to be mutable
   --> src/service/menu/menu_service.rs:511:23
    |
511 |         for (menu_id, mut menu) in all_menus {
    |                       ----^^^^
    |                       |
    |                       help: remove this `mut`

warning: unused variable: `enforcer`
   --> src/service/permission/casbin_service.rs:456:13
    |
456 |         let enforcer = self.get_tenant_enforcer(&policy.domain).await?;
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_enforcer`

warning: unused variable: `enforcer`
   --> src/service/permission/casbin_service.rs:484:13
    |
484 |         let enforcer = self.get_tenant_enforcer(&policy.domain).await?;
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_enforcer`

warning: value assigned to `where_count` is never read
   --> src/service/subject/subject_service.rs:141:21
    |
141 |                     where_count+=1;
    |                     ^^^^^^^^^^^
    |
    = help: maybe it is overwritten before being read?

warning: value assigned to `where_count` is never read
   --> src/service/workflow/workflow_service.rs:250:17
    |
250 |                 where_count += 1;
    |                 ^^^^^^^^^^^
    |
    = help: maybe it is overwritten before being read?

warning: value assigned to `where_count` is never read
   --> src/service/workflow/workflow_service.rs:379:17
    |
379 |                 where_count += 1;
    |                 ^^^^^^^^^^^
    |
    = help: maybe it is overwritten before being read?

warning: unused variable: `resource`
   --> src/utils/error_handler.rs:133:30
    |
133 |     fn ok_or_not_found(self, resource: &str) -> AppResult<T>
    |                              ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_resource`

warning: unused variable: `secret`
  --> src/utils/jwt.rs:40:9
   |
40 |     let secret = env::var("JWT_SECRET").map_err(|_| "JWT_SECRET not set".to_string());
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_secret`

warning: unused variable: `secret`
  --> src/utils/jwt.rs:56:9
   |
56 |     let secret = env::var("JWT_SECRET").map_err(|_| "JWT_SECRET not set".to_string());
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_secret`

warning: unused variable: `tenant_id`
  --> src/middleware/auth_middleware.rs:74:38
   |
74 |     pub fn is_admin_in_tenant(&self, tenant_id: Option<Uuid>) -> bool {
   |                                      ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `config`
   --> src/main.rs:140:9
    |
140 |     let config = Arc::new(GlobalConfig::new(
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_config`

warning: type `SmsStats` is more private than the item `SmsService::get_delivery_stats`
   --> src/service/sms/sms_service.rs:196:5
    |
196 |     pub async fn get_delivery_stats(&self) -> HashMap<String, SmsStats> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method `SmsService::get_delivery_stats` is reachable at visibility `pub`
    |
note: but type `SmsStats` is only usable at visibility `pub(self)`
   --> src/service/sms/sms_service.rs:38:1
    |
38  | struct SmsStats {
    | ^^^^^^^^^^^^^^^
    = note: `#[warn(private_interfaces)]` on by default

warning: struct `CheckCodeResponse` is never constructed
   --> src/controller/education_stage/education_stage_controller.rs:269:8
    |
269 | struct CheckCodeResponse {
    |        ^^^^^^^^^^^^^^^^^
    |
    = note: `#[warn(dead_code)]` on by default

warning: function `create_exam_handler` is never used
  --> src/controller/exam/exam_controller.rs:12:14
   |
12 | pub async fn create_exam_handler(
   |              ^^^^^^^^^^^^^^^^^^^

warning: function `get_exam_handler` is never used
  --> src/controller/exam/exam_controller.rs:29:14
   |
29 | pub async fn get_exam_handler(
   |              ^^^^^^^^^^^^^^^^

warning: function `list_exams_handler` is never used
  --> src/controller/exam/exam_controller.rs:46:14
   |
46 | pub async fn list_exams_handler(
   |              ^^^^^^^^^^^^^^^^^^

warning: function `update_exam_handler` is never used
  --> src/controller/exam/exam_controller.rs:62:14
   |
62 | pub async fn update_exam_handler(
   |              ^^^^^^^^^^^^^^^^^^^

warning: function `delete_exam_handler` is never used
  --> src/controller/exam/exam_controller.rs:80:14
   |
80 | pub async fn delete_exam_handler(
   |              ^^^^^^^^^^^^^^^^^^^

warning: function `get_exam_statistics_handler` is never used
  --> src/controller/exam/exam_controller.rs:96:14
   |
96 | pub async fn get_exam_statistics_handler(
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: fields `operation`, `menu_ids`, `operation_data`, and `reason` are never read
  --> src/controller/menu/menu_controller.rs:67:9
   |
66 | pub struct BatchMenuOperationRequest {
   |            ------------------------- fields in this struct
67 |     pub operation: String, // "create", "update", "delete", "reorder", "status_change"
   |         ^^^^^^^^^
68 |     pub menu_ids: Vec<String>,
   |         ^^^^^^^^
69 |     pub operation_data: Option<serde_json::Value>,
   |         ^^^^^^^^^^^^^^
70 |     pub reason: Option<String>,
   |         ^^^^^^
   |
   = note: `BatchMenuOperationRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `menu_orders` and `reason` are never read
  --> src/controller/menu/menu_controller.rs:76:9
   |
75 | pub struct MenuReorderRequest {
   |            ------------------ fields in this struct
76 |     pub menu_orders: Vec<MenuOrderItem>,
   |         ^^^^^^^^^^^
77 |     pub reason: Option<String>,
   |         ^^^^^^
   |
   = note: `MenuReorderRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `menu_id`, `new_parent_id`, and `new_sort_order` are never read
  --> src/controller/menu/menu_controller.rs:82:9
   |
81 | pub struct MenuOrderItem {
   |            ------------- fields in this struct
82 |     pub menu_id: String,
   |         ^^^^^^^
83 |     pub new_parent_id: Option<String>,
   |         ^^^^^^^^^^^^^
84 |     pub new_sort_order: i32,
   |         ^^^^^^^^^^^^^^
   |
   = note: `MenuOrderItem` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
  --> src/controller/permission/casbin_policy_controller.rs:60:9
   |
59 | pub struct CreatePolicyRequest {
   |            ------------------- fields in this struct
60 |     pub tenant_id: String,
   |         ^^^^^^^^^
61 |     pub policy_type: String,  // "permission" or "role"
   |         ^^^^^^^^^^^
62 |     pub subject: String,
   |         ^^^^^^^
63 |     pub object: Option<String>,
   |         ^^^^^^
64 |     pub action: Option<String>,
   |         ^^^^^^
65 |     pub effect: Option<String>,
   |         ^^^^^^
66 |     pub role: Option<String>,  // for role policies
   |         ^^^^
   |
   = note: `CreatePolicyRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
  --> src/controller/permission/casbin_policy_controller.rs:72:9
   |
71 | pub struct DeletePolicyRequest {
   |            ------------------- fields in this struct
72 |     pub tenant_id: String,
   |         ^^^^^^^^^
73 |     pub policy_type: String,
   |         ^^^^^^^^^^^
74 |     pub subject: String,
   |         ^^^^^^^
75 |     pub object: Option<String>,
   |         ^^^^^^
76 |     pub action: Option<String>,
   |         ^^^^^^
77 |     pub effect: Option<String>,
   |         ^^^^^^
78 |     pub role: Option<String>,
   |         ^^^^
   |
   = note: `DeletePolicyRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `tenant_id`, `subject`, `object`, and `action` are never read
  --> src/controller/permission/casbin_policy_controller.rs:84:9
   |
83 | pub struct TestPermissionRequest {
   |            --------------------- fields in this struct
84 |     pub tenant_id: String,
   |         ^^^^^^^^^
85 |     pub subject: String,
   |         ^^^^^^^
86 |     pub object: String,
   |         ^^^^^^
87 |     pub action: String,
   |         ^^^^^^
   |
   = note: `TestPermissionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `tenant_id` and `policies` are never read
   --> src/controller/permission/casbin_policy_controller.rs:105:9
    |
104 | pub struct BatchPolicyRequest {
    |            ------------------ fields in this struct
105 |     pub tenant_id: String,
    |         ^^^^^^^^^
106 |     pub policies: Vec<CreatePolicyRequest>,
    |         ^^^^^^^^
    |
    = note: `BatchPolicyRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple associated functions are never used
   --> src/controller/permission/casbin_policy_controller.rs:232:18
    |
118 | impl CasbinPolicyController {
    | --------------------------- associated functions in this implementation
...
232 |     pub async fn create_policy(
    |                  ^^^^^^^^^^^^^
...
282 |     pub async fn delete_policy(
    |                  ^^^^^^^^^^^^^
...
332 |     pub async fn test_permission(
    |                  ^^^^^^^^^^^^^^^
...
429 |     pub async fn export_policies(
    |                  ^^^^^^^^^^^^^^^
...
476 |     pub async fn batch_create_policies(
    |                  ^^^^^^^^^^^^^^^^^^^^^
...
517 |     pub async fn clear_tenant_policies(
    |                  ^^^^^^^^^^^^^^^^^^^^^
...
537 |     pub async fn sync_tenant_policies(
    |                  ^^^^^^^^^^^^^^^^^^^^

warning: fields `override_existing` and `reason` are never read
  --> src/controller/permission/permission_template_controller.rs:53:9
   |
50 | pub struct ApplyTemplateRequest {
   |            -------------------- fields in this struct
...
53 |     pub override_existing: Option<bool>,
   |         ^^^^^^^^^^^^^^^^^
54 |     pub reason: Option<String>,
   |         ^^^^^^
   |
   = note: `ApplyTemplateRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `operation`, `template_ids`, and `reason` are never read
  --> src/controller/permission/permission_template_controller.rs:60:9
   |
59 | pub struct BatchTemplateRequest {
   |            -------------------- fields in this struct
60 |     pub operation: String, // "activate", "deactivate", "delete", "duplicate"
   |         ^^^^^^^^^
61 |     pub template_ids: Vec<Uuid>,
   |         ^^^^^^^^^^^^
62 |     pub reason: Option<String>,
   |         ^^^^^^
   |
   = note: `BatchTemplateRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: struct `PermissionTestController` is never constructed
  --> src/controller/permission/permission_test_controller.rs:25:12
   |
25 | pub struct PermissionTestController;
   |            ^^^^^^^^^^^^^^^^^^^^^^^^

warning: fields `tenant_id`, `role_types`, `menu_types`, `test_name`, `generate_report`, and `save_result` are never read
  --> src/controller/permission/permission_test_controller.rs:69:9
   |
68 | pub struct PermissionMatrixTestRequest {
   |            --------------------------- fields in this struct
69 |     pub tenant_id: String,
   |         ^^^^^^^^^
70 |     pub role_types: Option<Vec<String>>, // 如果为空，测试所有角色
   |         ^^^^^^^^^^
71 |     pub menu_types: Option<Vec<String>>, // 如果为空，测试所有菜单类型
   |         ^^^^^^^^^^
72 |     pub test_name: Option<String>,
   |         ^^^^^^^^^
73 |     pub generate_report: Option<bool>,
   |         ^^^^^^^^^^^^^^^
74 |     pub save_result: Option<bool>,
   |         ^^^^^^^^^^^
   |
   = note: `PermissionMatrixTestRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `tenant_id`, `scope`, `target_id`, `check_inheritance`, and `save_result` are never read
  --> src/controller/permission/permission_test_controller.rs:80:9
   |
79 | pub struct ConflictDetectionRequest {
   |            ------------------------ fields in this struct
80 |     pub tenant_id: Option<String>,
   |         ^^^^^^^^^
81 |     pub scope: String, // "global", "tenant", "role", "menu"
   |         ^^^^^
82 |     pub target_id: Option<String>, // 具体的租户ID、角色类型或菜单ID
   |         ^^^^^^^^^
83 |     pub check_inheritance: Option<bool>,
   |         ^^^^^^^^^^^^^^^^^
84 |     pub save_result: Option<bool>,
   |         ^^^^^^^^^^^
   |
   = note: `ConflictDetectionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
  --> src/controller/permission/permission_test_controller.rs:90:9
   |
89 | pub struct TestHistoryQueryParams {
   |            ---------------------- fields in this struct
90 |     pub test_type: Option<String>,
   |         ^^^^^^^^^
91 |     pub tester_id: Option<Uuid>,
   |         ^^^^^^^^^
92 |     pub tenant_id: Option<String>,
   |         ^^^^^^^^^
93 |     pub start_date: Option<String>,
   |         ^^^^^^^^^^
94 |     pub end_date: Option<String>,
   |         ^^^^^^^^
95 |     pub page: Option<u32>,
   |         ^^^^
96 |     pub page_size: Option<u32>,
   |         ^^^^^^^^^
   |
   = note: `TestHistoryQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple associated functions are never used
   --> src/controller/permission/permission_test_controller.rs:240:18
    |
238 | impl PermissionTestController {
    | ----------------------------- associated functions in this implementation
239 |     /// 单用户权限测试
240 |     pub async fn test_single_user(
    |                  ^^^^^^^^^^^^^^^^
...
339 |     pub async fn test_batch_users(
    |                  ^^^^^^^^^^^^^^^^
...
470 |     pub async fn test_role_simulation(
    |                  ^^^^^^^^^^^^^^^^^^^^
...
555 |     pub async fn get_test_history(
    |                  ^^^^^^^^^^^^^^^^
...
661 |     async fn get_user_roles(pool: &PgPool, user_id: &Uuid, tenant_id: &str) -> Result<Vec<String>, StatusCode> {
    |              ^^^^^^^^^^^^^^
...
667 |     async fn get_menu_info(pool: &PgPool, menu_id: &str) -> Result<MenuInfo, StatusCode> {
    |              ^^^^^^^^^^^^^
...
688 |     async fn get_all_menu_ids(pool: &PgPool, tenant_id: &str) -> Result<Vec<String>, StatusCode> {
    |              ^^^^^^^^^^^^^^^^
...
700 |     async fn test_menu_permission(
    |              ^^^^^^^^^^^^^^^^^^^^
...
734 |     async fn save_test_history(
    |              ^^^^^^^^^^^^^^^^^
...
780 |     fn generate_test_summary(user_results: &[UserTestResult]) -> TestSummary {
    |        ^^^^^^^^^^^^^^^^^^^^^

warning: struct `MenuInfo` is never constructed
   --> src/controller/permission/permission_test_controller.rs:807:8
    |
807 | struct MenuInfo {
    |        ^^^^^^^^

warning: struct `MenuTestResult` is never constructed
   --> src/controller/permission/permission_test_controller.rs:813:8
    |
813 | struct MenuTestResult {
    |        ^^^^^^^^^^^^^^

warning: function `create_router` is never used
   --> src/controller/permission/permission_test_controller.rs:823:8
    |
823 | pub fn create_router() -> Router<AppState> {
    |        ^^^^^^^^^^^^^

warning: struct `PermissionAuditController` is never constructed
  --> src/controller/permission/permission_audit_controller.rs:19:12
   |
19 | pub struct PermissionAuditController;
   |            ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: multiple fields are never read
  --> src/controller/permission/permission_audit_controller.rs:24:9
   |
23 | pub struct AuditQueryParams {
   |            ---------------- fields in this struct
24 |     pub operation: Option<String>,
   |         ^^^^^^^^^
25 |     pub menu_id: Option<String>,
   |         ^^^^^^^
26 |     pub operator_id: Option<Uuid>,
   |         ^^^^^^^^^^^
27 |     pub tenant_id: Option<String>,
   |         ^^^^^^^^^
28 |     pub start_date: Option<String>,
   |         ^^^^^^^^^^
29 |     pub end_date: Option<String>,
   |         ^^^^^^^^
30 |     pub search: Option<String>,
   |         ^^^^^^
31 |     pub sort_by: Option<String>,
   |         ^^^^^^^
32 |     pub sort_order: Option<String>,
   |         ^^^^^^^^^^
33 |     pub page: Option<u32>,
   |         ^^^^
34 |     pub page_size: Option<u32>,
   |         ^^^^^^^^^
   |
   = note: `AuditQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `tenant_id`, `time_range`, `group_by`, `start_date`, and `end_date` are never read
  --> src/controller/permission/permission_audit_controller.rs:40:9
   |
39 | pub struct AuditStatsQueryParams {
   |            --------------------- fields in this struct
40 |     pub tenant_id: Option<String>,
   |         ^^^^^^^^^
41 |     pub time_range: Option<String>, // "day", "week", "month", "year"
   |         ^^^^^^^^^^
42 |     pub group_by: Option<String>,   // "operation", "operator", "menu", "tenant"
   |         ^^^^^^^^
43 |     pub start_date: Option<String>,
   |         ^^^^^^^^^^
44 |     pub end_date: Option<String>,
   |         ^^^^^^^^
   |
   = note: `AuditStatsQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `audit_log_id`, `reason`, and `confirm_rollback` are never read
   --> src/controller/permission/permission_audit_controller.rs:207:9
    |
206 | pub struct RollbackRequest {
    |            --------------- fields in this struct
207 |     pub audit_log_id: Uuid,
    |         ^^^^^^^^^^^^
208 |     pub reason: String,
    |         ^^^^^^
209 |     pub confirm_rollback: bool,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `RollbackRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple associated functions are never used
   --> src/controller/permission/permission_audit_controller.rs:224:18
    |
222 | impl PermissionAuditController {
    | ------------------------------ associated functions in this implementation
223 |     /// 获取审计日志列表
224 |     pub async fn get_audit_logs(
    |                  ^^^^^^^^^^^^^^
...
384 |     pub async fn get_audit_statistics(
    |                  ^^^^^^^^^^^^^^^^^^^^
...
452 |     pub async fn get_audit_summary(
    |                  ^^^^^^^^^^^^^^^^^
...
506 |     pub async fn rollback_change(
    |                  ^^^^^^^^^^^^^^^
...
627 |     fn analyze_change_magnitude(
    |        ^^^^^^^^^^^^^^^^^^^^^^^^
...
647 |     fn build_time_condition(params: &AuditStatsQueryParams) -> String {
    |        ^^^^^^^^^^^^^^^^^^^^
...
660 |     async fn get_operations_by_type(pool: &PgPool, time_condition: &str) -> Result<Vec<OperationTypeStats>, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^^^^^^
...
694 |     async fn get_operations_by_operator(pool: &PgPool, time_condition: &str) -> Result<Vec<OperatorStats>, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
732 |     async fn get_operations_by_menu(pool: &PgPool, time_condition: &str) -> Result<Vec<MenuStats>, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^^^^^^
...
737 |     async fn get_operations_by_tenant(pool: &PgPool, time_condition: &str) -> Result<Vec<TenantStats>, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^
...
742 |     async fn get_timeline_data(pool: &PgPool, time_condition: &str, time_range: &str) -> Result<Vec<TimelinePoint>, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^
...
747 |     async fn analyze_risks(pool: &PgPool, time_condition: &str) -> Result<RiskAnalysis, StatusCode> {
    |              ^^^^^^^^^^^^^
...
757 |     async fn get_recent_activity(pool: &PgPool) -> Result<Vec<RecentActivity>, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^^^
...
762 |     async fn get_top_operators(pool: &PgPool) -> Result<Vec<TopOperator>, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^
...
767 |     async fn get_frequent_operations(pool: &PgPool) -> Result<Vec<FrequentOperation>, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^
...
772 |     async fn calculate_system_health(pool: &PgPool) -> Result<SystemHealthIndicator, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^
...
781 |     async fn generate_audit_alerts(pool: &PgPool) -> Result<Vec<AuditAlert>, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^^^^^
...
786 |     async fn get_audit_log_by_id(pool: &PgPool, audit_log_id: &Uuid) -> Result<AuditLogResponse, StatusCode> {
    |              ^^^^^^^^^^^^^^^^^^^
...
791 |     async fn execute_rollback(
    |              ^^^^^^^^^^^^^^^^

warning: function `create_router` is never used
   --> src/controller/permission/permission_audit_controller.rs:802:8
    |
802 | pub fn create_router() -> Router<PgPool> {
    |        ^^^^^^^^^^^^^

warning: struct `CheckCodeResponse` is never constructed
   --> src/controller/subject/subject_controller.rs:244:8
    |
244 | struct CheckCodeResponse {
    |        ^^^^^^^^^^^^^^^^^

warning: struct `UserIdentitiesResponse` is never constructed
   --> src/controller/user/identity_controller.rs:270:8
    |
270 | struct UserIdentitiesResponse {
    |        ^^^^^^^^^^^^^^^^^^^^^^

warning: struct `UserIdentitiesData` is never constructed
   --> src/controller/user/identity_controller.rs:276:8
    |
276 | struct UserIdentitiesData {
    |        ^^^^^^^^^^^^^^^^^^

warning: fields `paper_service` and `textbook_paper_service` are never read
  --> src/controller/teaching_aids/teaching_aids_controller.rs:57:9
   |
52 | pub struct TeachingAidsRouteState {
   |            ---------------------- fields in this struct
...
57 |     pub paper_service: Arc<PaperService>,
   |         ^^^^^^^^^^^^^
58 |     pub textbook_paper_service: Arc<TextbookPaperService>,
   |         ^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_chapter_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:531:14
    |
531 | pub async fn create_chapter_handler(
    |              ^^^^^^^^^^^^^^^^^^^^^^

warning: function `get_chapter_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:619:14
    |
619 | pub async fn get_chapter_handler(
    |              ^^^^^^^^^^^^^^^^^^^

warning: function `delete_chapter_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:636:14
    |
636 | pub async fn delete_chapter_handler(
    |              ^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_authorization_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:647:14
    |
647 | pub async fn create_authorization_handler(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `get_authorizations_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:663:14
    |
663 | pub async fn get_authorizations_handler(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `delete_authorization_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:672:14
    |
672 | pub async fn delete_authorization_handler(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: fields `student_id`, `subject`, `level`, `level_description`, and `assessment_date` are never read
   --> src/model/analysis/analysis.rs:121:9
    |
120 | pub struct CreateStudentProfileLevelRequest {
    |            -------------------------------- fields in this struct
121 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
122 |     pub subject: String,
    |         ^^^^^^^
123 |     pub level: String,
    |         ^^^^^
124 |     pub level_description: Option<String>,
    |         ^^^^^^^^^^^^^^^^^
125 |     pub assessment_date: DateTime<Utc>,
    |         ^^^^^^^^^^^^^^^
    |
    = note: `CreateStudentProfileLevelRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `level`, `level_description`, and `assessment_date` are never read
   --> src/model/analysis/analysis.rs:130:9
    |
129 | pub struct UpdateStudentProfileLevelRequest {
    |            -------------------------------- fields in this struct
130 |     pub level: Option<String>,
    |         ^^^^^
131 |     pub level_description: Option<String>,
    |         ^^^^^^^^^^^^^^^^^
132 |     pub assessment_date: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^^^^^^
    |
    = note: `UpdateStudentProfileLevelRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `student_id`, `tag_name`, `tag_value`, and `tag_category` are never read
   --> src/model/analysis/analysis.rs:137:9
    |
136 | pub struct CreateStudentProfileTagRequest {
    |            ------------------------------ fields in this struct
137 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
138 |     pub tag_name: String,
    |         ^^^^^^^^
139 |     pub tag_value: Option<String>,
    |         ^^^^^^^^^
140 |     pub tag_category: String,
    |         ^^^^^^^^^^^^
    |
    = note: `CreateStudentProfileTagRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `tag_value` and `tag_category` are never read
   --> src/model/analysis/analysis.rs:145:9
    |
144 | pub struct UpdateStudentProfileTagRequest {
    |            ------------------------------ fields in this struct
145 |     pub tag_value: Option<String>,
    |         ^^^^^^^^^
146 |     pub tag_category: Option<String>,
    |         ^^^^^^^^^^^^
    |
    = note: `UpdateStudentProfileTagRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id`, `student_ids`, `subjects`, and `force_regenerate` are never read
   --> src/model/analysis/analysis.rs:151:9
    |
150 | pub struct GenerateLearningRecordRequest {
    |            ----------------------------- fields in this struct
151 |     pub exam_id: Uuid,
    |         ^^^^^^^
152 |     pub student_ids: Option<Vec<Uuid>>,
    |         ^^^^^^^^^^^
153 |     pub subjects: Option<Vec<String>>,
    |         ^^^^^^^^
154 |     pub force_regenerate: Option<bool>,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `GenerateLearningRecordRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/analysis/analysis.rs:159:9
    |
158 | pub struct AnalysisQueryParams {
    |            ------------------- fields in this struct
159 |     pub exam_id: Option<Uuid>,
    |         ^^^^^^^
160 |     pub student_id: Option<Uuid>,
    |         ^^^^^^^^^^
161 |     pub subject: Option<String>,
    |         ^^^^^^^
162 |     pub class_id: Option<Uuid>,
    |         ^^^^^^^^
163 |     pub grade_level: Option<String>,
    |         ^^^^^^^^^^^
164 |     pub start_date: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^
165 |     pub end_date: Option<DateTime<Utc>>,
    |         ^^^^^^^^
166 |     pub page: Option<i32>,
    |         ^^^^
167 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
    |
    = note: `AnalysisQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `base_exam_id`, `compare_exam_ids`, `analysis_type`, and `target_ids` are never read
   --> src/model/analysis/analysis.rs:172:9
    |
171 | pub struct ComparisonAnalysisRequest {
    |            ------------------------- fields in this struct
172 |     pub base_exam_id: Uuid,
    |         ^^^^^^^^^^^^
173 |     pub compare_exam_ids: Vec<Uuid>,
    |         ^^^^^^^^^^^^^^^^
174 |     pub analysis_type: String, // 'student', 'class', 'grade', 'subject'
    |         ^^^^^^^^^^^^^
175 |     pub target_ids: Vec<Uuid>,
    |         ^^^^^^^^^^
    |
    = note: `ComparisonAnalysisRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: associated functions `error` and `success` are never used
  --> src/model/base.rs:58:12
   |
55 | impl<T> ApiResponse<T> {
   | ---------------------- associated functions in this implementation
...
58 |     pub fn error(message: Option<String>) -> ApiResponse<Option<String>> {
   |            ^^^^^
...
67 |     pub fn success(data: Option<T>, _message: Option<T>) -> ApiResponse<Option<T>> {
   |            ^^^^^^^

warning: struct `SystemEducationStages` is never constructed
   --> src/model/education_stage/education_stage.rs:145:12
    |
145 | pub struct SystemEducationStages;
    |            ^^^^^^^^^^^^^^^^^^^^^

warning: multiple associated constants are never used
   --> src/model/education_stage/education_stage.rs:148:15
    |
147 | impl SystemEducationStages {
    | -------------------------- associated constants in this implementation
148 |     pub const PRIMARY: &'static str = "PRIMARY";
    |               ^^^^^^^
149 |     pub const MIDDLE: &'static str = "MIDDLE";
    |               ^^^^^^
150 |     pub const HIGH: &'static str = "HIGH";
    |               ^^^^
151 |     pub const VOCATIONAL: &'static str = "VOCATIONAL";
    |               ^^^^^^^^^^
152 |     pub const INTERNATIONAL_IB: &'static str = "INTERNATIONAL_IB";
    |               ^^^^^^^^^^^^^^^^
153 |     pub const INTERNATIONAL_AP: &'static str = "INTERNATIONAL_AP";
    |               ^^^^^^^^^^^^^^^^
154 |     pub const INTERNATIONAL_ALEVEL: &'static str = "INTERNATIONAL_ALEVEL";
    |               ^^^^^^^^^^^^^^^^^^^^

warning: multiple fields are never read
   --> src/model/exam/exam.rs:91:9
    |
90  | pub struct CreateExamRequest {
    |            ----------------- fields in this struct
91  |     pub name: String,
    |         ^^^^
92  |     pub exam_type: String,
    |         ^^^^^^^^^
93  |     pub grade_level: String,
    |         ^^^^^^^^^^^
94  |     pub exam_nature: String,
    |         ^^^^^^^^^^^
95  |     pub description: Option<String>,
    |         ^^^^^^^^^^^
96  |     pub start_time: DateTime<Utc>,
    |         ^^^^^^^^^^
97  |     pub end_time: DateTime<Utc>,
    |         ^^^^^^^^
98  |     pub expected_collection_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^^^^^^^^^^^^^^^
99  |     pub scan_start_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^^^^^^
100 |     pub grading_mode: String,
    |         ^^^^^^^^^^^^
101 |     pub quality_control: String,
    |         ^^^^^^^^^^^^^^^
102 |     pub ai_confidence_threshold: Option<f32>,
    |         ^^^^^^^^^^^^^^^^^^^^^^^
103 |     pub manual_review_ratio: Option<f32>,
    |         ^^^^^^^^^^^^^^^^^^^
104 |     pub subjects: Vec<ExamSubjectRequest>,
    |         ^^^^^^^^
105 |     pub classes: Vec<ExamClassRequest>,
    |         ^^^^^^^
106 |     pub selected_students: Option<Vec<Uuid>>,
    |         ^^^^^^^^^^^^^^^^^
    |
    = note: `CreateExamRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `subject_id`, `paper_template_id`, `total_score`, and `pass_score` are never read
   --> src/model/exam/exam.rs:111:9
    |
110 | pub struct ExamSubjectRequest {
    |            ------------------ fields in this struct
111 |     pub subject_id: Uuid,
    |         ^^^^^^^^^^
112 |     pub paper_template_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^^^^
113 |     pub total_score: f32,
    |         ^^^^^^^^^^^
114 |     pub pass_score: Option<f32>,
    |         ^^^^^^^^^^
    |
    = note: `ExamSubjectRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `class_id` and `class_type` are never read
   --> src/model/exam/exam.rs:119:9
    |
118 | pub struct ExamClassRequest {
    |            ---------------- fields in this struct
119 |     pub class_id: Uuid,
    |         ^^^^^^^^
120 |     pub class_type: String,
    |         ^^^^^^^^^^
    |
    = note: `ExamClassRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/exam/exam.rs:125:9
    |
124 | pub struct UpdateExamRequest {
    |            ----------------- fields in this struct
125 |     pub name: Option<String>,
    |         ^^^^
126 |     pub description: Option<String>,
    |         ^^^^^^^^^^^
127 |     pub start_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^
128 |     pub end_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^
129 |     pub expected_collection_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^^^^^^^^^^^^^^^
130 |     pub scan_start_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^^^^^^
131 |     pub grading_mode: Option<String>,
    |         ^^^^^^^^^^^^
132 |     pub quality_control: Option<String>,
    |         ^^^^^^^^^^^^^^^
133 |     pub ai_confidence_threshold: Option<f32>,
    |         ^^^^^^^^^^^^^^^^^^^^^^^
134 |     pub manual_review_ratio: Option<f32>,
    |         ^^^^^^^^^^^^^^^^^^^
135 |     pub status: Option<String>,
    |         ^^^^^^
    |
    = note: `UpdateExamRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `participant_tenant_ids`, `exam_id`, and `invitation_message` are never read
   --> src/model/exam/exam.rs:140:9
    |
139 | pub struct JointExamInvitationRequest {
    |            -------------------------- fields in this struct
140 |     pub participant_tenant_ids: Vec<Uuid>,
    |         ^^^^^^^^^^^^^^^^^^^^^^
141 |     pub exam_id: Uuid,
    |         ^^^^^^^
142 |     pub invitation_message: Option<String>,
    |         ^^^^^^^^^^^^^^^^^^
    |
    = note: `JointExamInvitationRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `invitation_id`, `response`, and `response_message` are never read
   --> src/model/exam/exam.rs:147:9
    |
146 | pub struct JointExamResponseRequest {
    |            ------------------------ fields in this struct
147 |     pub invitation_id: Uuid,
    |         ^^^^^^^^^^^^^
148 |     pub response: String, // 'accept' or 'reject'
    |         ^^^^^^^^
149 |     pub response_message: Option<String>,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `JointExamResponseRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/exam/exam.rs:189:9
    |
188 | pub struct ExamQueryParams {
    |            --------------- fields in this struct
189 |     pub page: Option<i32>,
    |         ^^^^
190 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
191 |     pub name: Option<String>,
    |         ^^^^
192 |     pub exam_type: Option<String>,
    |         ^^^^^^^^^
193 |     pub grade_level: Option<String>,
    |         ^^^^^^^^^^^
194 |     pub status: Option<String>,
    |         ^^^^^^
195 |     pub start_date: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^
196 |     pub end_date: Option<DateTime<Utc>>,
    |         ^^^^^^^^
    |
    = note: `ExamQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id`, `student_id`, `paper_sequence`, `scan_method`, and `scan_device` are never read
   --> src/model/grading/grading.rs:181:9
    |
180 | pub struct UploadPaperRequest {
    |            ------------------ fields in this struct
181 |     pub exam_id: Uuid,
    |         ^^^^^^^
182 |     pub student_id: Option<Uuid>,
    |         ^^^^^^^^^^
183 |     pub paper_sequence: i32,
    |         ^^^^^^^^^^^^^^
184 |     pub scan_method: String,
    |         ^^^^^^^^^^^
185 |     pub scan_device: Option<String>,
    |         ^^^^^^^^^^^
    |
    = note: `UploadPaperRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `card_block_id`, `student_id`, `raw_score`, `grading_method`, `confidence_score`, and `grading_notes` are never read
   --> src/model/grading/grading.rs:210:9
    |
209 | pub struct GradeCardBlockRequest {
    |            --------------------- fields in this struct
210 |     pub card_block_id: Uuid,
    |         ^^^^^^^^^^^^^
211 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
212 |     pub raw_score: f32,
    |         ^^^^^^^^^
213 |     pub grading_method: String,
    |         ^^^^^^^^^^^^^^
214 |     pub confidence_score: Option<f32>,
    |         ^^^^^^^^^^^^^^^^
215 |     pub grading_notes: Option<String>,
    |         ^^^^^^^^^^^^^
    |
    = note: `GradeCardBlockRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id`, `question_id`, `grader_user_id`, `control_level`, `control_action`, and `control_reason` are never read
   --> src/model/grading/grading.rs:220:9
    |
219 | pub struct ControlGradingRequest {
    |            --------------------- fields in this struct
220 |     pub exam_id: Uuid,
    |         ^^^^^^^
221 |     pub question_id: Option<Uuid>,
    |         ^^^^^^^^^^^
222 |     pub grader_user_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^
223 |     pub control_level: String,
    |         ^^^^^^^^^^^^^
224 |     pub control_action: String,
    |         ^^^^^^^^^^^^^^
225 |     pub control_reason: Option<String>,
    |         ^^^^^^^^^^^^^^
    |
    = note: `ControlGradingRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exception_id`, `confirmed_student_id`, `resolution_method`, and `processing_notes` are never read
   --> src/model/grading/grading.rs:230:9
    |
229 | pub struct ResolveStudentIdExceptionRequest {
    |            -------------------------------- fields in this struct
230 |     pub exception_id: Uuid,
    |         ^^^^^^^^^^^^
231 |     pub confirmed_student_id: Uuid,
    |         ^^^^^^^^^^^^^^^^^^^^
232 |     pub resolution_method: String,
    |         ^^^^^^^^^^^^^^^^^
233 |     pub processing_notes: Option<String>,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `ResolveStudentIdExceptionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exception_id`, `resolution_status`, and `resolution_notes` are never read
   --> src/model/grading/grading.rs:238:9
    |
237 | pub struct ResolvePaperScanExceptionRequest {
    |            -------------------------------- fields in this struct
238 |     pub exception_id: Uuid,
    |         ^^^^^^^^^^^^
239 |     pub resolution_status: String,
    |         ^^^^^^^^^^^^^^^^^
240 |     pub resolution_notes: Option<String>,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `ResolvePaperScanExceptionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/grading/grading.rs:245:9
    |
244 | pub struct GradingQueryParams {
    |            ------------------ fields in this struct
245 |     pub exam_id: Option<Uuid>,
    |         ^^^^^^^
246 |     pub student_id: Option<Uuid>,
    |         ^^^^^^^^^^
247 |     pub grader_id: Option<Uuid>,
    |         ^^^^^^^^^
248 |     pub question_id: Option<Uuid>,
    |         ^^^^^^^^^^^
249 |     pub grading_method: Option<String>,
    |         ^^^^^^^^^^^^^^
250 |     pub quality_level: Option<String>,
    |         ^^^^^^^^^^^^^
251 |     pub page: Option<i32>,
    |         ^^^^
252 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
    |
    = note: `GradingQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/grading/grading.rs:257:9
    |
256 | pub struct ScanQueryParams {
    |            --------------- fields in this struct
257 |     pub exam_id: Option<Uuid>,
    |         ^^^^^^^
258 |     pub student_id: Option<Uuid>,
    |         ^^^^^^^^^^
259 |     pub scan_quality_min: Option<i32>,
    |         ^^^^^^^^^^^^^^^^
260 |     pub scan_quality_max: Option<i32>,
    |         ^^^^^^^^^^^^^^^^
261 |     pub is_abnormal: Option<bool>,
    |         ^^^^^^^^^^^
262 |     pub needs_review: Option<bool>,
    |         ^^^^^^^^^^^^
263 |     pub page: Option<i32>,
    |         ^^^^
264 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
    |
    = note: `ScanQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/grading/grading.rs:438:9
    |
437 | pub struct CreatePaperScanRequest {
    |            ---------------------- fields in this struct
438 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
439 |     pub paper_sequence: i32,
    |         ^^^^^^^^^^^^^^
440 |     pub file_url: Option<String>,
    |         ^^^^^^^^
441 |     pub scan_quality: i32,
    |         ^^^^^^^^^^^^
442 |     pub scan_method: String,
    |         ^^^^^^^^^^^
443 |     pub scan_device: Option<String>,
    |         ^^^^^^^^^^^
444 |     pub result:Option<Value>,
    |         ^^^^^^
445 |     pub file_size: i64,
    |         ^^^^^^^^^
446 |     pub exam_id: Uuid
    |         ^^^^^^^
    |
    = note: `CreatePaperScanRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple variants are never constructed
   --> src/model/grading/grading.rs:709:5
    |
708 | pub enum Error {
    |          ----- variants in this enum
709 |     MarkInfoError(String),
    |     ^^^^^^^^^^^^^
710 |     LoadImageError(String),
    |     ^^^^^^^^^^^^^^
711 |     CheckBlankError(String),
    |     ^^^^^^^^^^^^^^^
712 |     LocationError(String),
    |     ^^^^^^^^^^^^^
713 |     ImageHasherError(String),
    |     ^^^^^^^^^^^^^^^^
714 |     QrCodeError(String),
    |     ^^^^^^^^^^^
715 |     PageNumberError(String),
    |     ^^^^^^^^^^^^^^^
716 |     StudentNumberError(String),
    |     ^^^^^^^^^^^^^^^^^^
717 |     FillQuestionError(String),
    |     ^^^^^^^^^^^^^^^^^
718 |     HandwriteQuestionError(String),
    |     ^^^^^^^^^^^^^^^^^^^^^^
719 |     SaveImageError(String),
    |     ^^^^^^^^^^^^^^
    |
    = note: `Error` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: variants `Error`, `Blank`, and `Value` are never constructed
   --> src/model/grading/grading.rs:724:5
    |
723 | pub enum PageData {
    |          -------- variants in this enum
724 |     Error(Error),
    |     ^^^^^
725 |     Blank,
    |     ^^^^^
726 |     Value(PageValue)
    |     ^^^^^
    |
    = note: `PageData` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: variants `Objective`, `Objectives`, and `Subjective` are never constructed
   --> src/model/grading/grading.rs:736:5
    |
735 | pub enum BlockValue {
    |          ---------- variants in this enum
736 |     Objective((i64, String)),
    |     ^^^^^^^^^
737 |     Objectives(Vec<(i64, String)>),
    |     ^^^^^^^^^^
738 |     Subjective(Vec<i64>),
    |     ^^^^^^^^^^
    |
    = note: `BlockValue` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: constant `HOMEWORK_STATUS_DONING` is never used
  --> src/model/homework/homework.rs:31:11
   |
31 | pub const HOMEWORK_STATUS_DONING: &str = "Doing";
   |           ^^^^^^^^^^^^^^^^^^^^^^

warning: constant `HOMEWORK_STUDENTS_STATUS_UNSUBMITTED` is never used
  --> src/model/homework_students/homework_students.rs:19:11
   |
19 | pub const HOMEWORK_STUDENTS_STATUS_UNSUBMITTED: &str = "Unsubmitted";
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: constant `HOMEWORK_STUDENTS_STATUS_ERROR` is never used
  --> src/model/homework_students/homework_students.rs:21:11
   |
21 | pub const HOMEWORK_STUDENTS_STATUS_ERROR: &str = "Error";
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: constant `HOMEWORK_STUDENTS_STATUS_DONE` is never used
  --> src/model/homework_students/homework_students.rs:23:11
   |
23 | pub const HOMEWORK_STUDENTS_STATUS_DONE: &str = "Done";
   |           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: multiple fields are never read
   --> src/model/question/question.rs:104:9
    |
103 | pub struct CreateQuestionRequest {
    |            --------------------- fields in this struct
104 |     pub source_type: String,
    |         ^^^^^^^^^^^
105 |     pub source_id: Option<Uuid>,
    |         ^^^^^^^^^
106 |     pub question_type: String,
    |         ^^^^^^^^^^^^^
107 |     pub question_content: String,
    |         ^^^^^^^^^^^^^^^^
108 |     pub options: Option<serde_json::Value>,
    |         ^^^^^^^
109 |     pub answer_content: Option<String>,
    |         ^^^^^^^^^^^^^^
110 |     pub difficulty_level: i32,
    |         ^^^^^^^^^^^^^^^^
111 |     pub knowledge_points: serde_json::Value,
    |         ^^^^^^^^^^^^^^^^
112 |     pub subject: String,
    |         ^^^^^^^
113 |     pub grade_level: i32,
    |         ^^^^^^^^^^^
    |
    = note: `CreateQuestionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `question_content`, `answer_content`, `difficulty_level`, `knowledge_points`, and `status` are never read
   --> src/model/question/question.rs:118:9
    |
117 | pub struct UpdateQuestionRequest {
    |            --------------------- fields in this struct
118 |     pub question_content: Option<String>,
    |         ^^^^^^^^^^^^^^^^
119 |     pub answer_content: Option<String>,
    |         ^^^^^^^^^^^^^^
120 |     pub difficulty_level: Option<i32>,
    |         ^^^^^^^^^^^^^^^^
121 |     pub knowledge_points: Option<serde_json::Value>,
    |         ^^^^^^^^^^^^^^^^
122 |     pub status: Option<String>,
    |         ^^^^^^
    |
    = note: `UpdateQuestionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/question/question.rs:127:9
    |
126 | pub struct CreatePaperRequest {
    |            ------------------ fields in this struct
127 |     pub title: String,
    |         ^^^^^
128 |     pub subject: String,
    |         ^^^^^^^
129 |     pub grade_level: i32,
    |         ^^^^^^^^^^^
130 |     pub total_score: f32,
    |         ^^^^^^^^^^^
131 |     pub description: Option<String>,
    |         ^^^^^^^^^^^
132 |     pub structure: serde_json::Value,
    |         ^^^^^^^^^
133 |     pub questions: Vec<PaperQuestionRequest>,
    |         ^^^^^^^^^
    |
    = note: `CreatePaperRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `question_id`, `question_number`, `score`, `section_name`, and `display_order` are never read
   --> src/model/question/question.rs:138:9
    |
137 | pub struct PaperQuestionRequest {
    |            -------------------- fields in this struct
138 |     pub question_id: Uuid,
    |         ^^^^^^^^^^^
139 |     pub question_number: String,
    |         ^^^^^^^^^^^^^^^
140 |     pub score: f32,
    |         ^^^^^
141 |     pub section_name: String,
    |         ^^^^^^^^^^^^
142 |     pub display_order: i32,
    |         ^^^^^^^^^^^^^
    |
    = note: `PaperQuestionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `title`, `description`, `structure`, and `status` are never read
   --> src/model/question/question.rs:147:9
    |
146 | pub struct UpdatePaperRequest {
    |            ------------------ fields in this struct
147 |     pub title: Option<String>,
    |         ^^^^^
148 |     pub description: Option<String>,
    |         ^^^^^^^^^^^
149 |     pub structure: Option<serde_json::Value>,
    |         ^^^^^^^^^
150 |     pub status: Option<String>,
    |         ^^^^^^
    |
    = note: `UpdatePaperRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/question/question.rs:155:9
    |
154 | pub struct CreateTextbookRequest {
    |            --------------------- fields in this struct
155 |     pub title: String,
    |         ^^^^^
156 |     pub subject_id: Uuid,
    |         ^^^^^^^^^^
157 |     pub grade_level_id: Uuid,
    |         ^^^^^^^^^^^^^^
158 |     pub publisher: Option<String>,
    |         ^^^^^^^^^
159 |     pub publication_year: Option<i32>,
    |         ^^^^^^^^^^^^^^^^
160 |     pub isbn: Option<String>,
    |         ^^^^
161 |     pub version: Option<String>,
    |         ^^^^^^^
162 |     pub chapters: Vec<TextbookChapterRequest>,
    |         ^^^^^^^^
    |
    = note: `CreateTextbookRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `chapter_number`, `title`, `content`, `knowledge_points`, and `exercises` are never read
   --> src/model/question/question.rs:167:9
    |
166 | pub struct TextbookChapterRequest {
    |            ---------------------- fields in this struct
167 |     pub chapter_number: i32,
    |         ^^^^^^^^^^^^^^
168 |     pub title: String,
    |         ^^^^^
169 |     pub content: Option<String>,
    |         ^^^^^^^
170 |     pub knowledge_points: serde_json::Value,
    |         ^^^^^^^^^^^^^^^^
171 |     pub exercises: Vec<TextbookExerciseRequest>,
    |         ^^^^^^^^^
    |
    = note: `TextbookChapterRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `question_content`, `answer_content`, `difficulty_level`, and `knowledge_points` are never read
   --> src/model/question/question.rs:176:9
    |
175 | pub struct TextbookExerciseRequest {
    |            ----------------------- fields in this struct
176 |     pub question_content: String,
    |         ^^^^^^^^^^^^^^^^
177 |     pub answer_content: Option<String>,
    |         ^^^^^^^^^^^^^^
178 |     pub difficulty_level: i32,
    |         ^^^^^^^^^^^^^^^^
179 |     pub knowledge_points: serde_json::Value,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `TextbookExerciseRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `textbook_id` and `tenant_ids` are never read
   --> src/model/question/question.rs:184:9
    |
183 | pub struct GrantTextbookAccessRequest {
    |            -------------------------- fields in this struct
184 |     pub textbook_id: Uuid,
    |         ^^^^^^^^^^^
185 |     pub tenant_ids: Vec<Uuid>,
    |         ^^^^^^^^^^
    |
    = note: `GrantTextbookAccessRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/question/question.rs:190:9
    |
189 | pub struct QuestionQueryParams {
    |            ------------------- fields in this struct
190 |     pub page: Option<i32>,
    |         ^^^^
191 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
192 |     pub subject: Option<String>,
    |         ^^^^^^^
193 |     pub grade_level: Option<i32>,
    |         ^^^^^^^^^^^
194 |     pub difficulty_level: Option<i32>,
    |         ^^^^^^^^^^^^^^^^
195 |     pub question_type: Option<String>,
    |         ^^^^^^^^^^^^^
196 |     pub status: Option<String>,
    |         ^^^^^^
197 |     pub keyword: Option<String>,
    |         ^^^^^^^
198 |     pub source_type: Option<String>,
    |         ^^^^^^^^^^^
199 |     pub search: Option<String>,
    |         ^^^^^^
    |
    = note: `QuestionQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `page`, `page_size`, `subject`, `grade_level`, `status`, and `keyword` are never read
   --> src/model/question/question.rs:204:9
    |
203 | pub struct PaperQueryParams {
    |            ---------------- fields in this struct
204 |     pub page: Option<i32>,
    |         ^^^^
205 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
206 |     pub subject: Option<String>,
    |         ^^^^^^^
207 |     pub grade_level: Option<i32>,
    |         ^^^^^^^^^^^
208 |     pub status: Option<String>,
    |         ^^^^^^
209 |     pub keyword: Option<String>,
    |         ^^^^^^^
    |
    = note: `PaperQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/question/question.rs:214:9
    |
213 | pub struct TextbookQueryParams {
    |            ------------------- fields in this struct
214 |     pub page: Option<i32>,
    |         ^^^^
215 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
216 |     pub subject_id: Option<Uuid>,
    |         ^^^^^^^^^^
217 |     pub grade_level_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^
218 |     pub publisher: Option<String>,
    |         ^^^^^^^^^
219 |     pub status: Option<String>,
    |         ^^^^^^
220 |     pub keyword: Option<String>,
    |         ^^^^^^^
    |
    = note: `TextbookQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: methods `is_system_role` and `full_display_name` are never used
   --> src/model/role/role.rs:149:12
    |
147 | impl Role {
    | --------- methods in this implementation
148 |     /// 检查是否是系统预设角色
149 |     pub fn is_system_role(&self) -> bool {
    |            ^^^^^^^^^^^^^^
...
154 |     pub fn full_display_name(&self) -> String {
    |            ^^^^^^^^^^^^^^^^^

warning: struct `SystemRoles` is never constructed
   --> src/model/role/role.rs:184:12
    |
184 | pub struct SystemRoles;
    |            ^^^^^^^^^^^

warning: multiple associated constants are never used
   --> src/model/role/role.rs:187:15
    |
186 | impl SystemRoles {
    | ---------------- associated constants in this implementation
187 |     pub const SUPER_ADMIN: &'static str = "super_admin";
    |               ^^^^^^^^^^^
188 |     pub const SYSTEM_AGENT: &'static str = "system_agent";
    |               ^^^^^^^^^^^^
189 |     pub const OPERATOR: &'static str = "operator";
    |               ^^^^^^^^
190 |     pub const TENANT_ADMIN: &'static str = "tenant_admin";
    |               ^^^^^^^^^^^^
191 |     pub const PRINCIPAL: &'static str = "principal";
    |               ^^^^^^^^^
192 |     pub const ACADEMIC_DIRECTOR: &'static str = "academic_director";
    |               ^^^^^^^^^^^^^^^^^
193 |     pub const SUBJECT_LEADER: &'static str = "subject_leader";
    |               ^^^^^^^^^^^^^^
194 |     pub const EXAM_MANAGER: &'static str = "exam_manager";
    |               ^^^^^^^^^^^^
195 |     pub const GRADING_MANAGER: &'static str = "grading_manager";
    |               ^^^^^^^^^^^^^^^
196 |     pub const SCAN_OPERATOR: &'static str = "scan_operator";
    |               ^^^^^^^^^^^^^
197 |     pub const GRADER: &'static str = "grader";
    |               ^^^^^^
198 |     pub const GRADE_LEADER: &'static str = "grade_leader";
    |               ^^^^^^^^^^^^
199 |     pub const CLASS_TEACHER: &'static str = "class_teacher";
    |               ^^^^^^^^^^^^^
200 |     pub const TEACHER: &'static str = "teacher";
    |               ^^^^^^^
201 |     pub const STUDENT: &'static str = "student";
    |               ^^^^^^^
202 |     pub const PARENT: &'static str = "parent";
    |               ^^^^^^

warning: fields `id`, `role_id`, `permission_id`, and `created_at` are never read
  --> src/model/role/permission.rs:22:9
   |
21 | pub struct RolePermission {
   |            -------------- fields in this struct
22 |     pub id: Uuid,
   |         ^^
23 |     pub role_id: Uuid,
   |         ^^^^^^^
24 |     pub permission_id: Uuid,
   |         ^^^^^^^^^^^^^
25 |     pub created_at: DateTime<Utc>,
   |         ^^^^^^^^^^
   |
   = note: `RolePermission` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: fields `page`, `page_size`, `search`, `resource`, and `action` are never read
  --> src/model/role/permission.rs:31:9
   |
30 | pub struct PermissionQueryParams {
   |            --------------------- fields in this struct
31 |     pub page: Option<i32>,
   |         ^^^^
32 |     pub page_size: Option<i32>,
   |         ^^^^^^^^^
33 |     pub search: Option<String>,
   |         ^^^^^^
34 |     pub resource: Option<String>,
   |         ^^^^^^^^
35 |     pub action: Option<String>,
   |         ^^^^^^
   |
   = note: `PermissionQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `resource` and `action` are never read
  --> src/model/role/permission.rs:48:9
   |
47 | pub struct CheckPermissionRequest {
   |            ---------------------- fields in this struct
48 |     pub resource: String,
   |         ^^^^^^^^
49 |     pub action: String,
   |         ^^^^^^
   |
   = note: `CheckPermissionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: struct `SystemSubjects` is never constructed
   --> src/model/subject/subject.rs:116:12
    |
116 | pub struct SystemSubjects;
    |            ^^^^^^^^^^^^^^

warning: multiple associated constants are never used
   --> src/model/subject/subject.rs:119:15
    |
118 | impl SystemSubjects {
    | ------------------- associated constants in this implementation
119 |     pub const MATH: &'static str = "MATH";
    |               ^^^^
120 |     pub const CHINESE: &'static str = "CHINESE";
    |               ^^^^^^^
121 |     pub const ENGLISH: &'static str = "ENGLISH";
    |               ^^^^^^^
122 |     pub const PHYSICS: &'static str = "PHYSICS";
    |               ^^^^^^^
123 |     pub const CHEMISTRY: &'static str = "CHEMISTRY";
    |               ^^^^^^^^^
124 |     pub const BIOLOGY: &'static str = "BIOLOGY";
    |               ^^^^^^^
125 |     pub const HISTORY: &'static str = "HISTORY";
    |               ^^^^^^^
126 |     pub const GEOGRAPHY: &'static str = "GEOGRAPHY";
    |               ^^^^^^^^^
127 |     pub const POLITICS: &'static str = "POLITICS";
    |               ^^^^^^^^

warning: field `author` is never read
   --> src/model/teaching_aids/textbooks.rs:145:9
    |
142 | pub struct UpdateTeachingAidRequest {
    |            ------------------------ field in this struct
...
145 |     pub author: Option<String>,
    |         ^^^^^^
    |
    = note: `UpdateTeachingAidRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/teaching_aids/textbooks.rs:155:9
    |
154 | pub struct ImportTeachingAidRequest {
    |            ------------------------ fields in this struct
155 |     pub title: String,
    |         ^^^^^
156 |     pub description: Option<String>,
    |         ^^^^^^^^^^^
157 |     pub author: Option<String>,
    |         ^^^^^^
158 |     pub publisher: Option<String>,
    |         ^^^^^^^^^
159 |     pub subject_id: Option<Uuid>,
    |         ^^^^^^^^^^
160 |     pub grade_level_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^
161 |     pub version: Option<String>,
    |         ^^^^^^^
    |
    = note: `ImportTeachingAidRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `subject_id`, `grade_level_id`, `status`, and `search` are never read
   --> src/model/teaching_aids/textbooks.rs:168:9
    |
165 | pub struct TeachingAidQuery {
    |            ---------------- fields in this struct
...
168 |     pub subject_id: Option<Uuid>,
    |         ^^^^^^^^^^
169 |     pub grade_level_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^
170 |     pub status: Option<String>,
    |         ^^^^^^
171 |     pub search: Option<String>,
    |         ^^^^^^
    |
    = note: `TeachingAidQuery` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `chapter_number`, `title`, `content`, and `knowledge_points` are never read
   --> src/model/teaching_aids/textbooks.rs:176:9
    |
175 | pub struct CreateChapterRequest {
    |            -------------------- fields in this struct
176 |     pub chapter_number: i32,
    |         ^^^^^^^^^^^^^^
177 |     pub title: String,
    |         ^^^^^
178 |     pub content: Option<String>,
    |         ^^^^^^^
179 |     pub knowledge_points: Option<serde_json::Value>,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `CreateChapterRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `textbook_id` and `tenant_id` are never read
   --> src/model/teaching_aids/textbooks.rs:184:9
    |
183 | pub struct CreateAuthorizationRequest {
    |            -------------------------- fields in this struct
184 |     pub textbook_id: Uuid,
    |         ^^^^^^^^^^^
185 |     pub tenant_id: Uuid,
    |         ^^^^^^^^^
    |
    = note: `CreateAuthorizationRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: method `calculate_effective_risk_level` is never used
   --> src/model/tenant/permission.rs:251:12
    |
250 | impl PermissionTemplate {
    | ----------------------- method in this implementation
251 |     pub fn calculate_effective_risk_level(&self, context: &PermissionContext) -> i32 {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: methods `is_expired` and `can_auto_approve` are never used
   --> src/model/tenant/permission.rs:272:12
    |
271 | impl SpecialPermissionRequest {
    | ----------------------------- methods in this implementation
272 |     pub fn is_expired(&self) -> bool {
    |            ^^^^^^^^^^
...
280 |     pub fn can_auto_approve(&self, workflow: &ApprovalWorkflow) -> bool {
    |            ^^^^^^^^^^^^^^^^

warning: associated function `new` is never used
   --> src/model/tenant/permission.rs:298:12
    |
297 | impl PermissionAuditLog {
    | ----------------------- associated function in this implementation
298 |     pub fn new(
    |            ^^^

warning: fields `id`, `username`, and `password` are never read
 --> src/model/user/user.rs:7:8
  |
6 | pub struct UserRecord {
  |            ---------- fields in this struct
7 |    pub id: Uuid,
  |        ^^
8 |    pub username: String,
  |        ^^^^^^^^
9 |    pub password: String,
  |        ^^^^^^^^

warning: field `paper_name` is never read
  --> src/model/paper/paper.rs:20:9
   |
17 | pub struct PaperQuery {
   |            ---------- field in this struct
...
20 |     pub paper_name: Option<String>,
   |         ^^^^^^^^^^
   |
   = note: `PaperQuery` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: struct `ExamService` is never constructed
 --> src/service/exam/exam_service.rs:8:12
  |
8 | pub struct ExamService {
  |            ^^^^^^^^^^^

warning: multiple associated items are never used
   --> src/service/exam/exam_service.rs:13:12
    |
12  | impl ExamService {
    | ---------------- associated items in this implementation
13  |     pub fn new(pool: PgPool) -> Self {
    |            ^^^
...
17  |     fn schema_name(tenant_id: Uuid) -> String {
    |        ^^^^^^^^^^^
...
21  |     pub async fn create_exam(
    |                  ^^^^^^^^^^^
...
187 |     pub async fn get_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<Option<ExamResponse>> {
    |                  ^^^^^^^^
...
256 |     pub async fn update_exam(
    |                  ^^^^^^^^^^^
...
316 |     pub async fn delete_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
    |                  ^^^^^^^^^^^
...
327 |     pub async fn list_exams(
    |                  ^^^^^^^^^^
...
412 |     pub async fn create_joint_exam_invitation(
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
446 |     pub async fn respond_to_joint_exam_invitation(
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
477 |     pub async fn get_exam_statistics(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<ExamStatisticsResponse> {
    |                  ^^^^^^^^^^^^^^^^^^^
...
569 |     pub async fn get_exam_statistics_overview(&self, tenant_id: Uuid) -> Result<ExamStatistics> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
629 |     pub async fn publish_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
    |                  ^^^^^^^^^^^^
...
647 |     pub async fn start_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
    |                  ^^^^^^^^^^
...
665 |     pub async fn complete_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
    |                  ^^^^^^^^^^^^^

warning: struct `StartupSchemaService` is never constructed
 --> src/service/tenant/startup_schema_service.rs:8:12
  |
8 | pub struct StartupSchemaService {
  |            ^^^^^^^^^^^^^^^^^^^^

warning: multiple associated items are never used
   --> src/service/tenant/startup_schema_service.rs:14:12
    |
13  | impl StartupSchemaService {
    | ------------------------- associated items in this implementation
14  |     pub fn new(pool: PgPool, template_path: Option<String>) -> Self {
    |            ^^^
...
23  |     pub async fn ensure_startup_schemas(&self) -> Result<()> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^
...
71  |     async fn create_default_tenant_for_development(&self) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
115 |     async fn create_schema_from_template(&self, schema_name: &str) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
133 |     async fn execute_template_sql(
    |              ^^^^^^^^^^^^^^^^^^^^
...
158 |     async fn get_all_tenants(&self) -> Result<Vec<TenantInfo>> {
    |              ^^^^^^^^^^^^^^^
...
174 |     async fn get_existing_schemas(&self) -> Result<HashSet<String>> {
    |              ^^^^^^^^^^^^^^^^^^^^
...
194 |     async fn schema_exists(&self, schema_name: &str) -> Result<bool> {
    |              ^^^^^^^^^^^^^
...
206 |     fn validate_schema_name(&self, schema_name: &str) -> Result<()> {
    |        ^^^^^^^^^^^^^^^^^^^^
...
220 |     pub async fn create_compile_time_schemas(&self) -> Result<()> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
240 |     pub async fn cleanup_development_data(&self) -> Result<()> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^

warning: struct `TenantInfo` is never constructed
   --> src/service/tenant/startup_schema_service.rs:267:8
    |
267 | struct TenantInfo {
    |        ^^^^^^^^^^

warning: struct `StartupSchemaInitializer` is never constructed
   --> src/service/tenant/startup_schema_service.rs:273:12
    |
273 | pub struct StartupSchemaInitializer;
    |            ^^^^^^^^^^^^^^^^^^^^^^^^

warning: associated function `initialize_for_compilation` is never used
   --> src/service/tenant/startup_schema_service.rs:277:18
    |
275 | impl StartupSchemaInitializer {
    | ----------------------------- associated function in this implementation
276 |     /// 在程序启动时调用，确保编译时需要的 schema 存在
277 |     pub async fn initialize_for_compilation(pool: &PgPool) -> Result<()> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `search_teacher` is never used
 --> src/service/tenant/member.rs:9:14
  |
9 | pub async fn search_teacher(
  |              ^^^^^^^^^^^^^^

warning: struct `CompileSafeQueryService` is never constructed
  --> src/service/tenant/mod.rs:12:16
   |
12 |     pub struct CompileSafeQueryService;
   |                ^^^^^^^^^^^^^^^^^^^^^^^

warning: struct `StudentRow` is never constructed
  --> src/service/tenant/mod.rs:13:16
   |
13 |     pub struct StudentRow;
   |                ^^^^^^^^^^

warning: associated function `new` is never used
  --> src/service/tenant/mod.rs:16:16
   |
15 |     impl CompileSafeQueryService {
   |     ---------------------------- associated function in this implementation
16 |         pub fn new() -> Self {
   |                ^^^

warning: struct `DynamicQueryService` is never constructed
  --> src/service/tenant/mod.rs:25:16
   |
25 |     pub struct DynamicQueryService;
   |                ^^^^^^^^^^^^^^^^^^^

warning: associated function `new` is never used
  --> src/service/tenant/mod.rs:28:16
   |
27 |     impl DynamicQueryService {
   |     ------------------------ associated function in this implementation
28 |         pub fn new() -> Self {
   |                ^^^

warning: fields `id`, `username`, and `created_at` are never read
  --> src/service/user/user_service.rs:80:13
   |
79 |         struct UserRecord {
   |                ---------- fields in this struct
80 |             id: Uuid,
   |             ^^
81 |             username: String,
   |             ^^^^^^^^
82 |             created_at: DateTime<Utc>,
   |             ^^^^^^^^^^

warning: method `check_username_exists` is never used
   --> src/service/auth/auth_service.rs:491:14
    |
23  | impl AuthService {
    | ---------------- method in this implementation
...
491 |     async fn check_username_exists(&self, username: &str) -> AuthResult<bool> {
    |              ^^^^^^^^^^^^^^^^^^^^^

warning: field `pool` is never read
  --> src/service/classes/classes_service.rs:22:5
   |
21 | pub struct ClassesService {
   |            -------------- field in this struct
22 |     pool: PgPool,
   |     ^^^^
   |
   = note: `ClassesService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/service/education_stage/education_stage_service.rs:457:9
    |
456 | pub struct PageEducationStageParams {
    |            ------------------------ fields in this struct
457 |     pub page: i32,
    |         ^^^^
458 |     pub page_size: i32,
    |         ^^^^^^^^^
459 |     pub name_or_code_ilike: Option<String>,
    |         ^^^^^^^^^^^^^^^^^^
460 |     pub is_active: Option<bool>,
    |         ^^^^^^^^^
461 |     pub is_standard: Option<bool>,
    |         ^^^^^^^^^^^
462 |     pub order_by: Option<String>, // 排序字段：name, code, order_level, created_at
    |         ^^^^^^^^
463 |     pub order_direction: Option<String>, // 排序方向：asc, desc
    |         ^^^^^^^^^^^^^^^
    |
    = note: `PageEducationStageParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple methods are never used
   --> src/service/permission/casbin_service.rs:75:14
    |
70  | pub trait CasbinPermissionService: Send + Sync {
    |           ----------------------- methods in this trait
...
75  |     async fn batch_enforce(&self, requests: &[PermissionRequest]) -> Result<Vec<bool>>;
    |              ^^^^^^^^^^^^^
...
78  |     async fn add_policy(&self, policy: &PermissionPolicy) -> Result<bool>;
    |              ^^^^^^^^^^
...
81  |     async fn remove_policy(&self, policy: &PermissionPolicy) -> Result<bool>;
    |              ^^^^^^^^^^^^^
...
84  |     async fn add_role(&self, relation: &RoleRelation) -> Result<bool>;
    |              ^^^^^^^^
...
87  |     async fn remove_role(&self, relation: &RoleRelation) -> Result<bool>;
    |              ^^^^^^^^^^^
...
90  |     async fn get_roles_for_user(&self, user: &str, domain: &str) -> Result<Vec<String>>;
    |              ^^^^^^^^^^^^^^^^^^
...
102 |     async fn sync_permissions(&self, tenant_id: &str) -> Result<()>;
    |              ^^^^^^^^^^^^^^^^
...
105 |     async fn clear_tenant_policies(&self, tenant_id: &str) -> Result<()>;
    |              ^^^^^^^^^^^^^^^^^^^^^

warning: associated items `load_menu_permissions`, `convert_menu_tree_to_permissions`, and `build_user_identity` are never used
   --> src/service/permission/casbin_service.rs:175:14
    |
120 | impl MultiTenantCasbinService {
    | ----------------------------- associated items in this implementation
...
175 |     async fn load_menu_permissions(&self) -> Result<HashMap<String, MenuPermission>> {
    |              ^^^^^^^^^^^^^^^^^^^^^
...
207 |     fn convert_menu_tree_to_permissions(
    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
317 |     fn build_user_identity(user_id: &Uuid, role_code: &str, target_type: &str, target_id: Option<&Uuid>) -> String {
    |        ^^^^^^^^^^^^^^^^^^^

warning: struct `RolePermissionSyncService` is never constructed
  --> src/service/permission/role_sync_service.rs:54:12
   |
54 | pub struct RolePermissionSyncService {
   |            ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: multiple associated items are never used
   --> src/service/permission/role_sync_service.rs:61:12
    |
59  | impl RolePermissionSyncService {
    | ------------------------------ associated items in this implementation
60  |     /// 创建新的同步服务实例
61  |     pub fn new(pool: PgPool, casbin_service: Arc<MultiTenantCasbinService>) -> Self {
    |            ^^^
...
69  |     pub async fn sync_all_tenants(&self, config: &SyncConfig) -> Result<Vec<SyncResult>> {
    |                  ^^^^^^^^^^^^^^^^
...
104 |     pub async fn sync_tenant_permissions(&self, tenant_id: &str, config: &SyncConfig) -> Result<SyncResult> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^^
...
162 |     async fn sync_system_roles(
    |              ^^^^^^^^^^^^^^^^^
...
201 |     async fn sync_tenant_roles(
    |              ^^^^^^^^^^^^^^^^^
...
235 |     async fn sync_user_identities(
    |              ^^^^^^^^^^^^^^^^^^^^
...
321 |     async fn sync_role_permissions(
    |              ^^^^^^^^^^^^^^^^^^^^^
...
380 |     async fn create_role_inheritance_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
420 |     async fn add_inheritance_policy(&self, child_role: &str, parent_role: &str, tenant_id: &str, config: &SyncConfig) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^
...
434 |     async fn create_basic_role_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
504 |     async fn create_tenant_role_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
512 |     async fn create_user_data_policies(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^
...
611 |     fn build_user_identity(&self, user_id: &Uuid, role_code: &str, target_type: &str, target_id: Option<&Uuid>) -> String {
    |        ^^^^^^^^^^^^^^^^^^^
...
619 |     async fn get_all_tenants(&self) -> Result<Vec<TenantInfo>> {
    |              ^^^^^^^^^^^^^^^

warning: struct `TenantInfo` is never constructed
   --> src/service/permission/role_sync_service.rs:639:8
    |
639 | struct TenantInfo {
    |        ^^^^^^^^^^

warning: field `id` is never read
  --> src/service/permission/postgres_adapter.rs:26:9
   |
25 | pub struct CasbinRule {
   |            ---------- field in this struct
26 |     pub id: Option<i32>,
   |         ^^
   |
   = note: `CasbinRule` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: fields `template_id` and `variables` are never read
  --> src/service/sms/sms_service.rs:25:9
   |
24 | pub struct SmsTemplate {
   |            ----------- fields in this struct
25 |     pub template_id: String,
   |         ^^^^^^^^^^^
26 |     pub content: String,
27 |     pub variables: Vec<String>,
   |         ^^^^^^^^^
   |
   = note: `SmsTemplate` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: methods `employee_id_exists`, `user_already_teacher`, and `employee_id_conflicts` are never used
   --> src/service/teacher/teacher_service.rs:212:14
    |
18  | impl TeacherService {
    | ------------------- methods in this implementation
...
212 |     async fn employee_id_exists(
    |              ^^^^^^^^^^^^^^^^^^
...
230 |     async fn user_already_teacher(
    |              ^^^^^^^^^^^^^^^^^^^^
...
248 |     async fn employee_id_conflicts(
    |              ^^^^^^^^^^^^^^^^^^^^^

warning: field `storage_service` is never read
  --> src/service/teaching_aids/teaching_aids_service.rs:17:5
   |
15 | pub struct TeachingAidsService {
   |            ------------------- field in this struct
16 |     pool: PgPool,
17 |     storage_service: Arc<dyn StorageService>,
   |     ^^^^^^^^^^^^^^^
   |
   = note: `TeachingAidsService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis

warning: associated functions `new`, `with_data`, and `without_data` are never used
   --> src/utils/api_response.rs:106:12
    |
104 | impl<T> SuccessResponse<T> {
    | -------------------------- associated functions in this implementation
105 |     /// 创建成功响应
106 |     pub fn new(data: Option<T>, message: String) -> Self {
    |            ^^^
...
115 |     pub fn with_data(data: T, message: Option<String>) -> Self {
    |            ^^^^^^^^^
...
124 |     pub fn without_data(message: Option<String>) -> Self {
    |            ^^^^^^^^^^^^

warning: associated functions `new` and `simple` are never used
   --> src/utils/api_response.rs:143:12
    |
141 | impl ErrorResponse {
    | ------------------ associated functions in this implementation
142 |     /// 创建错误响应
143 |     pub fn new(message: String, error_code: Option<String>) -> Self {
    |            ^^^
...
152 |     pub fn simple(message: String) -> Self {
    |            ^^^^^^

warning: type alias `ApiResult` is never used
   --> src/utils/api_response.rs:341:10
    |
341 | pub type ApiResult<T> = Result<ApiResponse<T>, AppError>;
    |          ^^^^^^^^^

warning: type alias `PaginatedApiResult` is never used
   --> src/utils/api_response.rs:342:10
    |
342 | pub type PaginatedApiResult<T> = Result<PaginatedApiResponse<T>, AppError>;
    |          ^^^^^^^^^^^^^^^^^^

warning: static `SCHEMA_NAME_REGEX` is never used
 --> src/utils/db.rs:8:8
  |
8 | static SCHEMA_NAME_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"^[a-z0-9_]+$").unwrap());
  |        ^^^^^^^^^^^^^^^^^

warning: struct `DynamicSchemaQuery` is never constructed
  --> src/utils/db.rs:11:12
   |
11 | pub struct DynamicSchemaQuery {
   |            ^^^^^^^^^^^^^^^^^^

warning: associated items `new`, `query_in_schema`, and `with_schema_transaction` are never used
  --> src/utils/db.rs:18:12
   |
16 | impl DynamicSchemaQuery {
   | ----------------------- associated items in this implementation
17 |     /// 创建新的动态 schema 查询构建器
18 |     pub fn new(pool: PgPool, schema_name: String) -> Result<Self> {
   |            ^^^
...
28 |     pub async fn query_in_schema<T>(&self, sql: &str) -> Result<Vec<T>>
   |                  ^^^^^^^^^^^^^^^
...
40 |     pub async fn with_schema_transaction<F, R, Fut>(&self, f: F) -> Result<R>
   |                  ^^^^^^^^^^^^^^^^^^^^^^^

warning: function `get_tenant_schema` is never used
  --> src/utils/db.rs:63:14
   |
63 | pub async fn get_tenant_schema(pool: &PgPool, tenant_id: Uuid) -> Result<String> {
   |              ^^^^^^^^^^^^^^^^^

warning: function `build_table_name` is never used
  --> src/utils/db.rs:86:8
   |
86 | pub fn build_table_name(schema_name: &str, table_name: &str) -> Result<String> {
   |        ^^^^^^^^^^^^^^^^

warning: struct `ErrorHandler` is never constructed
  --> src/utils/error_handler.rs:17:12
   |
17 | pub struct ErrorHandler;
   |            ^^^^^^^^^^^^

warning: associated functions `handle_json_rejection`, `log_and_respond`, and `log_and_respond_api` are never used
  --> src/utils/error_handler.rs:21:12
   |
19 | impl ErrorHandler {
   | ----------------- associated functions in this implementation
20 |     /// 处理 JSON 解析错误
21 |     pub fn handle_json_rejection(rejection: JsonRejection) -> AppError {
   |            ^^^^^^^^^^^^^^^^^^^^^
...
47 |     pub fn log_and_respond(error: &AppError, context: &str) -> Response {
   |            ^^^^^^^^^^^^^^^
...
66 |     pub fn log_and_respond_api<T>(error: &AppError, context: &str) -> ApiResponse<T> {
   |            ^^^^^^^^^^^^^^^^^^^

warning: methods `into_app_error_with_context`, `into_db_error`, and `into_validation_error` are never used
  --> src/utils/error_handler.rs:86:8
   |
84 | pub trait AnyhowResultExt<T> {
   |           --------------- methods in this trait
85 |     /// 将 anyhow::Result 转换为 AppResult，并添加上下文
86 |     fn into_app_error_with_context(self, context: &str) -> AppResult<T>;
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
89 |     fn into_db_error(self, operation: &str) -> AppResult<T>;
   |        ^^^^^^^^^^^^^
...
92 |     fn into_validation_error(self, field: &str) -> AppResult<T>;
   |        ^^^^^^^^^^^^^^^^^^^^^

warning: methods `with_db_context` and `ok_or_not_found` are never used
   --> src/utils/error_handler.rs:117:8
    |
115 | pub trait SqlxResultExt<T> {
    |           ------------- methods in this trait
116 |     /// 将 sqlx::Result 转换为 AppResult，并添加操作上下文
117 |     fn with_db_context(self, operation: &str) -> AppResult<T>;
    |        ^^^^^^^^^^^^^^^
...
120 |     fn ok_or_not_found(self, resource: &str) -> AppResult<T>
    |        ^^^^^^^^^^^^^^^

warning: function `validation_error` is never used
   --> src/utils/error_handler.rs:172:12
    |
172 |     pub fn validation_error(message: impl Into<String>) -> AppError {
    |            ^^^^^^^^^^^^^^^^

warning: function `not_found` is never used
   --> src/utils/error_handler.rs:176:12
    |
176 |     pub fn not_found(resource: impl Into<String>) -> AppError {
    |            ^^^^^^^^^

warning: function `forbidden` is never used
   --> src/utils/error_handler.rs:180:12
    |
180 |     pub fn forbidden(message: impl Into<String>) -> AppError {
    |            ^^^^^^^^^

warning: function `conflict` is never used
   --> src/utils/error_handler.rs:184:12
    |
184 |     pub fn conflict(message: impl Into<String>) -> AppError {
    |            ^^^^^^^^

warning: function `bad_request` is never used
   --> src/utils/error_handler.rs:188:12
    |
188 |     pub fn bad_request(message: impl Into<String>) -> AppError {
    |            ^^^^^^^^^^^

warning: function `internal_error` is never used
   --> src/utils/error_handler.rs:192:12
    |
192 |     pub fn internal_error(message: impl Into<String>) -> AppError {
    |            ^^^^^^^^^^^^^^

warning: function `generate_base_token_with_username` is never used
  --> src/utils/jwt.rs:36:8
   |
36 | pub fn generate_base_token_with_username(
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `generate_tenant_token` is never used
  --> src/utils/jwt.rs:69:8
   |
69 | pub fn generate_tenant_token(user_id: Uuid, tenant_context: TenantContext) -> AuthResult<String> {
   |        ^^^^^^^^^^^^^^^^^^^^^

warning: variants `HttpError` and `Timeout` are never constructed
  --> src/utils/http_client.rs:14:5
   |
10 | pub enum HttpClientError {
   |          --------------- variants in this enum
...
14 |     HttpError(u16),
   |     ^^^^^^^^^
15 |     #[error("Timeout exceeded")]
16 |     Timeout,
   |     ^^^^^^^
   |
   = note: `HttpClientError` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: function `get_request` is never used
  --> src/utils/http_client.rs:32:14
   |
32 | pub async fn get_request(
   |              ^^^^^^^^^^^

warning: function `role_auth_middleware` is never used
   --> src/middleware/auth_middleware.rs:139:14
    |
139 | pub async fn role_auth_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^

warning: function `get_auth_context` is never used
   --> src/middleware/auth_middleware.rs:319:8
    |
319 | pub fn get_auth_context(request: &Request) -> Result<&AuthContext, AppError> {
    |        ^^^^^^^^^^^^^^^^

warning: function `check_role_middleware` is never used
   --> src/middleware/auth_middleware.rs:362:14
    |
362 | pub async fn check_role_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^

warning: function `require_admin_middleware` is never used
   --> src/middleware/auth_middleware.rs:382:14
    |
382 | pub async fn require_admin_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `require_teacher_middleware` is never used
   --> src/middleware/auth_middleware.rs:401:14
    |
401 | pub async fn require_teacher_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `require_student_middleware` is never used
   --> src/middleware/auth_middleware.rs:418:14
    |
418 | pub async fn require_student_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `require_any_role_middleware` is never used
   --> src/middleware/auth_middleware.rs:436:14
    |
436 | pub async fn require_any_role_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `require_tenant_role_middleware` is never used
   --> src/middleware/auth_middleware.rs:458:14
    |
458 | pub async fn require_tenant_role_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: field `tenant_name` is never read
  --> src/middleware/tenant_middleware.rs:17:9
   |
14 | pub struct TenantContext {
   |            ------------- field in this struct
...
17 |     pub tenant_name: String,
   |         ^^^^^^^^^^^
   |
   = note: `TenantContext` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `tenant_identification_middleware` is never used
  --> src/middleware/tenant_middleware.rs:21:14
   |
21 | pub async fn tenant_identification_middleware(
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_tenant_context` is never used
  --> src/middleware/tenant_middleware.rs:41:10
   |
41 | async fn extract_tenant_context(
   |          ^^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_subdomain` is never used
   --> src/middleware/tenant_middleware.rs:106:4
    |
106 | fn extract_subdomain(host: &str) -> Option<String> {
    |    ^^^^^^^^^^^^^^^^^

warning: fields `skip_system_admin`, `log_permission_checks`, `enable_caching`, and `cache_ttl_seconds` are never read
  --> src/middleware/permission_middleware.rs:25:9
   |
24 | pub struct PermissionConfig {
   |            ---------------- fields in this struct
25 |     pub skip_system_admin: bool,      // 是否跳过系统管理员权限检查
   |         ^^^^^^^^^^^^^^^^^
26 |     pub log_permission_checks: bool,  // 是否记录权限检查日志
   |         ^^^^^^^^^^^^^^^^^^^^^
27 |     pub enable_caching: bool,         // 是否启用权限检查缓存
   |         ^^^^^^^^^^^^^^
28 |     pub cache_ttl_seconds: u64,       // 缓存TTL
   |         ^^^^^^^^^^^^^^^^^
   |
   = note: `PermissionConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: fields `user_id`, `tenant_id`, `user_identity`, `roles`, and `is_system_admin` are never read
  --> src/middleware/permission_middleware.rs:54:9
   |
53 | pub struct UserContext {
   |            ----------- fields in this struct
54 |     pub user_id: Uuid,
   |         ^^^^^^^
55 |     pub tenant_id: String,
   |         ^^^^^^^^^
56 |     pub user_identity: String,  // user_id:role:target_type:target_id
   |         ^^^^^^^^^^^^^
57 |     pub roles: Vec<String>,
   |         ^^^^^
58 |     pub is_system_admin: bool,
   |         ^^^^^^^^^^^^^^^
   |
   = note: `UserContext` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: associated items `from_auth_context` and `identity_string` are never used
  --> src/middleware/permission_middleware.rs:63:12
   |
61 | impl UserContext {
   | ---------------- associated items in this implementation
62 |     /// 从认证上下文构建用户上下文
63 |     pub fn from_auth_context(auth_context: &AuthContext, tenant_id: &str) -> Result<Self> {
   |            ^^^^^^^^^^^^^^^^^
...
96 |     pub fn identity_string(&self) -> String {
   |            ^^^^^^^^^^^^^^^

warning: function `permission_middleware` is never used
   --> src/middleware/permission_middleware.rs:103:14
    |
103 | pub async fn permission_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_resource_and_action` is never used
   --> src/middleware/permission_middleware.rs:185:4
    |
185 | fn extract_resource_and_action(request: &Request) -> Result<(String, String), StatusCode> {
    |    ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_resource_from_path` is never used
   --> src/middleware/permission_middleware.rs:197:4
    |
197 | fn extract_resource_from_path(path: &str) -> String {
    |    ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `http_method_to_action` is never used
   --> src/middleware/permission_middleware.rs:231:4
    |
231 | fn http_method_to_action(method: &Method) -> String {
    |    ^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_tenant_id` is never used
   --> src/middleware/permission_middleware.rs:242:4
    |
242 | fn extract_tenant_id(request: &Request) -> Result<String, StatusCode> {
    |    ^^^^^^^^^^^^^^^^^

warning: function `extract_tenant_from_path` is never used
   --> src/middleware/permission_middleware.rs:271:4
    |
271 | fn extract_tenant_from_path(path: &str) -> Option<String> {
    |    ^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `check_permission_with_caching` is never used
   --> src/middleware/permission_middleware.rs:282:10
    |
282 | async fn check_permission_with_caching(
    |          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `require_permission` is never used
   --> src/middleware/permission_middleware.rs:313:8
    |
313 | pub fn require_permission(resource: String, action: String) -> impl Fn(Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output...
    |        ^^^^^^^^^^^^^^^^^^

warning: function `menu_permission_middleware` is never used
   --> src/middleware/permission_middleware.rs:341:14
    |
341 | pub async fn menu_permission_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_menu_id_from_path` is never used
   --> src/middleware/permission_middleware.rs:384:4
    |
384 | fn extract_menu_id_from_path(path: &str) -> Option<String> {
    |    ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `data_permission_middleware` is never used
   --> src/middleware/permission_middleware.rs:399:14
    |
399 | pub async fn data_permission_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: field `0` is never read
   --> src/middleware/permission_middleware.rs:430:33
    |
430 | pub struct UserContextExtractor(pub UserContext);
    |            -------------------- ^^^^^^^^^^^^^^^
    |            |
    |            field in this struct
    |
    = help: consider removing this field

warning: field `0` is never read
   --> src/middleware/permission_middleware.rs:452:38
    |
452 | pub struct PermissionResultExtractor(pub PermissionCheckResult);
    |            ------------------------- ^^^^^^^^^^^^^^^^^^^^^^^^^
    |            |
    |            field in this struct
    |
    = help: consider removing this field

warning: `backend` (bin "backend") generated 441 warnings (5 duplicates) (run `cargo fix --bin "backend"` to apply 120 suggestions)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 0.44s
     Running `target/debug/backend`
[2m2025-08-06T12:11:55.370352Z[0m [32m INFO[0m 🚀 Starting Deep-Mate Backend Server
[2m2025-08-06T12:11:55.376408Z[0m [32m INFO[0m 📊 Connecting to database...
[2m2025-08-06T12:11:55.421019Z[0m [32m INFO[0m ✅ Database connection established
[2m2025-08-06T12:11:55.421058Z[0m [32m INFO[0m 🔄 Running database migrations...
[2m2025-08-06T12:11:55.426459Z[0m [32m INFO[0m relation "_sqlx_migrations" already exists, skipping
[2m2025-08-06T12:11:55.435390Z[0m [32m INFO[0m ✅ Database migrations completed
[2m2025-08-06T12:11:55.435414Z[0m [32m INFO[0m 🔐 Initializing authentication services...
[2m2025-08-06T12:11:55.448669Z[0m [32m INFO[0m ✅ Authentication services initialized
[2m2025-08-06T12:11:55.449410Z[0m [32m INFO[0m 📋 Schema config loaded: warmup_strategy=Lazy, auto_create=true
[2m2025-08-06T12:11:55.449430Z[0m [32m INFO[0m 📁 Initializing storage service...
[2m2025-08-06T12:11:55.548923Z[0m [32m INFO[0m ✅ Storage service initialized
[2m2025-08-06T12:11:55.548963Z[0m [32m INFO[0m 📁 Initializing grading service...
[2m2025-08-06T12:11:55.548986Z[0m [32m INFO[0m ✅ Grading service initialized
[2m2025-08-06T12:11:55.549037Z[0m [32m INFO[0m 🔐 Initializing Casbin permission service...
[2m2025-08-06T12:11:55.550953Z[0m [32m INFO[0m ✅ Casbin permission service initialized
[2m2025-08-06T12:11:55.572398Z[0m [32m INFO[0m 🌐 Server starting on http://0.0.0.0:3000
[2m2025-08-06T12:11:55.572672Z[0m [32m INFO[0m ✅ Deep-Mate Backend Server is running on http://0.0.0.0:3000
[2m2025-08-06T12:11:55.572726Z[0m [32m INFO[0m 📚 API Documentation available at http://0.0.0.0:3000/docs
[2m2025-08-06T12:12:27.006612Z[0m [32m INFO[0m Login attempt with username: principal_gzxxzz from IP: None, User-Agent: Some("curl/8.7.1")
[2m2025-08-06T12:12:27.064396Z[0m [32m INFO[0m User logged in successfully with username: principal_gzxxzz
[2m2025-08-06T12:12:27.064441Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:14:03.468199Z[0m [32m INFO[0m User ID: 11111111-1111-1111-1111-111111111111    
[2m2025-08-06T12:14:03.489895Z[0m [32m INFO[0m relation "casbin_policies" already exists, skipping
[2m2025-08-06T12:14:03.490620Z[0m [32m INFO[0m relation "idx_casbin_policies_ptype" already exists, skipping
[2m2025-08-06T12:14:03.491225Z[0m [32m INFO[0m relation "idx_casbin_policies_tenant" already exists, skipping
[2m2025-08-06T12:14:03.491633Z[0m [32m INFO[0m relation "idx_casbin_policies_v0" already exists, skipping
[2m2025-08-06T12:14:03.492029Z[0m [32m INFO[0m relation "idx_casbin_policies_v1" already exists, skipping
[2m2025-08-06T12:14:03.492461Z[0m [32m INFO[0m relation "idx_casbin_policies_lookup" already exists, skipping
[2m2025-08-06T12:14:03.492910Z[0m [32m INFO[0m relation "idx_casbin_policies_tenant_lookup" already exists, skipping
[2m2025-08-06T12:14:03.509152Z[0m [32m INFO[0m Loaded 119 policies for tenant: Some("7ff2e111-1ca4-4402-bc02-af69c1a7283c")
[2m2025-08-06T12:14:03.510777Z[0m [32m INFO[0m Created new enforcer for tenant: 7ff2e111-1ca4-4402-bc02-af69c1a7283c
Aug 06 12:14:03.548 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.578 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.610 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.639 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.667 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.693 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.720 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.746 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.774 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.801 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.828 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.854 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.881 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.908 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.935 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.962 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:03.988 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.013 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.038 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.064 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.089 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.115 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.141 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.166 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.191 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.216 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.242 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:14:04.267 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:14:04.294492Z[0m [32m INFO[0m Retrieved 29 menus for user 11111111-1111-1111-1111-111111111111 (admin: false) in 819ms
Aug 06 12:14:04.294 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:15:18.258790Z[0m [32m INFO[0m User ID: 11111111-1111-1111-1111-111111111111    
[2m2025-08-06T12:15:18.259420Z[0m [32m INFO[0m User ID: 11111111-1111-1111-1111-111111111111    
[2m2025-08-06T12:15:22.193271Z[0m [32m INFO[0m Login attempt with username: principal_gzxxzz from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:15:22.201380Z[0m [32m INFO[0m User logged in successfully with username: principal_gzxxzz
[2m2025-08-06T12:15:22.201423Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:15:22.225986Z[0m [32m INFO[0m User ID: 11111111-1111-1111-1111-111111111111    
[2m2025-08-06T12:15:22.226914Z[0m [32m INFO[0m User ID: 11111111-1111-1111-1111-111111111111    
[2m2025-08-06T12:15:22.231537Z[0m [32m INFO[0m Retrieved 1 roles for user 11111111-1111-1111-1111-111111111111 (admin: false) in 0ms
Aug 06 12:15:22.279 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.312 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.345 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.378 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.411 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.444 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.476 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.508 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.539 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.571 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.602 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.632 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.661 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.695 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.725 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.753 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.783 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.813 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.843 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.874 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.902 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.933 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.964 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:22.994 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:23.022 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:23.050 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:23.080 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:15:23.107 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:15:23.137235Z[0m [32m INFO[0m Retrieved 29 menus for user 11111111-1111-1111-1111-111111111111 (admin: false) in 907ms
Aug 06 12:15:23.137 INFO Enforce Request, Response: true, Cached: false, Request: user:11111111-1111-1111-1111-111111111111,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:15:23.140196Z[0m [32m INFO[0m User ID: 11111111-1111-1111-1111-111111111111    
[2m2025-08-06T12:15:23.141053Z[0m [32m INFO[0m User ID: 11111111-1111-1111-1111-111111111111    
[2m2025-08-06T12:15:23.146345Z[0m [32m INFO[0m User ID: 11111111-1111-1111-1111-111111111111    
[2m2025-08-06T12:15:23.151668Z[0m [32m INFO[0m relation "casbin_policies" already exists, skipping
[2m2025-08-06T12:15:23.152287Z[0m [32m INFO[0m relation "idx_casbin_policies_ptype" already exists, skipping
[2m2025-08-06T12:15:23.152600Z[0m [32m INFO[0m relation "idx_casbin_policies_tenant" already exists, skipping
[2m2025-08-06T12:15:23.152971Z[0m [32m INFO[0m relation "idx_casbin_policies_v0" already exists, skipping
[2m2025-08-06T12:15:23.153694Z[0m [32m INFO[0m relation "idx_casbin_policies_v1" already exists, skipping
[2m2025-08-06T12:15:23.154188Z[0m [32m INFO[0m relation "idx_casbin_policies_lookup" already exists, skipping
[2m2025-08-06T12:15:23.154521Z[0m [32m INFO[0m relation "idx_casbin_policies_tenant_lookup" already exists, skipping
[2m2025-08-06T12:15:23.157719Z[0m [32m INFO[0m Loaded 0 policies for tenant: Some("")
[2m2025-08-06T12:15:23.157811Z[0m [32m INFO[0m Created new enforcer for tenant: 
[2m2025-08-06T12:15:23.158255Z[0m [32m INFO[0m Retrieved 0 data scopes for user 11111111-1111-1111-1111-111111111111 -> resource menu in 7ms
[2m2025-08-06T12:15:48.133371Z[0m [32m INFO[0m User ID: 11111111-1111-1111-1111-111111111111    
[2m2025-08-06T12:15:48.135815Z[0m [32m INFO[0m User ID: 11111111-1111-1111-1111-111111111111    
[2m2025-08-06T12:16:17.211155Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:16:17.223821Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-06T12:16:17.223861Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:16:17.243722Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:16:17.244721Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:16:17.247404Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:16:17.264 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.274 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.283 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.292 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.302 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.311 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.320 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.330 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.339 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.350 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.359 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.369 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.379 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.388 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.398 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.407 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.417 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.426 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.436 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.445 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.454 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.464 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.473 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.482 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.491 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.501 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.510 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:16:17.519 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:16:17.529544Z[0m [32m INFO[0m Retrieved 29 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 283ms
Aug 06 12:16:17.529 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:16:17.532167Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:16:17.532964Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:16:17.537944Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:16:17.539994Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:16:58.724978Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:16:58.728181Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:10.319806Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:11.818022Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:14.759169Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:14.780378Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:19.503125Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:19.514086Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:20.608414Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:20.611294Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:20.613344Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:20.620091Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:20.622719Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:20.623854Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:20.626714Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
[2m2025-08-06T12:17:20.654458Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:17:20.679 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.688 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.689 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.698 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.699 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.707 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.708 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.717 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.718 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.727 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.728 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.736 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.737 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.747 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.748 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.757 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.758 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.769 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.769 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.778 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.778 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.788 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.789 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.798 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.798 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.807 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.808 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.819 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.819 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.828 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.828 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.839 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.839 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.849 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.849 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.858 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.859 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.869 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.870 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.878 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.879 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.888 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.888 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.899 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.899 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.908 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.908 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.918 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.918 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.927 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.927 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.938 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.939 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.949 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.949 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:17:20.958510Z[0m [32m INFO[0m Retrieved 29 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 344ms
Aug 06 12:17:20.958 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.958 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:17:20.969 INFO[2m2025-08-06T12:17:20.969604Z[0m [32m INFO[0m Retrieved 29 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 310ms
 Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:17:21.019643Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:21.023526Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:17:21.090493Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:17:21.095099Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:18:04.048638Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:18:04.063894Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-06T12:18:04.063945Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:18:04.089948Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:18:04.090857Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:18:04.093375Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:18:04.114 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.123 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.132 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.142 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.151 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.161 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.171 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.180 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.190 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.199 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.209 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.218 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.228 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.237 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.247 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.256 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.265 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.275 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.284 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.293 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.303 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.312 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.322 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.331 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.340 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.350 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.359 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:18:04.368 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:18:04.377304Z[0m [32m INFO[0m Retrieved 29 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 282ms
Aug 06 12:18:04.377 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:18:04.379984Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:18:04.380442Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:18:04.386104Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:18:04.388115Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:18:40.132757Z[0m [32m INFO[0m Login attempt with username: admin from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:18:40.145490Z[0m [32m INFO[0m User logged in successfully with username: admin
[2m2025-08-06T12:18:40.145531Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:18:40.166884Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:18:40.167678Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:18:40.172861Z[0m [32m INFO[0m Retrieved 1 roles for user 8cc477db-2edd-47c1-80d6-44171a49af44 (admin: true) in 0ms
[2m2025-08-06T12:18:40.223094Z[0m [32m INFO[0m Retrieved 31 menus for user 8cc477db-2edd-47c1-80d6-44171a49af44 (admin: true) in 53ms
[2m2025-08-06T12:18:40.227987Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:18:40.229775Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:18:40.235784Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:18:40.237649Z[0m [32m INFO[0m Retrieved 0 data scopes for user 8cc477db-2edd-47c1-80d6-44171a49af44 -> resource menu in 0ms
[2m2025-08-06T12:18:48.151924Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:18:48.154925Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:18:48.156502Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:18:48.157417Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:18:48.161059Z[0m [32m INFO[0m Retrieved 10 permission templates for admin user 8cc477db-2edd-47c1-80d6-44171a49af44 in 3ms
[2m2025-08-06T12:18:48.163035Z[0m [32m INFO[0m Retrieved 10 permission templates for admin user 8cc477db-2edd-47c1-80d6-44171a49af44 in 2ms
[2m2025-08-06T12:18:48.187756Z[0m [32m INFO[0m Retrieved 31 menus for admin user 8cc477db-2edd-47c1-80d6-44171a49af44 in 33ms
[2m2025-08-06T12:18:48.190766Z[0m [32m INFO[0m Retrieved 31 menus for admin user 8cc477db-2edd-47c1-80d6-44171a49af44 in 32ms
[2m2025-08-06T12:20:19.443614Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:20:19.453897Z[0m [32m INFO[0m Successfully updated menu teaching_classes_management by admin user 8cc477db-2edd-47c1-80d6-44171a49af44
[2m2025-08-06T12:20:19.459282Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-06T12:20:19.486631Z[0m [32m INFO[0m Retrieved 31 menus for admin user 8cc477db-2edd-47c1-80d6-44171a49af44 in 25ms
[2m2025-08-06T12:20:29.099070Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:20:29.110106Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-06T12:20:29.110152Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:20:29.128709Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:20:29.130234Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:20:29.133307Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:20:29.149 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.158 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.167 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.177 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.186 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:20:29.195 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.204 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.214 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.223 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.233 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.244 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.253 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.263 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.272 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.281 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.291 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.300 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.309 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.318 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.328 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.337 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.347 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.356 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.366 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.375 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.384 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.394 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:20:29.402 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:20:29.413204Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 281ms
Aug 06 12:20:29.413 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:20:29.415726Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:20:29.416329Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:20:29.422918Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:20:29.425426Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:21:49.940569Z[0m [32m INFO[0m Login attempt with username: academic_director_gzxxzz from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:21:49.941908Z[0m [31mERROR[0m Login failed: UserNotFound
[2m2025-08-06T12:21:55.073844Z[0m [32m INFO[0m Login attempt with username: academic_director_gzxxzz from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:21:55.082228Z[0m [31mERROR[0m Login failed: UserNotFound
[2m2025-08-06T12:21:55.753471Z[0m [32m INFO[0m Login attempt with username: academic_director_gzxxzz from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:21:55.754470Z[0m [31mERROR[0m Login failed: UserNotFound
[2m2025-08-06T12:21:55.955520Z[0m [32m INFO[0m Login attempt with username: academic_director_gzxxzz from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:21:55.963082Z[0m [31mERROR[0m Login failed: UserNotFound
[2m2025-08-06T12:22:22.675292Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:22:22.684646Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-06T12:22:22.684687Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:22:22.713858Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:22:22.715060Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:22:22.717126Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:22:22.734 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.745 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.755 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.764 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.773 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:22:22.783 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.792 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.802 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.811 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.821 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.830 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.840 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.849 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.858 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.868 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.877 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.887 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.896 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.905 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.915 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.924 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.934 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.943 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.952 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.961 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.971 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.981 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:22:22.990 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:22:22.999114Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 283ms
Aug 06 12:22:22.999 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:22:23.001663Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:22:23.002181Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:22:23.007728Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:22:23.009725Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:23:17.379269Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:23:17.383620Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-06T12:23:17.383664Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:23:17.410689Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:17.414150Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:17.416972Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:17.417231Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
[2m2025-08-06T12:23:17.418158Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
Aug 06 12:23:17.437 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.446 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.456 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.466 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.475 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:23:17.484 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.494 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.504 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.514 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.524 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.533 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.542 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.551 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.561 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.570 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.579 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.589 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.598 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.608 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.617 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.627 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.636 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.645 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.655 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.664 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.673 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.683 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:17.692 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:23:17.702507Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 289ms
Aug 06 12:23:17.702 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:23:17.719925Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:17.722251Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:23:32.844876Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:32.847623Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:34.056393Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:34.058399Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:34.060581Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:34.063105Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:34.063102Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
[2m2025-08-06T12:23:34.064499Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:34.067985Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:34.070318Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:23:34.085 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.089 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.095 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.098 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.104 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.109 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.114 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.119 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.123 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:23:34.127 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:23:34.133 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.137 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.143 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.146 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.153 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.157 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.162 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.165 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.172 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.175 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.182 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.187 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.192 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.196 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.203 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.208 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.213 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.218 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.225 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.229 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.234 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.240 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.245 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.251 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.256 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.261 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.266 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.271 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.276 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.280 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.286 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.290 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.295 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.299 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.305 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.309 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.314 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.319 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.324 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.329 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.333 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.338 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.343 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.348 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.353 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.358 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:23:34.362 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:23:34.362527Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 301ms
Aug 06 12:23:34.370 INFO Enforce Request, Response: true, [2m2025-08-06T12:23:34.370193Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 302ms
Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:23:34.423308Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:34.425504Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:23:34.440225Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:23:34.442018Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:25:40.366078Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("curl/8.7.1")
[2m2025-08-06T12:25:40.370426Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-06T12:25:40.370466Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:26:03.407534Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
Aug 06 12:26:03.428 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.437 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.447 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.460 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.469 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:26:03.478 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.487 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.497 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.506 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.515 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.524 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.534 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.550 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.560 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.568 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.578 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.585 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.594 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.603 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.611 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.620 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.629 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.637 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.646 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.654 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.663 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.672 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.680 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:26:03.689[2m2025-08-06T12:26:03.689566Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 279ms
 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[src/middleware/auth_middleware.rs:338:9] e = JwtError(
    Error(
        InvalidSignature,
    ),
)
[2m2025-08-06T12:29:32.986143Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:32.987480Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:32.992061Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:32.993162Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:32.995094Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
[2m2025-08-06T12:29:33.000172Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:33.000962Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:33.004033Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:29:33.014 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.025 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.026 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.035 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.036 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.048 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.048 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.057 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:29:33.058 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.066 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.067 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:29:33.076 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.077 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.085 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.086 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.095 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.096 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.105 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.106 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.114 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.115 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.124 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.124 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.133 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.134 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.146 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.146 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.157 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.158 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.166 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.167 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.178 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.178 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.188 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.188 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.197 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.197 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.207 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.208 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.216 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.216 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.224 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.225 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.233 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.234 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.244 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.245 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.253 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.255 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.262 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.263 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.271 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.273 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.281 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.282 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.291 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.292 INFO Enforce Request, Response[2m2025-08-06T12:29:33.292695Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 302ms
: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:33.301 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:29:33.301355Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 297ms
[2m2025-08-06T12:29:33.479138Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:33.481816Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:29:33.482341Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:33.484616Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:29:56.599736Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:56.605136Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:56.607079Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:56.609350Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:29:56.629 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.639 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.650 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.660 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.669 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:29:56.679 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.688 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.698 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.708 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.718 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.728 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.738 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.748 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.759 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.768 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.779 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.789 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.799 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.809 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.819 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.829 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.839 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.849 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.859 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.869 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.879 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.888 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.897 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.908 INFO Enforce Request, Response: [2m2025-08-06T12:29:56.908941Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 300ms
true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:29:56.910063Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:56.910063Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:56.910124Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:56.913655Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:29:56.937 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.948 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.957 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.967 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.976 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:29:56.985 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:56.995 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.006 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.015 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.026 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.036 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.046 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.058 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.068 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.077 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.087 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.097 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.108 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.118 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.128 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.137 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.147 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.157 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.166 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.176 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.185 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.195 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:29:57.204 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:29:57.214576Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 301ms
Aug 06 12:29:57.214 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:29:57.217039Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:57.219307Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:29:57.261654Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:29:57.263340Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:30:59.581723Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:30:59.585753Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-06T12:30:59.585792Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:30:59.614859Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:30:59.615646Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:30:59.618043Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
[2m2025-08-06T12:30:59.624947Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:30:59.626870Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
Aug 06 12:30:59.636 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.647 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.657 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.667 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.678 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:30:59.687 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.697 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.706 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.716 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.726 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.735 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.745 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.755 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.765 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.775 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.784 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.793 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.803 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.812 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.822 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.831 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.842 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.851 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.861 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.870 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.880 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.889 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:30:59.899 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:30:59.909084Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 291ms
Aug 06 12:30:59.909 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:30:59.917239Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:30:59.919623Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:33:25.560885Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:33:25.562151Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:33:29.362701Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:33:29.366312Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-06T12:33:29.366343Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:33:29.386204Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:33:29.387033Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:33:29.389167Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:33:29.407 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.416 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.426 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.435 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.444 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:33:29.454 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.463 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.472 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.482 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.491 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.501 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.511 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.520 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.530 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.539 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.549 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.558 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.567 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.576 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.585 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.595 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.603 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.613 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.621 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.631 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.639 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.649 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:33:29.657 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:33:29.665835Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 277ms
Aug 06 12:33:29.665 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:33:29.668498Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:33:29.669255Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:33:29.677709Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:33:29.679473Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:34:46.402893Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:34:46.403874Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:34:58.320855Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-06T12:34:58.324811Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-06T12:34:58.324853Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-06T12:34:58.348229Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:34:58.348949Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:34:58.351499Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 06 12:34:58.371 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.381 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.390 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.400 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.409 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 06 12:34:58.418 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.427 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.436 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.446 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.455 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.464 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.474 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.483 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.492 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.502 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.513 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.522 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.531 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.541 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.550 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.559 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.568 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.577 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.587 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.596 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.605 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.615 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 06 12:34:58.624 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:34:58.633538Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 282ms
Aug 06 12:34:58.633 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-06T12:34:58.636592Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:34:58.637098Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:34:58.642671Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:34:58.644664Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-06T12:35:33.381621Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:35:33.382888Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:36:16.280097Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:36:16.281317Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:36:23.292765Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-06T12:36:23.294174Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T01:54:08.813259Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T01:54:08.826949Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T01:54:13.407928Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-07T01:54:13.429606Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-07T01:54:13.429648Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-07T01:54:13.476696Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T01:54:13.477355Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T01:54:13.480915Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
[2m2025-08-07T01:54:13.495960Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T01:54:13.498389Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
Aug 07 01:54:13.532 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.543 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.555 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.566 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.576 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 07 01:54:13.585 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.594 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.604 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.614 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.623 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.632 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.642 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.651 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.661 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.670 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.679 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.689 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.698 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.708 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.717 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.726 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.736 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.746 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.756 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.765 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.775 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.784 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.793 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 01:54:13.802 INFO Enforce Request, Response[2m2025-08-07T01:54:13.802741Z[0m [32m INFO[0m Retrieved 28 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 322ms
: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-07T01:54:13.813272Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T01:54:13.817943Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-07T01:57:42.049363Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T01:57:42.050999Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T01:57:47.528774Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T01:57:47.529977Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:08:00.131593Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:08:00.132825Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:08:05.754319Z[0m [32m INFO[0m Login attempt with username: admin from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-07T02:08:05.766685Z[0m [32m INFO[0m User logged in successfully with username: admin
[2m2025-08-07T02:08:05.766726Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-07T02:08:05.786658Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:05.787413Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:05.790822Z[0m [32m INFO[0m Retrieved 1 roles for user 8cc477db-2edd-47c1-80d6-44171a49af44 (admin: true) in 0ms
[2m2025-08-07T02:08:05.797958Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:05.799453Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:05.848076Z[0m [32m INFO[0m Retrieved 31 menus for user 8cc477db-2edd-47c1-80d6-44171a49af44 (admin: true) in 58ms
[2m2025-08-07T02:08:05.856879Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:05.859872Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:05.860997Z[0m [32m INFO[0m Retrieved 0 data scopes for user 8cc477db-2edd-47c1-80d6-44171a49af44 -> resource menu in 0ms
[2m2025-08-07T02:08:05.861000Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:05.862143Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:05.862157Z[0m [32m INFO[0m Retrieved 0 data scopes for user 8cc477db-2edd-47c1-80d6-44171a49af44 -> resource student in 0ms
[2m2025-08-07T02:08:05.864135Z[0m [32m INFO[0m Retrieved 0 data scopes for user 8cc477db-2edd-47c1-80d6-44171a49af44 -> resource grade in 0ms
[2m2025-08-07T02:08:05.866011Z[0m [32m INFO[0m Retrieved 0 data scopes for user 8cc477db-2edd-47c1-80d6-44171a49af44 -> resource exam in 0ms
[2m2025-08-07T02:08:08.875320Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:08.877447Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:08.878300Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:08.879599Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:08.890019Z[0m [32m INFO[0m Retrieved 10 permission templates for admin user 8cc477db-2edd-47c1-80d6-44171a49af44 in 5ms
[2m2025-08-07T02:08:08.916538Z[0m [32m INFO[0m Retrieved 10 permission templates for admin user 8cc477db-2edd-47c1-80d6-44171a49af44 in 3ms
[2m2025-08-07T02:08:08.921095Z[0m [32m INFO[0m Retrieved 31 menus for admin user 8cc477db-2edd-47c1-80d6-44171a49af44 in 36ms
[2m2025-08-07T02:08:08.928208Z[0m [32m INFO[0m Retrieved 31 menus for admin user 8cc477db-2edd-47c1-80d6-44171a49af44 in 43ms
[2m2025-08-07T02:08:54.645497Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:54.666602Z[0m [32m INFO[0m Successfully updated menu student_management by admin user 8cc477db-2edd-47c1-80d6-44171a49af44
[2m2025-08-07T02:08:54.672879Z[0m [32m INFO[0m User ID: 8cc477db-2edd-47c1-80d6-44171a49af44    
[2m2025-08-07T02:08:54.704283Z[0m [32m INFO[0m Retrieved 31 menus for admin user 8cc477db-2edd-47c1-80d6-44171a49af44 in 29ms
[2m2025-08-07T02:09:12.275721Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-07T02:09:12.286648Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-07T02:09:12.286691Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-07T02:09:12.311247Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:12.311989Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:12.314382Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 07 02:09:12.333 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.343 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.353 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.362 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.372 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 07 02:09:12.381 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.391 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.400 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.409 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.419 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.428 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.438 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.447 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.456 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.465 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.474 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.484 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.493 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.502 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 07 02:09:12.511 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.521 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.530 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.539 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.548 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.558 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.566 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.575 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.584 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:12.593 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access[2m2025-08-07T02:09:12.593027Z[0m [32m INFO[0m Retrieved 27 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 278ms

[2m2025-08-07T02:09:12.596694Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:12.597450Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:12.605814Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:12.608508Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-07T02:09:42.006918Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:42.007861Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:46.350212Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-07T02:09:46.360523Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-07T02:09:46.360565Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-07T02:09:46.384563Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:46.385096Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:46.387026Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
[2m2025-08-07T02:09:46.393862Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:46.395265Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
Aug 07 02:09:46.409 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.419 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.429 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.438 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.447 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 07 02:09:46.456 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.465 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.474 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.484 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.493 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.503 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.512 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.522 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.530 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.539 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.549 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.558 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.568 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.576 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 07 02:09:46.585 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.593 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.603 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.610 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.621 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.629 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.638 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.646 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:09:46.656 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-07T02:09:46.664762Z[0m [32m INFO[0m Retrieved 27 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 276ms
Aug 07 02:09:46.664 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-07T02:09:46.672931Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:09:46.675021Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-07T02:10:44.386279Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:10:44.387373Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:11:06.999468Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-07T02:11:07.004475Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-07T02:11:07.004519Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-07T02:11:07.037499Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:11:07.038384Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:11:07.040984Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 07 02:11:07.061 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.071 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.080 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.090 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.099 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 07 02:11:07.109 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.118 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.128 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.138 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.148 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.158 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.167 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.177 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.186 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.195 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.204 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.214 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.224 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.233 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 07 02:11:07.243 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.252 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.262 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.272 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.281 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.290 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.299 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.308 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:11:07.318 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-07T02:11:07.327318Z[0m [32m INFO[0m Retrieved 27 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 287ms
Aug 07 02:11:07.327 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-07T02:11:07.330603Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:11:07.331167Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:11:07.339686Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:11:07.342801Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
[2m2025-08-07T02:18:48.862258Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:18:48.862984Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:18:54.017740Z[0m [32m INFO[0m Login attempt with username: class_teacher_2301 from IP: None, User-Agent: Some("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:142.0) Gecko/20100101 Firefox/142.0")
[2m2025-08-07T02:18:54.023587Z[0m [32m INFO[0m User logged in successfully with username: class_teacher_2301
[2m2025-08-07T02:18:54.023629Z[0m [32m INFO[0m User logged in successfully
[2m2025-08-07T02:18:54.045927Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:18:54.047036Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:18:54.049672Z[0m [32m INFO[0m Retrieved 1 roles for user 66666666-6666-6666-6666-666666666666 (admin: false) in 0ms
Aug 07 02:18:54.068 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.078 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.088 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.098 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.107 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 07 02:18:54.116 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.125 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.136 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.146 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.155 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.165 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.174 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.183 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.192 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.202 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.211 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.220 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.229 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.237 INFO Enforce Request, Response: false, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,student:read,read
Aug 07 02:18:54.248 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.256 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.266 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.274 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.283 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.292 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.302 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.310 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
Aug 07 02:18:54.319 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-07T02:18:54.328029Z[0m [32m INFO[0m Retrieved 27 menus for user 66666666-6666-6666-6666-666666666666 (admin: false) in 279ms
Aug 07 02:18:54.328 INFO Enforce Request, Response: true, Cached: false, Request: user:66666666-6666-6666-6666-666666666666,7ff2e111-1ca4-4402-bc02-af69c1a7283c,menu:access,access
[2m2025-08-07T02:18:54.330857Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:18:54.331433Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:18:54.337698Z[0m [32m INFO[0m User ID: 66666666-6666-6666-6666-666666666666    
[2m2025-08-07T02:18:54.339537Z[0m [32m INFO[0m Retrieved 0 data scopes for user 66666666-6666-6666-6666-666666666666 -> resource menu in 0ms
