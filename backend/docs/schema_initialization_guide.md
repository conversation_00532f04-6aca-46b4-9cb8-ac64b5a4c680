# Schema 初始化解决方案指南

本文档详细说明如何解决程序启动时租户 schema 不存在导致的编译错误问题。

## 问题描述

在多租户系统中，常见的问题包括：

1. **编译时检查失败**: `sqlx::query!` 宏在编译时连接数据库验证 SQL，但租户 schema 可能不存在
2. **动态创建的租户**: 新租户的 schema 在程序启动后才创建
3. **开发环境问题**: 开发者本地数据库可能没有完整的租户数据

## 解决方案架构

### 1. Schema 管理器 (SchemaManager)

核心组件，负责：
- 检查和创建缺失的 schema
- 缓存已验证的 schema
- 提供懒加载机制

```rust
use crate::service::schema_manager::SchemaManager;

let schema_manager = SchemaManager::new(pool, Some("path/to/template.migrations_temp"));

// 确保 schema 存在
schema_manager.ensure_schema_exists("tenant_001").await?;
```

### 2. 管理的查询服务 (ManagedQueryService)

包装了 Schema 管理器的查询服务：

```rust
use crate::service::schema_manager::ManagedQueryService;

let managed_query = ManagedQueryService::new(pool, schema_manager);

// 自动确保 schema 存在后执行查询
let students = managed_query
    .execute_tenant_query("tenant_001", |tx| {
        Box::pin(async move {
            sqlx::query_as!(Student, "SELECT * FROM students")
                .fetch_all(&mut **tx)
                .await
        })
    })
    .await?;
```

### 3. 配置驱动的初始化策略

通过环境变量控制不同环境下的行为：

```bash
# 开发环境 - 懒加载
RUST_ENV=development
SCHEMA_WARMUP_STRATEGY=lazy
SCHEMA_AUTO_CREATE=true

# 生产环境 - 预热所有
RUST_ENV=production
SCHEMA_WARMUP_STRATEGY=all
SCHEMA_AUTO_CREATE=false

# 测试环境 - 不预热
RUST_ENV=test
SCHEMA_WARMUP_STRATEGY=none
SCHEMA_AUTO_CREATE=true
```

## 使用方法

### 1. 基本设置

在 `main.rs` 中初始化：

```rust
#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // ... 数据库连接初始化 ...
    
    // 加载 Schema 配置
    let schema_config = crate::config::get_schema_config();
    
    // 初始化 Schema 管理器
    let schema_manager = Arc::new(SchemaManager::new(
        pool.clone(), 
        Some(schema_config.tenant_template_path.clone())
    ));
    
    // 根据配置进行预热
    match schema_config.warmup_strategy {
        WarmupStrategy::None => {
            info!("Skipping schema warmup");
        }
        _ => {
            schema_manager.warmup().await?;
        }
    }
    
    // 将 schema_manager 传递给应用状态
    let state = AppState {
        db: pool,
        schema_manager,
        // ... 其他服务 ...
    };
    
    // ... 启动服务器 ...
}
```

### 2. 在服务中使用

#### 方法 A: 使用管理的查询服务（推荐）

```rust
pub struct StudentService {
    managed_query: ManagedQueryService,
}

impl StudentService {
    pub fn new(pool: PgPool, schema_manager: Arc<SchemaManager>) -> Self {
        Self {
            managed_query: ManagedQueryService::new(pool, schema_manager),
        }
    }
    
    pub async fn get_students(&self, tenant_schema: &str) -> Result<Vec<Student>> {
        // 自动确保 schema 存在，然后执行查询
        self.managed_query
            .execute_tenant_query(tenant_schema, |tx| {
                Box::pin(async move {
                    sqlx::query_as!(
                        Student,
                        "SELECT id, name, student_number FROM students"
                    )
                    .fetch_all(&mut **tx)
                    .await
                })
            })
            .await
    }
}
```

#### 方法 B: 手动检查 schema

```rust
pub async fn get_students_manual(&self, tenant_schema: &str) -> Result<Vec<Student>> {
    // 手动确保 schema 存在
    self.schema_manager.ensure_schema_exists(tenant_schema).await?;
    
    // 执行查询
    let mut tx = self.pool.begin().await?;
    sqlx::query(&format!(r#"SET LOCAL search_path TO "{}""#, tenant_schema))
        .execute(&mut *tx)
        .await?;
    
    let students = sqlx::query_as!(
        Student,
        "SELECT id, name, student_number FROM students"
    )
    .fetch_all(&mut *tx)
    .await?;
    
    tx.commit().await?;
    Ok(students)
}
```

### 3. 租户创建时的处理

```rust
pub async fn create_tenant(&self, request: CreateTenantRequest) -> Result<Tenant> {
    let schema_name = format!("tenant_{}", request.code);
    
    // 1. 创建租户记录
    let tenant = self.create_tenant_record(&request, &schema_name).await?;
    
    // 2. 初始化 schema
    self.schema_manager
        .initialize_tenant_schema_from_file(&schema_name, &self.template_path)
        .await?;
    
    // 3. 标记为已验证（添加到缓存）
    self.schema_manager.mark_schema_as_verified(&schema_name).await;
    
    Ok(tenant)
}
```

## 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SCHEMA_AUTO_CREATE` | `true` | 是否自动创建缺失的 schema |
| `TENANT_TEMPLATE_PATH` | `tenants/template/init_tenant_schema.sql` | 租户模板文件路径 |
| `SCHEMA_WARMUP_STRATEGY` | `all` | 预热策略：`none`/`all`/`active`/`lazy` |
| `SCHEMA_ENABLE_CACHE` | `true` | 是否启用 schema 缓存 |
| `SCHEMA_CHECK_INTERVAL` | `300` | Schema 检查间隔（秒） |

### 预热策略说明

- **None**: 不进行预热，适用于测试环境
- **All**: 预热所有已注册的租户 schema，适用于生产环境
- **Active**: 仅预热活跃的租户 schema
- **Lazy**: 懒加载，仅在需要时创建，适用于开发环境

## 最佳实践

### 1. 开发环境

```bash
# .env.development
RUST_ENV=development
SCHEMA_WARMUP_STRATEGY=lazy
SCHEMA_AUTO_CREATE=true
SCHEMA_CHECK_INTERVAL=60
```

**优点**: 快速启动，按需创建
**适用场景**: 本地开发，频繁重启

### 2. 生产环境

```bash
# .env.production
RUST_ENV=production
SCHEMA_WARMUP_STRATEGY=all
SCHEMA_AUTO_CREATE=false
SCHEMA_CHECK_INTERVAL=600
```

**优点**: 启动时确保所有 schema 就绪，避免运行时创建
**适用场景**: 生产部署，稳定性优先

### 3. 测试环境

```bash
# .env.test
RUST_ENV=test
SCHEMA_WARMUP_STRATEGY=none
SCHEMA_AUTO_CREATE=true
SCHEMA_ENABLE_CACHE=false
```

**优点**: 每次测试都是干净的环境
**适用场景**: 自动化测试，CI/CD

## 故障排除

### 1. 编译时错误

**错误**: `sqlx::query!` 宏编译失败，提示表不存在

**解决方案**:
1. 使用 `sqlx::query` 替代 `sqlx::query!`
2. 或者使用 `ManagedQueryService` 在运行时确保 schema 存在

### 2. 启动时间过长

**原因**: 预热策略设置为 `all`，但有大量租户

**解决方案**:
1. 改为 `lazy` 策略
2. 或者使用 `active` 策略仅预热活跃租户
3. 增加 `max_concurrent_schema_creation` 配置

### 3. Schema 创建失败

**常见原因**:
1. 模板文件路径错误
2. 数据库权限不足
3. 模板 SQL 语法错误

**调试方法**:
1. 检查日志中的详细错误信息
2. 验证模板文件是否存在且可读
3. 手动执行模板 SQL 验证语法

## 性能考虑

### 1. 缓存策略

- 启用 schema 缓存减少重复检查
- 定期清理缓存避免内存泄漏

### 2. 并发控制

- 限制同时创建的 schema 数量
- 使用连接池避免连接耗尽

### 3. 监控指标

- Schema 创建成功/失败次数
- 预热耗时
- 缓存命中率

## 总结

通过 Schema 管理器和配置驱动的初始化策略，我们可以：

1. **解决编译时问题**: 运行时确保 schema 存在
2. **提高开发效率**: 懒加载策略快速启动
3. **保证生产稳定**: 预热策略确保就绪
4. **灵活配置**: 不同环境使用不同策略

这套解决方案既保持了 Sqlx 的类型安全优势，又解决了多租户环境下的实际问题。
