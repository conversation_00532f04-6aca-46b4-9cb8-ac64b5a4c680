# 统一API响应格式指南

## 概述

本项目采用统一的API响应格式，确保前后端接口的一致性和可维护性。所有API响应都遵循相同的结构，便于前端处理和错误管理。

## 响应结构

### 标准响应格式

```json
{
  "success": true,
  "data": { /* 响应数据 */ },
  "message": "操作成功",
  "error_code": null,
  "timestamp": 1640995200
}
```

### 错误响应格式

```json
{
  "success": false,
  "data": null,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": 1640995200
}
```

### 分页响应格式

```json
{
  "success": true,
  "data": [/* 数据列表 */],
  "message": "查询成功",
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 100,
    "total_pages": 10,
    "has_next": true,
    "has_prev": false
  },
  "error_code": null,
  "timestamp": 1640995200
}
```

## 后端使用方法

### 1. 导入必要的模块

```rust
use crate::utils::api_response::{ApiResponse, PaginatedApiResponse, responses};
use crate::utils::error_handler::ErrorHandler;
use axum::response::IntoResponse;
```

### 2. 标准响应

```rust
// 成功响应（带数据）
pub async fn get_user(id: Uuid) -> impl IntoResponse {
    match user_service.get_user(id).await {
        Ok(user) => responses::success(user, Some("用户获取成功")),
        Err(e) => ErrorHandler::log_and_respond_api(&e, "get_user"),
    }
}

// 成功响应（无数据）
pub async fn delete_user(id: Uuid) -> impl IntoResponse {
    match user_service.delete_user(id).await {
        Ok(_) => responses::success_no_data(Some("用户删除成功")),
        Err(e) => ErrorHandler::log_and_respond_api(&e, "delete_user"),
    }
}
```

### 3. 分页响应

```rust
pub async fn list_users(
    Query(params): Query<PageParams>,
) -> impl IntoResponse {
    let page = params.get_page();
    let page_size = params.get_page_size();
    
    match user_service.list_users(page, page_size).await {
        Ok((users, total)) => {
            responses::paginated_success(
                users,
                page,
                page_size,
                total,
                Some("用户列表获取成功")
            )
        }
        Err(e) => PaginatedApiResponse::from_error(&e),
    }
}
```

### 4. 错误处理

```rust
// 自动错误处理（推荐）
pub async fn create_user(Json(request): Json<CreateUserRequest>) -> impl IntoResponse {
    match user_service.create_user(request).await {
        Ok(user) => responses::success(user, Some("用户创建成功")),
        Err(e) => ErrorHandler::log_and_respond_api(&e, "create_user"),
    }
}

// 手动错误处理
pub async fn manual_error_example() -> impl IntoResponse {
    if some_condition {
        responses::error("自定义错误消息", Some("CUSTOM_ERROR"))
    } else {
        responses::success(data, Some("操作成功"))
    }
}
```

## 错误代码映射

| AppError 类型 | HTTP 状态码 | 错误代码 |
|--------------|------------|----------|
| NotFound | 404 | NOT_FOUND |
| InvalidInput | 400 | INVALID_INPUT |
| Unauthorized | 401 | UNAUTHORIZED |
| Forbidden | 403 | FORBIDDEN |
| Conflict | 409 | CONFLICT |
| DatabaseError | 500 | DATABASE_ERROR |
| InternalServerError | 500 | INTERNAL_SERVER_ERROR |

## 前端使用方法

### TypeScript 类型定义

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  error_code?: string;
  timestamp: number;
}

interface PaginatedApiResponse<T> {
  success: boolean;
  data: T[];
  message: string;
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  error_code?: string;
  timestamp: number;
}
```

### 使用示例

```typescript
// 处理标准响应
async function getUser(id: string): Promise<User | null> {
  try {
    const response = await fetch(`/api/users/${id}`);
    const result: ApiResponse<User> = await response.json();
    
    if (result.success) {
      return result.data || null;
    } else {
      console.error('Error:', result.message, result.error_code);
      return null;
    }
  } catch (error) {
    console.error('Network error:', error);
    return null;
  }
}

// 处理分页响应
async function listUsers(page: number = 1): Promise<PaginatedApiResponse<User>> {
  const response = await fetch(`/api/users?page=${page}&page_size=10`);
  return await response.json();
}
```

## 迁移指南

### 从旧的 ApiResponse 迁移

**旧代码：**
```rust
Ok(Json(ApiResponse::success(Some(data), None)))
```

**新代码：**
```rust
responses::success(data, Some("操作成功"))
```

### 从旧的 SuccessResponse/ErrorResponse 迁移

**旧代码：**
```rust
Ok(Json(SuccessResponse::with_data(data, Some("Success".to_string()))))
```

**新代码：**
```rust
responses::success(data, Some("Success"))
```

## 最佳实践

1. **统一使用新的响应格式**：所有新的API都应该使用 `ApiResponse` 或 `PaginatedApiResponse`
2. **提供有意义的消息**：为成功和错误响应提供清晰的消息
3. **使用错误代码**：为前端提供标准化的错误代码以便处理
4. **记录错误**：使用 `ErrorHandler::log_and_respond_api` 确保错误被正确记录
5. **保持一致性**：所有控制器都应该遵循相同的响应模式

## 注意事项

- 旧的 `ApiResponse` 已被标记为弃用，建议尽快迁移
- 时间戳使用 UTC 时间戳格式
- 分页从第1页开始计数
- 错误响应的 `data` 字段始终为 `null`
