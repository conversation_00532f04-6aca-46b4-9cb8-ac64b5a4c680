# gzxxzz 租户教师权限测试数据指南

本文档详细说明了为 gzxxzz 租户创建的模拟数据，用于测试教师权限系统（包括菜单权限）。

## 数据概览

### 总体统计
- **用户账号**: 120 个 (20 个教师 + 100 个学生)
- **教师**: 20 名 (包含各个层级)
- **学生**: 100 名 (分布在 5 个班级)
- **管理班级**: 5 个
- **教学班级**: 10 个
- **学科组**: 5 个
- **身份绑定**: 120 个
- **权限策略**: 72 个 casbin 策略

### 组织架构

```
广州信息中职学校 (gzxxzz)
├── 校长 (1名)
├── 教导主任 (1名)  
├── 学科组长 (3名)
│   ├── 语文组长
│   ├── 数学组长
│   └── 英语组长
├── 班主任 (5名)
│   ├── 2023级计算机应用1班班主任
│   ├── 2023级计算机应用2班班主任
│   ├── 2023级电子商务班班主任
│   ├── 2024级计算机应用1班班主任
│   └── 2024级计算机应用2班班主任
└── 任课老师 (10名)
    ├── 语文老师 (3名)
    ├── 数学老师 (3名)
    ├── 英语老师 (3名)
    └── 专业课老师 (1名)
```

## 测试账号信息

### 管理层账号

| 角色 | 用户名 | 手机号 | 密码 | 权限范围 |
|------|--------|--------|------|----------|
| 校长 | `principal_gzxxzz` | 13800000001 | principal_gzxxzz | 全部菜单和功能 |
| 教导主任 | `director_gzxxzz` | 13800000002 | director_gzxxzz | 学生、考试、成绩、班级、教师管理 |

### 学科组长账号

| 学科 | 用户名 | 手机号 | 密码 | 权限范围 |
|------|--------|--------|------|----------|
| 语文组长 | `chinese_leader_gzxxzz` | 13800000003 | chinese_leader_gzxxzz | 语文学科相关管理 |
| 数学组长 | `math_leader_gzxxzz` | 13800000004 | math_leader_gzxxzz | 数学学科相关管理 |
| 英语组长 | `english_leader_gzxxzz` | 13800000005 | english_leader_gzxxzz | 英语学科相关管理 |

### 班主任账号

| 班级 | 用户名 | 手机号 | 密码 | 管理班级 |
|------|--------|--------|------|----------|
| 2301班主任 | `class_teacher_2301` | 13800000006 | class_teacher_2301 | 2023级计算机应用1班 |
| 2302班主任 | `class_teacher_2302` | 13800000007 | class_teacher_2302 | 2023级计算机应用2班 |
| 2303班主任 | `class_teacher_2303` | 13800000008 | class_teacher_2303 | 2023级电子商务班 |
| 2401班主任 | `class_teacher_2401` | 13800000009 | class_teacher_2401 | 2024级计算机应用1班 |
| 2402班主任 | `class_teacher_2402` | 13800000010 | class_teacher_2402 | 2024级计算机应用2班 |

### 任课老师账号

| 姓名 | 用户名 | 手机号 | 密码 | 任教学科 |
|------|--------|--------|------|----------|
| 王老师 | `teacher_wang` | 13800000011 | teacher_wang | 语文 |
| 李老师 | `teacher_li` | 13800000012 | teacher_li | 语文 |
| 张老师 | `teacher_zhang` | 13800000013 | teacher_zhang | 语文 |
| 刘老师 | `teacher_liu` | 13800000014 | teacher_liu | 数学 |
| 陈老师 | `teacher_chen` | 13800000015 | teacher_chen | 数学 |
| 黄老师 | `teacher_huang` | 13800000016 | teacher_huang | 数学 |
| 吴老师 | `teacher_wu` | 13800000017 | teacher_wu | 英语 |
| 赵老师 | `teacher_zhao` | 13800000018 | teacher_zhao | 英语 |
| 孙老师 | `teacher_sun` | 13800000019 | teacher_sun | 英语 |
| 周老师 | `teacher_zhou` | 13800000020 | teacher_zhou | 计算机 |

### 学生账号 (示例前10名)

| 学号 | 用户名 | 手机号 | 密码 | 所属班级 |
|------|--------|--------|------|----------|
| 2301001 | `student_001_gzxxzz` | 13800000101 | student_001_gzxxzz | 2023级计算机应用1班 |
| 2301002 | `student_002_gzxxzz` | 13800000102 | student_002_gzxxzz | 2023级计算机应用1班 |
| ... | ... | ... | ... | ... |

*注：共100名学生，每班20名*

## 权限系统测试

### 菜单权限配置

#### 校长权限
- ✅ 所有菜单 (menu:*)
- ✅ 学生管理 (增删改查)
- ✅ 教师管理 (增删改查)
- ✅ 班级管理 (增删改查)
- ✅ 考试管理 (增删改查)
- ✅ 成绩管理 (增删改查)
- ✅ 系统管理 (增删改查)

#### 教导主任权限
- ✅ 学生管理 (查看)
- ✅ 考试管理 (完全管理)
- ✅ 成绩管理 (查看)
- ✅ 班级管理 (查看)
- ✅ 教师管理 (查看)
- ❌ 系统管理 (无权限)

#### 学科组长权限
- ✅ 学生管理 (本学科组范围)
- ✅ 考试管理 (本学科组范围)
- ✅ 成绩管理 (本学科组范围)
- ✅ 教师管理 (本学科组范围)
- ❌ 系统管理 (无权限)

#### 班主任权限
- ✅ 学生管理 (本班级范围)
- ✅ 成绩管理 (本班级查看)
- ✅ 班级管理 (本班级管理)
- ✅ 考试管理 (本班级查看)
- ❌ 教师管理 (无权限)
- ❌ 系统管理 (无权限)

#### 任课老师权限
- ✅ 学生管理 (任教班级查看)
- ✅ 成绩管理 (任教班级录入)
- ✅ 个人中心 (个人信息)
- ❌ 班级管理 (无权限)
- ❌ 教师管理 (无权限)
- ❌ 系统管理 (无权限)

#### 学生权限
- ✅ 个人中心 (查看个人信息)
- ✅ 我的成绩 (查看自己成绩)
- ✅ 我的课表 (查看个人课表)
- ❌ 其他管理功能 (无权限)

### 数据权限配置

权限系统使用 Casbin RBAC 模型实现：

1. **用户-角色映射 (g 策略)**
   - 30个用户角色绑定
   - 支持一个用户多个角色

2. **角色权限策略 (p 策略)**
   - 39个角色权限策略
   - 细粒度的菜单和数据访问控制

3. **角色继承 (g2 策略)**
   - 建立角色层次关系
   - 上级角色继承下级角色权限

## 测试场景

### 场景 1: 校长登录测试
1. 使用账号 `principal_gzxxzz` 登录
2. 验证能访问所有菜单
3. 验证能查看所有班级学生信息
4. 验证能管理所有教师信息

### 场景 2: 班主任权限测试
1. 使用账号 `class_teacher_2301` 登录
2. 验证只能访问允许的菜单
3. 验证只能查看本班级学生信息
4. 验证无法查看其他班级信息

### 场景 3: 任课老师权限测试
1. 使用账号 `teacher_wang` 登录
2. 验证只能访问基础菜单
3. 验证只能录入任教班级成绩
4. 验证无法访问管理类功能

### 场景 4: 学科组长权限测试
1. 使用账号 `chinese_leader_gzxxzz` 登录
2. 验证能管理语文学科相关功能
3. 验证无法操作其他学科数据
4. 验证具有比普通老师更多权限

### 场景 5: 学生权限测试
1. 使用账号 `student_001_gzxxzz` 登录
2. 验证只能访问个人中心
3. 验证只能查看自己的成绩
4. 验证无法访问任何管理功能

## API 测试建议

### 权限验证接口
```bash
# 获取用户菜单权限
GET /api/v1/auth/user/menus

# 获取用户数据权限
GET /api/v1/auth/user/permissions

# 验证特定权限
POST /api/v1/auth/verify-permission
{
  "resource": "student",
  "action": "read",
  "scope": "class"
}
```

### 数据访问接口
```bash
# 获取学生列表 (不同角色看到不同范围数据)
GET /api/v1/students

# 获取班级列表 (权限范围过滤)
GET /api/v1/classes

# 成绩录入 (只能录入有权限的班级)
POST /api/v1/grades
```

## 数据库验证

### 检查权限策略
```sql
-- 查看所有权限策略
SELECT ptype, v0, v1, v2, v3 
FROM public.casbin_policies 
WHERE tenant_id = '7ff2e111-1ca4-4402-bc02-af69c1a7283c'
ORDER BY ptype, v0;

-- 查看用户身份绑定
SELECT ui.user_id, r.name as role_name, ui.target_type, ui.subject
FROM tenant_gzxxzz.user_identities ui
JOIN public.roles r ON ui.role_id = r.id
ORDER BY r.level;
```

### 检查基础数据
```sql
-- 查看教师分布
SELECT 
    r.name as 角色,
    COUNT(*) as 人数
FROM tenant_gzxxzz.user_identities ui
JOIN public.roles r ON ui.role_id = r.id
JOIN tenant_gzxxzz.teachers t ON ui.user_id = t.user_id
GROUP BY r.name, r.level
ORDER BY r.level;

-- 查看学生分布
SELECT 
    ac.class_name as 班级名称,
    COUNT(s.*) as 学生人数
FROM tenant_gzxxzz.administrative_classes ac
LEFT JOIN tenant_gzxxzz.students s ON ac.id = s.administrative_class_id
GROUP BY ac.class_name, ac.code
ORDER BY ac.code;
```

## 注意事项

1. **密码说明**: 所有账号密码都与用户名相同，仅用于测试
2. **数据隔离**: gzxxzz 租户数据完全隔离，不会影响其他租户
3. **权限继承**: 上级角色自动继承下级角色权限
4. **菜单动态**: 菜单根据用户权限动态显示
5. **数据范围**: 所有数据访问都按权限范围过滤

## 问题排查

如果权限测试出现问题，检查以下方面：

1. **用户身份绑定**: 确认用户与角色正确绑定
2. **Casbin 策略**: 确认权限策略正确配置  
3. **菜单配置**: 确认菜单权限正确设置
4. **代码实现**: 确认后端权限验证逻辑正确
5. **缓存问题**: 清理可能的权限缓存

## 清理测试数据

如需清理测试数据，执行以下 SQL：

```sql
-- 清理 gzxxzz 租户相关数据
DELETE FROM public.casbin_policies WHERE tenant_id = '7ff2e111-1ca4-4402-bc02-af69c1a7283c';
DELETE FROM public.users WHERE phone_number LIKE '1380000%';

-- 或者删除整个租户 schema
DROP SCHEMA IF EXISTS tenant_gzxxzz CASCADE;
```

---

**测试数据创建完成！可以开始测试教师权限系统的各项功能。**