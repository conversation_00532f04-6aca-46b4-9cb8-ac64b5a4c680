# 管理员租户管理 API

## 概述

本文档描述了管理员租户管理API的使用方法。这些API允许系统管理员动态创建、管理和删除租户。

## 认证

所有管理员API都需要管理员认证。请在请求头中包含有效的JWT token：

```
Authorization: Bearer <your_admin_jwt_token>
```

## API 端点

### 1. 创建租户

**POST** `/api/admin/tenants`

动态创建新租户，包括数据库schema和表结构的初始化。

#### 请求体

```json
{
  "name": "示例公司",
  "tenantType": "standard",
  "domain": "example.com",
  "settings": {
    "max_users": 100,
    "features": ["basic", "analytics"]
  }
}
```

#### 字段说明

- `name` (string, 必需): 租户名称
- `tenantType` (string, 必需): 租户类型，可选值：`standard`, `premium`, `enterprise`
- `domain` (string, 可选): 租户域名
- `settings` (object, 可选): 租户配置设置

#### 响应

```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "示例公司",
    "tenantType": "standard",
    "schemaName": "tenant_example_company_1642678800",
    "domain": "example.com",
    "status": "active",
    "settings": {
      "max_users": 100,
      "features": ["basic", "analytics"]
    },
    "createdAt": "2024-01-20T10:30:00Z",
    "updatedAt": "2024-01-20T10:30:00Z"
  },
  "message": "Tenant created successfully"
}
```

### 2. 获取租户列表

**GET** `/api/admin/tenants`

获取所有租户的列表。

#### 响应

```json
{
  "success": true,
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "示例公司",
      "tenantType": "standard",
      "schemaName": "tenant_example_company_1642678800",
      "domain": "example.com",
      "status": "active",
      "settings": {},
      "createdAt": "2024-01-20T10:30:00Z",
      "updatedAt": "2024-01-20T10:30:00Z"
    }
  ],
  "message": "Tenants retrieved successfully"
}
```

### 3. 获取单个租户

**GET** `/api/admin/tenants/{id}`

根据ID获取特定租户的详细信息。

#### 路径参数

- `id` (UUID): 租户ID

#### 响应

```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "示例公司",
    "tenantType": "standard",
    "schemaName": "tenant_example_company_1642678800",
    "domain": "example.com",
    "status": "active",
    "settings": {},
    "createdAt": "2024-01-20T10:30:00Z",
    "updatedAt": "2024-01-20T10:30:00Z"
  },
  "message": "Tenant retrieved successfully"
}
```

### 4. 更新租户

**PUT** `/api/admin/tenants/{id}`

更新租户信息。

#### 请求体

```json
{
  "name": "更新后的公司名称",
  "domain": "new-domain.com",
  "status": "active",
  "settings": {
    "max_users": 200,
    "features": ["basic", "analytics", "premium"]
  }
}
```

#### 响应

```json
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "更新后的公司名称",
    "tenantType": "standard",
    "schemaName": "tenant_example_company_1642678800",
    "domain": "new-domain.com",
    "status": "active",
    "settings": {
      "max_users": 200,
      "features": ["basic", "analytics", "premium"]
    },
    "createdAt": "2024-01-20T10:30:00Z",
    "updatedAt": "2024-01-20T11:00:00Z"
  },
  "message": "Tenant updated successfully"
}
```

### 5. 删除租户

**DELETE** `/api/admin/tenants/{id}`

软删除租户（将状态设置为'deleted'）。

#### 路径参数

- `id` (UUID): 租户ID

#### 响应

```json
{
  "success": true,
  "data": null,
  "message": "Tenant deleted successfully"
}
```

## 错误响应

所有API在出错时都会返回统一的错误格式：

```json
{
  "success": false,
  "error": "具体错误信息",
  "message": "操作失败的描述"
}
```

### 常见错误码

- `400 Bad Request`: 请求参数无效
- `401 Unauthorized`: 未提供有效的认证token
- `403 Forbidden`: 用户不是系统管理员
- `404 Not Found`: 租户不存在
- `409 Conflict`: 租户名称或域名已存在
- `500 Internal Server Error`: 服务器内部错误

## 租户创建流程

当创建新租户时，系统会执行以下步骤：

1. **验证管理员权限**: 确认请求者是系统管理员
2. **生成安全的schema名称**: 基于租户名称和时间戳生成唯一的数据库schema名称
3. **创建数据库schema**: 在数据库中创建新的schema
4. **初始化表结构**: 使用模板文件在新schema中创建所有必要的表
5. **记录租户信息**: 在public.tenants表中保存租户元数据
6. **返回租户信息**: 返回完整的租户信息给客户端

## 安全考虑

- 所有管理员API都需要有效的JWT认证
- Schema名称经过严格验证，防止SQL注入
- 租户创建使用数据库事务，确保数据一致性
- 敏感操作会记录审计日志

## 使用示例

### 创建新租户

```bash
curl -X POST http://localhost:8080/api/admin/tenants \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "新客户公司",
    "tenantType": "standard",
    "domain": "newclient.com",
    "settings": {
      "max_users": 50,
      "features": ["basic"]
    }
  }'
```

### 获取所有租户

```bash
curl -X GET http://localhost:8080/api/admin/tenants \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```
