# 🔐 gzxxzz 租户测试账号登录信息

## 统一登录信息
**所有测试账号的密码都是: `admin123`**

## 主要测试账号

### 🎓 管理层账号
| 角色 | 用户名 | 手机号 | 密码 | 权限范围 |
|------|--------|--------|------|----------|
| 校长 | `principal_gzxxzz` | 13800000001 | `admin123` | 全部菜单和功能 |
| 教导主任 | `director_gzxxzz` | 13800000002 | `admin123` | 学生、考试、成绩、班级、教师管理 |

### 👨‍🏫 学科组长账号
| 学科 | 用户名 | 手机号 | 密码 | 权限范围 |
|------|--------|--------|------|----------|
| 语文组长 | `chinese_leader_gzxxzz` | 13800000003 | `admin123` | 语文学科相关管理 |
| 数学组长 | `math_leader_gzxxzz` | 13800000004 | `admin123` | 数学学科相关管理 |
| 英语组长 | `english_leader_gzxxzz` | 13800000005 | `admin123` | 英语学科相关管理 |

### 👩‍🏫 班主任账号
| 班级 | 用户名 | 手机号 | 密码 | 管理班级 |
|------|--------|--------|------|----------|
| 2301班主任 | `class_teacher_2301` | 13800000006 | `admin123` | 2023级计算机应用1班 |
| 2302班主任 | `class_teacher_2302` | 13800000007 | `admin123` | 2023级计算机应用2班 |
| 2303班主任 | `class_teacher_2303` | 13800000008 | `admin123` | 2023级电子商务班 |
| 2401班主任 | `class_teacher_2401` | 13800000009 | `admin123` | 2024级计算机应用1班 |
| 2402班主任 | `class_teacher_2402` | 13800000010 | `admin123` | 2024级计算机应用2班 |

### 🧑‍🏫 任课老师账号
| 姓名 | 用户名 | 手机号 | 密码 | 任教学科 |
|------|--------|--------|------|----------|
| 王老师 | `teacher_wang` | 13800000011 | `admin123` | 语文 |
| 李老师 | `teacher_li` | 13800000012 | `admin123` | 语文 |
| 张老师 | `teacher_zhang` | 13800000013 | `admin123` | 语文 |
| 刘老师 | `teacher_liu` | 13800000014 | `admin123` | 数学 |
| 陈老师 | `teacher_chen` | 13800000015 | `admin123` | 数学 |

### 🎓 学生账号 (示例)
| 学号 | 用户名 | 手机号 | 密码 | 所属班级 |
|------|--------|--------|------|----------|
| 2301001 | `student_001_gzxxzz` | 13800000101 | `admin123` | 2023级计算机应用1班 |
| 2301002 | `student_002_gzxxzz` | 13800000102 | `admin123` | 2023级计算机应用1班 |

*注：共100名学生，密码都是 `admin123`*

## 🧪 快速测试步骤

1. **打开登录页面**
2. **选择任意测试账号**，例如:
   - 用户名: `principal_gzxxzz` 
   - 密码: `admin123`
3. **登录后验证权限**:
   - 校长应该能看到所有菜单
   - 班主任只能看到班级相关菜单
   - 任课老师只能看到基础教学菜单
   - 学生只能看到个人中心

## 🔍 权限测试场景

### 场景 1: 校长权限测试
```
账号: principal_gzxxzz
密码: admin123
预期: 能访问所有功能菜单
```

### 场景 2: 班主任权限测试  
```
账号: class_teacher_2301
密码: admin123
预期: 只能管理本班级(2301)学生，无法查看其他班级
```

### 场景 3: 任课老师权限测试
```
账号: teacher_wang
密码: admin123
预期: 只能查看和录入任教班级成绩，无管理权限
```

### 场景 4: 学生权限测试
```
账号: student_001_gzxxzz  
密码: admin123
预期: 只能查看个人信息和成绩
```

## ⚠️ 重要提醒

- **所有账号密码统一为**: `admin123`
- **仅用于测试环境**，请勿在生产环境使用
- **权限按角色严格隔离**，上级角色可查看下级数据
- **数据完全隔离在 gzxxzz 租户**，不影响其他租户

---

**现在可以使用这些账号登录测试教师权限系统了！** 🎉