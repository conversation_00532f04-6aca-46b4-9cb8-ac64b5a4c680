# 统一错误处理系统

本文档描述了基于 `anyhow` 和 `thiserror` 的统一错误处理系统的使用方法。

## 概述

新的错误处理系统提供了：

1. **统一的错误类型** - `AppError` 作为应用程序的主要错误类型
2. **领域特定错误** - 使用 `thiserror` 定义的特定领域错误（如 `AuthError`）
3. **错误链支持** - 使用 `anyhow` 进行错误上下文和链式处理
4. **统一的 HTTP 响应** - 自动将错误转换为适当的 HTTP 响应
5. **便捷的错误创建** - 宏和函数简化错误创建过程

## 核心组件

### 1. AppError

主要的应用程序错误类型，定义在 `utils/error.rs` 中：

```rust
#[derive(Debug, Error)]
pub enum AppError {
    DatabaseError(#[from] sqlx::Error),
    ValidationError { message: String },
    AuthenticationError { message: String },
    Forbidden { message: String },
    NotFound { message: String },
    Conflict { message: String },
    BadRequest { message: String },
    InternalServerError { message: String },
    JwtError(#[from] jsonwebtoken::errors::Error),
    SerializationError(#[from] serde_json::Error),
    AuthError(#[from] crate::model::auth::AuthError),
    Generic(#[from] anyhow::Error),
}
```

### 2. 错误处理工具

定义在 `utils/error_handler.rs` 中：

- `ErrorHandler` - 统一的错误处理器
- `SqlxResultExt` - 为 sqlx::Result 提供便捷方法
- `AnyhowResultExt` - 为 anyhow::Result 提供便捷方法
- `errors` 模块 - 便捷的错误创建函数

## 使用指南

### 1. 在服务层中使用

```rust
use crate::utils::error::{AppResult, AppError};
use crate::utils::error_handler::{SqlxResultExt, errors};

pub async fn create_user(pool: &PgPool, request: CreateUserRequest) -> AppResult<User> {
    // 验证输入
    if request.name.trim().is_empty() {
        return Err(errors::validation_error("Name cannot be empty"));
    }
    
    // 数据库操作，自动处理错误
    let user = sqlx::query_as!(
        User,
        "INSERT INTO users (name, email) VALUES ($1, $2) RETURNING *",
        request.name,
        request.email
    )
    .fetch_one(pool)
    .await
    .with_db_context("create user")?;
    
    Ok(user)
}
```

### 2. 在控制器中使用

```rust
use crate::utils::error_handler::ErrorHandler;

pub async fn create_user_handler(
    State(state): State<AppState>,
    Json(request): Json<CreateUserRequest>,
) -> impl IntoResponse {
    match create_user(&state.db, request).await {
        Ok(user) => Json(ApiResponse::success(Some(user), None)).into_response(),
        Err(e) => ErrorHandler::log_and_respond(&e, "create_user_handler"),
    }
}
```

### 3. 错误创建的便捷方法

```rust
// 使用便捷函数
return Err(errors::validation_error("Invalid input"));
return Err(errors::not_found("User"));
return Err(errors::forbidden("Access denied"));

// 使用宏
return Err(app_error!(validation, "Invalid email format"));
return Err(app_error!(not_found, "User not found"));
```

### 4. 数据库操作错误处理

```rust
// 基本用法
let user = sqlx::query_as!(User, "SELECT * FROM users WHERE id = $1", id)
    .fetch_one(pool)
    .await
    .with_db_context("get user by id")?;

// 处理可选结果
let user = sqlx::query_as!(User, "SELECT * FROM users WHERE id = $1", id)
    .fetch_optional(pool)
    .await
    .with_db_context("get user by id")?
    .ok_or_else(|| errors::not_found("User"))?;
```

### 5. 错误链和上下文

```rust
use anyhow::Context;

// 添加上下文
let result = some_operation()
    .await
    .with_context(|| format!("Failed to process user {}", user_id))?;

// 转换为 AppResult
let result = anyhow_operation()
    .await
    .into_app_error_with_context("processing user data")?;
```

## 错误响应格式

所有错误都会转换为统一的 JSON 响应格式：

```json
{
    "success": false,
    "error": "User-friendly error message",
    "error_code": "ERROR_CODE_FOR_FRONTEND",
    "message": "Request failed"
}
```

## 错误代码映射

| 错误类型 | HTTP 状态码 | 错误代码 |
|---------|------------|----------|
| ValidationError | 400 | VALIDATION_ERROR |
| AuthenticationError | 401 | AUTHENTICATION_ERROR |
| Forbidden | 403 | FORBIDDEN |
| NotFound | 404 | NOT_FOUND |
| Conflict | 409 | CONFLICT |
| DatabaseError | 500 | DATABASE_ERROR |
| InternalServerError | 500 | INTERNAL_SERVER_ERROR |

## 最佳实践

### 1. 错误分层

- **控制器层**：处理 HTTP 相关错误，使用 `ErrorHandler::log_and_respond`
- **服务层**：处理业务逻辑错误，返回 `AppResult<T>`
- **数据访问层**：使用 `SqlxResultExt` 处理数据库错误

### 2. 错误日志

- 数据库错误和内部错误使用 `error!` 级别
- 业务逻辑错误使用 `warn!` 级别
- 验证错误使用 `info!` 级别

### 3. 错误消息

- 面向用户的错误消息应该清晰、有帮助
- 避免暴露内部实现细节
- 为前端提供错误代码以便本地化

### 4. 错误处理模式

```rust
// 好的做法
match service.operation().await {
    Ok(result) => Ok(Json(ApiResponse::success(Some(result), None))),
    Err(e) => Err(ErrorHandler::log_and_respond(&e, "operation_handler")),
}

// 避免的做法
match service.operation().await {
    Ok(result) => Ok(Json(ApiResponse::success(Some(result), None))),
    Err(e) => {
        println!("Error: {}", e); // 使用 tracing 而不是 println!
        Ok(Json(ApiResponse::error(Some("Something went wrong".to_string()))))
    }
}
```

## 迁移指南

### 从旧的错误处理系统迁移

1. **更新导入**：
   ```rust
   // 旧的
   use anyhow::Result;
   
   // 新的
   use crate::utils::error::{AppResult, AppError};
   use crate::utils::error_handler::{ErrorHandler, errors};
   ```

2. **更新函数签名**：
   ```rust
   // 旧的
   pub async fn create_user(...) -> Result<User> { ... }
   
   // 新的
   pub async fn create_user(...) -> AppResult<User> { ... }
   ```

3. **更新错误处理**：
   ```rust
   // 旧的
   .await?
   
   // 新的
   .await.with_db_context("operation description")?
   ```

4. **更新控制器**：
   ```rust
   // 旧的
   match service.operation().await {
       Ok(result) => Ok(Json(ApiResponse::success(Some(result), None))),
       Err(e) => Ok(Json(ApiResponse::error(Some(e.to_string())))),
   }
   
   // 新的
   match service.operation().await {
       Ok(result) => Json(ApiResponse::success(Some(result), None)).into_response(),
       Err(e) => ErrorHandler::log_and_respond(&e, "operation_handler"),
   }
   ```

## 测试

在测试中，可以使用 `AppError` 的方法来验证错误类型：

```rust
#[tokio::test]
async fn test_validation_error() {
    let result = service.create_user(invalid_request).await;
    
    assert!(result.is_err());
    let error = result.unwrap_err();
    assert_eq!(error.error_code(), "VALIDATION_ERROR");
    assert_eq!(error.status_code(), StatusCode::BAD_REQUEST);
}
```
