-- 插入默认菜单权限数据
-- Migration: 20250806_insert_default_menu_permissions.sql
-- Description: Insert default menu permissions data from hardcoded casbin_service into database

-- ================================================================
-- 1. 清理现有数据（如果需要重新运行）
-- ================================================================

-- 删除现有的菜单权限数据（保留用户自定义的）
DELETE FROM public.menu_permissions 
WHERE menu_id IN (
    'teaching_aids_management', 'exam_management', 'homework_management',
    'administrative_classes_management', 'teaching_classes_management',
    'grading_center', 'statistics_analysis', 'tenant_management',
    'role_management', 'casbin_policy_management', 'menu_management',
    'subject_management', 'education_stage_management', 'subject_groups_management',
    'grade_level_management', 'student_management', 'teacher_management',
    'user_management', 'grade_score_management', 'personal_center',
    'grade_input', 'grade_analysis', 'my_grades'
);

-- ================================================================
-- 2. 插入主要功能菜单
-- ================================================================

INSERT INTO public.menu_permissions (
    menu_id, name, path, icon, parent_id, menu_type, description,
    required_permissions, data_scopes, permission_mode, access_level,
    sort_order, is_active, cache_enabled, version
) VALUES
-- 教辅管理
(
    'teaching_aids_management', '教辅管理', '/teaching-aids', 'book', NULL,
    'functional', '教学辅助材料管理功能',
    ARRAY['teaching_aids:read'], NULL, 'any', 0,
    10, TRUE, TRUE, 1
),
-- 考试管理
(
    'exam_management', '考试管理', '/exam-management', 'file-text', NULL,
    'functional', '考试安排和管理功能',
    ARRAY['exam:read'], ARRAY['school:*'], 'any', 0,
    20, TRUE, TRUE, 1
),
-- 作业管理
(
    'homework_management', '作业管理', '/homework-management', 'book-check', NULL,
    'functional', '作业布置和管理功能',
    ARRAY['homework:read'], ARRAY['class:*'], 'any', 0,
    30, TRUE, TRUE, 1
),
-- 行政班管理
(
    'administrative_classes_management', '行政班管理', '/administrative-classes', 'school', NULL,
    'functional', '行政班级管理功能',
    ARRAY['administrative_class:read'], ARRAY['school:*'], 'any', 0,
    40, TRUE, TRUE, 1
),
-- 教学班管理
(
    'teaching_classes_management', '教学班管理', '/teaching-classes', 'users-2', NULL,
    'functional', '教学班级管理功能',
    ARRAY['teaching_class:read'], ARRAY['subject_group:*'], 'any', 0,
    50, TRUE, TRUE, 1
),
-- 阅卷中心
(
    'grading_center', '阅卷中心', '/grading-center', 'clipboard-check', NULL,
    'functional', '考试阅卷和评分功能',
    ARRAY['grading:read'], ARRAY['exam:*'], 'any', 0,
    60, TRUE, TRUE, 1
),
-- 统计分析
(
    'statistics_analysis', '统计分析', '/statistics', 'bar-chart-2', NULL,
    'functional', '数据统计和分析功能',
    ARRAY['statistics:read'], ARRAY['school:*'], 'any', 0,
    70, TRUE, TRUE, 1
);

-- ================================================================
-- 3. 插入系统管理菜单
-- ================================================================

INSERT INTO public.menu_permissions (
    menu_id, name, path, icon, parent_id, menu_type, description,
    required_permissions, data_scopes, permission_mode, access_level,
    sort_order, is_active, cache_enabled, version
) VALUES
-- 租户管理
(
    'tenant_management', '租户管理', '/tenants', 'building-2', NULL,
    'admin', '多租户系统管理功能',
    ARRAY['tenant:read'], NULL, 'any', 10,
    100, TRUE, TRUE, 1
),
-- 角色管理
(
    'role_management', '角色管理', '/roles', 'shield', NULL,
    'admin', '用户角色和权限管理',
    ARRAY['role:read'], NULL, 'any', 10,
    110, TRUE, TRUE, 1
),
-- 策略管理
(
    'casbin_policy_management', '策略管理', '/casbin-policies', 'settings', NULL,
    'admin', 'Casbin权限策略管理',
    ARRAY['casbin:read'], NULL, 'any', 10,
    120, TRUE, TRUE, 1
),
-- 菜单管理
(
    'menu_management', '菜单管理', '/menu-management', 'menu', NULL,
    'admin', '系统菜单权限管理',
    ARRAY['menu:read'], NULL, 'any', 10,
    130, TRUE, TRUE, 1
),
-- 学科管理
(
    'subject_management', '学科管理', '/subjects', 'book-open', NULL,
    'admin', '学科信息管理',
    ARRAY['subject:read'], NULL, 'any', 5,
    140, TRUE, TRUE, 1
),
-- 学段管理
(
    'education_stage_management', '学段管理', '/education-stages', 'layers', NULL,
    'admin', '教育阶段管理',
    ARRAY['education_stage:read'], NULL, 'any', 5,
    150, TRUE, TRUE, 1
),
-- 学科组管理
(
    'subject_groups_management', '学科组管理', '/subject-groups', 'users-round', NULL,
    'admin', '学科组织管理',
    ARRAY['subject_group:read'], ARRAY['school:*'], 'any', 5,
    160, TRUE, TRUE, 1
),
-- 年级管理
(
    'grade_level_management', '年级管理', '/grades', 'graduation-cap', NULL,
    'admin', '年级信息管理',
    ARRAY['grade_level:read'], ARRAY['school:*'], 'any', 5,
    170, TRUE, TRUE, 1
),
-- 学生管理
(
    'student_management', '学生管理', '/students', 'user-check', NULL,
    'functional', '学生信息管理',
    ARRAY['student:read'], ARRAY['class:*'], 'any', 0,
    180, TRUE, TRUE, 1
),
-- 教师管理
(
    'teacher_management', '教师管理', '/teachers', 'users', NULL,
    'functional', '教师信息管理',
    ARRAY['teacher:read'], ARRAY['school:*'], 'any', 0,
    190, TRUE, TRUE, 1
),
-- 用户管理
(
    'user_management', '用户管理', '/users', 'user', NULL,
    'admin', '系统用户管理',
    ARRAY['user:read'], NULL, 'any', 10,
    200, TRUE, TRUE, 1
);

-- ================================================================
-- 4. 插入兼容性保留的原有菜单（带子菜单）
-- ================================================================

-- 成绩管理主菜单
INSERT INTO public.menu_permissions (
    menu_id, name, path, icon, parent_id, menu_type, description,
    required_permissions, data_scopes, permission_mode, access_level,
    sort_order, is_active, cache_enabled, version
) VALUES
(
    'grade_score_management', '成绩管理', '/grade-scores', 'bar-chart', NULL,
    'functional', '学生成绩管理功能',
    ARRAY['grade:read'], NULL, 'any', 0,
    80, TRUE, TRUE, 1
);

-- 成绩管理子菜单
INSERT INTO public.menu_permissions (
    menu_id, name, path, icon, parent_id, menu_type, description,
    required_permissions, data_scopes, permission_mode, access_level,
    sort_order, is_active, cache_enabled, version
) VALUES
(
    'grade_input', '成绩录入', '/grade-scores/input', 'edit', 'grade_score_management',
    'functional', '成绩录入功能',
    ARRAY['grade:write'], NULL, 'any', 0,
    81, TRUE, TRUE, 1
),
(
    'grade_analysis', '成绩分析', '/grade-scores/analysis', 'pie-chart', 'grade_score_management',
    'functional', '成绩分析功能',
    ARRAY['grade:read'], NULL, 'any', 0,
    82, TRUE, TRUE, 1
);

-- 个人中心主菜单
INSERT INTO public.menu_permissions (
    menu_id, name, path, icon, parent_id, menu_type, description,
    required_permissions, data_scopes, permission_mode, access_level,
    sort_order, is_active, cache_enabled, version
) VALUES
(
    'personal_center', '个人中心', '/personal', 'user', NULL,
    'personal', '个人信息和功能中心',
    ARRAY['menu:personal_center'], ARRAY['student:self'], 'any', 0,
    300, TRUE, TRUE, 1
);

-- 个人中心子菜单
INSERT INTO public.menu_permissions (
    menu_id, name, path, icon, parent_id, menu_type, description,
    required_permissions, data_scopes, permission_mode, access_level,
    sort_order, is_active, cache_enabled, version
) VALUES
(
    'my_grades', '我的成绩', '/personal/grades', 'trophy', 'personal_center',
    'personal', '查看个人成绩',
    ARRAY['grade:self'], ARRAY['student:self'], 'any', 0,
    301, TRUE, TRUE, 1
);

-- ================================================================
-- 5. 更新统计信息
-- ================================================================

-- 更新菜单权限审计记录
INSERT INTO public.menu_permission_audit (
    menu_id,
    operation,
    new_config,
    changes_summary,
    operator_id,
    operator_identity,
    reason
) VALUES (
    'SYSTEM_MIGRATION',
    'BATCH_UPDATE',
    jsonb_build_object(
        'migration', '20250806_insert_default_menu_permissions',
        'total_menus_inserted', (SELECT COUNT(*) FROM public.menu_permissions),
        'timestamp', NOW()
    ),
    jsonb_build_object(
        'action', 'default_menu_data_migration',
        'source', 'casbin_service_hardcoded_data',
        'menus_migrated', ARRAY[
            'teaching_aids_management', 'exam_management', 'homework_management',
            'administrative_classes_management', 'teaching_classes_management',
            'grading_center', 'statistics_analysis', 'tenant_management',
            'role_management', 'casbin_policy_management', 'menu_management',
            'subject_management', 'education_stage_management', 'subject_groups_management',
            'grade_level_management', 'student_management', 'teacher_management',
            'user_management', 'grade_score_management', 'personal_center'
        ]
    ),
    '00000000-0000-0000-0000-000000000000',
    'system_migration',
    '从casbin_service硬编码数据迁移默认菜单权限到数据库'
);

-- ================================================================
-- 迁移完成
-- ================================================================
