-- AI Grading Agent Workflows Migration
-- Migration: 20250804_ai_grading_agent_workflows
-- Description: Add AI grading agent workflow management tables for configurable grading processes

-- =============================================
-- AI GRADING AGENT WORKFLOW SYSTEM
-- =============================================

-- Workflows table (工作流定义表)
CREATE TABLE IF NOT EXISTS public.workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_name VARCHAR(200) NOT NULL,                 -- 工作流名称
    workflow_type VARCHAR(50) NOT NULL,                  -- 工作流类型
    description TEXT,                                     -- 工作流描述
    config JSONB DEFAULT '{}',                           -- 工作流配置参数
    is_active BOOLEAN DEFAULT TRUE,                      -- 是否启用
    version INTEGER DEFAULT 1,                           -- 版本号
    created_by UUID REFERENCES public.users(id),        -- 创建者
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 确保工作流名称在同一版本下唯一
    UNIQUE(workflow_name, version)
);

-- Workflow settings table (工作流配置表)
CREATE TABLE IF NOT EXISTS public.workflow_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES public.workflows(id) ON DELETE CASCADE,
    subject_codes JSONB DEFAULT '[]',                    -- 适用学科编码数组 ["MATH", "CHINESE", "ENGLISH"]
    grade_level_codes JSONB DEFAULT '[]',               -- 适用年级编码数组 ["G1", "G2", "G3"]
    question_type_codes JSONB DEFAULT '[]',             -- 适用题型编码数组 ["SINGLE_CHOICE", "MULTI_CHOICE"]
    schema_names JSONB DEFAULT '[]',                     -- 适用租户模式名数组 ["tenant_001", "tenant_002"]
    priority INTEGER DEFAULT 100,                        -- 配置优先级 (数值越小优先级越高)
    is_default BOOLEAN DEFAULT FALSE,                   -- 是否为默认配置
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 确保每个工作流只有一个默认配置
    EXCLUDE (workflow_id WITH =) WHERE (is_default = TRUE)
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Indexes for workflows table
CREATE INDEX IF NOT EXISTS idx_workflows_name ON public.workflows(workflow_name);
CREATE INDEX IF NOT EXISTS idx_workflows_type ON public.workflows(workflow_type);
CREATE INDEX IF NOT EXISTS idx_workflows_active ON public.workflows(is_active);
CREATE INDEX IF NOT EXISTS idx_workflows_version ON public.workflows(version);
CREATE INDEX IF NOT EXISTS idx_workflows_created_by ON public.workflows(created_by);

-- Indexes for workflow_settings table
CREATE INDEX IF NOT EXISTS idx_workflow_settings_workflow ON public.workflow_settings(workflow_id);
CREATE INDEX IF NOT EXISTS idx_workflow_settings_priority ON public.workflow_settings(priority);
CREATE INDEX IF NOT EXISTS idx_workflow_settings_default ON public.workflow_settings(is_default);

-- GIN indexes for JSONB arrays to support array containment queries
CREATE INDEX IF NOT EXISTS idx_workflow_settings_subject_codes ON public.workflow_settings USING GIN(subject_codes);
CREATE INDEX IF NOT EXISTS idx_workflow_settings_grade_level_codes ON public.workflow_settings USING GIN(grade_level_codes);
CREATE INDEX IF NOT EXISTS idx_workflow_settings_question_type_codes ON public.workflow_settings USING GIN(question_type_codes);
CREATE INDEX IF NOT EXISTS idx_workflow_settings_schema_names ON public.workflow_settings USING GIN(schema_names);