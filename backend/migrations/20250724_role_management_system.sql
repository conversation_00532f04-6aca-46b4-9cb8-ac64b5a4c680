-- Role Management System Migration
-- Migration: 20250724_role_management_system.sql

-- Create role category enum type
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'role_category') THEN
        CREATE TYPE role_category AS ENUM ('system', 'tenant', 'school', 'business', 'class_grade', 'end_user');
    END IF;
END $$;

-- Create role level enum type (using integers for easier comparison)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'role_level') THEN
        CREATE TYPE role_level AS ENUM ('1', '2', '3', '4', '5', '6', '7', '8', '9', '10');
    END IF;
END $$;

-- Permissions table (public schema - shared across tenants)
CREATE TABLE IF NOT EXISTS public.permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    resource VARCHAR(100) NOT NULL,      -- 资源类型 (e.g., 'exam', 'class', 'user')
    action VARCHAR(50) NOT NULL,         -- 操作类型 (e.g., 'read', 'write', 'delete')
    scope VARCHAR(100) NOT NULL,         -- 权限范围 (e.g., 'tenant', 'school', 'class')
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Roles table (public schema - shared across tenants)
CREATE TABLE IF NOT EXISTS public.roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    category role_category NOT NULL,
    level INTEGER NOT NULL CHECK (level >= 1 AND level <= 10),
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    tenant_id UUID REFERENCES public.tenants(id),  -- NULL for system roles
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_role_code_per_tenant UNIQUE (code, tenant_id),
    CONSTRAINT system_roles_no_tenant CHECK (
        (is_system = TRUE AND tenant_id IS NULL) OR 
        (is_system = FALSE)
    )
);

-- Role permissions relationship table (public schema)
CREATE TABLE IF NOT EXISTS public.role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES public.roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES public.permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_role_permission UNIQUE (role_id, permission_id)
);

-- User identities table (extends existing functionality - public schema)
-- This table already exists, we need to add the role_id field if it doesn't exist
DO $$ 
BEGIN
    -- Add role_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_identities' 
        AND column_name = 'role_id'
    ) THEN
        ALTER TABLE public.user_identities 
        ADD COLUMN role_id UUID REFERENCES public.roles(id);
    END IF;
    
    -- Add target_type column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_identities' 
        AND column_name = 'target_type'
    ) THEN
        ALTER TABLE public.user_identities 
        ADD COLUMN target_type VARCHAR(50); -- 目标类型 (subject_group, grade, class, student)
    END IF;
    
    -- Add target_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_identities' 
        AND column_name = 'target_id'
    ) THEN
        ALTER TABLE public.user_identities 
        ADD COLUMN target_id UUID; -- 目标ID
    END IF;
    
    -- Add subject column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_identities' 
        AND column_name = 'subject'
    ) THEN
        ALTER TABLE public.user_identities 
        ADD COLUMN subject VARCHAR(50); -- 学科
    END IF;
    
    -- Add unique constraint for new role-based identities if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_schema = 'public' 
        AND table_name = 'user_identities' 
        AND constraint_name = 'unique_user_role_target'
    ) THEN
        ALTER TABLE public.user_identities 
        ADD CONSTRAINT unique_user_role_target UNIQUE (user_id, role_id, target_id, subject);
    END IF;
END $$;

-- Identity switch history table (public schema)
CREATE TABLE IF NOT EXISTS public.user_identity_switches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id),
    from_tenant_id UUID REFERENCES public.tenants(id),
    to_tenant_id UUID REFERENCES public.tenants(id),
    from_identity_info JSONB,
    to_identity_info JSONB,
    switch_type VARCHAR(30) CHECK (switch_type IN ('tenant_switch', 'role_switch', 'student_switch')),
    switch_reason VARCHAR(100),
    session_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    switched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_roles_category ON public.roles(category);
CREATE INDEX IF NOT EXISTS idx_roles_level ON public.roles(level);
CREATE INDEX IF NOT EXISTS idx_roles_tenant_id ON public.roles(tenant_id);
CREATE INDEX IF NOT EXISTS idx_roles_is_active ON public.roles(is_active);
CREATE INDEX IF NOT EXISTS idx_roles_is_system ON public.roles(is_system);

CREATE INDEX IF NOT EXISTS idx_permissions_resource ON public.permissions(resource);
CREATE INDEX IF NOT EXISTS idx_permissions_action ON public.permissions(action);

CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON public.role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON public.role_permissions(permission_id);

CREATE INDEX IF NOT EXISTS idx_user_identities_user_id ON public.user_identities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_identities_tenant_id ON public.user_identities(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_identities_role_id ON public.user_identities(role_id);
CREATE INDEX IF NOT EXISTS idx_user_identities_is_verified ON public.user_identities(is_verified);

CREATE INDEX IF NOT EXISTS idx_identity_switches_user_id ON public.user_identity_switches(user_id);
CREATE INDEX IF NOT EXISTS idx_identity_switches_switched_at ON public.user_identity_switches(switched_at);

-- Insert default permissions
INSERT INTO public.permissions (name, description, resource, action, scope) VALUES
-- User management permissions
('查看用户', '查看用户信息', 'user', 'read', 'tenant'),
('创建用户', '创建新用户', 'user', 'create', 'tenant'),
('编辑用户', '编辑用户信息', 'user', 'update', 'tenant'),
('删除用户', '删除用户', 'user', 'delete', 'tenant'),

-- Role management permissions
('查看角色', '查看角色信息', 'role', 'read', 'tenant'),
('创建角色', '创建新角色', 'role', 'create', 'tenant'),
('编辑角色', '编辑角色信息', 'role', 'update', 'tenant'),
('删除角色', '删除角色', 'role', 'delete', 'tenant'),
('分配角色', '为用户分配角色', 'role', 'assign', 'tenant'),

-- Class management permissions  
('查看班级', '查看班级信息', 'class', 'read', 'tenant'),
('创建班级', '创建新班级', 'class', 'create', 'tenant'),
('编辑班级', '编辑班级信息', 'class', 'update', 'tenant'),
('删除班级', '删除班级', 'class', 'delete', 'tenant'),

-- Exam management permissions
('查看考试', '查看考试信息', 'exam', 'read', 'tenant'),
('创建考试', '创建新考试', 'exam', 'create', 'tenant'),
('编辑考试', '编辑考试信息', 'exam', 'update', 'tenant'),
('删除考试', '删除考试', 'exam', 'delete', 'tenant'),

-- Grading permissions
('查看成绩', '查看考试成绩', 'grading', 'read', 'class'),
('录入成绩', '录入考试成绩', 'grading', 'create', 'class'),
('编辑成绩', '编辑考试成绩', 'grading', 'update', 'class'),
('发布成绩', '发布考试成绩', 'grading', 'publish', 'class'),

-- Analysis permissions
('查看分析', '查看学情分析', 'analysis', 'read', 'class'),
('生成报告', '生成分析报告', 'analysis', 'generate', 'class'),

-- System management permissions
('系统管理', '系统级别管理权限', 'system', 'manage', 'global'),
('租户管理', '租户管理权限', 'tenant', 'manage', 'global')
ON CONFLICT DO NOTHING;

-- Insert default system roles (only if there are users in the system)
INSERT INTO public.roles (name, code, description, category, level, is_system, is_active, created_by) 
SELECT 
    role_data.name,
    role_data.code,
    role_data.description,
    role_data.category::role_category,
    role_data.level,
    TRUE,
    TRUE,
    u.id
FROM (VALUES
    ('系统超级管理员', 'super_admin', '系统最高权限管理员', 'system', 1),
    ('系统代理商', 'system_agent', '系统代理商，负责租户销售和技术支持', 'system', 1),
    ('运维人员', 'operator', '系统运维人员', 'system', 1),
    ('租户管理员', 'tenant_admin', '租户管理员', 'tenant', 2),
    ('校长', 'principal', '学校校长', 'school', 3),
    ('教导主任', 'academic_director', '教导主任', 'school', 4),
    ('学科组长', 'subject_leader', '学科组长', 'business', 5),
    ('考试管理员', 'exam_manager', '考试管理员', 'business', 5),
    ('阅卷管理员', 'grading_manager', '阅卷管理员', 'business', 5),
    ('试卷扫描员', 'scan_operator', '试卷扫描员', 'business', 8),
    ('阅卷员', 'grader', '阅卷员', 'business', 8),
    ('年级长', 'grade_leader', '年级长', 'class_grade', 6),
    ('班主任', 'class_teacher', '班主任', 'class_grade', 7),
    ('任课老师', 'teacher', '任课老师', 'class_grade', 8),
    ('学生', 'student', '学生', 'end_user', 9),
    ('家长', 'parent', '家长', 'end_user', 10)
) AS role_data(name, code, description, category, level)
CROSS JOIN (
    SELECT id FROM public.users 
    ORDER BY created_at
    LIMIT 1
) u
WHERE EXISTS (SELECT 1 FROM public.users LIMIT 1) -- Only create roles if users exist
ON CONFLICT (code, tenant_id) DO NOTHING;

-- Assign permissions to default roles
-- Super Admin gets all permissions
INSERT INTO public.role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM public.roles r
CROSS JOIN public.permissions p
WHERE r.code = 'super_admin' AND r.is_system = TRUE
ON CONFLICT DO NOTHING;

-- Principal gets most tenant-level permissions
INSERT INTO public.role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM public.roles r
CROSS JOIN public.permissions p
WHERE r.code = 'principal' AND r.is_system = TRUE
  AND p.scope IN ('tenant', 'class')
  AND p.resource != 'system'
ON CONFLICT DO NOTHING;

-- Teachers get basic class-level permissions
INSERT INTO public.role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM public.roles r
CROSS JOIN public.permissions p
WHERE r.code = 'teacher' AND r.is_system = TRUE
  AND p.action = 'read'
  AND p.resource IN ('class', 'exam', 'grading', 'analysis')
ON CONFLICT DO NOTHING;

-- Update timestamps trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add update triggers
DROP TRIGGER IF EXISTS update_roles_updated_at ON public.roles;
CREATE TRIGGER update_roles_updated_at
    BEFORE UPDATE ON public.roles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_permissions_updated_at ON public.permissions;
CREATE TRIGGER update_permissions_updated_at
    BEFORE UPDATE ON public.permissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_identities_updated_at ON public.user_identities;
CREATE TRIGGER update_user_identities_updated_at
    BEFORE UPDATE ON public.user_identities
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments
COMMENT ON TABLE public.roles IS '角色表，定义系统中的各种角色';
COMMENT ON TABLE public.permissions IS '权限表，定义系统中的各种权限';
COMMENT ON TABLE public.role_permissions IS '角色权限关联表';
COMMENT ON TABLE public.user_identities IS '用户身份表，用户在不同租户下的角色身份';
COMMENT ON TABLE public.user_identity_switches IS '用户身份切换历史记录表';

COMMENT ON COLUMN public.roles.category IS '角色分类：system, tenant, school, business, class_grade, end_user';
COMMENT ON COLUMN public.roles.level IS '角色级别：1-10，数字越小权限越高';
COMMENT ON COLUMN public.roles.is_system IS '是否为系统预设角色';
COMMENT ON COLUMN public.permissions.resource IS '权限资源类型';
COMMENT ON COLUMN public.permissions.action IS '权限操作类型';
COMMENT ON COLUMN public.permissions.scope IS '权限作用范围';