-- 创建 Casbin 权限策略存储表
-- Migration: 20250801_casbin_policies_storage.sql
-- Description: Create tables for storing Casbin RBAC policies and rules

-- ================================================================
-- Casbin 策略表 (用于存储 p, g, g2 策略)
-- ================================================================

-- 主策略表：存储权限策略 (p 规则)
CREATE TABLE IF NOT EXISTS public.casbin_policies (
    id BIGSERIAL PRIMARY KEY,
    ptype VARCHAR(100) NOT NULL,      -- 策略类型: p, g, g2
    v0 VARCHAR(256),                  -- 主体 (subject)
    v1 VARCHAR(256),                  -- 域 (domain/tenant)  
    v2 VARCHAR(256),                  -- 对象 (object)
    v3 VARCHAR(256),                  -- 动作 (action)
    v4 VARCHAR(256),                  -- 效果 (effect: allow/deny)
    v5 VARCHAR(256),                  -- 扩展字段
    tenant_id VARCHAR(100),           -- 租户标识，用于策略隔离
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引优化查询性能
CREATE INDEX idx_casbin_policies_ptype ON public.casbin_policies(ptype);
CREATE INDEX idx_casbin_policies_tenant ON public.casbin_policies(tenant_id);
CREATE INDEX idx_casbin_policies_v0 ON public.casbin_policies(v0);
CREATE INDEX idx_casbin_policies_v1 ON public.casbin_policies(v1);
CREATE INDEX idx_casbin_policies_lookup ON public.casbin_policies(ptype, v0, v1);
CREATE INDEX idx_casbin_policies_tenant_lookup ON public.casbin_policies(tenant_id, ptype, v0, v1);

-- ================================================================
-- 权限策略审计表 (记录策略变更历史)
-- ================================================================

CREATE TABLE IF NOT EXISTS public.casbin_policy_audit (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(100) NOT NULL,
    operation VARCHAR(20) NOT NULL,     -- 操作类型: ADD, REMOVE, UPDATE
    policy_type VARCHAR(10) NOT NULL,   -- 策略类型: p, g, g2
    old_policy JSONB,                   -- 旧策略数据
    new_policy JSONB,                   -- 新策略数据
    operator_id UUID,                   -- 操作者ID
    operator_identity VARCHAR(256),     -- 操作者身份标识
    reason TEXT,                        -- 变更原因
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 审计表索引
CREATE INDEX idx_casbin_audit_tenant ON public.casbin_policy_audit(tenant_id);
CREATE INDEX idx_casbin_audit_operation ON public.casbin_policy_audit(operation);
CREATE INDEX idx_casbin_audit_created_at ON public.casbin_policy_audit(created_at);
CREATE INDEX idx_casbin_audit_operator ON public.casbin_policy_audit(operator_id);

-- ================================================================
-- 前端菜单权限配置表
-- ================================================================

CREATE TABLE IF NOT EXISTS public.menu_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    menu_id VARCHAR(100) UNIQUE NOT NULL,  -- 菜单标识
    name VARCHAR(200) NOT NULL,             -- 菜单名称
    path VARCHAR(500) NOT NULL,             -- 路由路径
    icon VARCHAR(100),                      -- 图标名称
    parent_id VARCHAR(100),                 -- 父菜单ID
    sort_order INTEGER DEFAULT 0,          -- 排序顺序
    required_permissions TEXT[],            -- 所需权限列表
    data_scopes TEXT[],                     -- 数据范围限制
    is_active BOOLEAN DEFAULT TRUE,         -- 是否激活
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    FOREIGN KEY (parent_id) REFERENCES public.menu_permissions(menu_id) ON DELETE CASCADE
);

-- 菜单权限表索引
CREATE INDEX idx_menu_permissions_parent ON public.menu_permissions(parent_id);
CREATE INDEX idx_menu_permissions_active ON public.menu_permissions(is_active);
CREATE INDEX idx_menu_permissions_sort ON public.menu_permissions(parent_id, sort_order);

-- ================================================================
-- 租户权限策略统计表 (用于监控和优化)
-- ================================================================

CREATE TABLE IF NOT EXISTS public.tenant_policy_stats (
    tenant_id VARCHAR(100) PRIMARY KEY,
    total_policies INTEGER DEFAULT 0,
    user_role_mappings INTEGER DEFAULT 0,
    permission_policies INTEGER DEFAULT 0,
    role_inheritance_count INTEGER DEFAULT 0,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- 插入预定义菜单权限数据
-- ================================================================

-- 主菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes) VALUES
('student_management', '学生管理', '/students', 'users', NULL, 100, ARRAY['student:read'], ARRAY['class:*']),
('exam_management', '考试管理', '/exams', 'file-text', NULL, 200, ARRAY['exam:read'], ARRAY['school:*']),
('grade_management', '成绩管理', '/grades', 'bar-chart', NULL, 300, ARRAY['grade:read'], NULL),
('class_management', '班级管理', '/classes', 'home', NULL, 400, ARRAY['class:read'], ARRAY['class:*']),
('teacher_management', '教师管理', '/teachers', 'user-check', NULL, 500, ARRAY['teacher:read'], ARRAY['school:*']),
('system_management', '系统管理', '/system', 'settings', NULL, 900, ARRAY['system:read'], NULL),
('personal_center', '个人中心', '/personal', 'user', NULL, 1000, ARRAY['menu:personal_center'], ARRAY['student:self']);

-- 学生管理子菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes) VALUES
('student_list', '学生列表', '/students/list', 'list', 'student_management', 101, ARRAY['student:read'], ARRAY['class:*']),
('student_create', '添加学生', '/students/create', 'plus', 'student_management', 102, ARRAY['student:create'], ARRAY['class:*']),
('student_import', '批量导入', '/students/import', 'upload', 'student_management', 103, ARRAY['student:create', 'student:import'], ARRAY['class:*']);

-- 考试管理子菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes) VALUES
('exam_list', '考试列表', '/exams/list', 'list', 'exam_management', 201, ARRAY['exam:read'], ARRAY['school:*']),
('exam_create', '创建考试', '/exams/create', 'plus', 'exam_management', 202, ARRAY['exam:create'], ARRAY['school:*']),
('exam_schedule', '考试安排', '/exams/schedule', 'calendar', 'exam_management', 203, ARRAY['exam:manage'], ARRAY['school:*']);

-- 成绩管理子菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes) VALUES
('grade_input', '成绩录入', '/grades/input', 'edit', 'grade_management', 301, ARRAY['grade:write'], NULL),
('grade_analysis', '成绩分析', '/grades/analysis', 'pie-chart', 'grade_management', 302, ARRAY['grade:read'], NULL),
('grade_report', '成绩报告', '/grades/report', 'file-text', 'grade_management', 303, ARRAY['grade:read'], NULL);

-- 班级管理子菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes) VALUES
('class_list', '班级列表', '/classes/list', 'list', 'class_management', 401, ARRAY['class:read'], ARRAY['class:*']),
('class_create', '创建班级', '/classes/create', 'plus', 'class_management', 402, ARRAY['class:create'], ARRAY['class:*']),
('class_assignment', '班级分配', '/classes/assignment', 'shuffle', 'class_management', 403, ARRAY['class:manage'], ARRAY['class:*']);

-- 教师管理子菜单  
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes) VALUES
('teacher_list', '教师列表', '/teachers/list', 'list', 'teacher_management', 501, ARRAY['teacher:read'], ARRAY['school:*']),
('teacher_create', '添加教师', '/teachers/create', 'plus', 'teacher_management', 502, ARRAY['teacher:create'], ARRAY['school:*']),
('teacher_schedule', '排课管理', '/teachers/schedule', 'calendar', 'teacher_management', 503, ARRAY['teacher:manage'], ARRAY['school:*']);

-- 系统管理子菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes) VALUES
('role_management', '角色管理', '/system/roles', 'shield', 'system_management', 901, ARRAY['role:read'], NULL),
('permission_management', '权限管理', '/system/permissions', 'lock', 'system_management', 902, ARRAY['permission:read'], NULL),
('tenant_management', '租户管理', '/system/tenants', 'building', 'system_management', 903, ARRAY['tenant:read'], NULL);

-- 个人中心子菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes) VALUES
('my_grades', '我的成绩', '/personal/grades', 'trophy', 'personal_center', 1001, ARRAY['grade:self'], ARRAY['student:self']),
('my_profile', '个人信息', '/personal/profile', 'user', 'personal_center', 1002, ARRAY['profile:self'], ARRAY['student:self']),
('my_schedule', '我的课表', '/personal/schedule', 'calendar', 'personal_center', 1003, ARRAY['schedule:self'], ARRAY['student:self']);

-- ================================================================
-- 创建函数和触发器维护策略统计
-- ================================================================

-- 更新租户策略统计的函数
CREATE OR REPLACE FUNCTION update_tenant_policy_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 插入或更新统计信息
    INSERT INTO public.tenant_policy_stats (tenant_id, total_policies, user_role_mappings, permission_policies, role_inheritance_count, last_sync_at)
    SELECT 
        COALESCE(NEW.tenant_id, OLD.tenant_id) as tenant_id,
        (SELECT COUNT(*) FROM public.casbin_policies WHERE tenant_id = COALESCE(NEW.tenant_id, OLD.tenant_id)) as total_policies,
        (SELECT COUNT(*) FROM public.casbin_policies WHERE tenant_id = COALESCE(NEW.tenant_id, OLD.tenant_id) AND ptype = 'g') as user_role_mappings,
        (SELECT COUNT(*) FROM public.casbin_policies WHERE tenant_id = COALESCE(NEW.tenant_id, OLD.tenant_id) AND ptype = 'p') as permission_policies,
        (SELECT COUNT(*) FROM public.casbin_policies WHERE tenant_id = COALESCE(NEW.tenant_id, OLD.tenant_id) AND ptype = 'g2') as role_inheritance_count,
        NOW() as last_sync_at
    ON CONFLICT (tenant_id) DO UPDATE SET
        total_policies = EXCLUDED.total_policies,
        user_role_mappings = EXCLUDED.user_role_mappings,
        permission_policies = EXCLUDED.permission_policies,
        role_inheritance_count = EXCLUDED.role_inheritance_count,
        last_sync_at = EXCLUDED.last_sync_at,
        updated_at = NOW();
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER casbin_policies_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.casbin_policies
    FOR EACH ROW EXECUTE FUNCTION update_tenant_policy_stats();

-- ================================================================
-- 创建权限策略审计触发器
-- ================================================================

CREATE OR REPLACE FUNCTION audit_casbin_policy_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO public.casbin_policy_audit (tenant_id, operation, policy_type, new_policy)
        VALUES (NEW.tenant_id, 'ADD', NEW.ptype, row_to_json(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.casbin_policy_audit (tenant_id, operation, policy_type, old_policy, new_policy)
        VALUES (NEW.tenant_id, 'UPDATE', NEW.ptype, row_to_json(OLD), row_to_json(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO public.casbin_policy_audit (tenant_id, operation, policy_type, old_policy)
        VALUES (OLD.tenant_id, 'REMOVE', OLD.ptype, row_to_json(OLD));
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建审计触发器
CREATE TRIGGER casbin_policies_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.casbin_policies
    FOR EACH ROW EXECUTE FUNCTION audit_casbin_policy_changes();

-- ================================================================
-- 初始化系统角色的默认权限策略
-- ================================================================

-- 为现有租户创建基础统计记录
INSERT INTO public.tenant_policy_stats (tenant_id, total_policies, user_role_mappings, permission_policies, role_inheritance_count, last_sync_at)
SELECT 
    id::text as tenant_id,
    0 as total_policies,
    0 as user_role_mappings, 
    0 as permission_policies,
    0 as role_inheritance_count,
    NOW() as last_sync_at
FROM public.tenants
ON CONFLICT (tenant_id) DO NOTHING;

-- ================================================================
-- 添加注释
-- ================================================================

COMMENT ON TABLE public.casbin_policies IS 'Casbin RBAC 权限策略存储表';
COMMENT ON TABLE public.casbin_policy_audit IS 'Casbin 权限策略变更审计表';
COMMENT ON TABLE public.menu_permissions IS '前端菜单权限配置表';
COMMENT ON TABLE public.tenant_policy_stats IS '租户权限策略统计表';

COMMENT ON COLUMN public.casbin_policies.ptype IS '策略类型: p(权限策略), g(用户-角色), g2(角色继承)';
COMMENT ON COLUMN public.casbin_policies.tenant_id IS '租户标识，用于多租户策略隔离';
COMMENT ON COLUMN public.casbin_policy_audit.operation IS '操作类型: ADD, REMOVE, UPDATE';
COMMENT ON COLUMN public.menu_permissions.required_permissions IS '访问该菜单所需的权限列表';
COMMENT ON COLUMN public.menu_permissions.data_scopes IS '菜单对应的数据访问范围限制';