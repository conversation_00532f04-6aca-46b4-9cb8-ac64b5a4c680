-- 添加管理员菜单权限管理界面菜单项
-- Migration: 20250804_add_menu_management_menus.sql
-- Description: Add menu items for super admin menu permission management interface

-- ================================================================
-- 添加菜单权限管理相关菜单
-- ================================================================

-- 在系统管理下添加菜单权限管理子菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes, menu_type, description) VALUES
('menu_management', '菜单权限管理', '/system/menu-management', 'menu', 'system_management', 904, ARRAY['menu:manage', 'permission:manage'], NULL, 'admin', '管理员菜单权限管理界面'),
('menu_structure', '菜单结构管理', '/system/menu-management/structure', 'layers', 'menu_management', 9041, ARRAY['menu:read', 'menu:write'], NULL, 'admin', '菜单结构和层次管理'),
('permission_config', '权限配置管理', '/system/menu-management/permissions', 'shield-check', 'menu_management', 9042, ARRAY['permission:read', 'permission:write'], NULL, 'admin', '菜单权限配置管理'),
('role_menu_mapping', '角色菜单映射', '/system/menu-management/role-mapping', 'users-cog', 'menu_management', 9043, ARRAY['role:read', 'menu:read'], NULL, 'admin', '角色与菜单权限映射管理'),
('permission_templates', '权限模板管理', '/system/menu-management/templates', 'bookmark', 'menu_management', 9044, ARRAY['template:read', 'template:write'], NULL, 'admin', '菜单权限模板管理'),
('permission_testing', '权限测试工具', '/system/menu-management/testing', 'test-tube', 'menu_management', 9045, ARRAY['permission:test'], NULL, 'admin', '菜单权限测试和验证工具'),
('permission_audit', '权限审计日志', '/system/menu-management/audit', 'file-search', 'menu_management', 9046, ARRAY['audit:read'], NULL, 'admin', '菜单权限变更审计和历史记录');

-- 在系统管理下添加高级权限功能菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes, menu_type, description) VALUES
('advanced_permissions', '高级权限管理', '/system/advanced-permissions', 'lock-keyhole', 'system_management', 905, ARRAY['permission:advanced'], NULL, 'admin', '高级权限管理功能'),
('casbin_policies', 'Casbin策略管理', '/system/advanced-permissions/casbin', 'database', 'advanced_permissions', 9051, ARRAY['casbin:read', 'casbin:write'], NULL, 'admin', 'Casbin权限策略直接管理'),
('permission_matrix', '权限矩阵视图', '/system/advanced-permissions/matrix', 'grid', 'advanced_permissions', 9052, ARRAY['permission:read'], NULL, 'admin', '可视化权限矩阵管理'),
('bulk_operations', '批量权限操作', '/system/advanced-permissions/bulk', 'package-plus', 'advanced_permissions', 9053, ARRAY['permission:bulk'], NULL, 'admin', '批量权限分配和管理'),
('permission_analytics', '权限使用分析', '/system/advanced-permissions/analytics', 'bar-chart-3', 'advanced_permissions', 9054, ARRAY['analytics:read'], NULL, 'admin', '权限使用情况统计分析');

-- 添加系统监控菜单
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes, menu_type, description) VALUES
('system_monitoring', '系统监控', '/system/monitoring', 'activity', 'system_management', 906, ARRAY['monitor:read'], NULL, 'admin', '系统运行状态监控'),
('menu_usage_stats', '菜单使用统计', '/system/monitoring/menu-usage', 'trending-up', 'system_monitoring', 9061, ARRAY['stats:read'], NULL, 'admin', '菜单访问和使用情况统计'),
('permission_performance', '权限性能监控', '/system/monitoring/permission-performance', 'gauge', 'system_monitoring', 9062, ARRAY['performance:read'], NULL, 'admin', '权限检查性能监控'),
('system_health', '系统健康检查', '/system/monitoring/health', 'heart-pulse', 'system_monitoring', 9063, ARRAY['health:read'], NULL, 'admin', '系统整体健康状态检查');

-- ================================================================
-- 更新现有系统管理菜单的权限要求
-- ================================================================

-- 更新系统管理主菜单的权限要求，确保只有管理员可以访问
UPDATE public.menu_permissions 
SET required_permissions = ARRAY['system:admin', 'menu:system_management']
WHERE menu_id = 'system_management';

-- 更新角色管理的权限要求
UPDATE public.menu_permissions 
SET required_permissions = ARRAY['role:read', 'role:manage']
WHERE menu_id = 'role_management';

-- 更新权限管理的权限要求
UPDATE public.menu_permissions 
SET required_permissions = ARRAY['permission:read', 'permission:manage']
WHERE menu_id = 'permission_management';

-- 更新租户管理的权限要求
UPDATE public.menu_permissions 
SET required_permissions = ARRAY['tenant:read', 'tenant:manage']
WHERE menu_id = 'tenant_management';

-- ================================================================
-- 添加开发者工具菜单（仅开发环境可见）
-- ================================================================

INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes, menu_type, description, is_active) VALUES
('developer_tools', '开发者工具', '/system/dev-tools', 'code', 'system_management', 999, ARRAY['dev:access'], NULL, 'system', '开发者调试和测试工具', FALSE),
('permission_debugger', '权限调试器', '/system/dev-tools/permission-debugger', 'bug', 'developer_tools', 9991, ARRAY['dev:debug'], NULL, 'system', '权限系统调试工具', FALSE),
('cache_manager', '缓存管理', '/system/dev-tools/cache', 'hard-drive', 'developer_tools', 9992, ARRAY['dev:cache'], NULL, 'system', '权限缓存管理工具', FALSE),
('api_explorer', 'API浏览器', '/system/dev-tools/api-explorer', 'globe', 'developer_tools', 9993, ARRAY['dev:api'], NULL, 'system', '权限API接口测试工具', FALSE);

-- ================================================================
-- 添加快捷访问菜单（桌面快捷方式）
-- ================================================================

INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes, menu_type, description) VALUES
('quick_access', '快捷访问', '/quick-access', 'zap', NULL, 50, ARRAY['menu:quick_access'], NULL, 'functional', '快捷访问面板'),
('quick_menu_config', '快速菜单配置', '/quick-access/menu-config', 'settings', 'quick_access', 501, ARRAY['menu:quick_config'], NULL, 'functional', '快速菜单权限配置'),
('quick_role_assign', '快速角色分配', '/quick-access/role-assign', 'user-plus', 'quick_access', 502, ARRAY['role:quick_assign'], NULL, 'functional', '快速角色权限分配'),
('quick_permission_test', '快速权限测试', '/quick-access/permission-test', 'play', 'quick_access', 503, ARRAY['permission:quick_test'], NULL, 'functional', '快速权限验证测试');

-- ================================================================
-- 为新菜单设置合适的元数据
-- ================================================================

-- 为菜单权限管理相关菜单添加元数据
UPDATE public.menu_permissions 
SET metadata = jsonb_build_object(
    'feature_flags', ARRAY['menu_management', 'super_admin_only'],
    'min_role_level', 1,
    'requires_confirmation', ARRAY['DELETE', 'BATCH_DELETE'],
    'help_url', '/docs/menu-management',
    'keyboard_shortcuts', jsonb_build_object(
        'new', 'Ctrl+N',
        'save', 'Ctrl+S',
        'test', 'Ctrl+T'
    )
)
WHERE menu_id IN ('menu_management', 'menu_structure', 'permission_config', 'role_menu_mapping', 'permission_templates', 'permission_testing', 'permission_audit');

-- 为高级权限管理菜单添加元数据
UPDATE public.menu_permissions 
SET metadata = jsonb_build_object(
    'feature_flags', ARRAY['advanced_permissions', 'expert_mode'],
    'min_role_level', 1,
    'warning_level', 'high',
    'requires_confirmation', ARRAY['UPDATE', 'DELETE', 'BULK_UPDATE'],
    'help_url', '/docs/advanced-permissions'
)
WHERE menu_id IN ('advanced_permissions', 'casbin_policies', 'permission_matrix', 'bulk_operations', 'permission_analytics');

-- 为系统监控菜单添加元数据
UPDATE public.menu_permissions 
SET metadata = jsonb_build_object(
    'feature_flags', ARRAY['system_monitoring'],
    'refresh_interval', 30000,
    'auto_refresh', true,
    'chart_types', ARRAY['line', 'bar', 'pie'],
    'export_formats', ARRAY['csv', 'json', 'pdf']
)
WHERE menu_id IN ('system_monitoring', 'menu_usage_stats', 'permission_performance', 'system_health');

-- ================================================================
-- 添加菜单访问统计初始化
-- ================================================================

-- 为新创建的菜单初始化使用统计记录
INSERT INTO public.menu_usage_statistics (menu_id, tenant_id, access_count, unique_user_count, denied_access_count, statistics_date)
SELECT 
    mp.menu_id,
    'system' as tenant_id,
    0 as access_count,
    0 as unique_user_count,
    0 as denied_access_count,
    CURRENT_DATE as statistics_date
FROM public.menu_permissions mp
WHERE mp.menu_id IN (
    'menu_management', 'menu_structure', 'permission_config', 'role_menu_mapping', 
    'permission_templates', 'permission_testing', 'permission_audit',
    'advanced_permissions', 'casbin_policies', 'permission_matrix', 'bulk_operations', 'permission_analytics',
    'system_monitoring', 'menu_usage_stats', 'permission_performance', 'system_health',
    'developer_tools', 'permission_debugger', 'cache_manager', 'api_explorer',
    'quick_access', 'quick_menu_config', 'quick_role_assign', 'quick_permission_test'
)
ON CONFLICT (menu_id, tenant_id, statistics_date) DO NOTHING;

-- ================================================================
-- 审计日志记录
-- ================================================================

-- 记录菜单权限管理界面菜单的创建
INSERT INTO public.menu_permission_audit (
    menu_id,
    operation,
    new_config,
    changes_summary,
    operator_id,
    operator_identity,
    reason
) VALUES (
    'MENU_MANAGEMENT_INIT',
    'CREATE',
    jsonb_build_object(
        'created_menus', ARRAY[
            'menu_management', 'menu_structure', 'permission_config', 'role_menu_mapping',
            'permission_templates', 'permission_testing', 'permission_audit',
            'advanced_permissions', 'casbin_policies', 'permission_matrix', 'bulk_operations', 'permission_analytics',
            'system_monitoring', 'menu_usage_stats', 'permission_performance', 'system_health',
            'developer_tools', 'permission_debugger', 'cache_manager', 'api_explorer',
            'quick_access', 'quick_menu_config', 'quick_role_assign', 'quick_permission_test'
        ],
        'total_count', 22,
        'creation_timestamp', NOW()
    ),
    jsonb_build_object(
        'action', 'menu_management_interface_initialization',
        'category', 'system_enhancement',
        'impact_level', 'major',
        'affected_roles', ARRAY['super_admin', 'system_admin']
    ),
    '00000000-0000-0000-0000-000000000000',
    'system_migration',
    '创建管理员菜单权限管理界面相关菜单项'
);

-- ================================================================
-- 最终检查和优化
-- ================================================================

-- 检查菜单层级深度是否合理（不超过3级）
DO $$
DECLARE
    max_depth INTEGER;
BEGIN
    WITH RECURSIVE menu_depth AS (
        -- 根菜单（深度为1）
        SELECT menu_id, name, 1 as depth
        FROM public.menu_permissions
        WHERE parent_id IS NULL
        
        UNION ALL
        
        -- 递归查找子菜单
        SELECT mp.menu_id, mp.name, md.depth + 1
        FROM public.menu_permissions mp
        INNER JOIN menu_depth md ON mp.parent_id = md.menu_id
    )
    SELECT MAX(depth) INTO max_depth FROM menu_depth;
    
    IF max_depth > 3 THEN
        RAISE WARNING '菜单层级深度超过建议值3级，当前最大深度：%', max_depth;
    ELSE
        RAISE NOTICE '菜单层级深度正常，最大深度：%', max_depth;
    END IF;
END $$;

-- 检查是否有重复的菜单路径
DO $$
DECLARE
    duplicate_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO duplicate_count
    FROM (
        SELECT path, COUNT(*) as cnt
        FROM public.menu_permissions
        WHERE path IS NOT NULL
        GROUP BY path
        HAVING COUNT(*) > 1
    ) duplicates;
    
    IF duplicate_count > 0 THEN
        RAISE WARNING '发现 % 个重复的菜单路径', duplicate_count;
    ELSE
        RAISE NOTICE '菜单路径检查通过，无重复路径';
    END IF;
END $$;

-- 更新菜单权限表的统计信息
ANALYZE public.menu_permissions;
ANALYZE public.menu_permission_templates;
ANALYZE public.menu_permission_audit;
ANALYZE public.menu_usage_statistics;

-- ================================================================
-- 完成标记
-- ================================================================
SELECT 
    COUNT(*) as total_menus,
    COUNT(*) FILTER (WHERE parent_id IS NULL) as root_menus,
    COUNT(*) FILTER (WHERE menu_type = 'admin') as admin_menus,
    COUNT(*) FILTER (WHERE is_active = true) as active_menus
FROM public.menu_permissions;