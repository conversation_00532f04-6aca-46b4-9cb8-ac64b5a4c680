-- Enhanced Authentication and Identity Management System
-- Migration: 20250720_enhanced_auth_system_complete

-- Enhanced Users Table with phone verification support
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone_number VARCHAR(20) UNIQUE NOT NULL,
    phone_verified BOOLEAN DEFAULT FALSE,
    phone_verified_at TIMESTAMP WITH TIME ZONE,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE
);

-- Indexes for users table
CREATE INDEX IF NOT EXISTS idx_users_phone ON public.users(phone_number);
CREATE INDEX IF NOT EXISTS idx_users_verified ON public.users(phone_verified);
CREATE INDEX IF NOT EXISTS idx_users_active ON public.users(is_active);

-- SMS verification code management
CREATE TABLE IF NOT EXISTS public.phone_verification_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone_number VARCHAR(20) NOT NULL,
    verification_code VARCHAR(6) NOT NULL,
    code_type VARCHAR(20) NOT NULL, -- 'registration', 'login', 'reset'
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    verified BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for phone verification codes
CREATE INDEX IF NOT EXISTS idx_phone_codes_phone ON public.phone_verification_codes(phone_number);
CREATE INDEX IF NOT EXISTS idx_phone_codes_expires ON public.phone_verification_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_phone_codes_type ON public.phone_verification_codes(code_type);

-- Tenant management table
CREATE TABLE IF NOT EXISTS public.tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    schema_name VARCHAR(50) UNIQUE NOT NULL,
    domain VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'deleted')),
    settings JSONB DEFAULT '{}',
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for tenants table
CREATE INDEX IF NOT EXISTS idx_tenants_schema ON public.tenants(schema_name);
CREATE INDEX IF NOT EXISTS idx_tenants_domain ON public.tenants(domain);
CREATE INDEX IF NOT EXISTS idx_tenants_status ON public.tenants(status);

-- User identities within tenants
CREATE TABLE IF NOT EXISTS public.user_identities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
    identity_type VARCHAR(50) NOT NULL, -- 'teacher', 'student', 'admin', 'grader'
    organizational_target_id UUID, -- References tenant-specific org tables
    display_name VARCHAR(100),
    is_primary BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_method VARCHAR(50), -- 'admin_approval', 'self_service', 'invitation'
    verified_by UUID REFERENCES public.users(id),
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for user identities
CREATE INDEX IF NOT EXISTS idx_user_identities_user ON public.user_identities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_identities_tenant ON public.user_identities(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_identities_type ON public.user_identities(identity_type);
CREATE INDEX IF NOT EXISTS idx_user_identities_verified ON public.user_identities(is_verified);

-- Smart matching suggestions for identity binding
CREATE TABLE IF NOT EXISTS public.identity_binding_suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
    suggested_identity_type VARCHAR(50) NOT NULL,
    suggested_target_id UUID NOT NULL,
    suggested_target_name VARCHAR(200),
    match_confidence DECIMAL(3,2), -- 0.00 to 1.00
    match_criteria JSONB, -- Details about matching criteria used
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'rejected', 'expired'
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for identity binding suggestions
CREATE INDEX IF NOT EXISTS idx_binding_suggestions_user ON public.identity_binding_suggestions(user_id);
CREATE INDEX IF NOT EXISTS idx_binding_suggestions_tenant ON public.identity_binding_suggestions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_binding_suggestions_status ON public.identity_binding_suggestions(status);
CREATE INDEX IF NOT EXISTS idx_binding_suggestions_confidence ON public.identity_binding_suggestions(match_confidence DESC);

-- Parent-student relationships across tenants
CREATE TABLE IF NOT EXISTS public.parent_student_relations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    student_user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    student_tenant_id UUID NOT NULL,
    student_identity_id UUID NOT NULL REFERENCES public.user_identities(id),
    relationship_type VARCHAR(20) NOT NULL, -- 'parent', 'guardian', 'relative'
    verification_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'verified', 'rejected'
    verification_method VARCHAR(50), -- 'phone_verification', 'admin_approval', 'document_upload'
    verification_data JSONB, -- Additional verification information
    verified_by UUID REFERENCES public.users(id),
    verified_at TIMESTAMP WITH TIME ZONE,
    access_permissions JSONB, -- Specific permissions for this relationship
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Constraints and indexes for parent-student relations
CREATE UNIQUE INDEX IF NOT EXISTS idx_parent_relations_unique ON public.parent_student_relations(parent_user_id, student_user_id, student_tenant_id);
CREATE INDEX IF NOT EXISTS idx_parent_relations_parent ON public.parent_student_relations(parent_user_id);
CREATE INDEX IF NOT EXISTS idx_parent_relations_student ON public.parent_student_relations(student_user_id);
CREATE INDEX IF NOT EXISTS idx_parent_relations_tenant ON public.parent_student_relations(student_tenant_id);
CREATE INDEX IF NOT EXISTS idx_parent_relations_status ON public.parent_student_relations(verification_status);

-- Multi-identity session management
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    current_identity_id UUID REFERENCES public.user_identities(id),
    available_identities JSONB, -- Array of accessible identity IDs
    device_info JSONB, -- Device and browser information
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    refresh_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for user sessions
CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON public.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_refresh ON public.user_sessions(refresh_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON public.user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON public.user_sessions(is_active);

-- Identity switching audit log
CREATE TABLE IF NOT EXISTS public.identity_switch_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    session_id UUID NOT NULL REFERENCES public.user_sessions(id) ON DELETE CASCADE,
    from_identity_id UUID REFERENCES public.user_identities(id),
    to_identity_id UUID NOT NULL REFERENCES public.user_identities(id),
    switch_reason VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for identity switch logs
CREATE INDEX IF NOT EXISTS idx_identity_switch_user ON public.identity_switch_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_identity_switch_session ON public.identity_switch_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_identity_switch_created ON public.identity_switch_logs(created_at);