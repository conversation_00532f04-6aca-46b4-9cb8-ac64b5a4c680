-- Question Type Composition System Migration
-- Migration: 20250731_question_type_composition_system
-- Description: Add question type dictionary and composition tables for subject-grade-type validation

-- =============================================
-- QUESTION TYPE MANAGEMENT SYSTEM
-- =============================================

-- Question types dictionary table (题型字典表)
CREATE TABLE IF NOT EXISTS public.question_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(30) UNIQUE NOT NULL,              -- 题型编码 (SINGLE_CHOICE, MULTI_CHOICE, FILL_BLANK, etc.)
    type_name VARCHAR(100) NOT NULL,               -- 题型名称 (单选题、多选题、填空题等)
    description TEXT,                              -- 题型描述
    is_active BOOLEAN DEFAULT TRUE,               -- 是否启用
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Question type composition table (题型组合表 - 定义学科-年级-题型的有效组合)
CREATE TABLE IF NOT EXISTS public.compose_question_types (
    question_type_code VARCHAR(30) NOT NULL,
    subject_code VARCHAR(20) NOT NULL,
    grade_level_code VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (question_type_code, subject_code, grade_level_code)
);

-- Add foreign key constraints
ALTER TABLE public.compose_question_types 
ADD CONSTRAINT fk_compose_question_types_question_type 
FOREIGN KEY (question_type_code) REFERENCES public.question_types(code) ON DELETE CASCADE;

ALTER TABLE public.compose_question_types 
ADD CONSTRAINT fk_compose_question_types_subject 
FOREIGN KEY (subject_code) REFERENCES public.subjects(code) ON DELETE CASCADE;

ALTER TABLE public.compose_question_types 
ADD CONSTRAINT fk_compose_question_types_grade_level 
FOREIGN KEY (grade_level_code) REFERENCES public.grade_levels(code) ON DELETE CASCADE;

-- =============================================
-- UPDATE EXISTING QUESTION_BANK TABLE
-- =============================================

-- Add new columns to question_bank table
ALTER TABLE public.question_bank 
ADD COLUMN IF NOT EXISTS question_type_code VARCHAR(30);

ALTER TABLE public.question_bank 
ADD COLUMN IF NOT EXISTS subject_code VARCHAR(20);

ALTER TABLE public.question_bank 
ADD COLUMN IF NOT EXISTS grade_level_code VARCHAR(20);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Indexes for question_types table
CREATE INDEX IF NOT EXISTS idx_question_types_code ON public.question_types(code);
CREATE INDEX IF NOT EXISTS idx_question_types_active ON public.question_types(is_active);

-- Indexes for compose_question_types table
CREATE INDEX IF NOT EXISTS idx_compose_question_types_question_type ON public.compose_question_types(question_type_code);
CREATE INDEX IF NOT EXISTS idx_compose_question_types_subject ON public.compose_question_types(subject_code);
CREATE INDEX IF NOT EXISTS idx_compose_question_types_grade_level ON public.compose_question_types(grade_level_code);

-- Indexes for updated question_bank table
CREATE INDEX IF NOT EXISTS idx_question_bank_question_type_code ON public.question_bank(question_type_code);
CREATE INDEX IF NOT EXISTS idx_question_bank_subject_code ON public.question_bank(subject_code);
CREATE INDEX IF NOT EXISTS idx_question_bank_grade_level_code ON public.question_bank(grade_level_code);

-- =============================================
-- INSERT DEFAULT QUESTION TYPES DATA
-- =============================================

-- Insert standard question types (标准题型数据)
INSERT INTO public.question_types (code, type_name, description, is_active) 
VALUES 
    ('SINGLE_CHOICE', '单选题', '从多个选项中选择一个正确答案', TRUE),
    ('MULTI_CHOICE', '多选题', '从多个选项中选择多个正确答案', TRUE),
    ('TRUE_FALSE', '判断题', '判断陈述的真假', TRUE),
    ('FILL_BLANK', '填空题', '在空白处填入正确答案', TRUE),
    ('SHORT_ANSWER', '简答题', '用简短文字回答问题', TRUE),
    ('ESSAY', '论述题', '详细论述和分析问题', TRUE),
    ('CALCULATION', '计算题', '数学计算类题目', TRUE),
    ('PROOF', '证明题', '数学证明类题目', TRUE),
    ('ANALYSIS', '分析题', '分析和解释问题', TRUE),
    ('PRACTICAL', '实践题', '实际操作或应用题', TRUE),
    ('READING_COMPREHENSION', '阅读理解', '基于阅读材料回答问题', TRUE),
    ('LISTENING_COMPREHENSION', '听力理解', '基于听力材料回答问题', TRUE),
    ('TRANSLATION', '翻译题', '语言翻译类题目', TRUE),
    ('COMPOSITION', '作文题', '写作类题目', TRUE),
    ('EXPERIMENT', '实验题', '实验设计和分析题', TRUE)
ON CONFLICT (code) DO NOTHING;

-- =============================================
-- INSERT SUBJECT-GRADE-QUESTIONTYPE COMPOSITIONS
-- =============================================

-- Mathematics compositions (数学学科题型组合)
INSERT INTO public.compose_question_types (question_type_code, subject_code, grade_level_code) 
VALUES 
    -- 小学数学
    ('SINGLE_CHOICE', 'MATH', 'G1'),
    ('SINGLE_CHOICE', 'MATH', 'G2'),
    ('SINGLE_CHOICE', 'MATH', 'G3'),
    ('SINGLE_CHOICE', 'MATH', 'G4'),
    ('SINGLE_CHOICE', 'MATH', 'G5'),
    ('SINGLE_CHOICE', 'MATH', 'G6'),
    ('FILL_BLANK', 'MATH', 'G1'),
    ('FILL_BLANK', 'MATH', 'G2'),
    ('FILL_BLANK', 'MATH', 'G3'),
    ('FILL_BLANK', 'MATH', 'G4'),
    ('FILL_BLANK', 'MATH', 'G5'),
    ('FILL_BLANK', 'MATH', 'G6'),
    ('CALCULATION', 'MATH', 'G1'),
    ('CALCULATION', 'MATH', 'G2'),
    ('CALCULATION', 'MATH', 'G3'),
    ('CALCULATION', 'MATH', 'G4'),
    ('CALCULATION', 'MATH', 'G5'),
    ('CALCULATION', 'MATH', 'G6'),
    ('SHORT_ANSWER', 'MATH', 'G3'),
    ('SHORT_ANSWER', 'MATH', 'G4'),
    ('SHORT_ANSWER', 'MATH', 'G5'),
    ('SHORT_ANSWER', 'MATH', 'G6'),
    
    -- 初中数学
    ('SINGLE_CHOICE', 'MATH', 'G7'),
    ('SINGLE_CHOICE', 'MATH', 'G8'),
    ('SINGLE_CHOICE', 'MATH', 'G9'),
    ('FILL_BLANK', 'MATH', 'G7'),
    ('FILL_BLANK', 'MATH', 'G8'),
    ('FILL_BLANK', 'MATH', 'G9'),
    ('CALCULATION', 'MATH', 'G7'),
    ('CALCULATION', 'MATH', 'G8'),
    ('CALCULATION', 'MATH', 'G9'),
    ('SHORT_ANSWER', 'MATH', 'G7'),
    ('SHORT_ANSWER', 'MATH', 'G8'),
    ('SHORT_ANSWER', 'MATH', 'G9'),
    ('PROOF', 'MATH', 'G8'),
    ('PROOF', 'MATH', 'G9'),
    
    -- 高中数学
    ('SINGLE_CHOICE', 'MATH', 'G10'),
    ('SINGLE_CHOICE', 'MATH', 'G11'),
    ('SINGLE_CHOICE', 'MATH', 'G12'),
    ('FILL_BLANK', 'MATH', 'G10'),
    ('FILL_BLANK', 'MATH', 'G11'),
    ('FILL_BLANK', 'MATH', 'G12'),
    ('CALCULATION', 'MATH', 'G10'),
    ('CALCULATION', 'MATH', 'G11'),
    ('CALCULATION', 'MATH', 'G12'),
    ('SHORT_ANSWER', 'MATH', 'G10'),
    ('SHORT_ANSWER', 'MATH', 'G11'),
    ('SHORT_ANSWER', 'MATH', 'G12'),
    ('PROOF', 'MATH', 'G10'),
    ('PROOF', 'MATH', 'G11'),
    ('PROOF', 'MATH', 'G12')
ON CONFLICT (question_type_code, subject_code, grade_level_code) DO NOTHING;

-- Chinese compositions (语文学科题型组合)
INSERT INTO public.compose_question_types (question_type_code, subject_code, grade_level_code) 
VALUES 
    -- 小学语文
    ('SINGLE_CHOICE', 'CHINESE', 'G1'),
    ('SINGLE_CHOICE', 'CHINESE', 'G2'),
    ('SINGLE_CHOICE', 'CHINESE', 'G3'),
    ('SINGLE_CHOICE', 'CHINESE', 'G4'),
    ('SINGLE_CHOICE', 'CHINESE', 'G5'),
    ('SINGLE_CHOICE', 'CHINESE', 'G6'),
    ('FILL_BLANK', 'CHINESE', 'G1'),
    ('FILL_BLANK', 'CHINESE', 'G2'),
    ('FILL_BLANK', 'CHINESE', 'G3'),
    ('FILL_BLANK', 'CHINESE', 'G4'),
    ('FILL_BLANK', 'CHINESE', 'G5'),
    ('FILL_BLANK', 'CHINESE', 'G6'),
    ('SHORT_ANSWER', 'CHINESE', 'G2'),
    ('SHORT_ANSWER', 'CHINESE', 'G3'),
    ('SHORT_ANSWER', 'CHINESE', 'G4'),
    ('SHORT_ANSWER', 'CHINESE', 'G5'),
    ('SHORT_ANSWER', 'CHINESE', 'G6'),
    ('READING_COMPREHENSION', 'CHINESE', 'G3'),
    ('READING_COMPREHENSION', 'CHINESE', 'G4'),
    ('READING_COMPREHENSION', 'CHINESE', 'G5'),
    ('READING_COMPREHENSION', 'CHINESE', 'G6'),
    ('COMPOSITION', 'CHINESE', 'G2'),
    ('COMPOSITION', 'CHINESE', 'G3'),
    ('COMPOSITION', 'CHINESE', 'G4'),
    ('COMPOSITION', 'CHINESE', 'G5'),
    ('COMPOSITION', 'CHINESE', 'G6'),
    
    -- 初中语文
    ('SINGLE_CHOICE', 'CHINESE', 'G7'),
    ('SINGLE_CHOICE', 'CHINESE', 'G8'),
    ('SINGLE_CHOICE', 'CHINESE', 'G9'),
    ('FILL_BLANK', 'CHINESE', 'G7'),
    ('FILL_BLANK', 'CHINESE', 'G8'),
    ('FILL_BLANK', 'CHINESE', 'G9'),
    ('SHORT_ANSWER', 'CHINESE', 'G7'),
    ('SHORT_ANSWER', 'CHINESE', 'G8'),
    ('SHORT_ANSWER', 'CHINESE', 'G9'),
    ('READING_COMPREHENSION', 'CHINESE', 'G7'),
    ('READING_COMPREHENSION', 'CHINESE', 'G8'),
    ('READING_COMPREHENSION', 'CHINESE', 'G9'),
    ('COMPOSITION', 'CHINESE', 'G7'),
    ('COMPOSITION', 'CHINESE', 'G8'),
    ('COMPOSITION', 'CHINESE', 'G9'),
    
    -- 高中语文
    ('SINGLE_CHOICE', 'CHINESE', 'G10'),
    ('SINGLE_CHOICE', 'CHINESE', 'G11'),
    ('SINGLE_CHOICE', 'CHINESE', 'G12'),
    ('FILL_BLANK', 'CHINESE', 'G10'),
    ('FILL_BLANK', 'CHINESE', 'G11'),
    ('FILL_BLANK', 'CHINESE', 'G12'),
    ('SHORT_ANSWER', 'CHINESE', 'G10'),
    ('SHORT_ANSWER', 'CHINESE', 'G11'),
    ('SHORT_ANSWER', 'CHINESE', 'G12'),
    ('READING_COMPREHENSION', 'CHINESE', 'G10'),
    ('READING_COMPREHENSION', 'CHINESE', 'G11'),
    ('READING_COMPREHENSION', 'CHINESE', 'G12'),
    ('COMPOSITION', 'CHINESE', 'G10'),
    ('COMPOSITION', 'CHINESE', 'G11'),
    ('COMPOSITION', 'CHINESE', 'G12')
ON CONFLICT (question_type_code, subject_code, grade_level_code) DO NOTHING;

-- English compositions (英语学科题型组合)
INSERT INTO public.compose_question_types (question_type_code, subject_code, grade_level_code) 
VALUES 
    -- 小学英语
    ('SINGLE_CHOICE', 'ENGLISH', 'G3'),
    ('SINGLE_CHOICE', 'ENGLISH', 'G4'),
    ('SINGLE_CHOICE', 'ENGLISH', 'G5'),
    ('SINGLE_CHOICE', 'ENGLISH', 'G6'),
    ('FILL_BLANK', 'ENGLISH', 'G3'),
    ('FILL_BLANK', 'ENGLISH', 'G4'),
    ('FILL_BLANK', 'ENGLISH', 'G5'),
    ('FILL_BLANK', 'ENGLISH', 'G6'),
    ('LISTENING_COMPREHENSION', 'ENGLISH', 'G3'),
    ('LISTENING_COMPREHENSION', 'ENGLISH', 'G4'),
    ('LISTENING_COMPREHENSION', 'ENGLISH', 'G5'),
    ('LISTENING_COMPREHENSION', 'ENGLISH', 'G6'),
    ('READING_COMPREHENSION', 'ENGLISH', 'G5'),
    ('READING_COMPREHENSION', 'ENGLISH', 'G6'),
    
    -- 初中英语
    ('SINGLE_CHOICE', 'ENGLISH', 'G7'),
    ('SINGLE_CHOICE', 'ENGLISH', 'G8'),
    ('SINGLE_CHOICE', 'ENGLISH', 'G9'),
    ('FILL_BLANK', 'ENGLISH', 'G7'),
    ('FILL_BLANK', 'ENGLISH', 'G8'),
    ('FILL_BLANK', 'ENGLISH', 'G9'),
    ('LISTENING_COMPREHENSION', 'ENGLISH', 'G7'),
    ('LISTENING_COMPREHENSION', 'ENGLISH', 'G8'),
    ('LISTENING_COMPREHENSION', 'ENGLISH', 'G9'),
    ('READING_COMPREHENSION', 'ENGLISH', 'G7'),
    ('READING_COMPREHENSION', 'ENGLISH', 'G8'),
    ('READING_COMPREHENSION', 'ENGLISH', 'G9'),
    ('TRANSLATION', 'ENGLISH', 'G8'),
    ('TRANSLATION', 'ENGLISH', 'G9'),
    ('COMPOSITION', 'ENGLISH', 'G8'),
    ('COMPOSITION', 'ENGLISH', 'G9'),
    
    -- 高中英语
    ('SINGLE_CHOICE', 'ENGLISH', 'G10'),
    ('SINGLE_CHOICE', 'ENGLISH', 'G11'),
    ('SINGLE_CHOICE', 'ENGLISH', 'G12'),
    ('FILL_BLANK', 'ENGLISH', 'G10'),
    ('FILL_BLANK', 'ENGLISH', 'G11'),
    ('FILL_BLANK', 'ENGLISH', 'G12'),
    ('LISTENING_COMPREHENSION', 'ENGLISH', 'G10'),
    ('LISTENING_COMPREHENSION', 'ENGLISH', 'G11'),
    ('LISTENING_COMPREHENSION', 'ENGLISH', 'G12'),
    ('READING_COMPREHENSION', 'ENGLISH', 'G10'),
    ('READING_COMPREHENSION', 'ENGLISH', 'G11'),
    ('READING_COMPREHENSION', 'ENGLISH', 'G12'),
    ('TRANSLATION', 'ENGLISH', 'G10'),
    ('TRANSLATION', 'ENGLISH', 'G11'),
    ('TRANSLATION', 'ENGLISH', 'G12'),
    ('COMPOSITION', 'ENGLISH', 'G10'),
    ('COMPOSITION', 'ENGLISH', 'G11'),
    ('COMPOSITION', 'ENGLISH', 'G12')
ON CONFLICT (question_type_code, subject_code, grade_level_code) DO NOTHING;


-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON TABLE public.question_types IS '题型字典表 - 存储系统支持的标准题型定义';
COMMENT ON COLUMN public.question_types.code IS '题型编码，全局唯一';
COMMENT ON COLUMN public.question_types.type_name IS '题型名称';
COMMENT ON COLUMN public.question_types.description IS '题型描述';
COMMENT ON COLUMN public.question_types.is_active IS '是否启用';

COMMENT ON TABLE public.compose_question_types IS '题型组合表 - 定义学科-年级-题型的有效组合';
COMMENT ON COLUMN public.compose_question_types.question_type_code IS '题型编码，关联question_types.code';
COMMENT ON COLUMN public.compose_question_types.subject_code IS '学科编码，关联subjects.code';
COMMENT ON COLUMN public.compose_question_types.grade_level_code IS '年级编码，关联grade_levels.code';

COMMENT ON COLUMN public.question_bank.question_type_code IS '题型编码，关联question_types.code';
COMMENT ON COLUMN public.question_bank.subject_code IS '学科编码，关联subjects.code';
COMMENT ON COLUMN public.question_bank.grade_level_code IS '年级编码，关联grade_levels.code';