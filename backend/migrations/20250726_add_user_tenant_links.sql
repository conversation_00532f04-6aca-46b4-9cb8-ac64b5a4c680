-- Add User-Tenant Association Table
-- Migration: 20250726_add_user_tenant_links
-- Purpose: Create a central mapping table between users and tenants to track access relationships

-- User-Tenant Links Table (Central Mapping)
-- This table establishes which users have access to which tenants
-- It's separate from user_identities which tracks specific roles within tenants
CREATE TABLE IF NOT EXISTS public.user_tenant_links (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
    access_type VARCHAR(30) DEFAULT 'member' CHECK (access_type IN ('member', 'admin', 'viewer', 'suspended')),
    granted_by UUID REFERENCES public.users(id), -- Who granted this access
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE, -- Optional expiration date
    last_accessed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one user can only have one access record per tenant
    UNIQUE(user_id, tenant_id)
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_user_tenant_links_user ON public.user_tenant_links(user_id);
CREATE INDEX IF NOT EXISTS idx_user_tenant_links_tenant ON public.user_tenant_links(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_tenant_links_access_type ON public.user_tenant_links(access_type);
CREATE INDEX IF NOT EXISTS idx_user_tenant_links_granted_by ON public.user_tenant_links(granted_by);
CREATE INDEX IF NOT EXISTS idx_user_tenant_links_expires ON public.user_tenant_links(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_tenant_links_last_accessed ON public.user_tenant_links(last_accessed_at);

-- Comments for table documentation
COMMENT ON TABLE public.user_tenant_links IS 'Central mapping table between users and tenants to track access relationships';
COMMENT ON COLUMN public.user_tenant_links.access_type IS 'Type of access: member (normal access), admin (tenant admin), viewer (read-only), suspended (temporarily blocked)';
COMMENT ON COLUMN public.user_tenant_links.granted_by IS 'User who granted this access (usually a tenant admin or system admin)';
COMMENT ON COLUMN public.user_tenant_links.expires_at IS 'Optional expiration date for temporary access';
COMMENT ON COLUMN public.user_tenant_links.last_accessed_at IS 'Timestamp of last tenant access for tracking activity';