-- 初始化 Casbin 权限策略数据
-- Migration: 20250802_casbin_policies_initialization.sql
-- Description: Initialize Casbin RBAC policies based on PRD requirements
-- Depends on: 20250801_casbin_policies_storage.sql

-- ================================================================
-- 系统级角色权限策略 (全局)
-- ================================================================

-- 系统超级管理员 - 拥有所有权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
('p', 'super_admin', '*', '*', '*', 'allow', 'system'),
-- 系统管理相关
('p', 'super_admin', '*', 'system', 'read', 'allow', 'system'),
('p', 'super_admin', '*', 'system', 'write', 'allow', 'system'),
('p', 'super_admin', '*', 'system', 'manage', 'allow', 'system'),
-- 租户管理
('p', 'super_admin', '*', 'tenant', 'read', 'allow', 'system'),
('p', 'super_admin', '*', 'tenant', 'create', 'allow', 'system'),
('p', 'super_admin', '*', 'tenant', 'update', 'allow', 'system'),
('p', 'super_admin', '*', 'tenant', 'delete', 'allow', 'system'),
('p', 'super_admin', '*', 'tenant', 'manage', 'allow', 'system'),
-- 角色权限管理
('p', 'super_admin', '*', 'role', 'read', 'allow', 'system'),
('p', 'super_admin', '*', 'role', 'create', 'allow', 'system'),
('p', 'super_admin', '*', 'role', 'update', 'allow', 'system'),
('p', 'super_admin', '*', 'role', 'delete', 'allow', 'system'),
('p', 'super_admin', '*', 'role', 'assign', 'allow', 'system'),
-- 权限管理
('p', 'super_admin', '*', 'permission', 'read', 'allow', 'system'),
('p', 'super_admin', '*', 'permission', 'create', 'allow', 'system'),
('p', 'super_admin', '*', 'permission', 'update', 'allow', 'system'),
('p', 'super_admin', '*', 'permission', 'delete', 'allow', 'system'),
-- Casbin策略管理
('p', 'super_admin', '*', 'casbin', 'read', 'allow', 'system'),
('p', 'super_admin', '*', 'casbin', 'write', 'allow', 'system'),
('p', 'super_admin', '*', 'casbin', 'manage', 'allow', 'system');

-- ================================================================
-- 租户级角色权限策略模板 (适用于所有租户)
-- ================================================================

-- 租户管理员权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 租户内用户管理
('p', 'tenant_admin', 'tenant_*', 'user', 'read', 'allow', 'template'),
('p', 'tenant_admin', 'tenant_*', 'user', 'create', 'allow', 'template'),
('p', 'tenant_admin', 'tenant_*', 'user', 'update', 'allow', 'template'),
('p', 'tenant_admin', 'tenant_*', 'user', 'delete', 'allow', 'template'),
-- 租户内角色管理
('p', 'tenant_admin', 'tenant_*', 'role', 'read', 'allow', 'template'),
('p', 'tenant_admin', 'tenant_*', 'role', 'assign', 'allow', 'template'),
-- 租户配置管理
('p', 'tenant_admin', 'tenant_*', 'tenant_config', 'read', 'allow', 'template'),
('p', 'tenant_admin', 'tenant_*', 'tenant_config', 'update', 'allow', 'template');

-- 校长权限 - 学校整体管理决策
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 学校级别全部数据
('p', 'principal', 'tenant_*', 'school', 'read', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'school', 'write', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'school', 'manage', 'allow', 'template'),
-- 学生管理
('p', 'principal', 'tenant_*', 'student', 'read', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'student', 'write', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'student', 'manage', 'allow', 'template'),
-- 教师管理
('p', 'principal', 'tenant_*', 'teacher', 'read', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'teacher', 'write', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'teacher', 'manage', 'allow', 'template'),
-- 考试管理
('p', 'principal', 'tenant_*', 'exam', 'read', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'exam', 'create', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'exam', 'manage', 'allow', 'template'),
-- 成绩管理
('p', 'principal', 'tenant_*', 'grade', 'read', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'grade', 'write', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'grade', 'manage', 'allow', 'template'),
-- 班级管理
('p', 'principal', 'tenant_*', 'class', 'read', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'class', 'create', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'class', 'manage', 'allow', 'template'),
-- 决策分析
('p', 'principal', 'tenant_*', 'analysis', 'read', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'report', 'read', 'allow', 'template');

-- 教导主任权限 - 教学管理、考试安排、成绩审核
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 教学相关数据
('p', 'academic_director', 'tenant_*', 'teaching', 'read', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'teaching', 'write', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'teaching', 'manage', 'allow', 'template'),
-- 学生管理（教学相关）
('p', 'academic_director', 'tenant_*', 'student', 'read', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'student', 'write', 'allow', 'template'),
-- 教师管理（教学相关）
('p', 'academic_director', 'tenant_*', 'teacher', 'read', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'teacher', 'write', 'allow', 'template'),
-- 考试管理
('p', 'academic_director', 'tenant_*', 'exam', 'read', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'exam', 'create', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'exam', 'write', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'exam', 'manage', 'allow', 'template'),
-- 成绩审核
('p', 'academic_director', 'tenant_*', 'grade', 'read', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'grade', 'write', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'grade', 'audit', 'allow', 'template'),
-- 班级管理（教学相关）
('p', 'academic_director', 'tenant_*', 'class', 'read', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'class', 'write', 'allow', 'template');

-- 学科组长权限 - 学科教学管理、跨年级学科协调
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 学科数据（跨年级）
('p', 'subject_leader', 'tenant_*', 'subject', 'read', 'allow', 'template'),
('p', 'subject_leader', 'tenant_*', 'subject', 'write', 'allow', 'template'),
('p', 'subject_leader', 'tenant_*', 'subject', 'manage', 'allow', 'template'),
-- 学科考试统筹
('p', 'subject_leader', 'tenant_*', 'subject_exam', 'read', 'allow', 'template'),
('p', 'subject_leader', 'tenant_*', 'subject_exam', 'create', 'allow', 'template'),
('p', 'subject_leader', 'tenant_*', 'subject_exam', 'manage', 'allow', 'template'),
-- 学科成绩管理
('p', 'subject_leader', 'tenant_*', 'subject_grade', 'read', 'allow', 'template'),
('p', 'subject_leader', 'tenant_*', 'subject_grade', 'write', 'allow', 'template'),
-- 学科教师管理
('p', 'subject_leader', 'tenant_*', 'subject_teacher', 'read', 'allow', 'template'),
('p', 'subject_leader', 'tenant_*', 'subject_teacher', 'write', 'allow', 'template');

-- 年级长权限 - 年级管理、年级考试管理、成绩分析
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 年级数据（跨学科）
('p', 'grade_leader', 'tenant_*', 'grade_data', 'read', 'allow', 'template'),
('p', 'grade_leader', 'tenant_*', 'grade_data', 'write', 'allow', 'template'),
('p', 'grade_leader', 'tenant_*', 'grade_data', 'manage', 'allow', 'template'),
-- 年级考试管理
('p', 'grade_leader', 'tenant_*', 'grade_exam', 'read', 'allow', 'template'),
('p', 'grade_leader', 'tenant_*', 'grade_exam', 'create', 'allow', 'template'),
('p', 'grade_leader', 'tenant_*', 'grade_exam', 'manage', 'allow', 'template'),
-- 年级成绩分析
('p', 'grade_leader', 'tenant_*', 'grade_analysis', 'read', 'allow', 'template'),
('p', 'grade_leader', 'tenant_*', 'grade_analysis', 'write', 'allow', 'template'),
-- 年级学生管理
('p', 'grade_leader', 'tenant_*', 'grade_student', 'read', 'allow', 'template'),
('p', 'grade_leader', 'tenant_*', 'grade_student', 'write', 'allow', 'template');

-- 班主任权限 - 本班级学生成绩跟踪、波动分析
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 班级数据
('p', 'class_teacher', 'tenant_*', 'class_data', 'read', 'allow', 'template'),
('p', 'class_teacher', 'tenant_*', 'class_data', 'write', 'allow', 'template'),
('p', 'class_teacher', 'tenant_*', 'class_data', 'manage', 'allow', 'template'),
-- 班级学生管理
('p', 'class_teacher', 'tenant_*', 'class_student', 'read', 'allow', 'template'),
('p', 'class_teacher', 'tenant_*', 'class_student', 'write', 'allow', 'template'),
-- 班级成绩跟踪
('p', 'class_teacher', 'tenant_*', 'class_grade', 'read', 'allow', 'template'),
('p', 'class_teacher', 'tenant_*', 'class_grade', 'write', 'allow', 'template'),
-- 成绩分析
('p', 'class_teacher', 'tenant_*', 'class_analysis', 'read', 'allow', 'template'),
('p', 'class_teacher', 'tenant_*', 'class_analysis', 'write', 'allow', 'template');

-- 任课老师权限 - 任教学科班级成绩查看、录入
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 任课数据
('p', 'teacher', 'tenant_*', 'teaching_data', 'read', 'allow', 'template'),
('p', 'teacher', 'tenant_*', 'teaching_data', 'write', 'allow', 'template'),
-- 任课学生成绩
('p', 'teacher', 'tenant_*', 'teaching_grade', 'read', 'allow', 'template'),
('p', 'teacher', 'tenant_*', 'teaching_grade', 'write', 'allow', 'template'),
-- 任课考试参与
('p', 'teacher', 'tenant_*', 'teaching_exam', 'read', 'allow', 'template'),
('p', 'teacher', 'tenant_*', 'teaching_exam', 'write', 'allow', 'template');

-- 学生权限 - 个人相关数据查看
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 个人成绩查看
('p', 'student', 'tenant_*', 'student_grade', 'read', 'allow', 'template'),
-- 个人信息
('p', 'student', 'tenant_*', 'student_profile', 'read', 'allow', 'template'),
('p', 'student', 'tenant_*', 'student_profile', 'write', 'allow', 'template'),
-- 个人学情分析
('p', 'student', 'tenant_*', 'student_analysis', 'read', 'allow', 'template'),
-- 个人课表
('p', 'student', 'tenant_*', 'student_schedule', 'read', 'allow', 'template');

-- 家长权限 - 子女相关数据查看（支持跨租户）
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 子女成绩查看
('p', 'parent', 'tenant_*', 'child_grade', 'read', 'allow', 'template'),
-- 子女信息查看
('p', 'parent', 'tenant_*', 'child_profile', 'read', 'allow', 'template'),
-- 子女学情关注
('p', 'parent', 'tenant_*', 'child_analysis', 'read', 'allow', 'template'),
-- 子女课表
('p', 'parent', 'tenant_*', 'child_schedule', 'read', 'allow', 'template');

-- ================================================================
-- 业务操作角色权限策略
-- ================================================================

-- 考试管理员权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 考试管理
('p', 'exam_manager', 'tenant_*', 'exam', 'read', 'allow', 'template'),
('p', 'exam_manager', 'tenant_*', 'exam', 'create', 'allow', 'template'),
('p', 'exam_manager', 'tenant_*', 'exam', 'write', 'allow', 'template'),
('p', 'exam_manager', 'tenant_*', 'exam', 'manage', 'allow', 'template'),
-- 试卷管理
('p', 'exam_manager', 'tenant_*', 'paper', 'read', 'allow', 'template'),
('p', 'exam_manager', 'tenant_*', 'paper', 'create', 'allow', 'template'),
('p', 'exam_manager', 'tenant_*', 'paper', 'write', 'allow', 'template'),
('p', 'exam_manager', 'tenant_*', 'paper', 'manage', 'allow', 'template'),
-- 评分标准管理
('p', 'exam_manager', 'tenant_*', 'scoring_standard', 'read', 'allow', 'template'),
('p', 'exam_manager', 'tenant_*', 'scoring_standard', 'create', 'allow', 'template'),
('p', 'exam_manager', 'tenant_*', 'scoring_standard', 'write', 'allow', 'template'),
('p', 'exam_manager', 'tenant_*', 'scoring_standard', 'manage', 'allow', 'template');

-- 阅卷管理员权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 阅卷管理
('p', 'grading_manager', 'tenant_*', 'grading', 'read', 'allow', 'template'),
('p', 'grading_manager', 'tenant_*', 'grading', 'write', 'allow', 'template'),
('p', 'grading_manager', 'tenant_*', 'grading', 'manage', 'allow', 'template'),
-- 阅卷任务分配
('p', 'grading_manager', 'tenant_*', 'grading_task', 'read', 'allow', 'template'),
('p', 'grading_manager', 'tenant_*', 'grading_task', 'create', 'allow', 'template'),
('p', 'grading_manager', 'tenant_*', 'grading_task', 'assign', 'allow', 'template'),
-- 质量控制
('p', 'grading_manager', 'tenant_*', 'quality_control', 'read', 'allow', 'template'),
('p', 'grading_manager', 'tenant_*', 'quality_control', 'write', 'allow', 'template'),
('p', 'grading_manager', 'tenant_*', 'quality_control', 'manage', 'allow', 'template');

-- 试卷扫描员权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 试卷扫描
('p', 'paper_scanner', 'tenant_*', 'paper_scan', 'read', 'allow', 'template'),
('p', 'paper_scanner', 'tenant_*', 'paper_scan', 'write', 'allow', 'template'),
('p', 'paper_scanner', 'tenant_*', 'paper_scan', 'manage', 'allow', 'template'),
-- 异常处理
('p', 'paper_scanner', 'tenant_*', 'scan_exception', 'read', 'allow', 'template'),
('p', 'paper_scanner', 'tenant_*', 'scan_exception', 'write', 'allow', 'template');

-- 阅卷员权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 试卷评阅
('p', 'grader', 'tenant_*', 'paper_grading', 'read', 'allow', 'template'),
('p', 'grader', 'tenant_*', 'paper_grading', 'write', 'allow', 'template'),
-- 成绩录入
('p', 'grader', 'tenant_*', 'grade_input', 'read', 'allow', 'template'),
('p', 'grader', 'tenant_*', 'grade_input', 'write', 'allow', 'template');

-- ================================================================
-- 菜单访问权限策略
-- ================================================================

-- 为各角色分配菜单访问权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
-- 系统超级管理员 - 所有菜单
('p', 'super_admin', '*', 'menu', 'access', 'allow', 'system'),

-- 校长 - 除系统管理外的所有菜单
('p', 'principal', 'tenant_*', 'menu:student_management', 'access', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'menu:exam_management', 'access', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'menu:grade_management', 'access', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'menu:class_management', 'access', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'menu:teacher_management', 'access', 'allow', 'template'),
('p', 'principal', 'tenant_*', 'menu:personal_center', 'access', 'allow', 'template'),

-- 教导主任 - 教学相关菜单
('p', 'academic_director', 'tenant_*', 'menu:student_management', 'access', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'menu:exam_management', 'access', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'menu:grade_management', 'access', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'menu:class_management', 'access', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'menu:teacher_management', 'access', 'allow', 'template'),
('p', 'academic_director', 'tenant_*', 'menu:personal_center', 'access', 'allow', 'template'),

-- 学科组长 - 学科相关菜单
('p', 'subject_leader', 'tenant_*', 'menu:exam_management', 'access', 'allow', 'template'),
('p', 'subject_leader', 'tenant_*', 'menu:grade_management', 'access', 'allow', 'template'),
('p', 'subject_leader', 'tenant_*', 'menu:teacher_management', 'access', 'allow', 'template'),
('p', 'subject_leader', 'tenant_*', 'menu:personal_center', 'access', 'allow', 'template'),

-- 年级长 - 年级相关菜单
('p', 'grade_leader', 'tenant_*', 'menu:student_management', 'access', 'allow', 'template'),
('p', 'grade_leader', 'tenant_*', 'menu:exam_management', 'access', 'allow', 'template'),
('p', 'grade_leader', 'tenant_*', 'menu:grade_management', 'access', 'allow', 'template'),
('p', 'grade_leader', 'tenant_*', 'menu:class_management', 'access', 'allow', 'template'),
('p', 'grade_leader', 'tenant_*', 'menu:personal_center', 'access', 'allow', 'template'),

-- 班主任 - 班级相关菜单
('p', 'class_teacher', 'tenant_*', 'menu:student_management', 'access', 'allow', 'template'),
('p', 'class_teacher', 'tenant_*', 'menu:grade_management', 'access', 'allow', 'template'),
('p', 'class_teacher', 'tenant_*', 'menu:class_management', 'access', 'allow', 'template'),
('p', 'class_teacher', 'tenant_*', 'menu:personal_center', 'access', 'allow', 'template'),

-- 任课老师 - 教学相关菜单
('p', 'teacher', 'tenant_*', 'menu:grade_management', 'access', 'allow', 'template'),
('p', 'teacher', 'tenant_*', 'menu:personal_center', 'access', 'allow', 'template'),

-- 学生 - 个人中心
('p', 'student', 'tenant_*', 'menu:personal_center', 'access', 'allow', 'template'),

-- 家长 - 个人中心
('p', 'parent', 'tenant_*', 'menu:personal_center', 'access', 'allow', 'template');

-- ================================================================
-- 角色继承关系 (g2策略)
-- ================================================================

-- 建立角色继承层次
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, tenant_id) VALUES
-- 校长继承教导主任权限
('g2', 'principal', 'academic_director', 'tenant_*', 'template'),
-- 教导主任继承学科组长权限
('g2', 'academic_director', 'subject_leader', 'tenant_*', 'template'),
-- 学科组长继承任课老师权限  
('g2', 'subject_leader', 'teacher', 'tenant_*', 'template'),
-- 年级长继承班主任权限
('g2', 'grade_leader', 'class_teacher', 'tenant_*', 'template'),
-- 班主任继承任课老师权限
('g2', 'class_teacher', 'teacher', 'tenant_*', 'template');

-- ================================================================
-- 临时权限策略模板 (简化的特殊授权)
-- ================================================================

-- 临时查看权限模板
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
('p', 'temp_viewer', 'tenant_*', 'temp_data', 'read', 'allow', 'template'),
('p', 'temp_viewer', 'tenant_*', 'temp_analysis', 'read', 'allow', 'template');

-- 临时管理权限模板
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
('p', 'temp_manager', 'tenant_*', 'temp_data', 'read', 'allow', 'template'),
('p', 'temp_manager', 'tenant_*', 'temp_data', 'write', 'allow', 'template'),
('p', 'temp_manager', 'tenant_*', 'temp_manage', 'manage', 'allow', 'template');

-- 代理职务权限模板
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES
('p', 'deputy_role', 'tenant_*', 'deputy_data', 'read', 'allow', 'template'),
('p', 'deputy_role', 'tenant_*', 'deputy_data', 'write', 'allow', 'template'),
('p', 'deputy_role', 'tenant_*', 'deputy_data', 'manage', 'allow', 'template');

-- ================================================================
-- 为已存在的租户创建实际策略
-- ================================================================

-- 为每个现有租户创建基于模板的实际策略
-- 这里使用存储过程来批量创建策略，避免重复的INSERT语句

CREATE OR REPLACE FUNCTION initialize_tenant_policies()
RETURNS VOID AS $$
DECLARE
    tenant_record RECORD;
    policy_record RECORD;
    new_tenant_id TEXT;
BEGIN
    -- 为每个活跃租户创建策略
    FOR tenant_record IN 
        SELECT id::text as tenant_id, schema_name 
        FROM public.tenants 
        WHERE status = 'active'
    LOOP
        new_tenant_id := tenant_record.tenant_id;
        
        -- 复制模板策略到具体租户
        FOR policy_record IN 
            SELECT ptype, v0, 
                   REPLACE(v1, 'tenant_*', new_tenant_id) as v1,
                   v2, v3, v4
            FROM public.casbin_policies 
            WHERE tenant_id = 'template'
        LOOP
            -- 插入租户特定的策略
            INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id)
            VALUES (
                policy_record.ptype,
                policy_record.v0,
                policy_record.v1,
                policy_record.v2,
                policy_record.v3,
                policy_record.v4,
                new_tenant_id
            );
        END LOOP;
        
        RAISE NOTICE 'Initialized policies for tenant: %', new_tenant_id;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 执行函数创建租户策略
SELECT initialize_tenant_policies();

-- 删除临时函数
DROP FUNCTION initialize_tenant_policies();

-- ================================================================
-- 为策略管理页面添加菜单权限
-- ================================================================

-- 添加策略管理菜单项到系统管理下
INSERT INTO public.menu_permissions (menu_id, name, path, icon, parent_id, sort_order, required_permissions, data_scopes) VALUES
('casbin_policy_management', '策略管理', '/casbin-policies', 'settings', 'system_management', 904, ARRAY['casbin:read'], NULL)
ON CONFLICT (menu_id) DO UPDATE SET
    name = EXCLUDED.name,
    path = EXCLUDED.path,
    icon = EXCLUDED.icon,
    parent_id = EXCLUDED.parent_id,
    sort_order = EXCLUDED.sort_order,
    required_permissions = EXCLUDED.required_permissions,
    updated_at = NOW();

-- ================================================================
-- 统计信息更新
-- ================================================================

-- 更新租户策略统计
UPDATE public.tenant_policy_stats 
SET 
    total_policies = (SELECT COUNT(*) FROM public.casbin_policies WHERE tenant_id = tenant_policy_stats.tenant_id),
    user_role_mappings = (SELECT COUNT(*) FROM public.casbin_policies WHERE tenant_id = tenant_policy_stats.tenant_id AND ptype = 'g'),
    permission_policies = (SELECT COUNT(*) FROM public.casbin_policies WHERE tenant_id = tenant_policy_stats.tenant_id AND ptype = 'p'),
    role_inheritance_count = (SELECT COUNT(*) FROM public.casbin_policies WHERE tenant_id = tenant_policy_stats.tenant_id AND ptype = 'g2'),
    last_sync_at = NOW(),
    updated_at = NOW();

-- ================================================================
-- 添加注释说明
-- ================================================================

COMMENT ON COLUMN public.casbin_policies.v0 IS '策略主体：角色名称或用户身份标识';
COMMENT ON COLUMN public.casbin_policies.v1 IS '策略域：租户ID或通配符*';
COMMENT ON COLUMN public.casbin_policies.v2 IS '策略对象：资源名称或资源:范围';
COMMENT ON COLUMN public.casbin_policies.v3 IS '策略动作：read, write, create, delete, manage等';
COMMENT ON COLUMN public.casbin_policies.v4 IS '策略效果：allow允许或deny拒绝';
COMMENT ON COLUMN public.casbin_policies.tenant_id IS '租户隔离标识：system=系统级, template=模板, 其他=具体租户ID';