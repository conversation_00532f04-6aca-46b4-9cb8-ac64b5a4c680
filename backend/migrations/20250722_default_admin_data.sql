-- Default Admin Data Migration
-- Migration: 20250722_default_admin_data

-- Create default super admin user (phone: admin, password: admin123)
-- Note: This should be changed in production
INSERT INTO public.users (phone_number, phone_verified, password_hash, salt, is_active, username) VALUES
('admin', true, 
 '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jg/L7G', -- admin123
 'default_salt', true, 'admin')
ON CONFLICT (phone_number) DO NOTHING;

-- Assign super admin role to default admin user
INSERT INTO public.admin_user_roles (user_id, role_id, granted_by)
SELECT u.id, r.id, u.id
FROM public.users u, public.admin_roles r
WHERE u.phone_number = 'admin' AND r.name = 'super_admin'
ON CONFLICT (user_id, role_id) DO NOTHING;

-- Create a default tenant for development
INSERT INTO public.tenants (name, schema_name, status, created_by) 
SELECT 'Default Tenant', 'tenant_default', 'active', u.id
FROM public.users u 
WHERE u.phone_number = 'admin'
ON CONFLICT (schema_name) DO NOTHING;