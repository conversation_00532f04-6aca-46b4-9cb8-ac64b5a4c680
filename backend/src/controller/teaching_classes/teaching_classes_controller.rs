use std::{
    collections::{HashMap, HashSet},
    str::FromStr,
};

use axum::{
    extract::{Path, State},
    http::HeaderMap,
    routing::{get, post},
    Json, Router,
};
use uuid::Uuid;

use crate::{
    middleware::auth_middleware::AuthExtractor,
    model::{
        teaching_classes::teaching_classes::{
            CreateTeachingClassesParams, DeleteTeachingClassesParams, FindAllStudentInClassParams,
            MoveStudentToTeachingClassesParams, RemoveStudentFromTeachingClassesParams,
            TeachingClasses, TeachingClassesDetail, TeachingClassesStatistics,
            UpdateTeachingClassesParams,
        },
        Student,
    },
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/getStatistics", get(get_statistics))
        .route("/getUserClassList", get(get_user_class_list))
        .route("/createClasses", post(create_classes))
        .route("/updateClasses", post(update_classes))
        .route("/deleteClass", post(delete_class))
        .route("/findAllStudentInClass", post(find_all_student_in_class))
        .route(
            "/moveStudentToTeachingClasses",
            post(move_student_to_teaching_classes),
        )
        .route(
            "/removeStudentFromTeachingClasses",
            post(remove_student_from_teaching_classes),
        )
}

pub async fn get_statistics(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    header_map: HeaderMap,
    Path(tenant_name): Path<String>,
) -> Result<ApiResponse<TeachingClassesStatistics>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    match state
        .teaching_classes_service
        .get_statistics(&context, &tenant_id, &tenant_name)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(msg) => Err(responses::error(&msg, None)),
    }
}

pub async fn get_user_class_list(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    header_map: HeaderMap,
    Path(tenant_name): Path<String>,
) -> Result<ApiResponse<Vec<TeachingClassesDetail>>, ApiResponse<()>> {
    //查询班级列表
    let mut class_list: Vec<TeachingClasses> = vec![];
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if context.is_admin_in_tenant(tenant_id) {
        //管理员
        class_list.append(
            &mut state
                .teaching_classes_service
                .get_all_class_list(&tenant_name)
                .await
                .map_err(|e| responses::error(&e, None))?,
        );
    } else {
        class_list.append(
            &mut state
                .teaching_classes_service
                .get_user_class_list(&tenant_name, &context.user_id)
                .await
                .map_err(|e| responses::error(&e, None))?,
        );
    }

    //联查额外信息
    let mut teacher_id_set = HashSet::<Uuid>::new();
    let mut subject_group_id_set = HashSet::<Uuid>::new();
    let mut class_id_set = HashSet::<Uuid>::new();
    class_list.iter().for_each(|c| {
        if c.teacher_id.is_some() {
            teacher_id_set.insert(c.teacher_id.unwrap().clone());
        }
        if c.subject_group_id.is_some() {
            subject_group_id_set.insert(c.subject_group_id.unwrap().clone());
        }
        class_id_set.insert(c.id.clone());
    });
    let class_id_list = class_id_set.into_iter().collect();
    //联查教师信息
    let teacher_list = state
        .teacher_service
        .find_all_by_id_in(&tenant_name, teacher_id_set.into_iter().collect())
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))?;
    let mut teacher_id_to_name_map = HashMap::<Uuid, String>::new();
    teacher_list.iter().for_each(|teacher| {
        teacher_id_to_name_map.insert(teacher.id, teacher.name.clone());
    });
    //联查学科组信息
    let subject_groups_list = state
        .subject_groups_service
        .find_all_by_id_list(&tenant_name, &subject_group_id_set.into_iter().collect())
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))?;
    let mut subject_group_id_to_name_map = HashMap::<Uuid, String>::new();
    subject_groups_list.iter().for_each(|item| {
        subject_group_id_to_name_map.insert(item.id.clone(), item.group_name.clone());
    });
    //联查班级人数统计信息
    let class_id_to_student_count_map = state
        .teaching_classes_service
        .batch_count_by_class(&tenant_name, &class_id_list)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))?;
    Ok(responses::success(
        class_list
            .into_iter()
            .map(|classes| {
                let TeachingClasses {
                    id,
                    class_name,
                    code: class_code,
                    academic_year,
                    teacher_id,
                    created_at,
                    updated_at,
                    subject_group_id,
                    is_active,
                } = classes;
                TeachingClassesDetail {
                    id,
                    class_name,
                    code: class_code,
                    academic_year,
                    subject_group_id,
                    teacher_id,
                    created_at,
                    updated_at,
                    is_active,
                    teacher_name: teacher_id.map_or(None, |teacher_id| {
                        Some(
                            teacher_id_to_name_map
                                .get(&teacher_id)
                                .map_or("/".to_string(), |v| v.clone()),
                        )
                    }),
                    subject_groups_name: subject_group_id.map_or(None, |subject_group_id| {
                        Some(
                            subject_group_id_to_name_map
                                .get(&subject_group_id)
                                .map_or("/".to_string(), |v| v.clone()),
                        )
                    }),
                    total_student: class_id_to_student_count_map.get(&id).unwrap_or(&0).clone(),
                }
            })
            .collect(),
        None,
    ))
}

pub async fn create_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(admin_context): AuthExtractor,
    Json(params): Json<CreateTeachingClassesParams>,
) -> Result<ApiResponse<TeachingClasses>, ApiResponse<()>> {
    state
        .teaching_classes_service
        .create_classes(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn update_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(admin_context): AuthExtractor,
    Json(params): Json<UpdateTeachingClassesParams>,
) -> Result<ApiResponse<TeachingClasses>, ApiResponse<()>> {
    state
        .teaching_classes_service
        .update_classes(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn delete_class(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<DeleteTeachingClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .teaching_classes_service
        .delete_class(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|_| responses::success_no_data(None))
}

pub async fn find_all_student_in_class(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<FindAllStudentInClassParams>,
) -> Result<ApiResponse<Vec<Student>>, ApiResponse<()>> {
    state
        .teaching_classes_service
        .find_all_student_in_class(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

pub async fn move_student_to_teaching_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<MoveStudentToTeachingClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .teaching_classes_service
        .move_student_to_teaching_classes(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|_| responses::success_no_data(None))
}

pub async fn remove_student_from_teaching_classes(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<RemoveStudentFromTeachingClassesParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .teaching_classes_service
        .remove_student_from_teaching_classes(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|_| responses::success_no_data(None))
}
