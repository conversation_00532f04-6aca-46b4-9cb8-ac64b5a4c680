use crate::middleware::auth_middleware::AuthExtractor;
use crate::middleware::tenant_middleware::TenantExtractor;
use crate::service::role::role_service::RoleService;
use crate::model::role::{
    CreateRoleRequest, UpdateRoleRequest, RoleQueryParams, AssignRoleRequest
};
use crate::utils::api_response::{ErrorResponse, SuccessResponse, PagedResponse};
use axum::{
    extract::{Json, State, Path, Query},
    http::StatusCode,
    response::IntoResponse,
    routing::{get, post, delete, patch},
    Router,
};
use uuid::Uuid;
use tracing::{info, error};

/// 获取角色列表
#[axum::debug_handler]
pub async fn get_roles(
    Query(params): Query<RoleQueryParams>,
    AuthExtractor(auth_context): AuthExtractor,
    State(role_service): State<RoleService>,
) -> impl IntoResponse {
    // 权限检查
    if !auth_context.has_permission("role", "read") {
        return (
            StatusCode::F<PERSON><PERSON>D<PERSON><PERSON>,
            <PERSON><PERSON>(ErrorResponse {
                success: false,
                message: "没有权限查看角色".to_string(),
                error_code: Some("INSUFFICIENT_PERMISSIONS".to_string()),
            }),
        ).into_response();
    }

    match role_service.get_roles(params).await {
        Ok(page_result) => {
            Json(PagedResponse::new(
                page_result.data,
                page_result.total,
                page_result.page as i32,
                page_result.page_size as i32,
                Some("角色列表获取成功".to_string())
            )).into_response()
        },
        Err(e) => {
            error!("Failed to get roles: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    success: false,
                    message: e.to_string(),
                    error_code: Some("GET_ROLES_FAILED".to_string()),
                }),
            ).into_response()
        }
    }
}

/// 获取单个角色详情
#[axum::debug_handler]
pub async fn get_role_by_id(
    Path(role_id): Path<Uuid>,
    AuthExtractor(auth_context): AuthExtractor,
    State(role_service): State<RoleService>,
) -> impl IntoResponse {
    // 权限检查
    if !auth_context.has_permission("role", "read") {
        return (
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                success: false,
                message: "没有权限查看角色".to_string(),
                error_code: Some("INSUFFICIENT_PERMISSIONS".to_string()),
            }),
        ).into_response();
    }

    match role_service.get_role_by_id(role_id).await {
        Ok(role) => {
            Json(SuccessResponse {
                success: true,
                data: Some(role),
                message: "角色详情获取成功".to_string(),
            }).into_response()
        },
        Err(e) => {
            let status_code = match e {
                crate::utils::error::AppError::NotFound(_) => StatusCode::NOT_FOUND,
                _ => StatusCode::INTERNAL_SERVER_ERROR,
            };

            (
                status_code,
                Json(ErrorResponse {
                    success: false,
                    message: e.to_string(),
                    error_code: Some("GET_ROLE_FAILED".to_string()),
                }),
            ).into_response()
        }
    }
}

/// 创建角色
#[axum::debug_handler]
pub async fn create_role(
    AuthExtractor(auth_context): AuthExtractor,
    State(role_service): State<RoleService>,
    Json(request): Json<CreateRoleRequest>,
) -> impl IntoResponse {
    // 权限检查
    if !auth_context.has_permission("role", "create") {
        return (
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                success: false,
                message: "没有权限创建角色".to_string(),
                error_code: Some("INSUFFICIENT_PERMISSIONS".to_string()),
            }),
        ).into_response();
    }

    match role_service.create_role(request, auth_context.user_id).await {
        Ok(role) => {
            info!("Role created successfully: {}", role.id);
            (
                StatusCode::CREATED,
                Json(SuccessResponse {
                    success: true,
                    data: Some(role),
                    message: "角色创建成功".to_string(),
                }),
            ).into_response()
        },
        Err(e) => {
            error!("Failed to create role: {}", e);
            let status_code = match e {
                crate::utils::error::AppError::BadRequest(_) => StatusCode::BAD_REQUEST,
                _ => StatusCode::INTERNAL_SERVER_ERROR,
            };

            (
                status_code,
                Json(ErrorResponse {
                    success: false,
                    message: e.to_string(),
                    error_code: Some("CREATE_ROLE_FAILED".to_string()),
                }),
            ).into_response()
        }
    }
}

/// 更新角色
#[axum::debug_handler]
pub async fn update_role(
    Path(role_id): Path<Uuid>,
    AuthExtractor(auth_context): AuthExtractor,
    State(role_service): State<RoleService>,
    Json(request): Json<UpdateRoleRequest>,
) -> impl IntoResponse {
    // 权限检查
    if !auth_context.has_permission("role", "update") {
        return (
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                success: false,
                message: "没有权限编辑角色".to_string(),
                error_code: Some("INSUFFICIENT_PERMISSIONS".to_string()),
            }),
        ).into_response();
    }

    match role_service.update_role(role_id, request).await {
        Ok(role) => {
            info!("Role updated successfully: {}", role_id);
            Json(SuccessResponse {
                success: true,
                data: Some(role),
                message: "角色更新成功".to_string(),
            }).into_response()
        },
        Err(e) => {
            error!("Failed to update role {}: {}", role_id, e);
            let status_code = match e {
                crate::utils::error::AppError::NotFound(_) => StatusCode::NOT_FOUND,
                crate::utils::error::AppError::BadRequest(_) => StatusCode::BAD_REQUEST,
                _ => StatusCode::INTERNAL_SERVER_ERROR,
            };

            (
                status_code,
                Json(ErrorResponse {
                    success: false,
                    message: e.to_string(),
                    error_code: Some("UPDATE_ROLE_FAILED".to_string()),
                }),
            ).into_response()
        }
    }
}

/// 删除角色
#[axum::debug_handler]
pub async fn delete_role(
    Path(role_id): Path<Uuid>,
    AuthExtractor(auth_context): AuthExtractor,
    State(role_service): State<RoleService>,
) -> impl IntoResponse {
    // 权限检查
    if !auth_context.has_permission("role", "delete") {
        return (
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                success: false,
                message: "没有权限删除角色".to_string(),
                error_code: Some("INSUFFICIENT_PERMISSIONS".to_string()),
            }),
        ).into_response();
    }

    match role_service.delete_role(role_id).await {
        Ok(_) => {
            info!("Role deleted successfully: {}", role_id);
            Json(SuccessResponse {
                success: true,
                data: None::<()>,
                message: "角色删除成功".to_string(),
            }).into_response()
        },
        Err(e) => {
            error!("Failed to delete role {}: {}", role_id, e);
            let status_code = match e {
                crate::utils::error::AppError::NotFound(_) => StatusCode::NOT_FOUND,
                crate::utils::error::AppError::BadRequest(_) => StatusCode::BAD_REQUEST,
                _ => StatusCode::INTERNAL_SERVER_ERROR,
            };

            (
                status_code,
                Json(ErrorResponse {
                    success: false,
                    message: e.to_string(),
                    error_code: Some("DELETE_ROLE_FAILED".to_string()),
                }),
            ).into_response()
        }
    }
}

/// 切换角色状态
#[axum::debug_handler]
pub async fn toggle_role_status(
    Path(role_id): Path<Uuid>,
    AuthExtractor(auth_context): AuthExtractor,
    State(role_service): State<RoleService>,
    Json(request): Json<serde_json::Value>,
) -> impl IntoResponse {
    // 权限检查
    if !auth_context.has_permission("role", "update") {
        return (
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                success: false,
                message: "没有权限修改角色状态".to_string(),
                error_code: Some("INSUFFICIENT_PERMISSIONS".to_string()),
            }),
        ).into_response();
    }

    let is_active = match request.get("is_active").and_then(|v| v.as_bool()) {
        Some(value) => value,
        None => {
            return (
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    success: false,
                    message: "缺少或无效的is_active参数".to_string(),
                    error_code: Some("INVALID_PARAMETER".to_string()),
                }),
            ).into_response();
        }
    };

    match role_service.toggle_role_status(role_id, is_active).await {
        Ok(role) => {
            info!("Role status toggled successfully: {} -> {}", role_id, is_active);
            Json(SuccessResponse {
                success: true,
                data: Some(role),
                message: format!("角色已{}", if is_active { "启用" } else { "禁用" }),
            }).into_response()
        },
        Err(e) => {
            error!("Failed to toggle role status {}: {}", role_id, e);
            let status_code = match e {
                crate::utils::error::AppError::NotFound(_) => StatusCode::NOT_FOUND,
                crate::utils::error::AppError::BadRequest(_) => StatusCode::BAD_REQUEST,
                _ => StatusCode::INTERNAL_SERVER_ERROR,
            };

            (
                status_code,
                Json(ErrorResponse {
                    success: false,
                    message: e.to_string(),
                    error_code: Some("TOGGLE_ROLE_STATUS_FAILED".to_string()),
                }),
            ).into_response()
        }
    }
}

/// 获取角色统计信息
#[axum::debug_handler]
pub async fn get_role_statistics(
    AuthExtractor(auth_context): AuthExtractor,
    State(role_service): State<RoleService>,
) -> impl IntoResponse {
    // 权限检查
    if !auth_context.has_permission("role", "read") {
        return (
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                success: false,
                message: "没有权限查看角色统计".to_string(),
                error_code: Some("INSUFFICIENT_PERMISSIONS".to_string()),
            }),
        ).into_response();
    }

    match role_service.get_role_statistics().await {
        Ok(statistics) => {
            Json(SuccessResponse {
                success: true,
                data: Some(statistics),
                message: "角色统计获取成功".to_string(),
            }).into_response()
        },
        Err(e) => {
            error!("Failed to get role statistics: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    success: false,
                    message: e.to_string(),
                    error_code: Some("GET_STATISTICS_FAILED".to_string()),
                }),
            ).into_response()
        }
    }
}

/// 为用户分配角色
#[axum::debug_handler]
pub async fn assign_role(
    AuthExtractor(auth_context): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
    State(role_service): State<RoleService>,
    Json(request): Json<AssignRoleRequest>,
) -> impl IntoResponse {
    // 权限检查
    if !auth_context.has_permission("role", "assign") {
        return (
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                success: false,
                message: "没有权限分配角色".to_string(),
                error_code: Some("INSUFFICIENT_PERMISSIONS".to_string()),
            }),
        ).into_response();
    }

    match role_service.assign_role(request, &tenant_context.schema_name).await {
        Ok(user_identity) => {
            info!("Role assigned successfully: {}", user_identity.id);
            (
                StatusCode::CREATED,
                Json(SuccessResponse {
                    success: true,
                    data: Some(user_identity),
                    message: "角色分配成功".to_string(),
                }),
            ).into_response()
        },
        Err(e) => {
            error!("Failed to assign role: {}", e);
            let status_code = match e {
                crate::utils::error::AppError::NotFound(_) => StatusCode::NOT_FOUND,
                crate::utils::error::AppError::BadRequest(_) => StatusCode::BAD_REQUEST,
                _ => StatusCode::INTERNAL_SERVER_ERROR,
            };

            (
                status_code,
                Json(ErrorResponse {
                    success: false,
                    message: e.to_string(),
                    error_code: Some("ASSIGN_ROLE_FAILED".to_string()),
                }),
            ).into_response()
        }
    }
}

/// 移除用户角色
#[axum::debug_handler]
pub async fn remove_user_role(
    Path(identity_id): Path<Uuid>,
    AuthExtractor(auth_context): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
    State(role_service): State<RoleService>,
) -> impl IntoResponse {
    // 权限检查
    if !auth_context.has_permission("role", "assign") {
        return (
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                success: false,
                message: "没有权限移除角色".to_string(),
                error_code: Some("INSUFFICIENT_PERMISSIONS".to_string()),
            }),
        ).into_response();
    }

    match role_service.remove_user_role(identity_id, &tenant_context.schema_name).await {
        Ok(_) => {
            info!("User role removed successfully: {}", identity_id);
            Json(SuccessResponse {
                success: true,
                data: None::<()>,
                message: "用户角色移除成功".to_string(),
            }).into_response()
        },
        Err(e) => {
            error!("Failed to remove user role {}: {}", identity_id, e);
            let status_code = match e {
                crate::utils::error::AppError::NotFound(_) => StatusCode::NOT_FOUND,
                _ => StatusCode::INTERNAL_SERVER_ERROR,
            };

            (
                status_code,
                Json(ErrorResponse {
                    success: false,
                    message: e.to_string(),
                    error_code: Some("REMOVE_USER_ROLE_FAILED".to_string()),
                }),
            ).into_response()
        }
    }
}

/// 获取用户的所有身份
#[axum::debug_handler]
pub async fn get_user_identities(
    Path(user_id): Path<Uuid>,
    AuthExtractor(auth_context): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
    State(role_service): State<RoleService>,
) -> impl IntoResponse {
    // 权限检查: 用户可以查看自己的身份，管理员可以查看所有用户的身份
    if user_id != auth_context.user_id && !auth_context.has_permission("role", "read") {
        return (
            StatusCode::FORBIDDEN,
            Json(ErrorResponse {
                success: false,
                message: "没有权限查看用户身份".to_string(),
                error_code: Some("INSUFFICIENT_PERMISSIONS".to_string()),
            }),
        ).into_response();
    }

    match role_service.get_user_identities(
        user_id, 
        tenant_context.tenant_id,
        &tenant_context.schema_name
    ).await {
        Ok(identities) => {
            Json(SuccessResponse {
                success: true,
                data: Some(identities),
                message: "用户身份列表获取成功".to_string(),
            }).into_response()
        },
        Err(e) => {
            error!("Failed to get user identities for {}: {}", user_id, e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    success: false,
                    message: e.to_string(),
                    error_code: Some("GET_USER_IDENTITIES_FAILED".to_string()),
                }),
            ).into_response()
        }
    }
}

/// 创建角色管理路由
pub fn create_router() -> Router<RoleService> {
    Router::new()
        .route("/", get(get_roles).post(create_role))
        .route("/{role_id}", get(get_role_by_id).put(update_role).delete(delete_role))
        .route("/{role_id}/status", patch(toggle_role_status))
        .route("/statistics", get(get_role_statistics))
        .route("/assign", post(assign_role))
        .route("/user-identities/{identity_id}", delete(remove_user_role))
        .route("/user/{user_id}/identities", get(get_user_identities))
}