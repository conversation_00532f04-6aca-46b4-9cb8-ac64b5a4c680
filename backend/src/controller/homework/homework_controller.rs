use crate::middleware::auth_middleware::AuthExtractor;
use crate::model::homework::homework::{
    CreateHomeworkParams, Homework, HomeworkStatistics, PageHomeworkParams, UpdateHomeworkParams,
};
use crate::utils::api_response::{responses, ApiResponse, PaginatedApiResponse};
use crate::web_server::AppState;
use axum::http::HeaderMap;
use axum::{
    extract::{Path, State},
    routing::{get, post},
    Json, Router,
};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/getStatistics", post(get_statistics))
        .route("/createHomework", post(create_homework))
        .route("/updateHomework", post(update_homework))
        .route("/pageHomework", post(page_homework))
}

pub async fn get_statistics(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    header_map: HeaderMap,
    Path(tenant_name): Path<String>,
) -> Result<ApiResponse<HomeworkStatistics>, ApiResponse<()>> {
    state
        .homework_service
        .get_statistics(&tenant_name)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

pub async fn create_homework(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    header_map: HeaderMap,
    Path(tenant_name): Path<String>,
    Json(params): Json<CreateHomeworkParams>,
) -> Result<ApiResponse<Homework>, ApiResponse<()>> {
    state
        .homework_service
        .create_homework(&context, &tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn update_homework(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    header_map: HeaderMap,
    Path(tenant_name): Path<String>,
    Json(params): Json<UpdateHomeworkParams>,
) -> Result<ApiResponse<Homework>, ApiResponse<()>> {
    state
        .homework_service
        .update_homework(&context, &tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn page_homework(
    State(state): State<AppState>,
    AuthExtractor(context): AuthExtractor,
    header_map: HeaderMap,
    Path(tenant_name): Path<String>,
    Json(params): Json<PageHomeworkParams>,
) -> Result<PaginatedApiResponse<Homework>, PaginatedApiResponse<()>> {
    state
        .homework_service
        .page_homework(&context, &tenant_name, &params)
        .await
        .map_err(|e| responses::paginated_error(e.to_string().as_str(), None))
        .map(|(list, count)| {
            responses::paginated_success(
                list,
                params.page_params.get_page(),
                params.page_params.get_page_size(),
                count,
                None,
            )
        })
}
