use crate::model::base::PageResult;
use crate::model::education_stage::EducationStage;
use crate::model::education_stage::{
    CreateEducationStageRequest, EducationStageQueryParams, EducationStageStatistics,
    EducationStageSummary, EducationStageVO, UpdateEducationStageRequest,
};
use crate::model::grade::grade::GradeLevel;
use crate::service::education_stage::EducationStageService;
use crate::utils::api_response::{responses, ApiResponse};
use crate::utils::error::AppError;
use axum::response::IntoResponse;
use axum::{
    extract::{Path, Query, State},
    routing::{get, patch, post},
    Json, Router,
};
use serde::Deserialize;
use uuid::Uuid;

pub fn create_router() -> Router<EducationStageService> {
    Router::new()
        .route("/", get(get_education_stages))
        .route("/create", post(create_education_stage))
        .route("/summaries", get(get_education_stage_summaries))
        .route("/statistics", get(get_education_stage_statistics))
        .route("/orders", patch(update_education_stage_orders))
        .route("/check-code", get(check_code_availability))
        .route(
            "/{id}",
            get(get_education_stage_by_id)
                .put(update_education_stage)
                .delete(delete_education_stage),
        )
        .route("/{code}/grades", get(get_grades_by_education_stage_code))
}

/// 获取学段列表（支持分页和查询）
#[axum::debug_handler]
async fn get_education_stages(
    State(education_stage_service): State<EducationStageService>,
    Query(params): Query<EducationStageQueryParams>,
) -> impl IntoResponse {
    education_stage_service
        .get_education_stages(params)
        .await
        .map(|page_result| {
            responses::paginated_success(
                page_result.data,
                page_result.page as i32,      // 转换类型
                page_result.page_size as i32, // 转换类型
                page_result.total,
                Some("获取学段成功"),
            )
        })
        .unwrap_or_else(|e| {
            responses::paginated_error(
                &format!("获取学段信息失败：{}", e),
                Some("QUERY_EDUSTAGES_FAILED"),
            )
        })
}

/// 获取学段简要信息列表（用于下拉选择）
#[axum::debug_handler]
async fn get_education_stage_summaries(
    State(education_stage_service): State<EducationStageService>,
    Query(params): Query<EducationStageSummaryQuery>,
) -> impl IntoResponse {
    match education_stage_service
        .get_education_stage_summaries(params.is_active)
        .await
    {
        Ok(es) => responses::success(es, Some("获取学段信息成功")),
        Err(e) => responses::error(
            format!("获取学段信息失败：{}", e).as_str(),
            Some("QUERY_EDUSTAGES_FAILED"),
        ),
    }
}

/// 获取单个学段详情
#[axum::debug_handler]
async fn get_education_stage_by_id(
    State(education_stage_service): State<EducationStageService>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    match education_stage_service
        .get_education_stage_by_id(id)
        .await {
        Ok(es) => {responses::success(es, Some("获取学段信息成功"))}
        Err(e) => {responses::error(format!("获取学段信息失败：{}", e).as_str(), Some("QUERY_EDUSTAGES_FAILED"))}
    }
}

/// 创建学段
#[axum::debug_handler]
async fn create_education_stage(
    State(education_stage_service): State<EducationStageService>,
    Json(payload): Json<CreateEducationStageRequest>,
) -> impl IntoResponse {
    // 验证输入
    if payload.code.trim().is_empty() {
        return responses::error("学段编码不能为空", Some("CREATE_EDUSTAGES_FAILED"));
    }
    if payload.name.trim().is_empty() {
        return responses::error("学段名称不能为空", Some("CREATE_EDUSTAGES_FAILED"));
    }
    if payload.order_level < 0 {
        return responses::error("排序值必须为非负数", Some("CREATE_EDUSTAGES_FAILED"));
    }
    if let Some(duration) = payload.duration_years {
        if duration <= 0 {
            return responses::error("持续年限必须为非负数", Some("CREATE_EDUSTAGES_FAILED"));
        }
    }
    match education_stage_service
        .create_education_stage(payload)
        .await
    {
        Ok(es) => responses::success(es, Some("学段创建成功")),
        Err(e) => responses::error(
            format!("学段创建失败：{}", e).as_str(),
            Some("CREATE_SUBJECT_FAILED"),
        ),
    }
}

/// 更新学段
#[axum::debug_handler]
async fn update_education_stage(
    State(education_stage_service): State<EducationStageService>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateEducationStageRequest>,
) -> impl IntoResponse {
    // 验证输入
    if let Some(ref name) = payload.name {
        if name.trim().is_empty() {
            return responses::error("学段名称不能为空", Some("UPDATE_EDUSTAGES_FAILED"));
        }
    }
    if let Some(order_level) = payload.order_level {
        if order_level < 0 {
            return responses::error("排序值必须为非负数", Some("UPDATE_EDUSTAGES_FAILED"));
        }
    }
    if let Some(duration) = payload.duration_years {
        if duration <= 0 {
            return responses::error("持续年限必须为非负数", Some("UPDATE_EDUSTAGES_FAILED"));
        }
    }

    match education_stage_service
        .update_education_stage(id, payload)
        .await {
        Ok(es) => {responses::success(es, Some("学段更新成功"))}
        Err(e) => {responses::error(format!("学段更新失败：{}", e).as_str(), Some("UPDATE_EDUSTAGES_FAILED"))}
    }
}

/// 删除学段（软删除）
#[axum::debug_handler]
async fn delete_education_stage(
    State(education_stage_service): State<EducationStageService>,
    Path(id): Path<Uuid>,
) -> impl IntoResponse {
    match education_stage_service.delete_education_stage(id).await {
        Ok(_) => {responses::success_no_data(Some("学段删除成功"))}
        Err(e) => {
            responses::error(
                format!("无法删除学段：{}", e).as_str(),
                Some("DELETE_EDUSTAGES_FAILED"),
            )
        }
    }
}

/// 获取学段统计信息
#[axum::debug_handler]
async fn get_education_stage_statistics(
    State(education_stage_service): State<EducationStageService>,
) -> impl IntoResponse {
    match education_stage_service
        .get_education_stage_statistics()
        .await
    {
        Ok(es) => responses::success(es, Some("获取学段统计信息成功")),
        Err(e) => responses::error(
            format!("获取学段统计信息：{}", e).as_str(),
            Some("QUERY_EDUSTAGES_FAILED"),
        ),
    }
}

/// 批量更新学段排序
#[axum::debug_handler]
async fn update_education_stage_orders(
    State(education_stage_service): State<EducationStageService>,
    Json(payload): Json<UpdateEducationStageOrdersRequest>,
) -> impl IntoResponse {
    if payload.orders.is_empty() {
        return responses::error("排序列表不能为空", Some("UPDATE_EDUSTAGES_FAILED"));
    }
    match education_stage_service
        .update_education_stage_orders(payload.orders)
        .await
    {
        Ok(es) => responses::success(es, Some("更新学段排序成功")),
        Err(e) => responses::error(
            format!("更新学段排序失败：{}", e).as_str(),
            Some("UPDATE_EDUSTAGES_FAILED"),
        ),
    }
}

/// 检查学段代码是否可用
#[axum::debug_handler]
async fn check_code_availability(
    State(education_stage_service): State<EducationStageService>,
    Query(params): Query<CheckCodeQuery>,
) -> impl IntoResponse {
    if params.code.trim().is_empty() {
        return responses::error("学段代码不能为空", Some("CHECK_EDUSTAGES_FAILED"));
    }

    match education_stage_service
        .is_code_available(&params.code, params.exclude_id)
        .await {
        Ok(bool) => {responses::success(bool, Some("查询成功"))}
        Err(e) => responses::error(
            format!("查询失败：{}", e).as_str(),
            Some("CHECK_EDUSTAGES_FAILED"),
        )
    }
}

/// 根据学段编码获取关联年级列表
#[axum::debug_handler]
async fn get_grades_by_education_stage_code(
    State(education_stage_service): State<EducationStageService>,
    Path(code): Path<String>,
) -> Result<Json<ApiResponse<Vec<GradeLevel>>>, AppError> {
    let grades = education_stage_service
        .get_grades_by_education_stage_code(&code)
        .await?;
    Ok(Json(ApiResponse::success(grades, None)))
}

/// 学段简要信息查询参数
#[derive(Debug, Deserialize)]
struct EducationStageSummaryQuery {
    pub is_active: Option<bool>,
}

/// 批量更新学段排序请求
#[derive(Debug, Deserialize)]
struct UpdateEducationStageOrdersRequest {
    pub orders: Vec<(Uuid, i32)>,
}

/// 检查代码可用性查询参数
#[derive(Debug, Deserialize)]
struct CheckCodeQuery {
    pub code: String,
    pub exclude_id: Option<Uuid>,
}

/// 检查代码可用性响应
#[derive(Debug, serde::Serialize)]
struct CheckCodeResponse {
    pub is_available: bool,
}
