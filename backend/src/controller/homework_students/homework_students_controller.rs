use std::collections::HashMap;

use axum::{
    extract::{Path, State},
    routing::post,
    Json, Router,
};
use uuid::Uuid;

use crate::{
    model::{
        homework_students::homework_students::{
            BatchBindStudentsToHomeworkParams, BatchUnbindStudentsFromHomeworkParams,
            FindAllByHomeworkIdParams, HomeworkStudents, HomeworkStudentsWithStudentBaseInfo,
        },
        StudentBaseInfo,
    },
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route(
            "/batchBindStudentsToHomework",
            post(batch_bind_students_to_homework),
        )
        .route("/findAllByHomeworkId", post(find_all_by_homework_id))
        .route(
            "/batchUnbindStudentsFromHomework",
            post(batch_unbind_students_from_homework),
        )
}

pub async fn batch_bind_students_to_homework(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): <PERSON><PERSON><BatchBindStudentsToHomeworkParams>,
) -> Result<ApiResponse<Vec<HomeworkStudents>>, ApiResponse<()>> {
    state
        .homework_students_service
        .batch_bind_students_to_homework(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

pub async fn find_all_by_homework_id(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<FindAllByHomeworkIdParams>,
) -> Result<ApiResponse<Vec<HomeworkStudentsWithStudentBaseInfo>>, ApiResponse<()>> {
    let list = state
        .homework_students_service
        .find_all_by_homework_id(&tenant_name, &params.homework_id)
        .await
        .map_err(|e| responses::error(&e, None))?;
    //联查学生信息
    let student_id_list = list
        .iter()
        .map(|item| item.student_id)
        .collect::<Vec<Uuid>>();
    let student_base_info_list = state
        .student_service
        .batch_get_student_base_info_by_id_list(&tenant_name, student_id_list)
        .await
        .map_err(|e| responses::error(&e, None))?;
    let mut student_id_to_info_map = HashMap::<Uuid, StudentBaseInfo>::new();
    for ele in student_base_info_list {
        student_id_to_info_map.insert(ele.id, ele);
    }
    Ok(responses::success(
        list.into_iter()
            .map(|item| {
                let HomeworkStudents {
                    id,
                    homework_id,
                    student_id,
                    status,
                    created_at,
                    updated_at,
                } = item;
                HomeworkStudentsWithStudentBaseInfo {
                    id,
                    homework_id,
                    student_id,
                    student_base_info: student_id_to_info_map.get(&student_id).cloned(),
                    status,
                    created_at,
                    updated_at,
                }
            })
            .collect(),
        None,
    ))
}

pub async fn batch_unbind_students_from_homework(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    Json(params): Json<BatchUnbindStudentsFromHomeworkParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    state
        .homework_students_service
        .batch_unbind_students_from_homework(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|()| responses::success_no_data(None))
}
