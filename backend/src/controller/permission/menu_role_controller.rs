use std::sync::Arc;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::{get, post, put, delete},
    Router,
};
use serde::{Deserialize, Serialize};
use tracing::{info, error, debug};
use uuid::Uuid;

use crate::{
    middleware::auth_middleware::AuthExtractor,
    service::permission::{
        menu_role_permission_service::{MenuRolePermissionService, MenuRolePermissionRequest},
        casbin_service::MultiTenantCasbinService,
    },
    utils::api_response::ApiResponse,
};

/// 菜单角色权限控制器
pub struct MenuRoleController;

/// 菜单角色权限查询参数
#[derive(Debug, Deserialize)]
pub struct MenuRoleQueryParams {
    pub tenant_id: Option<String>,
}

/// 菜单角色权限设置请求
#[derive(Debug, Deserialize)]
pub struct SetMenuRolePermissionRequest {
    pub role_ids: Vec<String>,
}

/// 菜单角色权限响应
#[derive(Debug, Serialize)]
pub struct MenuRolePermissionResponse {
    pub menu_id: String,
    pub roles: Vec<RoleInfo>,
}

/// 角色信息
#[derive(Debug, Serialize)]
pub struct RoleInfo {
    pub id: String,
    pub name: String,
    pub code: String,
    pub category: String,
    pub level: i32,
}

impl MenuRoleController {
    /// 获取菜单的角色权限配置
    pub async fn get_menu_role_permissions(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Path(menu_id): Path<String>,
        Query(params): Query<MenuRoleQueryParams>,
    ) -> Result<Json<ApiResponse<MenuRolePermissionResponse>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        let tenant_id = params.tenant_id.unwrap_or_else(|| auth_context.get_default_tenant_id());

        debug!("获取菜单角色权限: menu_id={}, tenant_id={}", menu_id, tenant_id);

        let menu_role_service = MenuRolePermissionService::new(casbin_service);

        // 获取菜单的角色ID列表
        let role_ids = match menu_role_service.get_menu_role_permissions(&menu_id, &tenant_id).await {
            Ok(ids) => ids,
            Err(e) => {
                error!("获取菜单角色权限失败: {}", e);
                return Err(StatusCode::INTERNAL_SERVER_ERROR);
            }
        };

        // 简化角色信息，只返回ID（前端可以通过角色API获取详细信息）
        let roles: Vec<RoleInfo> = role_ids.into_iter().map(|role_id| {
            RoleInfo {
                id: role_id.clone(),
                name: format!("Role {}", role_id), // 临时名称
                code: role_id,
                category: "unknown".to_string(),
                level: 0,
            }
        }).collect();

        let response = MenuRolePermissionResponse {
            menu_id,
            roles,
        };

        Ok(Json(ApiResponse::success(response, Some("获取菜单角色权限成功".to_string()))))
    }

    /// 设置菜单的角色权限配置
    pub async fn set_menu_role_permissions(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Path(menu_id): Path<String>,
        Json(request): Json<SetMenuRolePermissionRequest>,
    ) -> Result<Json<ApiResponse<String>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        info!(
            "设置菜单角色权限: menu_id={}, roles={:?}, operator={}",
            menu_id, request.role_ids, auth_context.user_id
        );

        let menu_role_service = MenuRolePermissionService::new(casbin_service);

        let permission_request = MenuRolePermissionRequest {
            menu_id: menu_id.clone(),
            role_ids: request.role_ids,
            tenant_id: auth_context.get_default_tenant_id(),
        };

        match menu_role_service.set_menu_role_permissions(&permission_request).await {
            Ok(()) => {
                info!("菜单角色权限设置成功: menu_id={}", menu_id);
                Ok(Json(ApiResponse::success("设置成功".to_string(), Some("菜单角色权限设置成功".to_string()))))
            }
            Err(e) => {
                error!("设置菜单角色权限失败: menu_id={}, error={}", menu_id, e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }

    /// 添加单个角色的菜单权限
    pub async fn add_role_menu_permission(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Path((menu_id, role_id)): Path<(String, String)>,
    ) -> Result<Json<ApiResponse<String>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("添加角色菜单权限: menu_id={}, role_id={}", menu_id, role_id);

        let menu_role_service = MenuRolePermissionService::new(casbin_service);

        match menu_role_service.add_role_menu_permission(&menu_id, &role_id, &auth_context.get_default_tenant_id()).await {
            Ok(true) => {
                info!("角色菜单权限添加成功: menu_id={}, role_id={}", menu_id, role_id);
                Ok(Json(ApiResponse::success("添加成功".to_string(), Some("角色菜单权限添加成功".to_string()))))
            }
            Ok(false) => {
                Ok(Json(ApiResponse::success("已存在".to_string(), Some("角色菜单权限已存在".to_string()))))
            }
            Err(e) => {
                error!("添加角色菜单权限失败: menu_id={}, role_id={}, error={}", menu_id, role_id, e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }

    /// 移除单个角色的菜单权限
    pub async fn remove_role_menu_permission(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Path((menu_id, role_id)): Path<(String, String)>,
    ) -> Result<Json<ApiResponse<String>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("移除角色菜单权限: menu_id={}, role_id={}", menu_id, role_id);

        let menu_role_service = MenuRolePermissionService::new(casbin_service);

        match menu_role_service.remove_role_menu_permission(&menu_id, &role_id, &auth_context.get_default_tenant_id()).await {
            Ok(true) => {
                info!("角色菜单权限移除成功: menu_id={}, role_id={}", menu_id, role_id);
                Ok(Json(ApiResponse::success("移除成功".to_string(), Some("角色菜单权限移除成功".to_string()))))
            }
            Ok(false) => {
                Ok(Json(ApiResponse::success("不存在".to_string(), Some("角色菜单权限不存在".to_string()))))
            }
            Err(e) => {
                error!("移除角色菜单权限失败: menu_id={}, role_id={}, error={}", menu_id, role_id, e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }

    /// 检查角色是否有菜单访问权限
    pub async fn check_role_menu_permission(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Path((menu_id, role_id)): Path<(String, String)>,
    ) -> Result<Json<ApiResponse<bool>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("检查角色菜单权限: menu_id={}, role_id={}", menu_id, role_id);

        let menu_role_service = MenuRolePermissionService::new(casbin_service);

        match menu_role_service.check_role_menu_permission(&role_id, &menu_id, &auth_context.get_default_tenant_id()).await {
            Ok(has_permission) => {
                Ok(Json(ApiResponse::success(has_permission, Some("权限检查完成".to_string()))))
            }
            Err(e) => {
                error!("检查角色菜单权限失败: menu_id={}, role_id={}, error={}", menu_id, role_id, e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }

    /// 获取角色的所有菜单权限
    pub async fn get_role_menu_permissions(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Path(role_id): Path<String>,
    ) -> Result<Json<ApiResponse<Vec<String>>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("获取角色菜单权限: role_id={}", role_id);

        let menu_role_service = MenuRolePermissionService::new(casbin_service);

        match menu_role_service.get_role_menu_permissions(&role_id, &auth_context.get_default_tenant_id()).await {
            Ok(menu_ids) => {
                Ok(Json(ApiResponse::success(menu_ids, Some("获取角色菜单权限成功".to_string()))))
            }
            Err(e) => {
                error!("获取角色菜单权限失败: role_id={}, error={}", role_id, e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }
}

/// 创建菜单角色权限路由
pub fn create_router() -> Router<Arc<MultiTenantCasbinService>> {
    Router::new()
        .route("/menus/:menu_id/roles", get(MenuRoleController::get_menu_role_permissions))
        .route("/menus/:menu_id/roles", put(MenuRoleController::set_menu_role_permissions))
        .route("/menus/:menu_id/roles/:role_id", post(MenuRoleController::add_role_menu_permission))
        .route("/menus/:menu_id/roles/:role_id", delete(MenuRoleController::remove_role_menu_permission))
        .route("/menus/:menu_id/roles/:role_id/check", get(MenuRoleController::check_role_menu_permission))
        .route("/roles/:role_id/menus", get(MenuRoleController::get_role_menu_permissions))
}
