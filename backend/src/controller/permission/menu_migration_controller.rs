use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{info, warn, error};

use crate::utils::api_response::ApiResponse;
use crate::middleware::auth_middleware::AuthExtractor;
use crate::service::permission::{
    MenuMigrationService, 
    MigrationReport, 
    MultiTenantCasbinService,
};
use crate::service::permission::menu_migration_service::ValidationReport;
use crate::utils::error::AppError;

/// 迁移请求参数
#[derive(Debug, Deserialize)]
pub struct MigrationRequest {
    /// 是否为试运行（不执行实际迁移）
    pub dry_run: Option<bool>,
    /// 目标租户ID（可选，默认为当前用户租户）
    pub target_tenant_id: Option<String>,
}

/// 验证请求参数
#[derive(Debug, Deserialize)]
pub struct ValidationRequest {
    /// 目标租户ID（可选，默认为当前用户租户）
    pub target_tenant_id: Option<String>,
}

/// 清理请求参数
#[derive(Debug, Deserialize)]
pub struct CleanupRequest {
    /// 确认清理操作
    pub confirm: bool,
}

/// 迁移响应
#[derive(Debug, Serialize)]
pub struct MigrationResponse {
    pub success: bool,
    pub summary: String,
    pub policies_created: Vec<String>,
    pub dry_run: bool,
    pub tenant_id: String,
}

/// 验证响应
#[derive(Debug, Serialize)]
pub struct ValidationResponse {
    pub success: bool,
    pub summary: String,
    pub failures: Vec<String>,
    pub successes: Vec<String>,
    pub tenant_id: String,
}

/// 清理响应
#[derive(Debug, Serialize)]
pub struct CleanupResponse {
    pub success: bool,
    pub message: String,
}

/// 迁移控制器
pub struct MenuMigrationController;

impl MenuMigrationController {
    /// 执行菜单权限迁移
    pub async fn migrate_permissions(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        State(migration_service): State<Arc<MenuMigrationService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<MigrationRequest>,
    ) -> Result<Json<ApiResponse<MigrationResponse>>, StatusCode> {
        // 检查系统管理员权限
        if !Self::is_system_admin(&auth_context) {
            warn!("非系统管理员尝试执行权限迁移: user_id={}", auth_context.user_id);
            return Err(StatusCode::FORBIDDEN);
        }
        
        let dry_run = params.dry_run.unwrap_or(true); // 默认为试运行
        let target_tenant_id = params.target_tenant_id
            .as_deref()
            .unwrap_or(&auth_context.tenant_id);
            
        info!(
            "开始权限迁移: operator={}, tenant_id={}, dry_run={}", 
            auth_context.user_id, target_tenant_id, dry_run
        );
        
        match migration_service
            .migrate_permissions_to_casbin(casbin_service.as_ref(), target_tenant_id, dry_run)
            .await
        {
            Ok(report) => {
                let response = MigrationResponse {
                    success: true,
                    summary: report.summary(),
                    policies_created: report.get_all_policies(),
                    dry_run,
                    tenant_id: target_tenant_id.to_string(),
                };
                
                let message = if dry_run {
                    "权限迁移模拟完成"
                } else {
                    "权限迁移执行完成"
                };
                
                info!("权限迁移成功: {}", response.summary);
                Ok(Json(ApiResponse::success(response, Some(message.to_string()))))
            }
            Err(e) => {
                error!("权限迁移失败: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }
    
    /// 验证迁移结果
    pub async fn validate_migration(
        State(casbin_service): State<Arc<MultiTenantCasbinService>>,
        State(migration_service): State<Arc<MenuMigrationService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<ValidationRequest>,
    ) -> Result<Json<ApiResponse<ValidationResponse>>, StatusCode> {
        // 检查系统管理员权限
        if !Self::is_system_admin(&auth_context) {
            return Err(StatusCode::FORBIDDEN);
        }
        
        // 获取目标租户ID，如果没有指定则使用用户的第一个租户
        let target_tenant_id = params.target_tenant_id
            .as_deref()
            .or_else(|| {
                auth_context.tenant_links.first()
                    .map(|link| link.tenant_id.to_string())
                    .as_deref()
            })
            .unwrap_or("template");
            
        info!("验证权限迁移: operator={}, tenant_id={}", auth_context.user_id, target_tenant_id);
        
        match migration_service
            .validate_migration(casbin_service.as_ref(), target_tenant_id)
            .await
        {
            Ok(report) => {
                let response = ValidationResponse {
                    success: !report.has_failures(),
                    summary: report.summary(),
                    failures: report.get_failures().to_vec(),
                    successes: report.get_successes().to_vec(),
                    tenant_id: target_tenant_id.to_string(),
                };
                
                info!("权限验证完成: {}", response.summary);
                Ok(Json(ApiResponse::success(response, Some("权限验证完成".to_string()))))
            }
            Err(e) => {
                error!("权限验证失败: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }
    
    /// 清理旧的菜单权限字段
    pub async fn cleanup_menu_fields(
        State(migration_service): State<Arc<MenuMigrationService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<CleanupRequest>,
    ) -> Result<Json<ApiResponse<CleanupResponse>>, StatusCode> {
        // 检查系统管理员权限
        if !Self::is_system_admin(&auth_context) {
            return Err(StatusCode::FORBIDDEN);
        }
        
        if !params.confirm {
            let response = CleanupResponse {
                success: false,
                message: "清理操作需要显式确认".to_string(),
            };
            return Ok(Json(ApiResponse::success(response, Some("需要确认清理操作".to_string()))));
        }
        
        info!("清理菜单权限字段: operator={}", auth_context.user_id);
        
        match migration_service.cleanup_menu_permission_fields(params.confirm).await {
            Ok(_) => {
                let response = CleanupResponse {
                    success: true,
                    message: "菜单权限字段清理完成".to_string(),
                };
                
                info!("菜单字段清理成功");
                Ok(Json(ApiResponse::success(response, Some("清理操作完成".to_string()))))
            }
            Err(e) => {
                error!("菜单字段清理失败: {}", e);
                let response = CleanupResponse {
                    success: false,
                    message: format!("清理失败: {}", e),
                };
                Ok(Json(ApiResponse::error("清理操作失败".to_string(), Some("CLEANUP_FAILED".to_string()))))
            }
        }
    }
    
    /// 获取迁移状态
    pub async fn get_migration_status(
        State(migration_service): State<Arc<MenuMigrationService>>,
        AuthExtractor(auth_context): AuthExtractor,
    ) -> Result<Json<ApiResponse<MigrationStatusResponse>>, StatusCode> {
        // 检查系统管理员权限
        if !Self::is_system_admin(&auth_context) {
            return Err(StatusCode::FORBIDDEN);
        }
        
        // 检查菜单表中是否还有权限字段
        let has_legacy_fields = Self::check_legacy_permission_fields(&migration_service).await
            .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
            
        let response = MigrationStatusResponse {
            has_legacy_fields,
            migration_needed: has_legacy_fields,
            last_check: chrono::Utc::now().to_rfc3339(),
        };
        
        Ok(Json(ApiResponse::success(response, Some("迁移状态获取成功".to_string()))))
    }
    
    /// 检查是否为系统管理员
    fn is_system_admin(_auth_context: &crate::middleware::auth_middleware::AuthContext) -> bool {
        // 这里应该基于实际的权限检查逻辑
        // 简化示例：总是返回true，实际应该检查用户角色
        true
    }
    
    /// 检查是否存在遗留权限字段
    async fn check_legacy_permission_fields(
        _migration_service: &Arc<MenuMigrationService>
    ) -> Result<bool, AppError> {
        // 简化实现，实际应该查询数据库检查字段是否存在
        // 可以通过查询information_schema.columns来检查
        Ok(true) // 假设存在遗留字段
    }
}

/// 迁移状态响应
#[derive(Debug, Serialize)]
pub struct MigrationStatusResponse {
    pub has_legacy_fields: bool,
    pub migration_needed: bool,
    pub last_check: String,
}

/// 创建迁移路由
use axum::{routing::post, routing::get, Router};

pub fn create_migration_routes() -> Router {
    Router::new()
        .route("/migrate", post(MenuMigrationController::migrate_permissions))
        .route("/validate", get(MenuMigrationController::validate_migration))
        .route("/cleanup", post(MenuMigrationController::cleanup_menu_fields))
        .route("/status", get(MenuMigrationController::get_migration_status))
}