use std::sync::Arc;
use axum::{
    extract::{State, Query, Path},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use tracing::{info, warn, error, debug};
use sqlx::{PgPool, Row};

use crate::service::permission::{
    MultiTenantCasbinService,
    PermissionRequest,
    MenuPermission,
};
use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
use crate::utils::api_response::{ApiResponse, PaginatedApiResponse};
use crate::utils::error::AppError;
use crate::web_server::AppState;

/// 权限测试控制器
pub struct PermissionTestController;

/// 单用户权限测试请求
#[derive(Debug, Serialize, Deserialize)]
pub struct SingleUserTestRequest {
    pub user_id: Uuid,
    pub tenant_id: String,
    pub role_type: Option<String>,
    pub menu_ids: Vec<String>,
    pub test_name: Option<String>,
    pub save_result: Option<bool>,
}

/// 批量用户权限测试请求
#[derive(Debug, Serialize, Deserialize)]
pub struct BatchUserTestRequest {
    pub user_tests: Vec<UserTestSpec>,
    pub test_name: Option<String>,
    pub parallel_execution: Option<bool>,
    pub save_result: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserTestSpec {
    pub user_id: Uuid,
    pub tenant_id: String,
    pub role_type: Option<String>,
    pub menu_ids: Vec<String>,
}

/// 角色模拟测试请求
#[derive(Debug, Serialize, Deserialize)]
pub struct RoleSimulationTestRequest {
    pub role_type: String,
    pub tenant_id: String,
    pub menu_ids: Option<Vec<String>>, // 如果为空，测试所有菜单
    pub test_name: Option<String>,
    pub include_inheritance: Option<bool>,
    pub save_result: Option<bool>,
}

/// 权限矩阵测试请求
#[derive(Debug, Deserialize)]
pub struct PermissionMatrixTestRequest {
    pub tenant_id: String,
    pub role_types: Option<Vec<String>>, // 如果为空，测试所有角色
    pub menu_types: Option<Vec<String>>, // 如果为空，测试所有菜单类型
    pub test_name: Option<String>,
    pub generate_report: Option<bool>,
    pub save_result: Option<bool>,
}

/// 权限冲突检测请求
#[derive(Debug, Deserialize)]
pub struct ConflictDetectionRequest {
    pub tenant_id: Option<String>,
    pub scope: String, // "global", "tenant", "role", "menu"
    pub target_id: Option<String>, // 具体的租户ID、角色类型或菜单ID
    pub check_inheritance: Option<bool>,
    pub save_result: Option<bool>,
}

/// 测试历史查询参数
#[derive(Debug, Deserialize)]
pub struct TestHistoryQueryParams {
    pub test_type: Option<String>,
    pub tester_id: Option<Uuid>,
    pub tenant_id: Option<String>,
    pub start_date: Option<String>,
    pub end_date: Option<String>,
    pub page: Option<u32>,
    pub page_size: Option<u32>,
}

/// 权限测试结果
#[derive(Debug, Serialize)]
pub struct PermissionTestResult {
    pub user_id: Option<Uuid>,
    pub menu_id: String,
    pub menu_name: String,
    pub accessible: bool,
    pub reason: Option<String>,
    pub required_permissions: Vec<String>,
    pub user_permissions: Vec<String>,
    pub matched_permissions: Vec<String>,
    pub missing_permissions: Vec<String>,
    pub data_scopes_valid: bool,
    pub decision_path: Vec<String>,
    pub execution_time_ms: u64,
}

/// 批量测试响应
#[derive(Debug, Serialize)]
pub struct BatchTestResponse {
    pub test_id: Option<Uuid>,
    pub test_name: Option<String>,
    pub test_type: String,
    pub total_tests: i32,
    pub passed_tests: i32,
    pub failed_tests: i32,
    pub execution_time_ms: u64,
    pub results: Vec<UserTestResult>,
    pub summary: TestSummary,
}

#[derive(Debug, Serialize)]
pub struct UserTestResult {
    pub user_id: Uuid,
    pub tenant_id: String,
    pub role_type: Option<String>,
    pub total_menus_tested: i32,
    pub accessible_menus: i32,
    pub denied_menus: i32,
    pub menu_results: Vec<PermissionTestResult>,
}

/// 权限矩阵测试响应
#[derive(Debug, Serialize)]
pub struct PermissionMatrixResponse {
    pub test_id: Option<Uuid>,
    pub test_name: Option<String>,
    pub tenant_id: String,
    pub matrix: Vec<RoleMenuMatrix>,
    pub statistics: MatrixStatistics,
    pub execution_time_ms: u64,
}

#[derive(Debug, Serialize)]
pub struct RoleMenuMatrix {
    pub role_type: String,
    pub role_display_name: String,
    pub menu_permissions: Vec<MenuPermissionStatus>,
}

#[derive(Debug, Serialize)]
pub struct MenuPermissionStatus {
    pub menu_id: String,
    pub menu_name: String,
    pub menu_type: String,
    pub accessible: bool,
    pub permission_source: String, // "direct", "inherited", "denied"
}

/// 冲突检测响应
#[derive(Debug, Serialize)]
pub struct ConflictDetectionResponse {
    pub scope: String,
    pub target_id: Option<String>,
    pub conflicts_found: bool,
    pub total_conflicts: i32,
    pub conflicts: Vec<PermissionConflict>,
    pub recommendations: Vec<String>,
    pub execution_time_ms: u64,
}

#[derive(Debug, Serialize)]
pub struct PermissionConflict {
    pub conflict_type: String, // "role_conflict", "permission_overlap", "data_scope_conflict"
    pub severity: String,      // "high", "medium", "low"
    pub description: String,
    pub affected_entities: Vec<String>,
    pub suggested_resolution: String,
}

/// 测试摘要
#[derive(Debug, Serialize)]
pub struct TestSummary {
    pub total_users: i32,
    pub total_menus: i32,
    pub overall_pass_rate: f64,
    pub most_accessible_menu: Option<String>,
    pub least_accessible_menu: Option<String>,
    pub role_performance: Vec<RolePerformance>,
}

#[derive(Debug, Serialize)]
pub struct RolePerformance {
    pub role_type: String,
    pub total_tests: i32,
    pub pass_rate: f64,
    pub avg_accessible_menus: f64,
}

/// 矩阵统计
#[derive(Debug, Serialize)]
pub struct MatrixStatistics {
    pub total_roles: i32,
    pub total_menus: i32,
    pub total_permissions: i32,
    pub direct_permissions: i32,
    pub inherited_permissions: i32,
    pub denied_permissions: i32,
    pub coverage_percentage: f64,
}

/// 测试历史记录
#[derive(Debug, Serialize)]
pub struct TestHistoryRecord {
    pub id: Uuid,
    pub test_name: Option<String>,
    pub test_type: String,
    pub total_tests: i32,
    pub passed_tests: i32,
    pub failed_tests: i32,
    pub execution_time_ms: i32,
    pub tester_id: Uuid,
    pub tester_identity: String,
    pub tenant_id: Option<String>,
    pub created_at: String,
    pub test_parameters: serde_json::Value,
    pub test_results: serde_json::Value,
}

impl PermissionTestController {
    /// 单用户权限测试
    pub async fn test_single_user(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<SingleUserTestRequest>,
    ) -> Result<Json<ApiResponse<Vec<PermissionTestResult>>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        let start_time = std::time::Instant::now();
        
        debug!("Testing permissions for user {} in tenant {} by admin user {}", 
               request.user_id, request.tenant_id, auth_context.user_id);

        let mut results = Vec::new();

        // 获取用户在指定租户的角色信息
        let user_roles = Self::get_user_roles(&app_state.db, &request.user_id, &request.tenant_id).await?;
        
        if user_roles.is_empty() {
            warn!("User {} has no roles in tenant {}", request.user_id, request.tenant_id);
            return Err(StatusCode::BAD_REQUEST);
        }

        // 构建用户身份标识
        let user_identity = if let Some(role_type) = &request.role_type {
            format!("{}:{}:tenant:{}", request.user_id, role_type, request.tenant_id)
        } else {
            let first_role = user_roles.first().unwrap();
            format!("{}:{}:tenant:{}", request.user_id, first_role, request.tenant_id)
        };

        // 逐个测试菜单权限
        for menu_id in &request.menu_ids {
            let menu_test_start = std::time::Instant::now();
            
            // 获取菜单信息
            let menu_info = Self::get_menu_info(&app_state.db, menu_id).await?;
            
            // 测试权限
            let test_result = Self::test_menu_permission(
                &app_state.casbin_service,
                &user_identity,
                &request.tenant_id,
                &menu_info,
                &user_roles,
            ).await?;

            let execution_time = menu_test_start.elapsed();
            
            let result = PermissionTestResult {
                user_id: Some(request.user_id),
                menu_id: menu_id.clone(),
                menu_name: menu_info.name,
                accessible: test_result.accessible,
                reason: test_result.reason,
                required_permissions: menu_info.required_permissions,
                user_permissions: user_roles.clone(),
                matched_permissions: test_result.matched_permissions,
                missing_permissions: test_result.missing_permissions,
                data_scopes_valid: test_result.data_scopes_valid,
                decision_path: test_result.decision_path,
                execution_time_ms: execution_time.as_millis() as u64,
            };

            results.push(result);
        }

        let total_execution_time = start_time.elapsed();

        // 保存测试结果（如果需要）
        if request.save_result.unwrap_or(false) {
            Self::save_test_history(
                &app_state.db,
                &request.test_name.as_ref().unwrap_or(&"单用户权限测试".to_string()),
                "single_user",
                &serde_json::to_value(&request).unwrap(),
                &serde_json::to_value(&results).unwrap(),
                results.len() as i32,
                results.iter().filter(|r| r.accessible).count() as i32,
                results.iter().filter(|r| !r.accessible).count() as i32,
                total_execution_time.as_millis() as i32,
                auth_context.user_id,
                &auth_context.roles.first().map(|r| r.identity_type.clone()).unwrap_or_else(|| "admin".to_string()),
                Some(&request.tenant_id),
            ).await?;
        }

        info!("Single user permission test completed: {}/{} menus accessible for user {} in {}ms", 
              results.iter().filter(|r| r.accessible).count(),
              results.len(),
              request.user_id,
              total_execution_time.as_millis());

        Ok(Json(ApiResponse::success(results, Some("单用户权限测试完成".to_string()))))
    }

    /// 批量用户权限测试
    pub async fn test_batch_users(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<BatchUserTestRequest>,
    ) -> Result<Json<ApiResponse<BatchTestResponse>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        let start_time = std::time::Instant::now();
        
        debug!("Batch testing permissions for {} users by admin user {}", 
               request.user_tests.len(), auth_context.user_id);

        let mut user_results = Vec::new();
        let mut total_tests = 0;
        let mut passed_tests = 0;
        let mut failed_tests = 0;

        // 逐个测试用户
        for user_test in &request.user_tests {
            // 获取用户角色
            let user_roles = Self::get_user_roles(&app_state.db, &user_test.user_id, &user_test.tenant_id).await?;
            
            if user_roles.is_empty() {
                continue;
            }

            let user_identity = if let Some(role_type) = &user_test.role_type {
                format!("{}:{}:tenant:{}", user_test.user_id, role_type, user_test.tenant_id)
            } else {
                let first_role = user_roles.first().unwrap();
                format!("{}:{}:tenant:{}", user_test.user_id, first_role, user_test.tenant_id)
            };

            let mut menu_results = Vec::new();
            let mut accessible_count = 0;
            let mut denied_count = 0;

            // 测试该用户的所有菜单权限
            for menu_id in &user_test.menu_ids {
                let menu_info = Self::get_menu_info(&app_state.db, menu_id).await?;
                let test_result = Self::test_menu_permission(
                    &app_state.casbin_service,
                    &user_identity,
                    &user_test.tenant_id,
                    &menu_info,
                    &user_roles,
                ).await?;

                if test_result.accessible {
                    accessible_count += 1;
                    passed_tests += 1;
                } else {
                    denied_count += 1;
                    failed_tests += 1;
                }

                total_tests += 1;

                menu_results.push(PermissionTestResult {
                    user_id: Some(user_test.user_id),
                    menu_id: menu_id.clone(),
                    menu_name: menu_info.name,
                    accessible: test_result.accessible,
                    reason: test_result.reason,
                    required_permissions: menu_info.required_permissions,
                    user_permissions: user_roles.clone(),
                    matched_permissions: test_result.matched_permissions,
                    missing_permissions: test_result.missing_permissions,
                    data_scopes_valid: test_result.data_scopes_valid,
                    decision_path: test_result.decision_path,
                    execution_time_ms: 0, // 批量测试中单个菜单的执行时间不单独计算
                });
            }

            user_results.push(UserTestResult {
                user_id: user_test.user_id,
                tenant_id: user_test.tenant_id.clone(),
                role_type: user_test.role_type.clone(),
                total_menus_tested: user_test.menu_ids.len() as i32,
                accessible_menus: accessible_count,
                denied_menus: denied_count,
                menu_results,
            });
        }

        let total_execution_time = start_time.elapsed();

        // 生成测试摘要
        let summary = Self::generate_test_summary(&user_results);

        let test_id = if request.save_result.unwrap_or(false) {
            Some(Self::save_test_history(
                &app_state.db,
                &request.test_name.as_ref().unwrap_or(&"批量用户权限测试".to_string()),
                "batch_users",
                &serde_json::to_value(&request).unwrap(),
                &serde_json::to_value(&user_results).unwrap(),
                total_tests,
                passed_tests,
                failed_tests,
                total_execution_time.as_millis() as i32,
                auth_context.user_id,
                &auth_context.roles.first().map(|r| r.identity_type.clone()).unwrap_or_else(|| "admin".to_string()),
                None,
            ).await?)
        } else {
            None
        };

        let response = BatchTestResponse {
            test_id,
            test_name: request.test_name.clone(),
            test_type: "batch_users".to_string(),
            total_tests,
            passed_tests,
            failed_tests,
            execution_time_ms: total_execution_time.as_millis() as u64,
            results: user_results,
            summary,
        };

        info!("Batch user permission test completed: {}/{} tests passed for {} users in {}ms", 
              passed_tests, total_tests, request.user_tests.len(), total_execution_time.as_millis());

        Ok(Json(ApiResponse::success(response, Some("批量用户权限测试完成".to_string()))))
    }

    /// 角色模拟测试
    pub async fn test_role_simulation(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<RoleSimulationTestRequest>,
    ) -> Result<Json<ApiResponse<Vec<PermissionTestResult>>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        let start_time = std::time::Instant::now();
        
        debug!("Role simulation test for role {} in tenant {} by admin user {}", 
               request.role_type, request.tenant_id, auth_context.user_id);

        // 构建模拟用户身份
        let simulated_user_id = Uuid::new_v4(); // 临时生成的用户ID用于模拟
        let user_identity = format!("{}:{}:tenant:{}", simulated_user_id, request.role_type, request.tenant_id);

        // 获取要测试的菜单列表
        let menu_ids = if let Some(menu_ids) = &request.menu_ids {
            menu_ids.clone()
        } else {
            Self::get_all_menu_ids(&app_state.db, &request.tenant_id).await?
        };

        let mut results = Vec::new();

        // 逐个测试菜单权限
        for menu_id in &menu_ids {
            let menu_info = Self::get_menu_info(&app_state.db, menu_id).await?;
            let test_result = Self::test_menu_permission(
                &app_state.casbin_service,
                &user_identity,
                &request.tenant_id,
                &menu_info,
                &vec![request.role_type.clone()],
            ).await?;

            results.push(PermissionTestResult {
                user_id: Some(simulated_user_id),
                menu_id: menu_id.clone(),
                menu_name: menu_info.name,
                accessible: test_result.accessible,
                reason: test_result.reason,
                required_permissions: menu_info.required_permissions,
                user_permissions: vec![request.role_type.clone()],
                matched_permissions: test_result.matched_permissions,
                missing_permissions: test_result.missing_permissions,
                data_scopes_valid: test_result.data_scopes_valid,
                decision_path: test_result.decision_path,
                execution_time_ms: 0,
            });
        }

        let total_execution_time = start_time.elapsed();

        // 保存测试结果（如果需要）
        if request.save_result.unwrap_or(false) {
            Self::save_test_history(
                &app_state.db,
                &request.test_name.as_ref().unwrap_or(&format!("角色模拟测试-{}", request.role_type)),
                "role_simulation",
                &serde_json::to_value(&request).unwrap(),
                &serde_json::to_value(&results).unwrap(),
                results.len() as i32,
                results.iter().filter(|r| r.accessible).count() as i32,
                results.iter().filter(|r| !r.accessible).count() as i32,
                total_execution_time.as_millis() as i32,
                auth_context.user_id,
                &auth_context.roles.first().map(|r| r.identity_type.clone()).unwrap_or_else(|| "admin".to_string()),
                Some(&request.tenant_id),
            ).await?;
        }

        info!("Role simulation test completed: {}/{} menus accessible for role {} in {}ms", 
              results.iter().filter(|r| r.accessible).count(),
              results.len(),
              request.role_type,
              total_execution_time.as_millis());

        Ok(Json(ApiResponse::success(results, Some("角色模拟测试完成".to_string()))))
    }

    /// 获取测试历史
    pub async fn get_test_history(
        State(app_state): State<AppState>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<TestHistoryQueryParams>,
    ) -> Result<Json<PaginatedApiResponse<TestHistoryRecord>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        // 构建查询条件
        let mut where_conditions = vec!["1=1".to_string()];
        
        if let Some(test_type) = &params.test_type {
            where_conditions.push(format!("test_type = '{}'", test_type.replace("'", "''")));
        }
        
        if let Some(tester_id) = &params.tester_id {
            where_conditions.push(format!("tester_id = '{}'", tester_id));
        }
        
        if let Some(tenant_id) = &params.tenant_id {
            where_conditions.push(format!("tenant_id = '{}'", tenant_id.replace("'", "''")));
        }

        if let Some(start_date) = &params.start_date {
            where_conditions.push(format!("created_at >= '{}'", start_date.replace("'", "''")));
        }

        if let Some(end_date) = &params.end_date {
            where_conditions.push(format!("created_at <= '{}'", end_date.replace("'", "''")));
        }

        let where_clause = where_conditions.join(" AND ");

        // 分页参数
        let page = params.page.unwrap_or(1) as i32;
        let page_size = params.page_size.unwrap_or(20) as i32;
        let offset = (page - 1) * page_size;

        // 计算总数
        let count_query = format!(
            "SELECT COUNT(*) FROM public.menu_permission_test_history WHERE {}", 
            where_clause
        );
        
        let total: i64 = sqlx::query_scalar(&count_query)
            .fetch_one(&app_state.db)
            .await
            .map_err(|e| {
                error!("Failed to count test history: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;

        // 获取测试历史
        let main_query = format!(
            r#"
            SELECT 
                id, test_name, test_type, total_tests, passed_tests, failed_tests,
                execution_time_ms, tester_id, tester_identity, tenant_id,
                created_at, test_parameters, test_results
            FROM public.menu_permission_test_history
            WHERE {}
            ORDER BY created_at DESC
            LIMIT {} OFFSET {}
            "#,
            where_clause, page_size, offset
        );

        let rows = sqlx::query(&main_query)
            .fetch_all(&app_state.db)
            .await
            .map_err(|e| {
                error!("Failed to fetch test history: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;

        let mut records = Vec::new();
        for row in rows {
            records.push(TestHistoryRecord {
                id: row.get("id"),
                test_name: row.get("test_name"),
                test_type: row.get("test_type"),
                total_tests: row.get("total_tests"),
                passed_tests: row.get("passed_tests"),
                failed_tests: row.get("failed_tests"),
                execution_time_ms: row.get("execution_time_ms"),
                tester_id: row.get("tester_id"),
                tester_identity: row.get("tester_identity"),
                tenant_id: row.get("tenant_id"),
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>("created_at").to_rfc3339(),
                test_parameters: row.get("test_parameters"),
                test_results: row.get("test_results"),
            });
        }

        Ok(Json(PaginatedApiResponse::success(
            records,
            page,
            page_size,
            total,
            Some("获取测试历史成功".to_string())
        )))
    }

    // 辅助方法
    async fn get_user_roles(pool: &PgPool, user_id: &Uuid, tenant_id: &str) -> Result<Vec<String>, StatusCode> {
        // 这里应该从数据库查询用户在指定租户的角色
        // 简化实现，返回模拟数据
        Ok(vec!["teacher".to_string()])
    }

    async fn get_menu_info(pool: &PgPool, menu_id: &str) -> Result<MenuInfo, StatusCode> {
        let row = sqlx::query(
            "SELECT name, required_permissions FROM public.menu_permissions WHERE menu_id = $1"
        )
        .bind(menu_id)
        .fetch_optional(pool)
        .await
        .map_err(|e| {
            error!("Failed to fetch menu info: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        match row {
            Some(row) => Ok(MenuInfo {
                name: row.get("name"),
                required_permissions: row.get::<Vec<String>, _>("required_permissions"),
            }),
            None => Err(StatusCode::NOT_FOUND)
        }
    }

    async fn get_all_menu_ids(pool: &PgPool, tenant_id: &str) -> Result<Vec<String>, StatusCode> {
        let rows = sqlx::query("SELECT menu_id FROM public.menu_permissions WHERE is_active = true")
            .fetch_all(pool)
            .await
            .map_err(|e| {
                error!("Failed to fetch menu IDs: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;

        Ok(rows.into_iter().map(|row| row.get("menu_id")).collect())
    }

    async fn test_menu_permission(
        casbin_service: &Arc<MultiTenantCasbinService>,
        user_identity: &str,
        tenant_id: &str,
        menu_info: &MenuInfo,
        user_roles: &[String],
    ) -> Result<MenuTestResult, StatusCode> {
        // 简化的权限测试逻辑
        // 实际实现需要调用Casbin进行权限检查
        
        let mut matched_permissions = Vec::new();
        let mut missing_permissions = Vec::new();
        let mut accessible = false;

        for required_perm in &menu_info.required_permissions {
            // 简化检查：如果用户角色包含所需权限，则匹配
            if user_roles.iter().any(|role| required_perm.contains(role)) {
                matched_permissions.push(required_perm.clone());
                accessible = true;
            } else {
                missing_permissions.push(required_perm.clone());
            }
        }

        Ok(MenuTestResult {
            accessible,
            reason: if accessible { None } else { Some("权限不足".to_string()) },
            matched_permissions,
            missing_permissions,
            data_scopes_valid: true, // 简化处理
            decision_path: vec!["role_check".to_string()],
        })
    }

    async fn save_test_history(
        pool: &PgPool,
        test_name: &str,
        test_type: &str,
        test_parameters: &serde_json::Value,
        test_results: &serde_json::Value,
        total_tests: i32,
        passed_tests: i32,
        failed_tests: i32,
        execution_time_ms: i32,
        tester_id: Uuid,
        tester_identity: &str,
        tenant_id: Option<&str>,
    ) -> Result<Uuid, StatusCode> {
        let test_id = sqlx::query_scalar::<_, Uuid>(
            r#"
            INSERT INTO public.menu_permission_test_history (
                test_name, test_type, test_parameters, test_results,
                total_tests, passed_tests, failed_tests, execution_time_ms,
                tester_id, tester_identity, tenant_id
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
            ) RETURNING id
            "#
        )
        .bind(test_name)
        .bind(test_type)
        .bind(test_parameters)
        .bind(test_results)
        .bind(total_tests)
        .bind(passed_tests)
        .bind(failed_tests)
        .bind(execution_time_ms)
        .bind(tester_id)
        .bind(tester_identity)
        .bind(tenant_id)
        .fetch_one(pool)
        .await
        .map_err(|e| {
            error!("Failed to save test history: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        Ok(test_id)
    }

    fn generate_test_summary(user_results: &[UserTestResult]) -> TestSummary {
        let total_users = user_results.len() as i32;
        let total_menus = user_results.iter()
            .map(|ur| ur.total_menus_tested)
            .sum::<i32>();
        let total_passed = user_results.iter()
            .map(|ur| ur.accessible_menus)
            .sum::<i32>();
        
        let overall_pass_rate = if total_menus > 0 {
            total_passed as f64 / total_menus as f64 * 100.0
        } else {
            0.0
        };

        TestSummary {
            total_users,
            total_menus,
            overall_pass_rate,
            most_accessible_menu: None, // 需要复杂计算
            least_accessible_menu: None, // 需要复杂计算
            role_performance: Vec::new(), // 需要复杂计算
        }
    }
}

#[derive(Debug)]
struct MenuInfo {
    name: String,
    required_permissions: Vec<String>,
}

#[derive(Debug)]
struct MenuTestResult {
    accessible: bool,
    reason: Option<String>,
    matched_permissions: Vec<String>,
    missing_permissions: Vec<String>,
    data_scopes_valid: bool,
    decision_path: Vec<String>,
}

/// 创建权限测试路由
pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/admin/permission-tests/single-user", post(PermissionTestController::test_single_user))
        .route("/admin/permission-tests/batch-users", post(PermissionTestController::test_batch_users))
        .route("/admin/permission-tests/role-simulation", post(PermissionTestController::test_role_simulation))
        .route("/admin/permission-tests/history", get(PermissionTestController::get_test_history))
}