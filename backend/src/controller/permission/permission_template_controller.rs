use std::sync::Arc;
use axum::{
    extract::{State, Query, Path},
    http::StatusCode,
    response::Json,
    routing::{get, post, put, delete},
    Router,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use tracing::{info, warn, error, debug};
use sqlx::{PgPool, Row};

use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
use crate::utils::api_response::{ApiResponse, PaginatedApiResponse};
use crate::utils::error::AppError;

/// 权限模板管理控制器
pub struct PermissionTemplateController;

/// 权限模板创建请求
#[derive(Debug, Deserialize)]
pub struct TemplateCreateRequest {
    pub template_name: String,
    pub template_type: String,
    pub template_category: Option<String>,
    pub permissions: Vec<String>,
    pub data_scopes: Option<Vec<String>>,
    pub permission_mode: Option<String>,
    pub description: Option<String>,
    pub metadata: Option<serde_json::Value>,
}

/// 权限模板更新请求
#[derive(Debug, Deserialize)]
pub struct TemplateUpdateRequest {
    pub template_name: Option<String>,
    pub template_type: Option<String>,
    pub template_category: Option<String>,
    pub permissions: Option<Vec<String>>,
    pub data_scopes: Option<Vec<String>>,
    pub permission_mode: Option<String>,
    pub description: Option<String>,
    pub is_active: Option<bool>,
    pub metadata: Option<serde_json::Value>,
}

/// 模板应用请求
#[derive(Debug, Deserialize)]
pub struct ApplyTemplateRequest {
    pub template_id: Uuid,
    pub menu_ids: Vec<String>,
    pub override_existing: Option<bool>,
    pub reason: Option<String>,
}

/// 批量模板操作请求
#[derive(Debug, Deserialize)]
pub struct BatchTemplateRequest {
    pub operation: String, // "activate", "deactivate", "delete", "duplicate"
    pub template_ids: Vec<Uuid>,
    pub reason: Option<String>,
}

/// 模板查询参数
#[derive(Debug, Deserialize)]
pub struct TemplateQueryParams {
    pub template_type: Option<String>,
    pub template_category: Option<String>,
    pub is_active: Option<bool>,
    pub is_system_template: Option<bool>,
    pub search: Option<String>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
    pub page: Option<u32>,
    pub page_size: Option<u32>,
}

/// 权限模板响应
#[derive(Debug, Serialize)]
pub struct PermissionTemplateResponse {
    pub id: Uuid,
    pub template_name: String,
    pub template_type: String,
    pub template_category: Option<String>,
    pub permissions: Vec<String>,
    pub data_scopes: Option<Vec<String>>,
    pub permission_mode: String,
    pub description: Option<String>,
    pub usage_count: i32,
    pub is_system_template: bool,
    pub is_active: bool,
    pub metadata: Option<serde_json::Value>,
    pub created_by: Uuid,
    pub created_at: String,
    pub updated_at: String,
    pub last_used_at: Option<String>,
    pub compatible_menus: Option<Vec<String>>,
}

/// 模板应用结果
#[derive(Debug, Serialize)]
pub struct TemplateApplicationResult {
    pub template_id: Uuid,
    pub template_name: String,
    pub applied_to_menus: Vec<MenuApplicationResult>,
    pub total_requested: i32,
    pub successful_count: i32,
    pub failed_count: i32,
    pub execution_time_ms: u64,
}

#[derive(Debug, Serialize)]
pub struct MenuApplicationResult {
    pub menu_id: String,
    pub success: bool,
    pub message: Option<String>,
    pub previous_permissions: Option<Vec<String>>,
    pub new_permissions: Vec<String>,
}

/// 模板兼容性检查结果
#[derive(Debug, Serialize)]
pub struct TemplateCompatibilityCheck {
    pub template_id: Uuid,
    pub menu_id: String,
    pub compatible: bool,
    pub compatibility_score: f64, // 0.0 to 1.0
    pub reasons: Vec<String>,
    pub suggestions: Vec<String>,
}

impl PermissionTemplateController {
    /// 获取权限模板列表
    pub async fn get_templates(
        State(pool): State<PgPool>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<TemplateQueryParams>,
    ) -> Result<Json<PaginatedApiResponse<PermissionTemplateResponse>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        let start_time = std::time::Instant::now();
        
        debug!("Getting permission templates for admin user {} with params: {:?}", 
               auth_context.user_id, params);

        // 构建查询条件
        let mut where_conditions = vec!["1=1".to_string()];
        
        if let Some(template_type) = &params.template_type {
            where_conditions.push(format!("template_type = '{}'", template_type.replace("'", "''")));
        }
        
        if let Some(template_category) = &params.template_category {
            where_conditions.push(format!("template_category = '{}'", template_category.replace("'", "''")));
        }
        
        if let Some(is_active) = params.is_active {
            where_conditions.push(format!("is_active = {}", is_active));
        }
        
        if let Some(is_system_template) = params.is_system_template {
            where_conditions.push(format!("is_system_template = {}", is_system_template));
        }
        
        if let Some(search) = &params.search {
            where_conditions.push(format!(
                "(template_name ILIKE '%{}%' OR description ILIKE '%{}%')",
                search.replace("'", "''").replace("%", "\\%"),
                search.replace("'", "''").replace("%", "\\%")
            ));
        }

        let where_clause = where_conditions.join(" AND ");

        // 分页参数
        let page = params.page.unwrap_or(1) as i32;
        let page_size = params.page_size.unwrap_or(20) as i32;
        let offset = (page - 1) * page_size;

        // 排序参数
        let sort_column = match params.sort_by.as_deref() {
            Some("name") => "template_name",
            Some("type") => "template_type",
            Some("category") => "template_category",
            Some("usage") => "usage_count",
            Some("created") => "created_at",
            Some("updated") => "updated_at",
            _ => "created_at"
        };
        
        let sort_direction = match params.sort_order.as_deref() {
            Some("asc") => "ASC",
            _ => "DESC"
        };

        // 计算总数
        let count_query = format!(
            "SELECT COUNT(*) FROM public.menu_permission_templates WHERE {}", 
            where_clause
        );
        
        let total: i64 = sqlx::query_scalar(&count_query)
            .fetch_one(&pool)
            .await
            .map_err(|e| {
                error!("Failed to count templates: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;

        // 获取模板列表
        let main_query = format!(
            r#"
            SELECT
                id, template_name, template_type, template_category,
                permissions, data_scopes, permission_mode,
                description, usage_count, is_system_template, is_active,
                metadata, created_by, created_at, updated_at
            FROM public.menu_permission_templates
            WHERE {}
            ORDER BY {} {}
            LIMIT {} OFFSET {}
            "#,
            where_clause, sort_column, sort_direction, page_size, offset
        );

        let rows = sqlx::query(&main_query)
            .fetch_all(&pool)
            .await
            .map_err(|e| {
                error!("Failed to fetch templates: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;

        let mut templates = Vec::new();
        for row in rows {
            let template = PermissionTemplateResponse {
                id: row.get("id"),
                template_name: row.get("template_name"),
                template_type: row.get("template_type"),
                template_category: row.get("template_category"),
                permissions: row.get::<Vec<String>, _>("permissions"),
                data_scopes: row.get("data_scopes"),
                permission_mode: row.get("permission_mode"),
                description: row.get("description"),
                usage_count: row.get("usage_count"),
                is_system_template: row.get("is_system_template"),
                is_active: row.get("is_active"),
                metadata: row.get("metadata"),
                created_by: row.get("created_by"),
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>("created_at").to_rfc3339(),
                updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at").to_rfc3339(),
                last_used_at: None, // TODO: 实现使用统计功能时再添加
                compatible_menus: None, // 可选的兼容菜单列表
            };
            templates.push(template);
        }

        let duration = start_time.elapsed();
        info!("Retrieved {} permission templates for admin user {} in {}ms", 
              templates.len(), auth_context.user_id, duration.as_millis());

        Ok(Json(PaginatedApiResponse::success(
            templates,
            page,
            page_size,
            total,
            Some("获取权限模板成功".to_string())
        )))
    }

    /// 创建权限模板
    pub async fn create_template(
        State(pool): State<PgPool>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<TemplateCreateRequest>,
    ) -> Result<Json<ApiResponse<PermissionTemplateResponse>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("Creating permission template '{}' by admin user {}", 
               request.template_name, auth_context.user_id);

        // 验证模板名称唯一性
        let existing_count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM public.menu_permission_templates WHERE template_name = $1"
        )
        .bind(&request.template_name)
        .fetch_one(&pool)
        .await
        .map_err(|e| {
            error!("Failed to check template name uniqueness: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        if existing_count > 0 {
            warn!("Attempt to create template with duplicate name: {}", request.template_name);
            return Err(StatusCode::CONFLICT);
        }

        // 验证权限列表不为空
        if request.permissions.is_empty() {
            warn!("Attempt to create template with empty permissions");
            return Err(StatusCode::BAD_REQUEST);
        }

        // 插入新模板
        let new_template_id = sqlx::query_scalar::<_, Uuid>(
            r#"
            INSERT INTO public.menu_permission_templates (
                template_name, template_type, template_category,
                permissions, data_scopes, permission_mode,
                description, metadata, created_by
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9
            ) RETURNING id
            "#
        )
        .bind(&request.template_name)
        .bind(&request.template_type)
        .bind(&request.template_category)
        .bind(&request.permissions)
        .bind(&request.data_scopes)
        .bind(request.permission_mode.as_deref().unwrap_or("any"))
        .bind(&request.description)
        .bind(&request.metadata)
        .bind(auth_context.user_id)
        .fetch_one(&pool)
        .await
        .map_err(|e| {
            error!("Failed to create permission template: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        // 获取新创建的模板详情
        let template = Self::get_template_by_id(&pool, &new_template_id).await?;

        info!("Successfully created permission template '{}' by admin user {}", 
              request.template_name, auth_context.user_id);

        Ok(Json(ApiResponse::success(template, Some("权限模板创建成功".to_string()))))
    }

    /// 更新权限模板
    pub async fn update_template(
        State(pool): State<PgPool>,
        AuthExtractor(auth_context): AuthExtractor,
        Path(template_id): Path<Uuid>,
        Json(request): Json<TemplateUpdateRequest>,
    ) -> Result<Json<ApiResponse<PermissionTemplateResponse>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("Updating permission template {} by admin user {}", 
               template_id, auth_context.user_id);

        // 检查模板是否存在
        let template_exists: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM public.menu_permission_templates WHERE id = $1"
        )
        .bind(&template_id)
        .fetch_one(&pool)
        .await
        .map_err(|e| {
            error!("Failed to check template existence: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        if template_exists == 0 {
            return Err(StatusCode::NOT_FOUND);
        }

        // 检查是否为系统模板
        let is_system_template: bool = sqlx::query_scalar(
            "SELECT is_system_template FROM public.menu_permission_templates WHERE id = $1"
        )
        .bind(&template_id)
        .fetch_one(&pool)
        .await
        .map_err(|e| {
            error!("Failed to check if template is system template: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        if is_system_template {
            warn!("Attempt to modify system template: {}", template_id);
            return Err(StatusCode::FORBIDDEN);
        }

        // 如果要更新模板名称，检查名称唯一性
        if let Some(template_name) = &request.template_name {
            let name_conflict: i64 = sqlx::query_scalar(
                "SELECT COUNT(*) FROM public.menu_permission_templates WHERE template_name = $1 AND id != $2"
            )
            .bind(template_name)
            .bind(&template_id)
            .fetch_one(&pool)
            .await
            .map_err(|e| {
                error!("Failed to check template name conflict: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;

            if name_conflict > 0 {
                warn!("Attempt to update template with conflicting name: {}", template_name);
                return Err(StatusCode::CONFLICT);
            }
        }

        // 执行更新
        let affected_rows = sqlx::query(
            r#"
            UPDATE public.menu_permission_templates 
            SET 
                template_name = COALESCE($1, template_name),
                template_type = COALESCE($2, template_type),
                template_category = COALESCE($3, template_category),
                permissions = COALESCE($4, permissions),
                data_scopes = COALESCE($5, data_scopes),
                permission_mode = COALESCE($6, permission_mode),
                description = COALESCE($7, description),
                is_active = COALESCE($8, is_active),
                metadata = COALESCE($9, metadata),
                updated_at = NOW()
            WHERE id = $10
            "#
        )
        .bind(&request.template_name)
        .bind(&request.template_type)
        .bind(&request.template_category)
        .bind(&request.permissions)
        .bind(&request.data_scopes)
        .bind(&request.permission_mode)
        .bind(&request.description)
        .bind(&request.is_active)
        .bind(&request.metadata)
        .bind(&template_id)
        .execute(&pool)
        .await
        .map_err(|e| {
            error!("Failed to update permission template: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        if affected_rows.rows_affected() == 0 {
            return Err(StatusCode::NOT_FOUND);
        }

        // 获取更新后的模板详情
        let template = Self::get_template_by_id(&pool, &template_id).await?;

        info!("Successfully updated permission template {} by admin user {}", 
              template_id, auth_context.user_id);

        Ok(Json(ApiResponse::success(template, Some("权限模板更新成功".to_string()))))
    }

    /// 删除权限模板
    pub async fn delete_template(
        State(pool): State<PgPool>,
        AuthExtractor(auth_context): AuthExtractor,
        Path(template_id): Path<Uuid>,
    ) -> Result<Json<ApiResponse<String>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("Deleting permission template {} by admin user {}", 
               template_id, auth_context.user_id);

        // 检查是否为系统模板
        let template_info: Option<(bool, String)> = sqlx::query_as(
            "SELECT is_system_template, template_name FROM public.menu_permission_templates WHERE id = $1"
        )
        .bind(&template_id)
        .fetch_optional(&pool)
        .await
        .map_err(|e| {
            error!("Failed to check template info: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        match template_info {
            Some((is_system, name)) => {
                if is_system {
                    warn!("Attempt to delete system template: {} ({})", template_id, name);
                    return Err(StatusCode::FORBIDDEN);
                }
            }
            None => return Err(StatusCode::NOT_FOUND)
        }

        // 删除模板
        let affected_rows = sqlx::query("DELETE FROM public.menu_permission_templates WHERE id = $1")
            .bind(&template_id)
            .execute(&pool)
            .await
            .map_err(|e| {
                error!("Failed to delete permission template: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;

        if affected_rows.rows_affected() == 0 {
            return Err(StatusCode::NOT_FOUND);
        }

        info!("Successfully deleted permission template {} by admin user {}", 
              template_id, auth_context.user_id);

        Ok(Json(ApiResponse::success(
            format!("权限模板 {} 删除成功", template_id),
            Some("权限模板删除成功".to_string())
        )))
    }

    /// 应用权限模板到菜单
    pub async fn apply_template(
        State(pool): State<PgPool>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<ApplyTemplateRequest>,
    ) -> Result<Json<ApiResponse<TemplateApplicationResult>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        let start_time = std::time::Instant::now();
        
        debug!("Applying template {} to {} menus by admin user {}", 
               request.template_id, request.menu_ids.len(), auth_context.user_id);

        // 获取模板信息
        let template = Self::get_template_by_id(&pool, &request.template_id).await?;
        
        if !template.is_active {
            warn!("Attempt to apply inactive template: {}", request.template_id);
            return Err(StatusCode::BAD_REQUEST);
        }

        // 开始事务
        let mut tx = pool.begin().await.map_err(|e| {
            error!("Failed to begin transaction: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        let mut results = Vec::new();
        let mut successful_count = 0;
        let mut failed_count = 0;

        // 逐个应用模板到菜单
        for menu_id in &request.menu_ids {
            // 检查菜单是否存在
            let menu_exists: Option<(Vec<String>, Option<Vec<String>>)> = sqlx::query_as(
                "SELECT required_permissions, data_scopes FROM public.menu_permissions WHERE menu_id = $1"
            )
            .bind(menu_id)
            .fetch_optional(&mut *tx)
            .await
            .map_err(|e| {
                error!("Failed to check menu existence: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;

            match menu_exists {
                Some((previous_permissions, previous_data_scopes)) => {
                    // 应用模板权限
                    let update_result = sqlx::query(
                        r#"
                        UPDATE public.menu_permissions 
                        SET 
                            required_permissions = $1,
                            data_scopes = $2,
                            permission_mode = $3,
                            last_modified_by = $4,
                            last_modified_at = NOW()
                        WHERE menu_id = $5
                        "#
                    )
                    .bind(&template.permissions)
                    .bind(&template.data_scopes)
                    .bind(&template.permission_mode)
                    .bind(auth_context.user_id)
                    .bind(menu_id)
                    .execute(&mut *tx)
                    .await;

                    match update_result {
                        Ok(_) => {
                            successful_count += 1;
                            results.push(MenuApplicationResult {
                                menu_id: menu_id.clone(),
                                success: true,
                                message: Some("模板应用成功".to_string()),
                                previous_permissions: Some(previous_permissions),
                                new_permissions: template.permissions.clone(),
                            });
                        }
                        Err(e) => {
                            failed_count += 1;
                            results.push(MenuApplicationResult {
                                menu_id: menu_id.clone(),
                                success: false,
                                message: Some(format!("更新失败: {}", e)),
                                previous_permissions: Some(previous_permissions),
                                new_permissions: template.permissions.clone(),
                            });
                        }
                    }
                }
                None => {
                    failed_count += 1;
                    results.push(MenuApplicationResult {
                        menu_id: menu_id.clone(),
                        success: false,
                        message: Some("菜单不存在".to_string()),
                        previous_permissions: None,
                        new_permissions: template.permissions.clone(),
                    });
                }
            }
        }

        // 更新模板使用次数
        sqlx::query("SELECT increment_template_usage($1)")
            .bind(&request.template_id)
            .execute(&mut *tx)
            .await
            .map_err(|e| {
                error!("Failed to increment template usage: {}", e);
                StatusCode::INTERNAL_SERVER_ERROR
            })?;

        // 提交事务
        tx.commit().await.map_err(|e| {
            error!("Failed to commit transaction: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        let duration = start_time.elapsed();

        let result = TemplateApplicationResult {
            template_id: request.template_id,
            template_name: template.template_name,
            applied_to_menus: results,
            total_requested: request.menu_ids.len() as i32,
            successful_count,
            failed_count,
            execution_time_ms: duration.as_millis() as u64,
        };

        info!("Applied template {} to {}/{} menus successfully by admin user {} in {}ms", 
              request.template_id, successful_count, request.menu_ids.len(), 
              auth_context.user_id, duration.as_millis());

        Ok(Json(ApiResponse::success(result, Some("权限模板应用完成".to_string()))))
    }

    /// 检查模板与菜单的兼容性
    pub async fn check_template_compatibility(
        State(pool): State<PgPool>,
        AuthExtractor(auth_context): AuthExtractor,
        Path((template_id, menu_id)): Path<(Uuid, String)>,
    ) -> Result<Json<ApiResponse<TemplateCompatibilityCheck>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("Checking compatibility between template {} and menu {} by admin user {}", 
               template_id, menu_id, auth_context.user_id);

        // 获取模板信息
        let template = Self::get_template_by_id(&pool, &template_id).await?;

        // 获取菜单信息
        let menu_info: Option<(String, Vec<String>, Option<Vec<String>>)> = sqlx::query_as(
            "SELECT menu_type, required_permissions, data_scopes FROM public.menu_permissions WHERE menu_id = $1"
        )
        .bind(&menu_id)
        .fetch_optional(&pool)
        .await
        .map_err(|e| {
            error!("Failed to fetch menu info: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        let compatibility_check = match menu_info {
            Some((menu_type, current_permissions, current_data_scopes)) => {
                // 执行兼容性检查逻辑
                let mut reasons = Vec::new();
                let mut suggestions = Vec::new();
                let mut compatibility_score = 1.0;

                // 检查模板类型与菜单类型的匹配度
                if template.template_type == "role_based" && menu_type == "admin" {
                    compatibility_score *= 0.8;
                    reasons.push("模板类型与菜单类型不完全匹配".to_string());
                    suggestions.push("考虑使用admin类型的权限模板".to_string());
                }

                // 检查权限复杂度
                if template.permissions.len() > current_permissions.len() * 2 {
                    compatibility_score *= 0.7;
                    reasons.push("模板权限复杂度远超当前菜单".to_string());
                    suggestions.push("简化权限要求或分步应用".to_string());
                }

                // 检查数据范围兼容性
                if template.data_scopes.is_some() && current_data_scopes.is_none() {
                    compatibility_score *= 0.9;
                    reasons.push("模板包含数据范围限制，当前菜单没有".to_string());
                    suggestions.push("确认数据范围配置的必要性".to_string());
                }

                let compatible = compatibility_score >= 0.6;

                TemplateCompatibilityCheck {
                    template_id,
                    menu_id: menu_id.clone(),
                    compatible,
                    compatibility_score,
                    reasons,
                    suggestions,
                }
            }
            None => {
                TemplateCompatibilityCheck {
                    template_id,
                    menu_id: menu_id.clone(),
                    compatible: false,
                    compatibility_score: 0.0,
                    reasons: vec!["菜单不存在".to_string()],
                    suggestions: vec!["请检查菜单ID是否正确".to_string()],
                }
            }
        };

        Ok(Json(ApiResponse::success(compatibility_check, Some("兼容性检查完成".to_string()))))
    }

    /// 获取模板详情
    async fn get_template_by_id(pool: &PgPool, template_id: &Uuid) -> Result<PermissionTemplateResponse, StatusCode> {
        let row = sqlx::query(
            r#"
            SELECT 
                id, template_name, template_type, template_category,
                permissions, data_scopes, permission_mode,
                description, usage_count, is_system_template, is_active,
                metadata, created_by, created_at, updated_at
            FROM public.menu_permission_templates 
            WHERE id = $1
            "#
        )
        .bind(template_id)
        .fetch_optional(pool)
        .await
        .map_err(|e| {
            error!("Failed to fetch template by ID: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

        match row {
            Some(row) => Ok(PermissionTemplateResponse {
                id: row.get("id"),
                template_name: row.get("template_name"),
                template_type: row.get("template_type"),
                template_category: row.get("template_category"),
                permissions: row.get::<Vec<String>, _>("permissions"),
                data_scopes: row.get("data_scopes"),
                permission_mode: row.get("permission_mode"),
                description: row.get("description"),
                usage_count: row.get("usage_count"),
                is_system_template: row.get("is_system_template"),
                is_active: row.get("is_active"),
                metadata: row.get("metadata"),
                created_by: row.get("created_by"),
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>("created_at").to_rfc3339(),
                updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>("updated_at").to_rfc3339(),
                last_used_at: None,
                compatible_menus: None,
            }),
            None => Err(StatusCode::NOT_FOUND)
        }
    }
}

/// 创建权限模板管理路由
pub fn create_router() -> Router<PgPool> {
    Router::new()
        .route("/permission-templates", get(PermissionTemplateController::get_templates))
        .route("/permission-templates", post(PermissionTemplateController::create_template))
        .route("/permission-templates/{template_id}", put(PermissionTemplateController::update_template))
        .route("/permission-templates/{template_id}", delete(PermissionTemplateController::delete_template))
        .route("/permission-templates/apply", post(PermissionTemplateController::apply_template))
        .route("/permission-templates/{template_id}/compatibility/{menu_id}",
               get(PermissionTemplateController::check_template_compatibility))
}