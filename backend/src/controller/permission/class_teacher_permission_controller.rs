use axum::{
    extract::{Path, Query, State},
    http::<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::{
    middleware::auth_middleware::AuthExtractor,
    service::permission::{
        ClassTeacherPermissionManager,
        PermissionSyncResult,
        ClassTeacherPermission,
    },
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};

#[derive(Debug, Deserialize)]
pub struct AssignPermissionRequest {
    pub user_id: Uuid,
    pub class_ids: Vec<Uuid>,
}

#[derive(Debug, Deserialize)]
pub struct RevokePermissionRequest {
    pub user_id: Uuid,
    pub class_ids: Vec<Uuid>,
}

#[derive(Debug, Deserialize)]
pub struct SyncPermissionQuery {
    pub force: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct PermissionSyncResponse {
    pub success_count: usize,
    pub error_count: usize,
    pub errors: Vec<String>,
    pub message: String,
}

impl From<PermissionSyncResult> for PermissionSyncResponse {
    fn from(result: PermissionSyncResult) -> Self {
        let message = if result.error_count == 0 {
            format!("Successfully synced {} permissions", result.success_count)
        } else {
            format!("Synced {} permissions with {} errors", result.success_count, result.error_count)
        };

        Self {
            success_count: result.success_count,
            error_count: result.error_count,
            errors: result.errors,
            message,
        }
    }
}

/// 班主任权限管理控制器
pub struct ClassTeacherPermissionController;

impl ClassTeacherPermissionController {
    /// 为班主任分配学生数据访问权限
    pub async fn assign_permissions(
        State(state): State<AppState>,
        Path(tenant_name): Path<String>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<AssignPermissionRequest>,
    ) -> Result<Json<ApiResponse<PermissionSyncResponse>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        // 获取租户ID
        let tenant = match state.auth_integration.tenant_service.get_tenant_by_schema(&tenant_name).await {
            Ok(tenant) => tenant,
            Err(_) => return Err(StatusCode::NOT_FOUND),
        };
        let tenant_id = tenant.id.to_string();

        // 创建权限管理器
        let permission_manager = ClassTeacherPermissionManager::new(state.db.clone());
        
        // 分配权限
        match permission_manager.assign_class_teacher_permissions(
            &tenant_id,
            &tenant_name,
            request.user_id,
            &request.class_ids,
            state.casbin_service.as_ref(),
        ).await {
            Ok(result) => {
                let response = PermissionSyncResponse::from(result);
                Ok(Json(responses::success(response, None)))
            },
            Err(e) => {
                tracing::error!("Failed to assign class teacher permissions: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }

    /// 移除班主任的学生数据访问权限
    pub async fn revoke_permissions(
        State(state): State<AppState>,
        Path(tenant_name): Path<String>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<RevokePermissionRequest>,
    ) -> Result<Json<ApiResponse<PermissionSyncResponse>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        // 获取租户ID
        let tenant = match state.auth_integration.tenant_service.get_tenant_by_schema(&tenant_name).await {
            Ok(tenant) => tenant,
            Err(_) => return Err(StatusCode::NOT_FOUND),
        };
        let tenant_id = tenant.id.to_string();

        // 创建权限管理器
        let permission_manager = ClassTeacherPermissionManager::new(state.db.clone());
        
        // 移除权限
        match permission_manager.revoke_class_teacher_permissions(
            &tenant_id,
            request.user_id,
            &request.class_ids,
            state.casbin_service.as_ref(),
        ).await {
            Ok(result) => {
                let response = PermissionSyncResponse::from(result);
                Ok(Json(responses::success(response, None)))
            },
            Err(e) => {
                tracing::error!("Failed to revoke class teacher permissions: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }

    /// 同步所有班主任权限
    pub async fn sync_all_permissions(
        State(state): State<AppState>,
        Path(tenant_name): Path<String>,
        Query(query): Query<SyncPermissionQuery>,
        AuthExtractor(auth_context): AuthExtractor,
    ) -> Result<Json<ApiResponse<PermissionSyncResponse>>, StatusCode> {
        // 检查管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        // 获取租户ID
        let tenant = match state.auth_integration.tenant_service.get_tenant_by_schema(&tenant_name).await {
            Ok(tenant) => tenant,
            Err(_) => return Err(StatusCode::NOT_FOUND),
        };
        let tenant_id = tenant.id.to_string();

        // 创建权限管理器
        let permission_manager = ClassTeacherPermissionManager::new(state.db.clone());
        
        // 同步权限
        match permission_manager.sync_all_class_teacher_permissions(
            &tenant_id,
            &tenant_name,
            state.casbin_service.as_ref(),
        ).await {
            Ok(result) => {
                let response = PermissionSyncResponse::from(result);
                Ok(Json(responses::success(response, None)))
            },
            Err(e) => {
                tracing::error!("Failed to sync all class teacher permissions: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }

    /// 获取班主任权限详情
    pub async fn get_permissions(
        State(state): State<AppState>,
        Path((tenant_name, user_id)): Path<(String, Uuid)>,
        AuthExtractor(auth_context): AuthExtractor,
    ) -> Result<Json<ApiResponse<ClassTeacherPermission>>, StatusCode> {
        // 检查权限：管理员或本人可以查看
        if !auth_context.is_super_admin() && auth_context.user_id != user_id {
            return Err(StatusCode::FORBIDDEN);
        }

        // 获取租户ID
        let tenant = match state.auth_integration.tenant_service.get_tenant_by_schema(&tenant_name).await {
            Ok(tenant) => tenant,
            Err(_) => return Err(StatusCode::NOT_FOUND),
        };
        let tenant_id = tenant.id.to_string();

        // 创建权限管理器
        let permission_manager = ClassTeacherPermissionManager::new(state.db.clone());
        
        // 获取权限详情
        match permission_manager.get_class_teacher_permissions(
            &tenant_id,
            &tenant_name,
            user_id,
        ).await {
            Ok(permissions) => {
                Ok(Json(responses::success(permissions, None)))
            },
            Err(e) => {
                tracing::error!("Failed to get class teacher permissions: {}", e);
                Err(StatusCode::NOT_FOUND)
            }
        }
    }

    /// 验证班主任权限
    pub async fn verify_permissions(
        State(state): State<AppState>,
        Path((tenant_name, user_id, class_id)): Path<(String, Uuid, Uuid)>,
        AuthExtractor(auth_context): AuthExtractor,
    ) -> Result<Json<ApiResponse<bool>>, StatusCode> {
        // 检查权限：管理员或本人可以验证
        if !auth_context.is_super_admin() && auth_context.user_id != user_id {
            return Err(StatusCode::FORBIDDEN);
        }

        // 获取租户ID
        let tenant = match state.auth_integration.tenant_service.get_tenant_by_schema(&tenant_name).await {
            Ok(tenant) => tenant,
            Err(_) => return Err(StatusCode::NOT_FOUND),
        };
        let tenant_id = tenant.id.to_string();

        // 创建权限管理器
        let permission_manager = ClassTeacherPermissionManager::new(state.db.clone());
        
        // 验证权限
        match permission_manager.verify_class_teacher_permissions(
            &tenant_id,
            user_id,
            class_id,
            state.casbin_service.as_ref(),
        ).await {
            Ok(has_permission) => {
                Ok(Json(responses::success(has_permission, None)))
            },
            Err(e) => {
                tracing::error!("Failed to verify class teacher permissions: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }
}
