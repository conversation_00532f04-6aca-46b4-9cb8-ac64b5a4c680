use std::sync::Arc;
use crate::service::grading::grading_service::GradingService;
use crate::web_server::AppState;
use axum::{extract::{Path, Query, State}, routing::{get, post}, http::StatusCode, response::<PERSON><PERSON>, Router};
use axum::extract::Multipart;
use uuid::Uuid;
use crate::model::grading::grading::{AIGradingRecordResponse, AnswerCardBlockResponse, CardBlockGradingRecordResponse, CreateAnswerCardBlocksRequest, CreatePaperScanFileRequest, GradingAssignmentListResponse, GradingAssignmentQueryParams, GradingAssignmentRequest, GradingAssignmentResponse, GradingControlRequest, GradingControlStateResponse, GradingRecordResponse, GradingStatisticsQueryParams, GradingStatisticsResponse, HandleScanExceptionRequest, PaperScanBatchRequest, PaperScanListResponse, PaperScanPathRequest, PaperScanQueryParams, PaperScanResponse, ProcessAIGradingRequest, RecognizeRequest, ScanExceptionResponse, SheetRequest, SubmitCardBlockGradingRequest, SubmitGradingRecordRequest};
use crate::service::storage::storage_service::UploadOptions;
use crate::utils::api_response::{responses, ApiResponse};

pub fn create_router() -> Router<AppState> {
    Router::new()
        .route("/uploadPaperScansHandler/{exam_id}/{batch_no}", post(upload_paper_scans_handler))
        .route("/getPaperScanHandler", get(get_paper_scan_handler))
        .route("/listPaperScansHandler", get(list_paper_scans_handler))
        .route("/createAnswerCardBlocksHandler", post(create_answer_card_blocks_handler))
        .route("/assignGradingTasksHandler",post(assign_grading_tasks_handler))
        .route("/getGradingAssignmentsHandler", get(get_grading_assignments_handler))
        .route("/submitGradingRecordHandler",post(submit_grading_record_handler))
        .route("/submitCardBlockGradingHandler",post(submit_card_block_grading_handler))
        .route("/processAiGradingHandler",post(process_ai_grading_handler))
        .route("/controlGradingProcessHandler",get(control_grading_process_handler))
        .route("/handleScanExceptionHandler",get(handle_scan_exception_handler))
        .route("/getGradingStatisticsHandler",get(get_grading_statistics_handler))

}

pub async fn upload_paper_scans_handler(
    State(state): State<AppState>,
    Path(params): Path<PaperScanPathRequest>,
    mut payload: Multipart,
) -> Result<Json<ApiResponse<String>>, ApiResponse<String>> {

    // 初始化化默认值
    let mut request: PaperScanBatchRequest = PaperScanBatchRequest {
        id: Uuid::new_v4(),
        exam_id: params.exam_id,
        student_id: Default::default(),
        
        scan_method: "scanner_direct".to_string(),
        scan_device: None,
        status: "unprocessed".to_string(),
        result: None,
        scans: vec![],
    };

    // 1. 循环提取处理所有参数 并且处理文件同步 Minio 服务端
    while let Some(field) = payload.next_field().await
        .map_err(|e| ApiResponse::error(format!("Failed reading multipart field: {}", e),None))?
    {
        let field_name = field.name().unwrap_or_default().to_string();

        println!("field_name: {}",field_name);
        // 检查参数并提取
        match field_name.as_str() {
            "file" =>{
                let field_name = field.name().unwrap_or_default().to_string();
                // 处理文件字段
                if let Some(file_name) = field.file_name().map(|s| s.to_string()) {
                    let file_id = Uuid::new_v4();
                    let file_name_key = format!("{}_{}",file_id, file_name);
                    let content_type = field.content_type().unwrap_or("application/octet-stream").to_string();
                    let prefix = format!("{}/{}/{}/{}/{}/{}","tenants",params.tenant_name,"grading","paper_scans",params.exam_id,params.batch_no);
                    let bytes = field.bytes().await.map_err(|e| ApiResponse::error( format!("Failed to read file field: {}", e),None))?;

                    // 上传到 MinIO
                    let file= state.storage_service.upload(
                        &file_name_key,
                        bytes,
                        UploadOptions { preserve_filename: false, prefix: Some(prefix) },
                    ).await.map_err(|e|  ApiResponse::error(format!("Failed to upload file to MinIO: {}", e),None))?;

                    // 正反面转换
                    let paper_num = state.grading_service.parse_page_type_from_filename(file_name.as_str()).expect("Invalid filename format");

                    // 将当前同步成功的文件填充到请求对象中
                    request.scans.push(CreatePaperScanFileRequest {
                        id: Uuid::new_v4(),
                        paper_scan_id: request.id,
                        paper_num,
                        file_name,
                        file_url: file.key.clone(),
                        minio_bucket:Some( "deep-mate".to_string()), //TODO 目前暂时先固定为 deep-m
                        minio_object_key: Some(file.key.clone()),
                        file_size: file.size as i64,
                        scan_quality: 1,
                        is_duplicate: false,
                        is_blank: false,
                        is_abnormal: false,
                        abnormal_reason: None,
                        result: None,
                        create_at: Default::default(),
                        update_at: Default::default(),
                    })
                    // uploaded_files.push(file);
                }
            }
            "student_id" =>{
                let value = field.text().await.unwrap_or_default();
                request.student_id = Some(Uuid::parse_str(&value).unwrap_or_default());
            }
            "scan_method" => {
                let value = field.text().await.unwrap_or_default();
                request.scan_method = value.to_string();
            }
            "scan_device" =>{
                let value = field.text().await.unwrap_or_default();
                request.scan_device = Some(value.to_string());
            }
            _=>{}
        }
    }

    // 2.将当前纸张记录保存到数据库中
    let scan_pages = state.grading_service.save_upload_paper_scans(params.tenant_name,&request).await
        .map_err(|e| ApiResponse::error(format!("Error getting paper scan: : {}", e),None))?;

    // 3. 调用试卷识别服务解释当前结果
    state.grading_service.recognize_paper_scans(state.clone() ,&request).await.map_err(|e|ApiResponse::error(format!("Error recognize service: {}", e),None))?;

    // 4. 将当试卷识别结果更新入当前所属租户对应的结果中

    Ok(Json(ApiResponse::success("Upload successful".to_string(), None)))
}


pub async fn get_paper_scan_handler(
    State(state): State<AppState>,
    Path(scan_id): Path<Uuid>,
) -> Result<Json<ApiResponse<PaperScanResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.get_paper_scan(tenant_id, scan_id).await {
        Ok(Some(scan)) => Ok(Json(ApiResponse::success(scan, None))),
        Ok(None) => Ok(Json(ApiResponse::error("Paper scan not found".to_string(), None))),
        Err(e) => {
            eprintln!("Error getting paper scan: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn list_paper_scans_handler(
    State(state): State<AppState>,
    Query(params): Query<PaperScanQueryParams>,
) -> Result<Json<ApiResponse<PaperScanListResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.list_paper_scans(tenant_id, params).await {
        Ok(response) => Ok(Json(ApiResponse::success(response, None))),
        Err(e) => {
            eprintln!("Error listing paper scans: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn create_answer_card_blocks_handler(
    State(state): State<AppState>,
    Json(request): Json<CreateAnswerCardBlocksRequest>,
) -> Result<Json<ApiResponse<Vec<AnswerCardBlockResponse>>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.create_answer_card_blocks(tenant_id, request).await {
        Ok(blocks) => Ok(Json(ApiResponse::success(blocks, None))),
        Err(e) => {
            eprintln!("Error creating answer card blocks: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn assign_grading_tasks_handler(
    State(state): State<AppState>,
    Json(request): Json<GradingAssignmentRequest>,
) -> Result<Json<ApiResponse<Vec<GradingAssignmentResponse>>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.assign_grading_tasks(tenant_id, request).await {
        Ok(assignments) => Ok(Json(ApiResponse::success(assignments, None))),
        Err(e) => {
            eprintln!("Error assigning grading tasks: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn get_grading_assignments_handler(
    State(state): State<AppState>,
    Query(params): Query<GradingAssignmentQueryParams>,
) -> Result<Json<ApiResponse<GradingAssignmentListResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.get_grading_assignments(tenant_id, params).await {
        Ok(response) => Ok(Json(ApiResponse::success(response, None))),
        Err(e) => {
            eprintln!("Error getting grading assignments: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(),None)))
        }
    }
}

pub async fn submit_grading_record_handler(
    State(state): State<AppState>,
    Json(request): Json<SubmitGradingRecordRequest>,
) -> Result<Json<ApiResponse<GradingRecordResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.submit_grading_record(tenant_id, request).await {
        Ok(record) => Ok(Json(ApiResponse::success(record, None))),
        Err(e) => {
            eprintln!("Error submitting grading record: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn submit_card_block_grading_handler(
    State(state): State<AppState>,
    Json(request): Json<SubmitCardBlockGradingRequest>,
) -> Result<Json<ApiResponse<CardBlockGradingRecordResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.submit_card_block_grading(tenant_id, request).await {
        Ok(record) => Ok(Json(ApiResponse::success(record, None))),
        Err(e) => {
            eprintln!("Error submitting card block grading: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn process_ai_grading_handler(
    State(state): State<AppState>,
    Json(request): Json<ProcessAIGradingRequest>,
) -> Result<Json<ApiResponse<Vec<AIGradingRecordResponse>>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.process_ai_grading(tenant_id, request).await {
        Ok(records) => Ok(Json(ApiResponse::success(records, None))),
        Err(e) => {
            eprintln!("Error processing AI grading: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn control_grading_process_handler(
    State(state): State<AppState>,
    Json(request): Json<GradingControlRequest>,
) -> Result<Json<ApiResponse<GradingControlStateResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.control_grading_process(tenant_id, request).await {
        Ok(state) => Ok(Json(ApiResponse::success(state, None))),
        Err(e) => {
            eprintln!("Error controlling grading process: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn handle_scan_exception_handler(
    State(state): State<AppState>,
    Json(request): Json<HandleScanExceptionRequest>,
) -> Result<Json<ApiResponse<ScanExceptionResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.handle_scan_exception(tenant_id, request).await {
        Ok(response) => Ok(Json(ApiResponse::success(response, None))),
        Err(e) => {
            eprintln!("Error handling scan exception: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}

pub async fn get_grading_statistics_handler(
    State(state): State<AppState>,
    Query(params): Query<GradingStatisticsQueryParams>,
) -> Result<Json<ApiResponse<GradingStatisticsResponse>>, StatusCode> {
    let service = GradingService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    
    match service.get_grading_statistics(tenant_id, params).await {
        Ok(stats) => Ok(Json(ApiResponse::success(stats, None))),
        Err(e) => {
            eprintln!("Error getting grading statistics: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), None)))
        }
    }
}