use crate::middleware::auth_middleware::AuthExtractor;
use crate::middleware::tenant_middleware::TenantExtractor;
use crate::model::teacher::{CreateTeacherParams, TeacherQueryParams, UpdateTeacherParams};
use crate::model::{<PERSON><PERSON>llParams, PageAllTeacherPara<PERSON>, Teacher, TeacherSummary};
use crate::service::teacher::teacher_service::TeacherService;
use crate::utils::api_response::{responses, ApiResponse, PaginatedApiResponse};
use crate::utils::error::AppError;
use crate::web_server::AppState;
use axum::http::HeaderMap;
use axum::routing::post;
use axum::{
    extract::{Path, Query, State},
    response::Json,
    routing::{get, patch},
    Router,
};
use std::collections::HashMap;

/// 创建教师管理路由
pub fn create_router() -> Router<AppState> {
    Router::new()
        // .route("/", get(list_teachers).post(create_teacher))
        // .route(
        //     "/{id}",
        //     get(get_teacher).put(update_teacher).delete(delete_teacher),
        // )
        // .route("/{id}/toggle-status", patch(toggle_teacher_status))
        .route("/Summaries", post(get_teacher_summaries))
        .route("/findAll", post(find_all))
        .route("/pageAllTeacher", post(page_all_teacher))
        .route("/createTeacher", post(create_teacher))
        .route("/updateTeacher", post(update_teacher))
}

// /// 获取教师列表
// #[axum::debug_handler]
// async fn list_teachers(
//     State(teacher_service): State<TeacherService>,
//     TenantExtractor(tenant_context): TenantExtractor,
//     Query(params): Query<TeacherQueryParams>,
// ) -> Result<
//     Json<ApiResponse<crate::model::base::PageResult<crate::model::teacher::TeacherListVO>>>,
//     AppError,
// > {
//     let result = teacher_service
//         .get_teachers(&tenant_context.schema_name, params)
//         .await?;
//     Ok(Json(ApiResponse::success(result, None)))
// }

// /// 获取单个教师详情
// #[axum::debug_handler]
// async fn get_teacher(
//     State(teacher_service): State<TeacherService>,
//     TenantExtractor(tenant_context): TenantExtractor,
//     Path(teacher_id): Path<Uuid>,
// ) -> Result<Json<ApiResponse<crate::model::teacher::TeacherDetailVO>>, AppError> {
//     let teacher = teacher_service
//         .get_teacher_by_id(&tenant_context.schema_name, teacher_id)
//         .await?
//         .ok_or(AppError::NotFound("教师不存在".to_string()))?;
//     Ok(Json(ApiResponse::success(teacher, None)))
// }

// /// 创建教师
// #[axum::debug_handler]
// async fn create_teacher(
//     State(teacher_service): State<TeacherService>,
//     TenantExtractor(tenant_context): TenantExtractor,
//     Json(payload): Json<CreateTeacher>,
// ) -> Result<Json<ApiResponse<crate::model::teacher::Teacher>>, AppError> {
//     let teacher = teacher_service
//         .create_teacher(
//             &tenant_context.schema_name,
//             tenant_context.tenant_id,
//             payload,
//         )
//         .await?;
//     Ok(Json(ApiResponse::success(teacher, None)))
// }

// /// 更新教师信息
// #[axum::debug_handler]
// async fn update_teacher(
//     State(teacher_service): State<TeacherService>,
//     TenantExtractor(tenant_context): TenantExtractor,
//     Path(teacher_id): Path<Uuid>,
//     Json(payload): Json<UpdateTeacher>,
// ) -> Result<Json<ApiResponse<crate::model::teacher::Teacher>>, AppError> {
//     let teacher = teacher_service
//         .update_teacher(&tenant_context.schema_name, teacher_id, payload)
//         .await?;
//     Ok(Json(ApiResponse::success(teacher, None)))
// }

// /// 删除教师
// #[axum::debug_handler]
// async fn delete_teacher(
//     State(teacher_service): State<TeacherService>,
//     TenantExtractor(tenant_context): TenantExtractor,
//     Path(teacher_id): Path<Uuid>,
// ) -> Result<Json<ApiResponse<()>>, AppError> {
//     teacher_service
//         .delete_teacher(&tenant_context.schema_name, teacher_id)
//         .await?;
//     Ok(Json(ApiResponse::success((), None)))
// }

// #[derive(serde::Deserialize)]
// struct ToggleStatusPayload {
//     is_active: bool,
// }

// /// 切换教师状态
// #[axum::debug_handler]
// async fn toggle_teacher_status(
//     State(teacher_service): State<TeacherService>,
//     TenantExtractor(tenant_context): TenantExtractor,
//     Path(teacher_id): Path<Uuid>,
//     Json(payload): Json<ToggleStatusPayload>,
// ) -> Result<Json<ApiResponse<crate::model::teacher::Teacher>>, AppError> {
//     let teacher = teacher_service
//         .toggle_teacher_status(&tenant_context.schema_name, teacher_id, payload.is_active)
//         .await?;
//     Ok(Json(ApiResponse::success(teacher, None)))
// }

pub async fn get_teacher_summaries(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(_admin_context): AuthExtractor,
    Json(params): Json<Option<HashMap<String, bool>>>,
) -> Result<ApiResponse<Vec<TeacherSummary>>, ApiResponse<()>> {
    let is_active = params.and_then(|p| p.get("is_active").copied());
    match state
        .teacher_service
        .get_teacher_summaries(&tenant_name, is_active)
        .await
    {
        Ok(data) => Ok(responses::success(data, None)),
        Err(e) => Err(responses::error(&format!("{}", e), None)),
    }
}

pub async fn find_all(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    AuthExtractor(admin_context): AuthExtractor,
    Json(params): Json<FindAllParams>,
) -> Result<ApiResponse<Vec<Teacher>>, ApiResponse<()>> {
    state
        .teacher_service
        .find_all(&tenant_name, &params)
        .await
        .map_err(|e| responses::error(e.to_string().as_str(), None))
        .map(|data| responses::success(data, None))
}

pub async fn page_all_teacher(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<PageAllTeacherParams>,
) -> Result<PaginatedApiResponse<Teacher>, PaginatedApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::paginated_error("权限不足！", None));
    }
    state
        .teacher_service
        .page_all_teacher(&tenant_name, &params)
        .await
        .map_err(|e| responses::paginated_error(&e, None))
        .map(|(list, total)| {
            responses::paginated_success(
                list,
                params.page_params.get_page(),
                params.page_params.get_page_size(),
                total,
                None,
            )
        })
}

pub async fn create_teacher(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<CreateTeacherParams>,
) -> Result<ApiResponse<Teacher>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::error("权限不足！", None));
    }
    state
        .teacher_service
        .create_teacher(&tenant_name, &tenant_id.unwrap_or_default(), &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

pub async fn update_teacher(
    State(state): State<AppState>,
    Path(tenant_name): Path<String>,
    header_map: HeaderMap,
    AuthExtractor(context): AuthExtractor,
    Json(params): Json<UpdateTeacherParams>,
) -> Result<ApiResponse<Teacher>, ApiResponse<()>> {
    let tenant_id = context.get_tenant_id_from_headers(header_map);
    if !context.is_admin_in_tenant(tenant_id) {
        return Err(responses::error("权限不足！", None));
    }
    state
        .teacher_service
        .update_teacher(&tenant_name, &tenant_id, &params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}
