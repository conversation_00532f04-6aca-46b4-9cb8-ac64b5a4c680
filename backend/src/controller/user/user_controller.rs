use crate::middleware::auth_middleware::AuthExtractor;
use crate::service::user::user_service::{CreateUserRequest, UpdateUserRequest, UserService};
use crate::utils::api_response::{responses, ErrorResponse, SuccessResponse};
use axum::{
    extract::{Json, State},
    http::StatusCode,
    response::IntoResponse,
    routing::{get, post},
    Router,
};

/// 获取当前用户信息（使用新的认证系统）
// #[axum::debug_handler]
// pub async fn get_current_user_info(
//     AuthExtractor(auth_context): AuthExtractor,
// ) -> impl IntoResponse {
//     let user_info = UserInfo {
//         user_id: auth_context.user_id,
//         username: auth_context.username.clone(),
//         roles: auth_context
//             .roles
//             .iter()
//             .filter(|role| role.is_verified)
//             .map(|role| role.identity_type.clone())
//             .collect(),
//         is_admin: auth_context.is_super_admin(),
//         is_teacher: auth_context.is_teacher(),
//         is_student: auth_context.is_student(),
//
//         phone_number: auth_context.phone_number,
//         created_at: auth_context.created_at,
//         phone_verified: auth_context.phone_verified,
//         is_active: auth_context.is_active,
//     };
//
//     Json(SuccessResponse {
//         success: true,
//         data: Some(user_info),
//         message: "User information retrieved successfully".to_string(),
//     })
//     .into_response()
// }

/// 获取所有用户列表（使用传统的管理员认证）
#[axum::debug_handler]
pub async fn get_all_users(
    State(user_service): State<UserService>,
    AuthExtractor(_admin_context): AuthExtractor,
) -> impl IntoResponse {
    match user_service.get_all_users().await {
        Ok(users) => Json(SuccessResponse {
            success: true,
            data: Some(users),
            message: "用户检索成功".to_string(),
        })
        .into_response(),
        Err(e) => {
            eprintln!("Error fetching users: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    success: false,
                    message: "获取用户失败".to_string(),
                    error_code: Some("FETCH_USERS_FAILED".to_string()),
                }),
            )
                .into_response()
        }
    }
}
#[axum::debug_handler]
pub async fn create_user(
    State(user_service): State<UserService>,
    AuthExtractor(admin_context): AuthExtractor,
    Json(request): Json<CreateUserRequest>,
) -> impl IntoResponse {
    match user_service
        .create_user(request)
        .await
    {
        Ok(responses) => responses::success(responses, Some("用户创建成功")),
        Err(e) => {
            responses::error(format!("创建用户失败：{}",e).as_str(), Some("CREATE_USER_FAILED"))
        }
    }
}

#[axum::debug_handler]
pub async fn update_user(
    State(user_service): State<UserService>,
    AuthExtractor(admin_context): AuthExtractor,
    Json(request): Json<UpdateUserRequest>,
) -> impl IntoResponse {
    match user_service
        .update_user(request)
        .await
    {
        Ok(responses) => responses::success(responses, Some("用户更新成功")),
        Err(e) => {
            responses::error(format!("更新用户失败：{}",e).as_str(), Some("UPDATE_USER_FAILED"))
        }  
    }
}
/// 用户管理路由
pub fn crate_router() -> Router<UserService> {
    Router::new()
        // .route("/me", get(get_current_user_info))
        .route("/all", get(get_all_users))
        .route("/create", post(create_user))
        .route("/update", post(update_user))
}
