use std::sync::Arc;
use axum::{
    extract::{State, Query, Path},
    http::StatusCode,
    response::Json,
    routing::{get, post, put, delete},
    Router,
    Extension,
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use tracing::{info, warn, error, debug};

use crate::service::menu::MenuService;
use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
use crate::utils::api_response::{ApiResponse, PaginatedApiResponse};
use crate::utils::error::AppError;

/// 菜单管理控制器
pub struct MenuController;

/// 菜单创建请求
#[derive(Debug, Deserialize)]
pub struct MenuCreateRequest {
    pub menu_id: String,
    pub name: String,
    pub path: String,
    pub icon: Option<String>,
    pub parent_id: Option<String>,
    pub menu_type: Option<String>,
    pub description: Option<String>,
    pub component_path: Option<String>,
    pub external_link: Option<String>,
    pub required_roles: Option<Vec<String>>,
    pub access_level: Option<i32>,
    pub sort_order: Option<i32>,
    pub is_active: Option<bool>,
    pub metadata: Option<serde_json::Value>,
}

/// 菜单更新请求
#[derive(Debug, Deserialize)]
pub struct MenuUpdateRequest {
    pub name: Option<String>,
    pub path: Option<String>,
    pub icon: Option<String>,
    pub parent_id: Option<String>,
    pub menu_type: Option<String>,
    pub description: Option<String>,
    pub component_path: Option<String>,
    pub external_link: Option<String>,
    pub required_roles: Option<Vec<String>>,
    pub access_level: Option<i32>,
    pub sort_order: Option<i32>,
    pub is_active: Option<bool>,
    pub metadata: Option<serde_json::Value>,
    pub reason: Option<String>,
}

/// 批量菜单操作请求
#[derive(Debug, Deserialize)]
pub struct BatchMenuOperationRequest {
    pub operation: String, // "create", "update", "delete", "reorder", "status_change"
    pub menu_ids: Vec<String>,
    pub operation_data: Option<serde_json::Value>,
    pub reason: Option<String>,
}

/// 菜单重排序请求
#[derive(Debug, Deserialize)]
pub struct MenuReorderRequest {
    pub menu_orders: Vec<MenuOrderItem>,
    pub reason: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct MenuOrderItem {
    pub menu_id: String,
    pub new_parent_id: Option<String>,
    pub new_sort_order: i32,
}

/// 菜单查询参数
#[derive(Debug, Deserialize)]
pub struct MenuQueryParams {
    pub menu_type: Option<String>,
    pub parent_id: Option<String>,
    pub is_active: Option<bool>,
    pub include_children: Option<bool>,
    pub include_metadata: Option<bool>,
    pub search: Option<String>,
    pub page: Option<u32>,
    pub page_size: Option<u32>,
}

/// 详细菜单响应
#[derive(Debug, Serialize)]
pub struct DetailedMenuResponse {
    pub id: Option<Uuid>,
    pub menu_id: String,
    pub name: String,
    pub path: String,
    pub icon: Option<String>,
    pub parent_id: Option<String>,
    pub menu_type: String,
    pub description: Option<String>,
    pub component_path: Option<String>,
    pub external_link: Option<String>,
    pub required_roles: Option<Vec<String>>,
    pub access_level: i32,
    pub sort_order: i32,
    pub is_active: bool,
    pub cache_enabled: bool,
    pub metadata: Option<serde_json::Value>,
    pub version: i32,
    pub last_modified_by: Option<Uuid>,
    pub last_modified_at: Option<String>,
    pub created_at: String,
    pub updated_at: String,
    pub children: Option<Vec<DetailedMenuResponse>>,
    pub children_count: i32,
    pub depth_level: i32,
    pub usage_stats: Option<MenuUsageStats>,
}

/// 菜单使用统计
#[derive(Debug, Serialize)]
pub struct MenuUsageStats {
    pub total_access_count: i64,
    pub unique_user_count: i64,
    pub denied_access_count: i64,
    pub last_accessed_at: Option<String>,
    pub avg_daily_access: f64,
}

/// 批量操作响应
#[derive(Debug, Serialize)]
pub struct BatchOperationResponse {
    pub operation: String,
    pub total_requested: i32,
    pub successful_count: i32,
    pub failed_count: i32,
    pub results: Vec<BatchOperationResult>,
    pub execution_time_ms: u64,
}

#[derive(Debug, Serialize)]
pub struct BatchOperationResult {
    pub menu_id: String,
    pub success: bool,
    pub message: Option<String>,
    pub error_code: Option<String>,
}

impl MenuController {
    /// 获取菜单树结构（管理员专用）
    pub async fn get_menu_tree(
        State(menu_service): State<Arc<MenuService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Query(params): Query<MenuQueryParams>,
    ) -> Result<Json<ApiResponse<Vec<DetailedMenuResponse>>>, Json<ApiResponse<String>>> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            error!("Non-admin user {} attempted to access menu tree", auth_context.user_id);
            return Err(Json(ApiResponse::error(
                "权限不足，需要管理员权限".to_string(),
                Some("FORBIDDEN".to_string())
            )));
        }

        let start_time = std::time::Instant::now();
        
        debug!("Getting menu tree for admin user {} with params: {:?}", auth_context.user_id, params);

        // 调用service层获取菜单树
        let menus = menu_service
            .get_menu_tree(&params)
            .await
            .map_err(|e| {
                error!("Failed to get menu tree: {}", e);
                Json(ApiResponse::error(
                    "获取菜单树失败".to_string(),
                    Some("INTERNAL_ERROR".to_string())
                ))
            })?;

        let duration = start_time.elapsed();
        info!("Retrieved {} menus for admin user {} in {}ms", 
              menus.len(), auth_context.user_id, duration.as_millis());

        Ok(Json(ApiResponse::success(menus, Some("获取菜单树成功".to_string()))))
    }

    /// 创建新菜单
    pub async fn create_menu(
        State(menu_service): State<Arc<MenuService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Json(request): Json<MenuCreateRequest>,
    ) -> Result<Json<ApiResponse<DetailedMenuResponse>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("Creating menu {} by admin user {}", request.menu_id, auth_context.user_id);

        // 调用service层创建菜单
        let menu = menu_service
            .create_menu(&request, auth_context.user_id)
            .await
            .map_err(|e| {
                error!("Failed to create menu: {}", e);
                match e.as_str() {
                    "MENU_ID_EXISTS" => StatusCode::CONFLICT,
                    "PARENT_NOT_FOUND" => StatusCode::BAD_REQUEST,
                    _ => StatusCode::INTERNAL_SERVER_ERROR,
                }
            })?;

        info!("Successfully created menu {} by admin user {}", request.menu_id, auth_context.user_id);

        Ok(Json(ApiResponse::success(menu, Some("菜单创建成功".to_string()))))
    }

    /// 更新菜单
    pub async fn update_menu(
        State(menu_service): State<Arc<MenuService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Path(menu_id): Path<String>,
        Json(request): Json<MenuUpdateRequest>,
    ) -> Result<Json<ApiResponse<DetailedMenuResponse>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("Updating menu {} by admin user {}", menu_id, auth_context.user_id);

        // 调用service层更新菜单
        let menu = menu_service
            .update_menu(&menu_id, &request, auth_context.user_id)
            .await
            .map_err(|e| {
                error!("Failed to update menu: {}", e);
                match e.as_str() {
                    "MENU_NOT_FOUND" => StatusCode::NOT_FOUND,
                    _ => StatusCode::INTERNAL_SERVER_ERROR,
                }
            })?;

        info!("Successfully updated menu {} by admin user {}", menu_id, auth_context.user_id);

        Ok(Json(ApiResponse::success(menu, Some("菜单更新成功".to_string()))))
    }

    /// 删除菜单
    pub async fn delete_menu(
        State(menu_service): State<Arc<MenuService>>,
        AuthExtractor(auth_context): AuthExtractor,
        Path(menu_id): Path<String>,
        Query(params): Query<MenuQueryParams>,
    ) -> Result<Json<ApiResponse<String>>, StatusCode> {
        // 验证管理员权限
        if !auth_context.is_super_admin() {
            return Err(StatusCode::FORBIDDEN);
        }

        debug!("Deleting menu {} by admin user {}", menu_id, auth_context.user_id);

        // 检查是否强制删除
        let force_delete = params.search.as_deref() == Some("force");

        // 调用service层删除菜单
        menu_service
            .delete_menu(&menu_id, force_delete)
            .await
            .map_err(|e| {
                error!("Failed to delete menu: {}", e);
                match e.as_str() {
                    "MENU_NOT_FOUND" => StatusCode::NOT_FOUND,
                    "HAS_CHILDREN" => StatusCode::CONFLICT,
                    _ => StatusCode::INTERNAL_SERVER_ERROR,
                }
            })?;

        info!("Successfully deleted menu {} (force: {}) by admin user {}", 
              menu_id, force_delete, auth_context.user_id);

        Ok(Json(ApiResponse::success(
            format!("菜单 {} 删除成功", menu_id),
            Some("菜单删除成功".to_string())
        )))
    }
}

/// 创建菜单管理路由
pub fn create_router() -> Router<Arc<MenuService>> {
    Router::new()
        .route("/", get(MenuController::get_menu_tree))
        .route("/", post(MenuController::create_menu))
        .route("/{menu_id}", put(MenuController::update_menu))
        .route("/{menu_id}", delete(MenuController::delete_menu))
}
