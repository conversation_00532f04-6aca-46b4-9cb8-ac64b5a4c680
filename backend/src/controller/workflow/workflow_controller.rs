use crate::model::workflow::workflow::{WorkflowQueryParams, WorkflowSettingQueryParams, WorkflowSettingSummaryQueryParams};
use crate::service::workflow::workflow_service::WorkflowService;
use crate::utils::api_response::responses;
use axum::extract::{Query, State};
use axum::response::IntoResponse;
use axum::routing::get;
use axum::Router;
use std::sync::Arc;

pub fn create_router() -> Router<Arc<WorkflowService>> {
    Router::new()
        .route("/summary", get(get_workflow_summaries))
        .route("/setting", get(get_workflow_settings))
        .route("/setting/summary", get(get_workflow_summaries_in_setting))
}

pub async fn get_workflow_summaries(
    State(service): State<Arc<WorkflowService>>,
    Query(params): Query<WorkflowQueryParams>,
) -> impl IntoResponse {
    match service.get_workflow_summaries(params).await {
        Ok(vec) => responses::success(vec, Some("获取工作流设置摘要成功")),
        Err(e) => responses::error(
            format!("获取工作流设置摘要失败：{}", e).as_str(),
            Some("QUERY_WORKFLOW_SUMMARIES_FAILED"),
        ),
    }
}

pub async fn get_workflow_settings(
    State(service): State<Arc<WorkflowService>>,
    Query(params): Query<WorkflowSettingQueryParams>,
) -> impl IntoResponse {
    service
        .get_workflow_settings(params)
        .await
        .map(|page_result| {
            responses::paginated_success(
                page_result.data,
                page_result.page as i32,
                page_result.page_size as i32,
                page_result.total,
                Some("获取工作流设置成功"),
            )
        })
        .unwrap_or_else(|e| {
            responses::paginated_error(
                format!("获取工作流设置信息失败：{}", e).as_str(),
                Some("QUERY_WORKFLOW_SETTING_FAILED"),
            )
        })
}

pub async fn get_workflow_summaries_in_setting(
    State(service): State<Arc<WorkflowService>>,
    Query(params): Query<WorkflowSettingSummaryQueryParams>,
) -> impl IntoResponse {
    match service.get_workflow_summaries_in_setting(params).await {
        Ok(vec) => responses::success(vec, Some("获取工作流设置摘要成功")),
        Err(e) => responses::error(
            format!("获取工作流设置摘要失败：{}", e).as_str(),
            Some("QUERY_WORKFLOW_SUMMARIES_FAILED"),
        ),
    }
}
