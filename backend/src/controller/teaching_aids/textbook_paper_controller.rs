use crate::model::teaching_aids::textbook_paper::{
    CreateTextbookPaperRequest, TextbookPaper, TextbookPaperQuery, UpdateTextbookPaperRequest,
};
use crate::model::PageParams;
use crate::service::teaching_aids::textbook_paper_service::TextbookPaperService;
use crate::utils::api_response::ApiResponse;
use crate::utils::error::AppError;
use axum::extract::{Path, Query, State};
use axum::routing::{delete, get, post, put};
use axum::{J<PERSON>, Router};
use std::sync::Arc;
use tracing::error;
use uuid::Uuid;

pub fn create_router() -> Router<Arc<TextbookPaperService>> {
    Router::new()
        .route("/textbook-paper/list", get(list_textbook_papers))
        .route("/textbook-paper", post(create_textbook_paper))
        .route(
            "/textbook-paper/{id}",
            get(get_textbook_paper).put(update_textbook_paper).delete(delete_textbook_paper),
        )
}

pub async fn list_textbook_papers(
    State(service): State<Arc<TextbookPaperService>>,
    Query(params): Query<TextbookPaperQuery>,
) -> Result<ApiResponse<Vec<TextbookPaper>>, AppError> {
    let page_params = PageParams {
        page: Some(params.page.unwrap_or(1)),
        page_size: Some(params.page_size.unwrap_or(20)),
    };

    match service.get_textbook_papers().await {
        Ok(papers) => Ok(ApiResponse::success(papers, None)),
        Err(err) => {
            error!("Failed to list textbook papers: {}", err);
            Err(AppError::InternalServerError(
                "Failed to list textbook papers".to_string(),
            ))
        }
    }
}

// GET /textbook-paper/:id
pub async fn get_textbook_paper(
    State(service): State<Arc<TextbookPaperService>>,
    Path(id): Path<Uuid>,
) -> Result<ApiResponse<TextbookPaper>, AppError> {
    match service.get_textbook_paper_by_id(id).await {
        Ok(paper) => Ok(ApiResponse::success(paper, None)),
        Err(_) => Err(AppError::NotFound("TextbookPaper not found".to_string())),
    }
}

// POST /textbook-paper
pub async fn create_textbook_paper(
    State(service): State<Arc<TextbookPaperService>>,
    Json(payload): Json<CreateTextbookPaperRequest>,
) -> Result<ApiResponse<TextbookPaper>, AppError> {
    match service.create_textbook_paper(payload).await {
        Ok(paper) => Ok(ApiResponse::success(
            paper,
            Some("TextbookPaper created successfully".to_string()),
        )),
        Err(err) => {
            error!("Failed to create textbook paper: {}", err);
            Err(AppError::InternalServerError(
                "Failed to create textbook paper".to_string(),
            ))
        }
    }
}

// PUT /textbook-paper/:id
pub async fn update_textbook_paper(
    State(service): State<Arc<TextbookPaperService>>,
    Path(id): Path<Uuid>,
    Json(payload): Json<UpdateTextbookPaperRequest>,
) -> Result<ApiResponse<TextbookPaper>, AppError> {
    match service.update_textbook_paper(id, payload).await {
        Ok(paper) => Ok(ApiResponse::success(
            paper,
            Some("TextbookPaper updated successfully".to_string()),
        )),
        Err(_) => Err(AppError::NotFound("TextbookPaper not found".to_string())),
    }
}

// DELETE /textbook-paper/:id
pub async fn delete_textbook_paper(
    State(service): State<Arc<TextbookPaperService>>,
    Path(id): Path<Uuid>,
) -> Result<ApiResponse<()>, AppError> {
    match service.delete_textbook_paper(id).await {
        Ok(_) => Ok(ApiResponse::success(
            (),
            Some("TextbookPaper deleted successfully".to_string()),
        )),
        Err(_) => Err(AppError::NotFound("TextbookPaper not found".to_string())),
    }
}
