use crate::model::exam::exam::{CreateExamRequest, ExamListResponse, ExamQueryParams, ExamResponse, ExamStatisticsResponse, UpdateExamRequest};
use crate::service::exam::exam_service::ExamService;
use crate::utils::api_response::ApiResponse;
use crate::web_server::AppState;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
};
use uuid::Uuid;

pub async fn create_exam_handler(
    State(state): State<AppState>,
    Json(request): Json<CreateExamRequest>,
) -> Result<Json<ApiResponse<ExamResponse>>, StatusCode> {
    let service = ExamService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context
    let creator_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.create_exam(tenant_id, creator_id, request).await {
        Ok(exam) => Ok(Json(ApiResponse::success(exam, Some("Exam created successfully".to_string())))),
        Err(e) => {
            eprintln!("Error creating exam: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), Some("CREATE_EXAM_ERROR".to_string()))))
        }
    }
}

pub async fn get_exam_handler(
    State(state): State<AppState>,
    Path(exam_id): Path<Uuid>,
) -> Result<Json<ApiResponse<ExamResponse>>, StatusCode> {
    let service = ExamService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.get_exam(tenant_id, exam_id).await {
        Ok(Some(exam)) => Ok(Json(ApiResponse::success(exam, Some("Exam retrieved successfully".to_string())))),
        Ok(None) => Ok(Json(ApiResponse::error("Exam not found".to_string(), Some("EXAM_NOT_FOUND".to_string())))),
        Err(e) => {
            eprintln!("Error getting exam: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), Some("GET_EXAM_ERROR".to_string()))))
        }
    }
}

pub async fn list_exams_handler(
    State(state): State<AppState>,
    Query(params): Query<ExamQueryParams>,
) -> Result<Json<ApiResponse<ExamListResponse>>, StatusCode> {
    let service = ExamService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.list_exams(tenant_id, params).await {
        Ok(exams) => Ok(Json(ApiResponse::success(exams, Some("Exams retrieved successfully".to_string())))),
        Err(e) => {
            eprintln!("Error listing exams: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), Some("LIST_EXAMS_ERROR".to_string()))))
        }
    }
}

pub async fn update_exam_handler(
    State(state): State<AppState>,
    Path(exam_id): Path<Uuid>,
    Json(request): Json<UpdateExamRequest>,
) -> Result<Json<ApiResponse<ExamResponse>>, StatusCode> {
    let service = ExamService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.update_exam(tenant_id, exam_id, request).await {
        Ok(Some(exam)) => Ok(Json(ApiResponse::success(exam, Some("Exam updated successfully".to_string())))),
        Ok(None) => Ok(Json(ApiResponse::error("Exam not found".to_string(), Some("EXAM_NOT_FOUND".to_string())))),
        Err(e) => {
            eprintln!("Error updating exam: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), Some("UPDATE_EXAM_ERROR".to_string()))))
        }
    }
}

pub async fn delete_exam_handler(
    State(state): State<AppState>,
    Path(exam_id): Path<Uuid>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    let service = ExamService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.delete_exam(tenant_id, exam_id).await {
        Ok(_) => Ok(Json(ApiResponse::success((), Some("Exam deleted successfully".to_string())))),
        Err(e) => {
            eprintln!("Error deleting exam: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), Some("DELETE_EXAM_ERROR".to_string()))))
        }
    }
}

pub async fn get_exam_statistics_handler(
    State(state): State<AppState>,
    Path(exam_id): Path<Uuid>,
) -> Result<Json<ApiResponse<ExamStatisticsResponse>>, StatusCode> {
    let service = ExamService::new(state.db.clone());
    let tenant_id = Uuid::new_v4(); // TODO: Extract from auth context

    match service.get_exam_statistics(tenant_id, exam_id).await {
        Ok(stats) => Ok(Json(ApiResponse::success(stats, Some("Exam statistics retrieved successfully".to_string())))),
        Err(e) => {
            eprintln!("Error getting exam statistics: {}", e);
            Ok(Json(ApiResponse::error(e.to_string(), Some("GET_EXAM_STATISTICS_ERROR".to_string()))))
        }
    }
}