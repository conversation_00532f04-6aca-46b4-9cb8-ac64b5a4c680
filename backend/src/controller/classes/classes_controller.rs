use std::sync::Arc;

use axum::{
    extract::{Path, State},
    routing::{get, post},
    Json, Router,
};

use crate::{
    model::{
        base::IdStringParams,
        classes::classes::{
            AddTeacherParams, ClassesDetail, ClassesStatistics, CreateClassesParams,
            PageAdministrativeClassesParams, PageClassesStudentParams, PageTeachingClassesParams,
            RemoveTeacherParams, UpdateClassesParams,
        },
        Student,
    },
    service::classes::classes_service::ClassesService,
    utils::api_response::{responses, ApiResponse, PaginatedApiResponse},
};

pub fn create_router() -> Router<Arc<ClassesService>> {
    Router::new()
        .route("/getStatistics", get(get_statistics))
        .route(
            "/pageAdministrativeClasses",
            post(page_administrative_classes),
        )
        .route("/pageTeachingClasses", post(page_teaching_classes))
        .route("/createClasses", post(create_classes))
        .route("/updateClasses", post(update_classes))
        .route("/deleteClasses", post(delete_classes))
        .route("/pageClassesStudent", post(page_classes_student))
        .route("/addHeadTeacher", post(add_head_teacher))
        .route("/removeHeadTeacher", post(remove_head_teacher))
}

pub async fn get_statistics(
    State(service): State<Arc<ClassesService>>,
    Path(tenant_name): Path<String>,
) -> Result<ApiResponse<ClassesStatistics>, ApiResponse<()>> {
    service.get_statistics(tenant_name).await
}

pub async fn page_administrative_classes(
    State(service): State<Arc<ClassesService>>,
    Path(tenant_name): Path<String>,
    Json(params): Json<PageAdministrativeClassesParams>,
) -> Result<ApiResponse<Vec<ClassesDetail>>, ApiResponse<()>> {
    service
        .page_administrative_classes(tenant_name, params)
        .await
}

pub async fn page_teaching_classes(
    State(service): State<Arc<ClassesService>>,
    Path(tenant_name): Path<String>,
    Json(params): Json<PageTeachingClassesParams>,
) -> Result<ApiResponse<Vec<ClassesDetail>>, ApiResponse<()>> {
    service.page_teaching_classes(tenant_name, params).await
}

pub async fn create_classes(
    State(service): State<Arc<ClassesService>>,
    Path(tenant_name): Path<String>,
    Json(params): Json<CreateClassesParams>,
) -> Result<ApiResponse<ClassesDetail>, ApiResponse<()>> {
    service.create_classes(tenant_name, params).await
}

pub async fn update_classes(
    State(service): State<Arc<ClassesService>>,
    Path(tenant_name): Path<String>,
    Json(params): Json<UpdateClassesParams>,
) -> Result<ApiResponse<ClassesDetail>, ApiResponse<()>> {
    service.update_classes(tenant_name, params).await
}

pub async fn delete_classes(
    State(service): State<Arc<ClassesService>>,
    Path(tenant_name): Path<String>,
    Json(params): Json<IdStringParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    service.delete_classes(tenant_name, params).await
}

pub async fn page_classes_student(
    State(service): State<Arc<ClassesService>>,
    Path(tenant_name): Path<String>,
    Json(params): Json<PageClassesStudentParams>,
) -> Result<PaginatedApiResponse<Student>, ApiResponse<()>> {
    service.page_classes_student(tenant_name, params).await
}

pub async fn add_head_teacher(
    State(service): State<Arc<ClassesService>>,
    Path(tenant_name): Path<String>,
    Json(params): Json<AddTeacherParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    service.add_head_teacher(tenant_name, params).await
}

pub async fn remove_head_teacher(
    State(service): State<Arc<ClassesService>>,
    Path(tenant_name): Path<String>,
    Json(params): Json<RemoveTeacherParams>,
) -> Result<ApiResponse<()>, ApiResponse<()>> {
    match service.remove_head_teacher(tenant_name, params).await {
        Ok(_) => Ok(responses::success_no_data(None)),
        Err(_) => Err(responses::error("失败！", None)),
    }
}
