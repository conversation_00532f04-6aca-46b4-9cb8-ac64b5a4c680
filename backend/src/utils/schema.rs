use anyhow::{bail, Result};
use once_cell::sync::Lazy;
use regex::Regex;
use sqlx::pool::PoolConnection;
use sqlx::{PgPool, Postgres};

/// 校验 schema 名是否合法，只允许小写字母、数字、下划线
pub fn validate_schema_name(schema: &str) -> Result<String> {
    static SCHEMA_RE: Lazy<Regex> =
        Lazy::new(|| Regex::new(r"^[a-z0-9_]+$").expect("invalid schema regex"));

    if SCHEMA_RE.is_match(schema) {
        Ok(format!("{}", schema))
    } else {
        bail!("非法 schema 名，仅允许小写字母、数字、下划线")
    }
}

/// 获取schema下的conn
pub async fn connect_with_schema(pool: &PgPool, schema: &str) -> Result<PoolConnection<Postgres>> {
    let safe_schema = validate_schema_name(schema)?; // 加双引号 + 校验
    let mut conn: PoolConnection<Postgres> = pool.acquire().await?;
    sqlx::query(&format!(r#"SET search_path TO "{}""#, safe_schema))
        .execute(&mut *conn)
        .await?;

    Ok(conn)
}
