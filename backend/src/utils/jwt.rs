use std::env;
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Algorithm, Decoding<PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Validation};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use crate::model::user::auth::{AuthError, AuthResult};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TenantContext {
    pub tenant_id: Uuid,
    pub roles: Vec<String>,
    pub permissions: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Claims {
    pub sub: String,         // 用户ID (UUID)
    pub exp: usize,          // 过期时间
    pub iat: usize,          // 签发时间
    pub username: Option<String>,    // 用户名
    // 租户上下文 (可选)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tenant: Option<TenantContext>,
}

fn get_encoding_key() -> EncodingKey {
    let secret = std::env::var("JWT_SECRET").unwrap_or_else(|_| "default_secret".to_string());
    EncodingKey::from_secret(secret.as_bytes())
}

fn get_decoding_key() -> DecodingKey {
    let secret = std::env::var("JWT_SECRET").unwrap_or_else(|_| "default_secret".to_string());
    DecodingKey::from_secret(secret.as_bytes())
}
/// 生成基础 Token (无租户上下文)
pub fn generate_base_token_with_username(
    user_id: Uuid,
    username: &str,
) -> AuthResult<String>  {
    let secret = env::var("JWT_SECRET").map_err(|_| "JWT_SECRET not set".to_string());
    let now = Utc::now().timestamp() as usize;
    let exp = (Utc::now() + Duration::days(1)).timestamp() as usize;// 12小时过期;
    let claims = Claims {
        sub: user_id.to_string(),
        exp,
        iat: now,
        username: Some(username.to_owned()),
        tenant: None,
    };
    encode(&Header::default(), &claims, &get_encoding_key()).map_err(AuthError::from)
}

pub fn generate_base_token(
    user_id: Uuid,
) -> AuthResult<String>  {
    let secret = env::var("JWT_SECRET").map_err(|_| "JWT_SECRET not set".to_string());
    let now = Utc::now().timestamp() as usize;
    let exp = (Utc::now() + Duration::days(1)).timestamp() as usize;// 12小时过期;
    let claims = Claims {
        sub: user_id.to_string(),
        exp,
        iat: now,
        username: None,
        tenant: None,
    };
    encode(&Header::default(), &claims, &get_encoding_key()).map_err(AuthError::from)
}

pub fn generate_tenant_token(user_id: Uuid, tenant_context: TenantContext) -> AuthResult<String> {
    let now = Utc::now();
    let claims = Claims {
        sub: user_id.to_string(),
        iat: now.timestamp() as usize,
        exp: (now + Duration::days(1)).timestamp() as usize,
        tenant: Some(tenant_context),
        username: None,
    };
    encode(&Header::default(), &claims, &get_encoding_key()).map_err(AuthError::from)
}

pub fn validate_token(token: &str) -> AuthResult<Claims> {
    decode::<Claims>(token, &get_decoding_key(), &Validation::new(Algorithm::HS256))
        .map(|data| data.claims)
        .map_err(AuthError::from)
}
