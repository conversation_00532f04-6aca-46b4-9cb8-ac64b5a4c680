
use std::time::Duration;
use reqwest::{Client, Response, Error as ReqwestError};
use thiserror::Error;
use once_cell::sync::Lazy;


/// 自定义错误类型（覆盖所有可能的失败场景）
#[derive(Debug, Error)]
pub enum HttpClientError {
    #[error("Request failed: {0}")]
    RequestError(#[from] ReqwestError),
    #[error("HTTP error (status: {0})")]
    HttpError(u16),
    #[error("Timeout exceeded")]
    Timeout,
    #[error("JSON parse error: {0}")]
    JsonParseError(String),
}

/// 全局复用 HTTP 客户端（连接池）
static HTTP_CLIENT: Lazy<Client> = Lazy::new(|| {
    Client::builder()
        .timeout(Duration::from_secs(10))
        .connect_timeout(Duration::from_secs(3))
        .pool_max_idle_per_host(20)
        .build()
        .expect("Failed to create HTTP client")
});

/// 通用 GET 请求（带自动重试）
pub async fn get_request(
    url: &str,
    retries: u8,
) -> Result<Response, HttpClientError> {
    let mut last_error = None;

    for attempt in 0..retries {
        match HTTP_CLIENT.get(url).send().await {
            Ok(res) if res.status().is_success() => return Ok(res),
            Ok(res) => {
                tracing::warn!(
                    "Attempt {} failed: Status {}",
                    attempt + 1,
                    res.status()
                );
                last_error = Some(HttpClientError::HttpError(res.status().as_u16()));
            }
            Err(e) if e.is_timeout() => {
                tracing::warn!("Attempt {} timeout", attempt + 1);
                last_error = Some(HttpClientError::Timeout);
            }
            Err(e) => {
                tracing::warn!("Attempt {} error: {}", attempt + 1, e);
                last_error = Some(HttpClientError::RequestError(e));
            }
        }

        // 指数退避等待
        tokio::time::sleep(Duration::from_secs(2u64.pow(attempt as u32))).await;
    }

    Err(last_error.unwrap_or(HttpClientError::HttpError(500)))
}

/// 通用 POST 请求（JSON 数据）
pub async fn post_json<T: serde::Serialize>(
    url: &str,
    data: &T,
) -> Result<Response, HttpClientError> {
    HTTP_CLIENT
        .post(url)
        .json(data)
        .send()
        .await
        .map_err(HttpClientError::RequestError)
}

/// 解析 JSON 响应体
pub async fn parse_json_response<T: serde::de::DeserializeOwned>(
    response: Response,
) -> Result<T, HttpClientError> {
    response
        .json::<T>()
        .await
        .map_err(|e| HttpClientError::JsonParseError(e.to_string()))
}