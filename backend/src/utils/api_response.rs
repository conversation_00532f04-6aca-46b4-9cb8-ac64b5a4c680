use crate::utils::error::AppError;
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};

/// 统一的API响应结构体
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ApiResponse<T> {
    /// 请求是否成功
    pub success: bool,
    /// 响应数据
    pub data: Option<T>,
    /// 响应消息
    pub message: String,
    /// 错误代码（仅在失败时存在）
    pub error_code: Option<String>,
    /// 时间戳
    pub timestamp: i64,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T, message: Option<String>) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: message.unwrap_or_else(|| "Success".to_string()),
            error_code: None,
            timestamp: chrono::Utc::now().timestamp(),
        }
    }

    /// 创建成功响应（无数据）
    pub fn success_without_data(message: Option<String>) -> ApiResponse<()> {
        ApiResponse {
            success: true,
            data: Some(()),
            message: message.unwrap_or_else(|| "Success".to_string()),
            error_code: None,
            timestamp: chrono::Utc::now().timestamp(),
        }
    }

    /// 创建错误响应
    pub fn error(message: String, error_code: Option<String>) -> Self {
        Self {
            success: false,
            data: None,
            message,
            error_code,
            timestamp: chrono::Utc::now().timestamp(),
        }
    }

    /// 从 AppError 创建错误响应
    pub fn from_error(error: &AppError) -> Self {
        let (message, error_code) = match error {
            AppError::NotFound(msg) => (msg.clone(), Some("NOT_FOUND".to_string())),
            AppError::InvalidInput(msg) => (msg.clone(), Some("INVALID_INPUT".to_string())),
            AppError::Unauthorized(msg) => (msg.clone(), Some("UNAUTHORIZED".to_string())),
            AppError::Forbidden(msg) => (msg.clone(), Some("FORBIDDEN".to_string())),
            AppError::Conflict(msg) => (msg.clone(), Some("CONFLICT".to_string())),
            AppError::DatabaseError(_) => (
                "Database error occurred".to_string(),
                Some("DATABASE_ERROR".to_string()),
            ),
            AppError::InternalServerError(msg) => {
                (msg.clone(), Some("INTERNAL_SERVER_ERROR".to_string()))
            }
            AppError::ServiceUnavailable(msg) => {
                (msg.clone(), Some("SERVICE_UNAVAILABLE".to_string()))
            }
            AppError::TokenError(msg) => (msg.clone(), Some("TOKEN_ERROR".to_string())),
            AppError::PasswordError(msg) => (msg.clone(), Some("PASSWORD_ERROR".to_string())),
            AppError::ConfigError(msg) => (msg.clone(), Some("CONFIG_ERROR".to_string())),
            AppError::SerializationError(_) => (
                "Serialization error occurred".to_string(),
                Some("SERIALIZATION_ERROR".to_string()),
            ),
            AppError::AuthError(_) => (
                "Authentication error occurred".to_string(),
                Some("AUTH_ERROR".to_string()),
            ),
            AppError::BadRequest(msg) => (msg.clone(), Some("BAD_REQUEST".to_string())),
            AppError::AnyhowError(err) => (err.to_string(), Some("INTERNAL_ERROR".to_string())),
        };

        Self::error(message, error_code)
    }
}

// Legacy compatibility types from response.rs
/// 简单成功响应结构体（保持兼容性）
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SuccessResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: String,
}

impl<T> SuccessResponse<T> {
    /// 创建成功响应
    pub fn new(data: Option<T>, message: String) -> Self {
        Self {
            success: true,
            data,
            message,
        }
    }

    /// 创建带数据的成功响应
    pub fn with_data(data: T, message: Option<String>) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: message.unwrap_or_else(|| "Success".to_string()),
        }
    }

    /// 创建无数据的成功响应
    pub fn without_data(message: Option<String>) -> Self {
        Self {
            success: true,
            data: None,
            message: message.unwrap_or_else(|| "Success".to_string()),
        }
    }
}

/// 简单错误响应结构体（保持兼容性）
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ErrorResponse {
    pub success: bool,
    pub message: String,
    pub error_code: Option<String>,
}

impl ErrorResponse {
    /// 创建错误响应
    pub fn new(message: String, error_code: Option<String>) -> Self {
        Self {
            success: false,
            message,
            error_code,
        }
    }

    /// 创建简单错误响应
    pub fn simple(message: String) -> Self {
        Self {
            success: false,
            message,
            error_code: None,
        }
    }
}

/// 简单分页响应结构体（保持兼容性）
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PagedResponse<T> {
    pub success: bool,
    pub data: Vec<T>,
    pub total: i64,
    pub page: i32,
    pub page_size: i32,
    pub total_pages: i32,
    pub message: String,
}

impl<T> PagedResponse<T> {
    /// 创建分页响应
    pub fn new(
        data: Vec<T>,
        total: i64,
        page: i32,
        page_size: i32,
        message: Option<String>,
    ) -> Self {
        let total_pages = ((total as f64) / (page_size as f64)).ceil() as i32;
        
        Self {
            success: true,
            data,
            total,
            page,
            page_size,
            total_pages,
            message: message.unwrap_or_else(|| "Success".to_string()),
        }
    }
}

/// 分页响应结构体
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PaginatedApiResponse<T> {
    /// 请求是否成功
    pub success: bool,
    /// 响应数据列表
    pub data: Vec<T>,
    /// 响应消息
    pub message: String,
    /// 分页信息
    pub pagination: PaginationInfo,
    /// 错误代码（仅在失败时存在）
    pub error_code: Option<String>,
    /// 时间戳
    pub timestamp: i64,
}

/// 分页信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PaginationInfo {
    /// 当前页码
    pub page: i32,
    /// 每页大小
    pub page_size: i32,
    /// 总记录数
    pub total: i64,
    /// 总页数
    pub total_pages: i32,
    /// 是否有下一页
    pub has_next: bool,
    /// 是否有上一页
    pub has_prev: bool,
}

impl<T> PaginatedApiResponse<T> {
    /// 创建分页成功响应
    pub fn success(
        data: Vec<T>,
        page: i32,
        page_size: i32,
        total: i64,
        message: Option<String>,
    ) -> Self {
        let total_pages = ((total as f64) / (page_size as f64)).ceil() as i32;
        let has_next = page < total_pages;
        let has_prev = page > 1;

        Self {
            success: true,
            data,
            message: message.unwrap_or_else(|| "Success".to_string()),
            pagination: PaginationInfo {
                page,
                page_size,
                total,
                total_pages,
                has_next,
                has_prev,
            },
            error_code: None,
            timestamp: chrono::Utc::now().timestamp(),
        }
    }

    /// 创建分页错误响应
    pub fn error(message: String, error_code: Option<String>) -> Self {
        Self {
            success: false,
            data: vec![],
            message,
            pagination: PaginationInfo {
                page: 0,
                page_size: 0,
                total: 0,
                total_pages: 0,
                has_next: false,
                has_prev: false,
            },
            error_code,
            timestamp: chrono::Utc::now().timestamp(),
        }
    }

    /// 从 AppError 创建分页错误响应
    pub fn from_error(error: &AppError) -> Self {
        let api_response: ApiResponse<()> = ApiResponse::from_error(error);
        Self::error(api_response.message, api_response.error_code)
    }
}

/// 为 ApiResponse 实现 IntoResponse trait，便于在 Axum 中使用
impl<T> IntoResponse for ApiResponse<T>
where
    T: Serialize,
{
    fn into_response(self) -> Response {
        let status_code = if self.success {
            StatusCode::OK
        } else {
            match self.error_code.as_deref() {
                Some("NOT_FOUND") => StatusCode::NOT_FOUND,
                Some("INVALID_INPUT") => StatusCode::BAD_REQUEST,
                Some("UNAUTHORIZED") => StatusCode::UNAUTHORIZED,
                Some("FORBIDDEN") => StatusCode::FORBIDDEN,
                Some("CONFLICT") => StatusCode::CONFLICT,
                Some("DATABASE_ERROR") | Some("INTERNAL_SERVER_ERROR") => {
                    StatusCode::INTERNAL_SERVER_ERROR
                }
                Some("SERVICE_UNAVAILABLE") => StatusCode::SERVICE_UNAVAILABLE,
                _ => StatusCode::INTERNAL_SERVER_ERROR,
            }
        };

        (status_code, Json(self)).into_response()
    }
}

/// 为 PaginatedApiResponse 实现 IntoResponse trait
impl<T> IntoResponse for PaginatedApiResponse<T>
where
    T: Serialize,
{
    fn into_response(self) -> Response {
        let status_code = if self.success {
            StatusCode::OK
        } else {
            match self.error_code.as_deref() {
                Some("NOT_FOUND") => StatusCode::NOT_FOUND,
                Some("INVALID_INPUT") => StatusCode::BAD_REQUEST,
                Some("UNAUTHORIZED") => StatusCode::UNAUTHORIZED,
                Some("FORBIDDEN") => StatusCode::FORBIDDEN,
                Some("CONFLICT") => StatusCode::CONFLICT,
                Some("DATABASE_ERROR") | Some("INTERNAL_SERVER_ERROR") => {
                    StatusCode::INTERNAL_SERVER_ERROR
                }
                Some("SERVICE_UNAVAILABLE") => StatusCode::SERVICE_UNAVAILABLE,
                _ => StatusCode::INTERNAL_SERVER_ERROR,
            }
        };

        (status_code, Json(self)).into_response()
    }
}

/// 便捷的结果类型别名
pub type ApiResult<T> = Result<ApiResponse<T>, AppError>;
pub type PaginatedApiResult<T> = Result<PaginatedApiResponse<T>, AppError>;

/// 便捷的响应创建函数
pub mod responses {
    use super::*;

    /// 创建成功响应
    pub fn success<T>(data: T, message: Option<&str>) -> ApiResponse<T> {
        ApiResponse::success(data, message.map(|s| s.to_string()))
    }

    /// 创建成功响应（无数据）
    pub fn success_no_data(message: Option<&str>) -> ApiResponse<()> {
        ApiResponse::<()>::success_without_data(message.map(|s| s.to_string()))
    }

    /// 创建错误响应
    pub fn error<T>(message: &str, error_code: Option<&str>) -> ApiResponse<T> {
        ApiResponse::error(message.to_string(), error_code.map(|s| s.to_string()))
    }

    /// 创建分页成功响应
    pub fn paginated_success<T>(
        data: Vec<T>,
        page: i32,
        page_size: i32,
        total: i64,
        message: Option<&str>,
    ) -> PaginatedApiResponse<T> {
        PaginatedApiResponse::success(data, page, page_size, total, message.map(|s| s.to_string()))
    }

    /// 创建分页错误响应
    pub fn paginated_error<T>(message: &str, error_code: Option<&str>) -> PaginatedApiResponse<T> {
        PaginatedApiResponse::error(message.to_string(), error_code.map(|s| s.to_string()))
    }
}
