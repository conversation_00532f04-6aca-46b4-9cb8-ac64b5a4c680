use std::sync::Arc;
use anyhow::Result;
use axum::{
    extract::{Request, State, Path},
    http::{StatusCode, Method},
    middleware::Next,
    response::Response,
    Extension,
};
use tracing::{debug, warn, error};
use uuid::Uuid;
use serde::{Serialize, Deserialize};

use crate::service::permission::{
    CasbinPermissionService, 
    MultiTenantCasbinService, 
    PermissionRequest
};
use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
use crate::utils::error::AppError;

/// 权限中间件配置
#[derive(Debug, Clone)]
pub struct PermissionConfig {
    pub skip_system_admin: bool,      // 是否跳过系统管理员权限检查
    pub log_permission_checks: bool,  // 是否记录权限检查日志
    pub enable_caching: bool,         // 是否启用权限检查缓存
    pub cache_ttl_seconds: u64,       // 缓存TTL
}

impl Default for PermissionConfig {
    fn default() -> Self {
        Self {
            skip_system_admin: true,
            log_permission_checks: true,
            enable_caching: true,
            cache_ttl_seconds: 300, // 5分钟
        }
    }
}

/// 权限检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionCheckResult {
    pub allowed: bool,
    pub reason: Option<String>,
    pub check_duration_ms: u64,
    pub cached: bool,
}

/// 用户上下文扩展（包含权限信息）
#[derive(Debug, Clone)]
pub struct UserContext {
    pub user_id: Uuid,
    pub tenant_id: String,
    pub user_identity: String,  // user_id:role:target_type:target_id
    pub roles: Vec<String>,
    pub is_system_admin: bool,
}

impl UserContext {
    /// 从认证上下文构建用户上下文
    pub fn from_auth_context(auth_context: &AuthContext, tenant_id: &str) -> Result<Self> {
        let tenant_uuid = Uuid::parse_str(tenant_id)?;
        
        // 获取用户在指定租户的角色
        let tenant_roles = auth_context.get_roles_for_tenant(tenant_uuid);
        let roles: Vec<String> = tenant_roles
            .iter()
            .map(|role| role.identity_type.clone())
            .collect();
        
        // 构建用户身份标识（使用第一个角色）
        let user_identity = if let Some(first_role) = tenant_roles.first() {
            format!("{}:{}:{}:{}", 
                auth_context.user_id,
                first_role.identity_type,
                "tenant", // target_type
                tenant_id  // target_id
            )
        } else {
            // 如果没有租户角色，使用基础身份
            format!("{}:user:tenant:{}", auth_context.user_id, tenant_id)
        };
        
        Ok(Self {
            user_id: auth_context.user_id,
            tenant_id: tenant_id.to_string(),
            user_identity,
            roles,
            is_system_admin: auth_context.is_super_admin(),
        })
    }
    
    /// 获取用户的身份标识字符串
    pub fn identity_string(&self) -> String {
        self.user_identity.clone()
    }
}

/// 权限中间件
/// 基于 Casbin 进行细粒度权限检查
pub async fn permission_middleware(
    State(casbin_service): State<Arc<MultiTenantCasbinService>>,
    Extension(config): Extension<PermissionConfig>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let start_time = std::time::Instant::now();
    
    // 获取认证上下文
    let auth_context = request.extensions()
        .get::<AuthContext>()
        .ok_or(StatusCode::UNAUTHORIZED)?;
    
    // 从请求中提取资源和动作
    let (resource, action) = extract_resource_and_action(&request)?;
    
    // 从请求头或路径中获取租户ID
    let tenant_id = extract_tenant_id(&request)?;
    
    // 构建用户上下文
    let user_context = UserContext::from_auth_context(auth_context, &tenant_id)
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
    
    // 如果配置允许跳过系统管理员检查，且用户是系统管理员，则直接通过
    if config.skip_system_admin && user_context.is_system_admin {
        if config.log_permission_checks {
            debug!("System admin bypassed permission check: {} -> {}", user_context.user_identity, resource);
        }
        request.extensions_mut().insert(user_context);
        return Ok(next.run(request).await);
    }
    
    // 构建权限检查请求
    let permission_request = PermissionRequest {
        subject: user_context.identity_string(),
        domain: tenant_id.clone(),
        object: resource.clone(),
        action: action.clone(),
    };
    
    // 执行权限检查
    let check_result = check_permission_with_caching(
        &casbin_service,
        &permission_request,
        &config,
        start_time,
    ).await?;
    
    // 记录权限检查日志
    if config.log_permission_checks {
        debug!(
            "Permission check: {} -> {} @ {} | {} -> {} ({}ms, cached: {})",
            permission_request.subject,
            permission_request.object,
            permission_request.domain,
            permission_request.action,
            check_result.allowed,
            check_result.check_duration_ms,
            check_result.cached
        );
    }
    
    // 如果权限检查失败，返回403
    if !check_result.allowed {
        warn!(
            "Permission denied: {} -> {} @ {} | {}",
            permission_request.subject,
            permission_request.object,
            permission_request.domain,
            permission_request.action
        );
        return Err(StatusCode::FORBIDDEN);
    }
    
    // 将用户上下文添加到请求扩展中，供后续处理器使用
    request.extensions_mut().insert(user_context);
    request.extensions_mut().insert(check_result);
    
    Ok(next.run(request).await)
}

/// 从请求中提取资源和动作
fn extract_resource_and_action(request: &Request) -> Result<(String, String), StatusCode> {
    let path = request.uri().path();
    let method = request.method();
    
    // 解析API路径，提取资源类型
    let resource = extract_resource_from_path(path);
    let action = http_method_to_action(method);
    
    Ok((resource, action))
}

/// 从路径中提取资源类型
fn extract_resource_from_path(path: &str) -> String {
    // 移除 /api/v1/ 前缀
    let clean_path = path.strip_prefix("/api/v1/").unwrap_or(path);
    
    // 提取第一个路径段作为资源类型
    let resource = clean_path.split('/').next().unwrap_or("unknown");
    
    // 处理特殊路径映射
    match resource {
        "students" => "student:*".to_string(),
        "teachers" => "teacher:*".to_string(),
        "classes" => "class:*".to_string(),
        "exams" => "exam:*".to_string(),
        "grades" => "grade:*".to_string(),
        "roles" => "role:*".to_string(),
        "permissions" => "permission:*".to_string(),
        "tenants" => "tenant:*".to_string(),
        "menus" => "menu:*".to_string(),
        "system" => "system:*".to_string(),
        "personal" => {
            // 个人中心相关资源
            if clean_path.starts_with("personal/grades") {
                "grade:self".to_string()
            } else if clean_path.starts_with("personal/profile") {
                "profile:self".to_string()
            } else {
                "menu:personal_center".to_string()
            }
        },
        _ => format!("{}:*", resource),
    }
}

/// HTTP方法转换为权限动作
fn http_method_to_action(method: &Method) -> String {
    match method {
        &Method::GET => "read".to_string(),
        &Method::POST => "create".to_string(),
        &Method::PUT | &Method::PATCH => "write".to_string(),
        &Method::DELETE => "delete".to_string(),
        _ => "read".to_string(), // 默认为读取权限
    }
}

/// 从请求中提取租户ID
fn extract_tenant_id(request: &Request) -> Result<String, StatusCode> {
    // 1. 首先尝试从请求头获取
    if let Some(tenant_header) = request.headers().get("X-Tenant-ID") {
        if let Ok(tenant_str) = tenant_header.to_str() {
            return Ok(tenant_str.to_string());
        }
    }
    
    // 2. 尝试从路径参数获取
    let path = request.uri().path();
    if let Some(tenant_from_path) = extract_tenant_from_path(path) {
        return Ok(tenant_from_path);
    }
    
    // 3. 从查询参数获取
    if let Some(query) = request.uri().query() {
        for param in query.split('&') {
            if let Some((key, value)) = param.split_once('=') {
                if key == "tenant_id" {
                    return Ok(value.to_string());
                }
            }
        }
    }
    
    Err(StatusCode::BAD_REQUEST)
}

/// 从路径中提取租户ID
fn extract_tenant_from_path(path: &str) -> Option<String> {
    // 匹配路径模式: /api/v1/tenants/{tenant_id}/...
    let parts: Vec<&str> = path.split('/').collect();
    if parts.len() >= 5 && parts[3] == "tenants" {
        return Some(parts[4].to_string());
    }
    
    None
}

/// 带缓存的权限检查
async fn check_permission_with_caching(
    casbin_service: &Arc<MultiTenantCasbinService>,
    request: &PermissionRequest,
    config: &PermissionConfig,
    start_time: std::time::Instant,
) -> Result<PermissionCheckResult, StatusCode> {
    // TODO: 实现权限检查缓存
    // 目前直接调用 casbin 服务
    let allowed = casbin_service.enforce(request)
        .await
        .map_err(|e| {
            error!("Permission check failed: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;
    
    let duration = start_time.elapsed();
    
    Ok(PermissionCheckResult {
        allowed,
        reason: if !allowed { 
            Some("Access denied by policy".to_string()) 
        } else { 
            None 
        },
        check_duration_ms: duration.as_millis() as u64,
        cached: false, // TODO: 实现缓存检测
    })
}

/// 特定资源权限检查中间件生成器
/// 用于需要检查特定资源权限的路由
pub fn require_permission(resource: String, action: String) -> impl Fn(Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>> + Clone + Send + 'static {
    move |request: Request, next: Next| {
        let resource = resource.clone();
        let action = action.clone();
        
        Box::pin(async move {
            // 获取用户上下文
            let user_context = request.extensions()
                .get::<UserContext>()
                .ok_or(StatusCode::UNAUTHORIZED)?;
            
            // 如果是系统管理员，直接通过
            if user_context.is_system_admin {
                return Ok(next.run(request).await);
            }
            
            // 获取 casbin 服务
            // 注意：这里需要从应用状态中获取服务实例
            // 实际实现时可能需要调整
            
            debug!("Checking specific permission: {} -> {}", resource, action);
            Ok(next.run(request).await)
        })
    }
}

/// 菜单权限检查中间件
/// 专门用于检查前端菜单访问权限
pub async fn menu_permission_middleware(
    State(casbin_service): State<Arc<MultiTenantCasbinService>>,
    Extension(config): Extension<PermissionConfig>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let start_time = std::time::Instant::now();

    // 获取用户上下文
    let user_context = request.extensions()
        .get::<UserContext>()
        .ok_or(StatusCode::UNAUTHORIZED)?;

    // 从路径中提取菜单ID
    let menu_id = extract_menu_id_from_path(request.uri().path())
        .ok_or(StatusCode::BAD_REQUEST)?;

    // 使用新的Casbin策略检查菜单访问权限
    let has_access = casbin_service
        .check_menu_access(
            &user_context.identity_string(),
            &user_context.tenant_id,
            &menu_id
        )
        .await
        .map_err(|e| {
            error!("Failed to check menu access: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

    if !has_access {
        warn!("Menu access denied: {} -> menu:{}",
              user_context.identity_string(), menu_id);
        return Err(StatusCode::FORBIDDEN);
    }

    // 记录权限检查日志
    if config.log_permission_checks {
        let duration = start_time.elapsed();
        debug!("Menu access granted: {} -> menu:{} ({}ms)",
               user_context.identity_string(), menu_id, duration.as_millis());
    }

    Ok(next.run(request).await)
}

/// 从路径中提取菜单ID
fn extract_menu_id_from_path(path: &str) -> Option<String> {
    // 匹配路径模式: /api/v1/menus/{menu_id} 或 /menus/{menu_id}
    let parts: Vec<&str> = path.split('/').collect();
    
    for i in 0..parts.len() {
        if parts[i] == "menus" && i + 1 < parts.len() {
            return Some(parts[i + 1].to_string());
        }
    }
    
    None
}

/// 数据级权限中间件
/// 用于控制数据访问范围
pub async fn data_permission_middleware(
    State(casbin_service): State<Arc<MultiTenantCasbinService>>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // 获取用户上下文
    let user_context = request.extensions()
        .get::<UserContext>()
        .ok_or(StatusCode::UNAUTHORIZED)?;
    
    // 获取用户的数据访问范围
    let resource = extract_resource_from_path(request.uri().path());
    let data_scopes = casbin_service
        .get_user_data_scopes(
            &user_context.identity_string(),
            &user_context.tenant_id,
            &resource.split(':').next().unwrap_or("unknown")
        )
        .await
        .map_err(|e| {
            error!("Failed to get user data scopes: {}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;
    
    // 将数据范围添加到请求扩展中，供业务逻辑使用
    request.extensions_mut().insert(data_scopes);
    
    Ok(next.run(request).await)
}

/// 用户上下文提取器（用于 Axum 处理器）
pub struct UserContextExtractor(pub UserContext);

impl<S> axum::extract::FromRequestParts<S> for UserContextExtractor
where
    S: Send + Sync,
{
    type Rejection = (StatusCode, &'static str);

    async fn from_request_parts(
        parts: &mut axum::http::request::Parts,
        _state: &S,
    ) -> Result<Self, Self::Rejection> {
        parts
            .extensions
            .get::<UserContext>()
            .cloned()
            .map(UserContextExtractor)
            .ok_or((StatusCode::UNAUTHORIZED, "User context not found"))
    }
}

/// 权限检查结果提取器
pub struct PermissionResultExtractor(pub PermissionCheckResult);

impl<S> axum::extract::FromRequestParts<S> for PermissionResultExtractor
where
    S: Send + Sync,
{
    type Rejection = (StatusCode, &'static str);

    async fn from_request_parts(
        parts: &mut axum::http::request::Parts,
        _state: &S,
    ) -> Result<Self, Self::Rejection> {
        parts
            .extensions
            .get::<PermissionCheckResult>()
            .cloned()
            .map(PermissionResultExtractor)
            .ok_or((StatusCode::INTERNAL_SERVER_ERROR, "Permission result not found"))
    }
}