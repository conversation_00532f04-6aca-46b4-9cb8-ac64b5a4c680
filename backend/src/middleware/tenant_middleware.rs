use crate::service::tenant::TenantService;
use crate::utils::error::AppError;
use axum::{
    extract::{Request, State},
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::Response,
};
use std::sync::Arc;
use uuid::Uuid;

/// 租户上下文
#[derive(Debug, Clone)]
pub struct TenantContext {
    pub tenant_id: Uuid,
    pub schema_name: String,
    pub tenant_name: String,
}

/// 租户识别中间件
pub async fn tenant_identification_middleware(
    State(tenant_service): State<Arc<TenantService>>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // 从请求头中获取租户信息
    let tenant_context = match extract_tenant_context(&request, &tenant_service).await {
        Ok(context) => context,
        Err(_) => {
            return Err(StatusCode::BAD_REQUEST);
        }
    };

    // 将租户上下文添加到请求扩展中
    request.extensions_mut().insert(tenant_context);

    Ok(next.run(request).await)
}

/// 从请求中提取租户上下文
async fn extract_tenant_context(
    request: &Request,
    tenant_service: &TenantService,
) -> Result<TenantContext, AppError> {
    let headers = request.headers();

    // 方法1: 从 X-Tenant-ID 头部获取租户 ID
    if let Some(tenant_id_header) = headers.get("X-Tenant-ID") {
        if let Ok(tenant_id_str) = tenant_id_header.to_str() {
            if let Ok(tenant_id) = Uuid::parse_str(tenant_id_str) {
                let tenant = tenant_service.get_tenant_by_id(tenant_id).await?;
                return Ok(TenantContext {
                    tenant_id: tenant.id,
                    schema_name: tenant.schema_name,
                    tenant_name: tenant.name,
                });
            }
        }
    }

    // 方法2: 从 X-Tenant-Schema 头部获取 schema 名称
    if let Some(schema_header) = headers.get("X-Tenant-Schema") {
        if let Ok(schema_name) = schema_header.to_str() {
            let tenant = tenant_service.get_tenant_by_schema(schema_name).await?;
            return Ok(TenantContext {
                tenant_id: tenant.id,
                schema_name: tenant.schema_name,
                tenant_name: tenant.name,
            });
        }
    }

    // 方法3: 从 Host 头部获取域名
    if let Some(host_header) = headers.get("Host") {
        if let Ok(host) = host_header.to_str() {
            // 这里可以实现基于域名的租户识别逻辑
            // 例如: tenant1.example.com -> tenant1
            if let Some(subdomain) = extract_subdomain(host) {
                // 尝试根据子域名查找租户
                if let Ok(tenant) = tenant_service.get_tenant_by_schema(&format!("tenant_{}", subdomain)).await {
                    return Ok(TenantContext {
                        tenant_id: tenant.id,
                        schema_name: tenant.schema_name,
                        tenant_name: tenant.name,
                    });
                }
            }
        }
    }

    // 方法4: 从 URL 路径中提取租户信息
    let path = request.uri().path();
    if let Some(tenant_from_path) = extract_tenant_from_path(path) {
        let tenant = tenant_service.get_tenant_by_schema(&tenant_from_path).await?;
        return Ok(TenantContext {
            tenant_id: tenant.id,
            schema_name: tenant.schema_name,
            tenant_name: tenant.name,
        });
    }

    Err(AppError::InvalidInput("No valid tenant identifier found".to_string()))
}

/// 从主机名中提取子域名
fn extract_subdomain(host: &str) -> Option<String> {
    let parts: Vec<&str> = host.split('.').collect();
    if parts.len() >= 3 {
        // 假设格式为 subdomain.domain.com
        Some(parts[0].to_string())
    } else {
        None
    }
}

#[allow(unused)]
/// 从 URL 路径中提取租户信息
fn extract_tenant_from_path(path: &str) -> Option<String> {
    // 假设 URL 格式为 /api/tenant/{tenant_schema}/...
    let parts: Vec<&str> = path.split('/').collect();
    if parts.len() >= 4 && parts[1] == "api" && parts[2] == "tenant" {
        Some(parts[3].to_string())
    } else {
        None
    }
}

#[allow(unused)]
/// 获取当前请求的租户上下文
pub fn get_tenant_context(request: &Request) -> Result<&TenantContext, AppError> {
    request
        .extensions()
        .get::<TenantContext>()
        .ok_or_else(|| AppError::InternalServerError("Tenant context not found".to_string()))
}

/// 租户上下文提取器（用于 Axum 处理器）
pub struct TenantExtractor(pub TenantContext);

impl<S> axum::extract::FromRequestParts<S> for TenantExtractor
where
    S: Send + Sync,
{
    type Rejection = (StatusCode, &'static str);

    async fn from_request_parts(
        parts: &mut axum::http::request::Parts,
        _state: &S,
    ) -> Result<Self, Self::Rejection> {
        parts
            .extensions
            .get::<TenantContext>()
            .cloned()
            .map(TenantExtractor)
            .ok_or((StatusCode::BAD_REQUEST, "Tenant context not found"))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_subdomain() {
        assert_eq!(extract_subdomain("tenant1.example.com"), Some("tenant1".to_string()));
        assert_eq!(extract_subdomain("api.tenant1.example.com"), Some("api".to_string()));
        assert_eq!(extract_subdomain("localhost"), None);
        assert_eq!(extract_subdomain("example.com"), None);
    }

    #[test]
    fn test_extract_tenant_from_path() {
        assert_eq!(
            extract_tenant_from_path("/api/tenant/tenant_001/exams"),
            Some("tenant_001".to_string())
        );
        assert_eq!(extract_tenant_from_path("/api/public/auth"), None);
        assert_eq!(extract_tenant_from_path("/health"), None);
    }
}
