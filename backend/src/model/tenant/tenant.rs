use chrono::{DateTime, NaiveDateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Tenant {
    pub id: Uuid,
    pub name: String,
    #[serde(rename = "tenantType")]
    pub tenant_type: String,
    #[serde(rename = "schemaName")]
    pub schema_name: String,
    pub domain: Option<String>,
    pub status: Option<String>, // 'active', 'inactive', 'suspended'
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "createdBy")]
    pub created_by: Option<Uuid>,
    #[serde(rename = "createdAt")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTenantRequest {
    pub name: String,
    #[serde(rename = "schemaName")]
    pub schema_name: String,
    #[serde(rename = "tenantType")]
    pub tenant_type: Option<String>,
    pub domain: Option<String>,
    pub settings: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTenantRequest {
    pub name: Option<String>,
    pub domain: Option<String>,
    pub status: Option<String>,
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "tenantType")]
    pub tenant_type: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TenantResponse {
    pub id: Uuid,
    pub name: String,
    #[serde(rename = "tenantType")]
    pub tenant_type: String,
    #[serde(rename = "schemaName")]
    pub schema_name: String,
    pub domain: Option<String>,
    pub status: Option<String>,
    pub settings: Option<serde_json::Value>,
    #[serde(rename = "createdAt")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(rename = "updatedAt")]
    pub updated_at: Option<DateTime<Utc>>,
}

impl From<Tenant> for TenantResponse {
    fn from(tenant: Tenant) -> Self {
        Self {
            id: tenant.id,
            name: tenant.name,
            tenant_type: tenant.tenant_type,
            schema_name: tenant.schema_name,
            domain: tenant.domain,
            status: tenant.status,
            settings: tenant.settings,
            created_at: tenant.created_at,
            updated_at: tenant.updated_at,
        }
    }
}