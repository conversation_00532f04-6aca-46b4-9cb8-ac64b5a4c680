use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct Paper {
    pub id: Uuid,
    pub paper_name: String,
    pub paper_content: Option<Value>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct PaperQuery {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub paper_name: Option<String>,
}

// 新增：用于创建 Paper 的请求体
#[derive(Debug, Deserialize)]
pub struct CreatePaperRequest {
    pub paper_name: String,
    pub paper_content: Option<Value>,
}

// 新增：用于更新 Paper 的请求体
#[derive(Debug, Deserialize)]
pub struct UpdatePaperRequest {
    pub paper_name: Option<String>,
    pub paper_content: Option<Value>,
}
