use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::model::PageParams;

/// 学科数据库模型
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct Subject {
    pub id: Uuid,
    pub code: String,
    pub name: String,
    pub description: Option<String>,
    pub order_level: i32,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 学科创建请求
#[derive(Debug, Deserialize)]
pub struct CreateSubjectRequest {
    pub code: String,
    pub name: String,
    pub description: Option<String>,
    pub order_level: i32,
}

/// 学科更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateSubjectRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub order_level: Option<i32>,
    pub is_active: Option<bool>,
}

/// 学科查询参数
#[derive(Debug, Deserialize,<PERSON><PERSON>)]
pub struct SubjectQueryParams {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub search: Option<String>,
    pub is_active: Option<bool>,
    pub order_by: Option<String>, // 排序字段：name, code, order_level, created_at
    pub order_direction: Option<String>, // 排序方向：asc, desc
}

/// 学科统计信息
#[derive(Debug, Serialize)]
pub struct SubjectStatistics {
    pub total_subjects: i64,
    pub active_subjects: i64,
    pub inactive_subjects: i64,
    pub usage_stats: Vec<SubjectUsageStats>,
}

/// 学科使用统计
#[derive(Debug, Serialize)]
pub struct SubjectUsageStats {
    pub subject_id: Uuid,
    pub subject_name: String,
    pub question_count: i64,
    pub paper_count: i64,
    pub exam_count: Option<i64>,
}

/// 学科响应VO（视图对象）
#[derive(Debug, Serialize, FromRow)]
pub struct SubjectVO {
    pub id: Uuid,
    pub code: String,
    pub name: String,
    pub description: Option<String>,
    pub order_level: i32,
    pub is_active: Option<bool>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// 学科简要信息（用于下拉选择等场景）
#[derive(Debug, FromRow, Serialize)]
pub struct SubjectSummary {
    pub id: Uuid,
    pub code: String,
    pub name: String,
    pub is_active: bool,
}

impl Subject {
    /// 获取学科完整显示名称
    pub fn full_display_name(&self) -> String {
        format!("{} ({})", self.name, self.code)
    }

    /// 检查学科是否激活
    pub fn is_available(&self) -> bool {
        self.is_active
    }
}

impl Default for SubjectQueryParams {
    fn default() -> Self {
        Self {
            page: Some(1),
            page_size: Some(10),
            search: None,
            is_active: Some(true),
            order_by: Some("order_level".to_string()),
            order_direction: Some("asc".to_string()),
        }
    }
}

/// 预定义的系统学科常量
pub struct SystemSubjects;

impl SystemSubjects {
    pub const MATH: &'static str = "MATH";
    pub const CHINESE: &'static str = "CHINESE";
    pub const ENGLISH: &'static str = "ENGLISH";
    pub const PHYSICS: &'static str = "PHYSICS";
    pub const CHEMISTRY: &'static str = "CHEMISTRY";
    pub const BIOLOGY: &'static str = "BIOLOGY";
    pub const HISTORY: &'static str = "HISTORY";
    pub const GEOGRAPHY: &'static str = "GEOGRAPHY";
    pub const POLITICS: &'static str = "POLITICS";
}