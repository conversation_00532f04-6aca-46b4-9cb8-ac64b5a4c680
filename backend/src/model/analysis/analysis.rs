use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AcademicStatistics {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub student_id: Uuid,
    pub subject: String,
    pub total_score: f32,
    pub class_rank: i32,
    pub grade_rank: i32,
    pub school_rank: i32,
    pub is_absent: bool,
    pub absent_reason: Option<String>,
    pub performance_trend: serde_json::Value,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct QuestionAnalysis {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub question_id: Uuid,
    pub question_type: String,
    pub total_students: i32,
    pub correct_count: i32,
    pub score_rate: f32,
    pub average_score: f32,
    pub score_distribution: serde_json::Value,
    pub option_distribution: serde_json::Value,
    pub zero_score_count: i32,
    pub full_score_count: i32,
    pub difficulty_coefficient: f32,
    pub discrimination_index: f32,
    pub knowledge_points_mastery: serde_json::Value,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct QuestionScore {
    pub id: Uuid,
    pub exam_id: Uuid,
    pub student_id: Uuid,
    pub question_id: Uuid,
    pub question_type: String,
    pub max_score: f32,
    pub actual_score: f32,
    pub score_percentage: f32,
    pub answer_content: Option<String>,
    pub is_correct: Option<bool>,
    pub difficulty_level: i32,
    pub knowledge_points: serde_json::Value,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct StudentProfileLevel {
    pub id: Uuid,
    pub student_id: Uuid,
    pub subject: String,
    pub level: String, // 'A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D'
    pub level_description: Option<String>,
    pub assessment_date: DateTime<Utc>,
    pub assessed_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct StudentProfileTag {
    pub id: Uuid,
    pub student_id: Uuid,
    pub tag_name: String,
    pub tag_value: Option<String>,
    pub tag_category: String, // 'academic', 'behavior', 'interest', 'ability', 'other'
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct LearningRecord {
    pub id: Uuid,
    pub student_id: Uuid,
    pub question_id: Uuid,
    pub grading_record_id: Uuid,
    pub exam_id: Uuid,
    pub subject: String,
    pub knowledge_points: serde_json::Value,
    pub difficulty_level: i32,
    pub student_score: f32,
    pub max_score: f32,
    pub score_rate: f32,
    pub mastery_level: String, // 'excellent', 'good', 'fair', 'poor', 'not_mastered'
    pub learning_suggestions: serde_json::Value,
    pub recommended_exercises: serde_json::Value,
    pub improvement_areas: serde_json::Value,
    pub historical_comparison: serde_json::Value,
    pub generated_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct LearningRecordVersion {
    pub id: Uuid,
    pub learning_record_id: Uuid,
    pub version_number: i32,
    pub change_reason: Option<String>,
    pub changed_fields: serde_json::Value,
    pub previous_data: serde_json::Value,
    pub changed_by: Uuid,
    pub created_at: DateTime<Utc>,
}

// Request/Response DTOs
#[derive(Debug, Deserialize)]
pub struct CreateStudentProfileLevelRequest {
    pub student_id: Uuid,
    pub subject: String,
    pub level: String,
    pub level_description: Option<String>,
    pub assessment_date: DateTime<Utc>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateStudentProfileLevelRequest {
    pub level: Option<String>,
    pub level_description: Option<String>,
    pub assessment_date: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct CreateStudentProfileTagRequest {
    pub student_id: Uuid,
    pub tag_name: String,
    pub tag_value: Option<String>,
    pub tag_category: String,
}

#[derive(Debug, Deserialize)]
pub struct UpdateStudentProfileTagRequest {
    pub tag_value: Option<String>,
    pub tag_category: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct GenerateLearningRecordRequest {
    pub exam_id: Uuid,
    pub student_ids: Option<Vec<Uuid>>,
    pub subjects: Option<Vec<String>>,
    pub force_regenerate: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct AnalysisQueryParams {
    pub exam_id: Option<Uuid>,
    pub student_id: Option<Uuid>,
    pub subject: Option<String>,
    pub class_id: Option<Uuid>,
    pub grade_level: Option<String>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub page: Option<i32>,
    pub page_size: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct ComparisonAnalysisRequest {
    pub base_exam_id: Uuid,
    pub compare_exam_ids: Vec<Uuid>,
    pub analysis_type: String, // 'student', 'class', 'grade', 'subject'
    pub target_ids: Vec<Uuid>,
}

#[derive(Debug, Serialize)]
pub struct StudentAnalysisResponse {
    pub student_id: Uuid,
    pub student_name: String,
    pub exam_id: Uuid,
    pub exam_name: String,
    pub overall_performance: OverallPerformance,
    pub subject_performance: Vec<SubjectPerformance>,
    pub ranking_info: RankingInfo,
    pub performance_trend: PerformanceTrend,
    pub strength_weakness: StrengthWeakness,
    pub learning_suggestions: Vec<String>,
    pub profile_levels: Vec<StudentProfileLevel>,
    pub profile_tags: Vec<StudentProfileTag>,
    pub learning_records: Vec<LearningRecord>,
}

#[derive(Debug, Serialize)]
pub struct OverallPerformance {
    pub total_score: f32,
    pub full_score: f32,
    pub score_rate: f32,
    pub grade_level: String,
    pub improvement_from_last: Option<f32>,
    pub stability_index: f32,
}

#[derive(Debug, Serialize)]
pub struct SubjectPerformance {
    pub subject: String,
    pub score: f32,
    pub full_score: f32,
    pub score_rate: f32,
    pub class_rank: i32,
    pub grade_rank: i32,
    pub class_average: f32,
    pub grade_average: f32,
    pub difficulty_level: String,
    pub mastery_level: String,
    pub knowledge_points: Vec<KnowledgePointMastery>,
}

#[derive(Debug, Serialize)]
pub struct KnowledgePointMastery {
    pub knowledge_point: String,
    pub mastery_level: String,
    pub score_rate: f32,
    pub questions_count: i32,
    pub correct_count: i32,
    pub suggestions: Vec<String>,
}

#[derive(Debug, Serialize)]
pub struct RankingInfo {
    pub class_rank: i32,
    pub class_total: i32,
    pub grade_rank: i32,
    pub grade_total: i32,
    pub school_rank: i32,
    pub school_total: i32,
    pub percentile: f32,
}

#[derive(Debug, Serialize)]
pub struct PerformanceTrend {
    pub recent_exams: Vec<ExamPerformance>,
    pub trend_direction: String, // 'improving', 'stable', 'declining'
    pub trend_strength: f32,
    pub volatility: f32,
}

#[derive(Debug, Serialize)]
pub struct ExamPerformance {
    pub exam_id: Uuid,
    pub exam_name: String,
    pub exam_date: DateTime<Utc>,
    pub total_score: f32,
    pub score_rate: f32,
    pub rank: i32,
    pub rank_change: Option<i32>,
}

#[derive(Debug, Serialize)]
pub struct StrengthWeakness {
    pub strengths: Vec<StrengthWeaknessItem>,
    pub weaknesses: Vec<StrengthWeaknessItem>,
    pub potential_areas: Vec<StrengthWeaknessItem>,
}

#[derive(Debug, Serialize)]
pub struct StrengthWeaknessItem {
    pub area: String,
    pub description: String,
    pub score_rate: f32,
    pub evidence: Vec<String>,
    pub suggestions: Vec<String>,
}

#[derive(Debug, Serialize)]
pub struct ClassAnalysisResponse {
    pub class_id: Uuid,
    pub class_name: String,
    pub exam_id: Uuid,
    pub exam_name: String,
    pub overall_statistics: ClassOverallStatistics,
    pub subject_statistics: Vec<ClassSubjectStatistics>,
    pub student_distribution: StudentDistribution,
    pub ranking_analysis: ClassRankingAnalysis,
    pub improvement_analysis: ClassImprovementAnalysis,
    pub top_students: Vec<StudentSummary>,
    pub attention_students: Vec<StudentSummary>,
}

#[derive(Debug, Serialize)]
pub struct ClassOverallStatistics {
    pub total_students: i32,
    pub attended_students: i32,
    pub absent_students: i32,
    pub average_score: f32,
    pub median_score: f32,
    pub highest_score: f32,
    pub lowest_score: f32,
    pub standard_deviation: f32,
    pub pass_rate: f32,
    pub excellent_rate: f32,
}

#[derive(Debug, Serialize)]
pub struct ClassSubjectStatistics {
    pub subject: String,
    pub average_score: f32,
    pub median_score: f32,
    pub highest_score: f32,
    pub lowest_score: f32,
    pub standard_deviation: f32,
    pub pass_rate: f32,
    pub excellent_rate: f32,
    pub grade_ranking: i32,
    pub score_distribution: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct StudentDistribution {
    pub excellent: i32,
    pub good: i32,
    pub average: i32,
    pub below_average: i32,
    pub poor: i32,
    pub score_ranges: Vec<ScoreRange>,
}

#[derive(Debug, Serialize)]
pub struct ScoreRange {
    pub min_score: f32,
    pub max_score: f32,
    pub count: i32,
    pub percentage: f32,
}

#[derive(Debug, Serialize)]
pub struct ClassRankingAnalysis {
    pub grade_rank: i32,
    pub grade_total: i32,
    pub rank_change: Option<i32>,
    pub better_than_average: bool,
    pub competition_analysis: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct ClassImprovementAnalysis {
    pub improved_students: i32,
    pub stable_students: i32,
    pub declined_students: i32,
    pub average_improvement: f32,
    pub improvement_distribution: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct StudentSummary {
    pub student_id: Uuid,
    pub student_name: String,
    pub total_score: f32,
    pub rank: i32,
    pub rank_change: Option<i32>,
    pub strengths: Vec<String>,
    pub weaknesses: Vec<String>,
    pub attention_reason: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct QuestionAnalysisResponse {
    pub question_id: Uuid,
    pub question_type: String,
    pub exam_id: Uuid,
    pub exam_name: String,
    pub overall_statistics: QuestionOverallStatistics,
    pub score_distribution: serde_json::Value,
    pub option_analysis: Option<serde_json::Value>,
    pub knowledge_point_analysis: Vec<KnowledgePointAnalysis>,
    pub class_performance: Vec<ClassQuestionPerformance>,
    pub difficulty_analysis: DifficultyAnalysis,
    pub discrimination_analysis: DiscriminationAnalysis,
    pub suggestions: Vec<String>,
}

#[derive(Debug, Serialize)]
pub struct QuestionOverallStatistics {
    pub total_students: i32,
    pub attempted_students: i32,
    pub correct_count: i32,
    pub zero_score_count: i32,
    pub full_score_count: i32,
    pub average_score: f32,
    pub score_rate: f32,
    pub standard_deviation: f32,
}

#[derive(Debug, Serialize)]
pub struct KnowledgePointAnalysis {
    pub knowledge_point: String,
    pub mastery_rate: f32,
    pub common_errors: Vec<String>,
    pub suggestions: Vec<String>,
}

#[derive(Debug, Serialize)]
pub struct ClassQuestionPerformance {
    pub class_id: Uuid,
    pub class_name: String,
    pub average_score: f32,
    pub score_rate: f32,
    pub correct_count: i32,
    pub total_count: i32,
    pub rank: i32,
}

#[derive(Debug, Serialize)]
pub struct DifficultyAnalysis {
    pub difficulty_coefficient: f32,
    pub difficulty_level: String,
    pub appropriateness: String,
    pub suggestions: Vec<String>,
}

#[derive(Debug, Serialize)]
pub struct DiscriminationAnalysis {
    pub discrimination_index: f32,
    pub discrimination_level: String,
    pub high_performers_rate: f32,
    pub low_performers_rate: f32,
    pub validity: String,
}

#[derive(Debug, Serialize)]
pub struct ComparisonAnalysisResponse {
    pub base_exam: ExamBasicInfo,
    pub compare_exams: Vec<ExamBasicInfo>,
    pub analysis_type: String,
    pub comparison_results: Vec<ComparisonResult>,
    pub trend_analysis: TrendAnalysis,
    pub insights: Vec<String>,
}

#[derive(Debug, Serialize)]
pub struct ExamBasicInfo {
    pub exam_id: Uuid,
    pub exam_name: String,
    pub exam_date: DateTime<Utc>,
    pub total_students: i32,
    pub average_score: f32,
}

#[derive(Debug, Serialize)]
pub struct ComparisonResult {
    pub target_id: Uuid,
    pub target_name: String,
    pub base_performance: PerformanceMetrics,
    pub compare_performances: Vec<PerformanceMetrics>,
    pub changes: Vec<PerformanceChange>,
}

#[derive(Debug, Serialize)]
pub struct PerformanceMetrics {
    pub exam_id: Uuid,
    pub score: f32,
    pub rank: i32,
    pub percentile: f32,
    pub grade_level: String,
}

#[derive(Debug, Serialize)]
pub struct PerformanceChange {
    pub exam_id: Uuid,
    pub score_change: f32,
    pub rank_change: i32,
    pub percentile_change: f32,
    pub change_type: String, // 'improved', 'stable', 'declined'
}

#[derive(Debug, Serialize)]
pub struct TrendAnalysis {
    pub overall_trend: String,
    pub trend_strength: f32,
    pub consistency: f32,
    pub volatility: f32,
    pub predictions: Vec<String>,
}