use chrono::{DateTime, NaiveDate, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

use crate::model::PageParams;

#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize, FromRow)]
pub struct Student {
    pub id: Uuid,
    pub student_number: String,
    pub student_name: String,
    pub gender: Option<String>,
    pub birth_date: Option<DateTime<Utc>>,
    pub id_number: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub guardian_name: Option<String>,
    pub guardian_phone: Option<String>,
    pub guardian_relation: Option<String>,
    pub administrative_class_id: Option<Uuid>,
    pub user_id: Option<Uuid>,
    pub enrollment_date: Option<NaiveDate>,
    pub status: String,
    pub profile_level: Option<String>,
    pub profile_tags: Option<serde_json::Value>,
    pub notes: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, FromRow)]
pub struct StudentBaseInfo {
    pub id: Uuid,
    pub student_number: String,
    pub name: String,
    pub gender: Option<String>,
    pub phone: Option<String>,
    pub administrative_class_id: Option<Uuid>,
    pub user_id: Option<Uuid>,
    pub status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct CreateStudentParams {
    pub student_number: String,
    pub name: String,
    pub gender: Option<String>,
    pub birth_date: Option<DateTime<Utc>>,
    pub id_number: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub guardian_name: Option<String>,
    pub guardian_phone: Option<String>,
    pub guardian_relation: Option<String>,
    pub administrative_class_id: Option<Uuid>,
    pub user_id: Option<Uuid>,
    pub enrollment_date: Option<NaiveDate>,
    pub status: Option<String>,
    pub profile_level: Option<String>,
    pub profile_tags: Option<serde_json::Value>,
    pub notes: Option<String>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct UpdateStudentParams {
    pub id: Uuid,
    pub student_number: String,
    pub name: String,
    pub gender: Option<String>,
    pub birth_date: Option<DateTime<Utc>>,
    pub id_number: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub guardian_name: Option<String>,
    pub guardian_phone: Option<String>,
    pub guardian_relation: Option<String>,
    pub administrative_class_id: Option<Uuid>,
    pub user_id: Option<Uuid>,
    pub enrollment_date: Option<NaiveDate>,
    pub status: Option<String>,
    pub profile_level: Option<String>,
    pub profile_tags: Option<serde_json::Value>,
    pub notes: Option<String>,
}

// Student teaching class relationship
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, FromRow)]
pub struct StudentTeachingClass {
    pub id: Uuid,
    pub student_id: Uuid,
    pub class_id: Uuid,
    pub subject: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct CreateStudentTeachingClass {
    pub student_id: Uuid,
    pub class_id: Uuid,
    pub subject: String,
}

// Student profile level
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, FromRow)]
pub struct StudentProfileLevel {
    pub id: Uuid,
    pub student_id: Uuid,
    pub subject: String,
    pub level: String,
    pub level_description: Option<String>,
    pub assessment_date: DateTime<Utc>,
    pub assessed_by: Option<Uuid>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct CreateStudentProfileLevel {
    pub student_id: Uuid,
    pub subject: String,
    pub level: String,
    pub level_description: Option<String>,
    pub assessment_date: DateTime<Utc>,
    pub assessed_by: Option<Uuid>,
}

// Student profile tag
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, FromRow)]
pub struct StudentProfileTag {
    pub id: Uuid,
    pub student_id: Uuid,
    pub tag_name: String,
    pub tag_value: Option<String>,
    pub tag_category: String,
    pub created_by: Option<Uuid>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct CreateStudentProfileTag {
    pub student_id: Uuid,
    pub tag_name: String,
    pub tag_value: Option<String>,
    pub tag_category: String,
    pub created_by: Option<Uuid>,
}

// Query parameters for student search
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StudentSearchParams {
    pub name: Option<String>,
    pub student_number: Option<String>,
    pub class_id: Option<Uuid>,
    pub grade_level_id: Option<Uuid>,
    pub status: Option<String>,
    pub profile_level: Option<String>,
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

// Student detail with related information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StudentDetail {
    #[serde(flatten)]
    pub student: Student,
    pub class_name: Option<String>,
    pub grade_level_name: Option<String>,
    pub teaching_classes: Vec<StudentTeachingClass>,
    pub profile_levels: Vec<StudentProfileLevel>,
    pub profile_tags: Vec<StudentProfileTag>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FindAllStudentParams {
    pub name_like: Option<String>,
    pub student_number: Option<String>,
    pub phone: Option<String>,
    pub page_params: PageParams,
}
