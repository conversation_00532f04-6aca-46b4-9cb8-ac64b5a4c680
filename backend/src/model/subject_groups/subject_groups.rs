use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::prelude::FromRow;
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct SubjectGroups {
    pub id: Uuid,
    pub group_name: String,
    pub subject_code: String,
    pub description: Option<String>,
    pub leader_user_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CreateSubjectGroupsParams {
    pub group_name: String,
    pub subject_code: String,
    pub description: Option<String>,
    pub leader_user_id: Option<Uuid>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UpdateSubjectGroupsParams {
    pub id: Uuid,
    pub group_name: Option<String>,
    pub subject_code: Option<String>,
    pub description: Option<String>,
    pub leader_user_id: Option<Uuid>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct SubjectGroupsDetail {
    pub id: Uuid,
    pub group_name: String,
    pub subject_code: String,
    pub description: Option<String>,
    pub leader_user_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
    //额外字段
    pub is_active: bool,
    pub teacher_name: Option<String>,
    pub subject_name: Option<String>,
}
