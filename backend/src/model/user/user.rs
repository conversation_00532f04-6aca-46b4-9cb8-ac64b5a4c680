use serde::Serialize;
use sqlx::postgres::PgRow;
use sqlx::{FromRow, Row};
use uuid::Uuid;

pub struct UserRecord {
   pub id: Uuid,
   pub username: String,
   pub password: String,
}

// 实现从行数据到结构体的转换
impl sqlx::FromRow<'_, PgRow> for UserRecord {
    fn from_row(row: &PgRow) -> sqlx::Result<Self> {
        Ok(Self {
            id: row.get("id"),
            username: row.get("username"),
            password: row.get("password_hash"),
        })
    }
}

#[derive(Debug, Clone, FromRow, Serialize)]
pub struct UserIdentitySelectVO{
    #[serde(rename = "userIdentityId")]
    pub user_identity_id: Uuid,
    #[serde(rename = "tenantId")]
    pub tenant_id: Uuid,
    #[serde(rename = "schemaName")]
    pub schema_name: String,
    #[serde(rename = "roleId")]
    pub role_id: Uuid,
    #[serde(rename = "roleName")]
    pub role_name: String,
    #[serde(rename = "targetType")]
    pub target_type: String,
    #[serde(rename = "targetId")]
    pub target_id: Option<Uuid>,
    pub subject: Option<String>,
    #[serde(rename = "display")]
    pub display: String, // 构建的显示名称，如"深圳中学/张三（学号: 001）/[数学老师]"
}
