use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::prelude::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeachingClassesStatistics {
    pub total_classes: i32,
    pub total_teacher: i32,
    pub total_students: i32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct TeachingClasses {
    pub id: Uuid,
    pub class_name: String,
    pub code: Option<String>,
    pub academic_year: Option<String>,
    pub subject_group_id: Option<Uuid>,
    pub teacher_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TeachingClassesDetail {
    pub id: Uuid,
    pub class_name: String,
    pub code: Option<String>,
    pub academic_year: Option<String>,
    pub subject_group_id: Option<Uuid>,
    pub teacher_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
    pub is_active: bool,
    //额外信息
    pub teacher_name: Option<String>,
    pub subject_groups_name: Option<String>,
    pub total_student: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTeachingClassesParams {
    pub class_name: String,
    pub code: Option<String>,
    pub academic_year: Option<String>,
    pub subject_group_id: Option<Uuid>,
    pub teacher_id: Option<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTeachingClassesParams {
    pub id: Uuid,
    pub class_name: String,
    pub code: Option<String>,
    pub academic_year: Option<String>,
    pub subject_group_id: Option<Uuid>,
    pub teacher_id: Option<Uuid>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeleteTeachingClassesParams {
    pub class_id: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FindAllStudentInClassParams {
    pub class_id: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MoveStudentToTeachingClassesParams {
    pub class_id: Uuid,
    pub student_id: Uuid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoveStudentFromTeachingClassesParams {
    pub class_id: Uuid,
    pub student_id: Uuid,
}
