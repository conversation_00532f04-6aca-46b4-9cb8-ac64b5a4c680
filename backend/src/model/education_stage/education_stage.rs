use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::model::PageParams;

/// 学段数据库模型
#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct EducationStage {
    pub id: Uuid,
    pub code: String,
    pub name: String,
    pub short_name: Option<String>,
    pub description: Option<String>,
    pub order_level: i32,
    pub duration_years: Option<i32>,
    pub age_range: Option<String>,
    pub is_standard: bool,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 学段创建请求
#[derive(Debug, Deserialize)]
pub struct CreateEducationStageRequest {
    pub code: String,
    pub name: String,
    pub short_name: Option<String>,
    pub description: Option<String>,
    pub order_level: i32,
    pub duration_years: Option<i32>,
    pub age_range: Option<String>,
    pub is_standard: Option<bool>,
}

/// 学段更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateEducationStageRequest {
    pub name: Option<String>,
    pub short_name: Option<String>,
    pub description: Option<String>,
    pub order_level: Option<i32>,
    pub duration_years: Option<i32>,
    pub age_range: Option<String>,
    pub is_standard: Option<bool>,
    pub is_active: Option<bool>,
}

/// 学段查询参数
#[derive(Debug, Deserialize)]
pub struct EducationStageQueryParams {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
    pub search: Option<String>,
    pub is_active: Option<bool>,
    pub is_standard: Option<bool>,
    pub order_by: Option<String>, // 排序字段：name, code, order_level, created_at
    pub order_direction: Option<String>, // 排序方向：asc, desc
}

/// 学段统计信息
#[derive(Debug, Serialize)]
pub struct EducationStageStatistics {
    pub total_stages: i64,
    pub active_stages: i64,
    pub inactive_stages: i64,
    pub standard_stages: i64,
    pub custom_stages: i64,
    pub usage_stats: Vec<EducationStageUsageStats>,
}

/// 学段使用统计
#[derive(Debug, Serialize)]
pub struct EducationStageUsageStats {
    pub stage_id: Uuid,
    pub stage_name: String,
    pub grade_count: i64,
    pub question_count: i64,
    pub paper_count: i64,
}

/// 学段响应VO（视图对象）
#[derive(Debug, Serialize,FromRow)]
pub struct EducationStageVO {
    pub id: Uuid,
    pub code: String,
    pub name: String,
    pub short_name: Option<String>,
    pub description: Option<String>,
    pub order_level: i32,
    pub duration_years: Option<i32>,
    pub age_range: Option<String>,
    pub is_standard: bool,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 学段简要信息（用于下拉选择等场景）
#[derive(Debug, FromRow, Serialize)]
pub struct EducationStageSummary {
    pub id: Uuid,
    pub code: String,
    pub name: String,
    pub short_name: Option<String>,
    pub is_active: bool,
}

impl EducationStage {
    /// 获取学段完整显示名称
    pub fn full_display_name(&self) -> String {
        match &self.short_name {
            Some(short) => format!("{} ({})", self.name, short),
            None => self.name.clone(),
        }
    }

    /// 检查学段是否激活
    pub fn is_available(&self) -> bool {
        self.is_active
    }

    /// 检查是否为标准学段
    pub fn is_standard_stage(&self) -> bool {
        self.is_standard
    }
}

impl Default for EducationStageQueryParams {
    fn default() -> Self {
        Self {
            page: Some(1), 
            page_size: Some(10),
            search: None,
            is_active: Some(true),
            is_standard: None,
            order_by: Some("order_level".to_string()),
            order_direction: Some("asc".to_string()),
        }
    }
}

/// 预定义的系统学段常量
pub struct SystemEducationStages;

impl SystemEducationStages {
    pub const PRIMARY: &'static str = "PRIMARY";
    pub const MIDDLE: &'static str = "MIDDLE";
    pub const HIGH: &'static str = "HIGH";
    pub const VOCATIONAL: &'static str = "VOCATIONAL";
    pub const INTERNATIONAL_IB: &'static str = "INTERNATIONAL_IB";
    pub const INTERNATIONAL_AP: &'static str = "INTERNATIONAL_AP";
    pub const INTERNATIONAL_ALEVEL: &'static str = "INTERNATIONAL_ALEVEL";
}