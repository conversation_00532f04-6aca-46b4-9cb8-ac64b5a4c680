use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct QuestionBank {
    pub id: Uuid,
    pub source_type: String, // 'manual', 'textbook_exercise'
    pub source_id: Option<Uuid>,
    pub question_type: String, // 'choice', 'blank', 'essay', 'calculation'
    pub question_content: String,
    pub options: Option<serde_json::Value>,
    pub answer_content: Option<String>,
    pub difficulty_level: i32, // 1-5
    pub knowledge_points: serde_json::Value,
    pub subject: String,
    pub grade_level: i32,
    pub creator_id: Uuid,
    pub version: i32,
    pub status: String, // 'draft', 'published', 'archived'
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ExamPaper {
    pub id: Uuid,
    pub title: String,
    pub subject: String,
    pub grade_level: i32,
    pub total_score: f32,
    pub description: Option<String>,
    pub structure: serde_json::Value, // JSON structure for paper layout
    pub creator_id: Uuid,
    pub version: i32,
    pub status: String, // 'draft', 'published', 'archived'
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ExamPaperQuestion {
    pub id: Uuid,
    pub exam_paper_id: Uuid,
    pub question_id: Uuid,
    pub question_number: String,
    pub score: f32,
    pub section_name: String,
    pub display_order: i32,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Textbook {
    pub id: Uuid,
    pub title: String,
    pub subject_id: Option<Uuid>,
    pub grade_level_id: Option<Uuid>,
    pub publisher: Option<String>,
    pub publication_year: Option<i32>,
    pub isbn: Option<String>,
    pub version: Option<String>,
    pub status: Option<String>, // 'draft', 'published', 'archived'
    pub creator_id: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TextbookChapter {
    pub id: Uuid,
    pub textbook_id: Uuid,
    pub chapter_number: i32,
    pub title: String,
    pub content: Option<String>,
    pub knowledge_points: serde_json::Value,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TextbookExercise {
    pub id: Uuid,
    pub textbook_id: Uuid,
    pub chapter_id: Uuid,
    pub question_content: String,
    pub answer_content: Option<String>,
    pub difficulty_level: i32,
    pub knowledge_points: serde_json::Value,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TextbookTenantAccess {
    pub id: Uuid,
    pub textbook_id: Uuid,
    pub tenant_id: Uuid,
    pub granted_by: Uuid,
    pub created_at: DateTime<Utc>,
}

// Request/Response DTOs
#[derive(Debug, Deserialize)]
pub struct CreateQuestionRequest {
    pub source_type: String,
    pub source_id: Option<Uuid>,
    pub question_type: String,
    pub question_content: String,
    pub options: Option<serde_json::Value>,
    pub answer_content: Option<String>,
    pub difficulty_level: i32,
    pub knowledge_points: serde_json::Value,
    pub subject: String,
    pub grade_level: i32,
}

#[derive(Debug, Deserialize)]
pub struct UpdateQuestionRequest {
    pub question_content: Option<String>,
    pub answer_content: Option<String>,
    pub difficulty_level: Option<i32>,
    pub knowledge_points: Option<serde_json::Value>,
    pub status: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreatePaperRequest {
    pub title: String,
    pub subject: String,
    pub grade_level: i32,
    pub total_score: f32,
    pub description: Option<String>,
    pub structure: serde_json::Value,
    pub questions: Vec<PaperQuestionRequest>,
}

#[derive(Debug, Deserialize)]
pub struct PaperQuestionRequest {
    pub question_id: Uuid,
    pub question_number: String,
    pub score: f32,
    pub section_name: String,
    pub display_order: i32,
}

#[derive(Debug, Deserialize)]
pub struct UpdatePaperRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub structure: Option<serde_json::Value>,
    pub status: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateTextbookRequest {
    pub title: String,
    pub subject_id: Uuid,
    pub grade_level_id: Uuid,
    pub publisher: Option<String>,
    pub publication_year: Option<i32>,
    pub isbn: Option<String>,
    pub version: Option<String>,
    pub chapters: Vec<TextbookChapterRequest>,
}

#[derive(Debug, Deserialize)]
pub struct TextbookChapterRequest {
    pub chapter_number: i32,
    pub title: String,
    pub content: Option<String>,
    pub knowledge_points: serde_json::Value,
    pub exercises: Vec<TextbookExerciseRequest>,
}

#[derive(Debug, Deserialize)]
pub struct TextbookExerciseRequest {
    pub question_content: String,
    pub answer_content: Option<String>,
    pub difficulty_level: i32,
    pub knowledge_points: serde_json::Value,
}

#[derive(Debug, Deserialize)]
pub struct GrantTextbookAccessRequest {
    pub textbook_id: Uuid,
    pub tenant_ids: Vec<Uuid>,
}

#[derive(Debug, Deserialize)]
pub struct QuestionQueryParams {
    pub page: Option<i32>,
    pub page_size: Option<i32>,
    pub subject: Option<String>,
    pub grade_level: Option<i32>,
    pub difficulty_level: Option<i32>,
    pub question_type: Option<String>,
    pub status: Option<String>,
    pub keyword: Option<String>,
    pub source_type: Option<String>,
    pub search: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct PaperQueryParams {
    pub page: Option<i32>,
    pub page_size: Option<i32>,
    pub subject: Option<String>,
    pub grade_level: Option<i32>,
    pub status: Option<String>,
    pub keyword: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct TextbookQueryParams {
    pub page: Option<i32>,
    pub page_size: Option<i32>,
    pub subject_id: Option<Uuid>,
    pub grade_level_id: Option<Uuid>,
    pub publisher: Option<String>,
    pub status: Option<String>,
    pub keyword: Option<String>,
}



#[derive(Debug, Serialize)]
pub struct PaperResponse {
    pub id: Uuid,
    pub title: String,
    pub subject: String,
    pub grade_level: i32,
    pub total_score: f32,
    pub description: Option<String>,
    pub structure: serde_json::Value,
    pub creator_id: Uuid,
    pub version: i32,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub questions: Vec<ExamPaperQuestion>,
    pub usage_count: i64,
}

#[derive(Debug, Serialize)]
pub struct TextbookResponse {
    pub id: Uuid,
    pub title: String,
    pub subject_id: Uuid,
    pub grade_level_id: Uuid,
    pub publisher: Option<String>,
    pub publication_year: Option<i32>,
    pub isbn: Option<String>,
    pub version: Option<String>,
    pub status: String,
    pub creator_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub chapter_count: i64,
    pub exercise_count: i64,
    pub access_tenants: Vec<Uuid>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuestionResponse {
    pub id: Uuid,
    pub source_type: String,
    pub source_id: Option<Uuid>,
    pub question_type: String,
    pub question_content: String,
    pub answer_content: Option<String>,
    pub difficulty_level: i32,
    pub knowledge_points: serde_json::Value,
    pub subject: String,
    pub grade_level: i32,
    pub creator_id: Uuid,
    pub version: i32,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub usage_count: i64,
    pub average_score: Option<f32>,
}

#[derive(Debug, Serialize)]
pub struct QuestionListResponse {
    pub questions: Vec<QuestionResponse>,
    pub total: i64,
    pub page: i32,
    pub page_size: i32,
}

#[derive(Debug, Serialize)]
pub struct PaperListResponse {
    pub papers: Vec<PaperResponse>,
    pub total: i64,
    pub page: i32,
    pub page_size: i32,
}

#[derive(Debug, Serialize)]
pub struct TextbookListResponse {
    pub textbooks: Vec<TextbookResponse>,
    pub total: i64,
    pub page: i32,
    pub page_size: i32,
}

#[derive(Debug, Serialize)]
pub struct QuestionStatistics {
    pub total_questions: i64,
    pub published_questions: i64,
    pub draft_questions: i64,
    pub by_subject: serde_json::Value,
    pub by_difficulty: serde_json::Value,
    pub by_type: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct PaperStatistics {
    pub total_papers: i64,
    pub published_papers: i64,
    pub draft_papers: i64,
    pub by_subject: serde_json::Value,
    pub by_grade: serde_json::Value,
    pub usage_stats: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct QuestionStatisticsResponse {
    pub total_questions: i64,
    pub published_questions: i64,
    pub draft_questions: i64,
    pub by_subject: serde_json::Value,
    pub by_difficulty: serde_json::Value,
    pub by_type: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Question {
    pub id: Uuid,
    pub source_type: String,
    pub question_type: String,
    pub question_content: String,
    pub options: Option<serde_json::Value>,
    pub correct_answer: String,
    pub explanation: Option<String>,
    pub difficulty_level: String,
    pub knowledge_points: serde_json::Value,
    pub tags: serde_json::Value,
    pub estimated_time: i32,
    pub creator_id: Uuid,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkImportRequest {
    pub questions: Vec<BulkQuestionRequest>,
    pub validate_duplicates: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkQuestionRequest {
    pub source_type: String,
    pub question_type: String,
    pub question_content: String,
    pub options: serde_json::Value,
    pub correct_answer: String,
    pub explanation: Option<String>,
    pub difficulty_level: String,
    pub knowledge_points: serde_json::Value,
    pub tags: serde_json::Value,
    pub estimated_time: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkImportResponse {
    pub total_questions: i32,
    pub successful_imports: i32,
    pub failed_imports: i32,
    pub imported_questions: Vec<Question>,
    pub errors: Vec<ImportError>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportError {
    pub line_number: i32,
    pub error_message: String,
    pub question_content: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuestionSearchResponse {
    pub questions: Vec<QuestionResponse>,
    pub total: i64,
    pub page: i32,
    pub page_size: i32,
    pub filters_applied: serde_json::Value,
}

