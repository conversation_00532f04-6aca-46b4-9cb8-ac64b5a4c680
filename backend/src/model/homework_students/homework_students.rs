use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

use crate::model::StudentBaseInfo;

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct HomeworkStudents {
    pub id: Uuid,
    pub homework_id: Uuid,
    pub student_id: Uuid,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

///草稿，未发布的，学生未开始作答，只是处于作业创建阶段
pub const HOMEWORK_STUDENTS_STATUS_UNSUBMITTED: &str = "Unsubmitted";
///扫描后异常或者评阅中异常
pub const HOMEWORK_STUDENTS_STATUS_ERROR: &str = "Error";
///学生分数已经出来了
pub const HOMEWORK_STUDENTS_STATUS_DONE: &str = "Done";

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct BatchBindStudentsToHomeworkParams {
    pub homework_id: Uuid,
    pub student_id_list: Vec<Uuid>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct FindAllByHomeworkIdParams {
    pub homework_id: Uuid,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct HomeworkStudentsWithStudentBaseInfo {
    pub id: Uuid,
    pub homework_id: Uuid,
    pub student_id: Uuid,
    pub student_base_info: Option<StudentBaseInfo>,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct BatchUnbindStudentsFromHomeworkParams {
    pub homework_id: Uuid,
    pub student_id_list: Vec<Uuid>,
}
