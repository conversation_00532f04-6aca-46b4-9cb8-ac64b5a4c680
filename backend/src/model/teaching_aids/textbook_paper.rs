use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct TextbookPaper {
    pub id: Uuid,
    pub paper_id: Uuid,
    pub textbook_id: Uuid,
    #[serde(default = "default_serial_number")]
    pub serial_number: i32,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct TextbookPaperQuery {
    pub page: Option<i64>,
    pub page_size: Option<i64>,
}

// 新增：用于创建 TextbookPaper 的请求体
#[derive(Debug, Deserialize)]
pub struct CreateTextbookPaperRequest {
    pub paper_id: Uuid,
    pub textbook_id: Uuid,
    #[serde(default = "default_serial_number")]
    pub serial_number: i32,
}

// 新增：用于更新 TextbookPaper 的请求体
#[derive(Debug, Deserialize)]
pub struct UpdateTextbookPaperRequest {
    pub paper_id: Option<Uuid>,
    pub textbook_id: Option<Uuid>,
}

fn default_serial_number() -> i32 {
    0
}