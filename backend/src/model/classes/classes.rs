use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::model::{grade::grade::GradeLevel, PageParams, Teacher};

/**
 * 班级的实体，对应数据库
 */
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Classes {
    pub id: Uuid,
    pub name: String,
    pub code: String,
    pub class_type: ClassType,
    pub school_year: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/**
 * 班级类型
 */
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ClassType {
    /**
     * 行政班
     */
    Administrative,
    /**
     * 教学班
     */
    Teaching,
}

/**
 * 班级统计数据
 */
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClassesStatistics {
    pub total_classes: i32,
    pub total_teacher: i32,
    pub total_students: i32,
}

/**
 * 在班级基础上有更多联查信息的实体
 */
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClassesDetail {
    //数据库内部分
    pub classes: Classes,
    //如果是行政班则具有的额外信息
    pub administrative_detail: Option<AdministrativeDetail>,
    //教学班才具有的额外信息
    pub teaching_detail: Option<TeachingDetail>,
    //年级列表
    pub grade_level_list: Option<Vec<GradeLevel>>,
    //学生总数
    pub student_totals: i32,
}

/**
 * 行政班的额外详情信息
 */
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdministrativeDetail {
    //班主任列表
    pub head_teacher_list: Vec<HeadTeacher>,
}

/**
 * 班主任信息
 */
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeadTeacher {
    pub(crate) teacher: Teacher,
    //任课结束时间，对于临时代班的才有
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
}

///教学班的额外信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeachingDetail {
    //任课老师列表
    pub teacher_list: Vec<TeachingTeacher>,
}

///任课老师信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeachingTeacher {
    //教师信息
    pub teacher: Teacher,
    //学科
    pub subject_code: String,
    //任课结束时间，对于临时代班的才有
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
}

/**
 * 创建班级用的参数对象
 */
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateClassesParams {
    pub name: String,
    pub code: String,
    pub class_type: ClassType,
    pub school_year: i32,
    //教学班额外参数
    pub grade_level_code: Option<Vec<String>>,
    pub subject_code: Option<Vec<String>>,
}

/**
 * 更新班级用的参数对象
 */
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateClassesParams {
    pub id: String,
    pub name: String,
    pub code: String,
    pub class_type: ClassType,
    pub school_year: i32,
    //教学班额外参数
    pub grade_level_code: Option<Vec<String>>,
    pub subject_code: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageAdministrativeClassesParams {
    pub pagination: PageParams,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageTeachingClassesParams {
    pub pagination: PageParams,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageClassesStudentParams {
    pub id: String,
    pub pagination: PageParams,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AddTeacherParams {
    pub classed_id: Uuid,
    pub teacher_id: Uuid,
    pub end_time: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RemoveTeacherParams {
    pub classed_id: Uuid,
    pub teacher_id: Uuid,
}
