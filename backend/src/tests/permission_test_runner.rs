//! 权限测试使用示例和运行脚本
//! Permission Test Usage Examples and Runner Scripts
//! 
//! 提供权限测试的使用示例和便捷的运行脚本

use crate::tests::permission_system_tests::PermissionTester;
use std::env;

/// 主要的权限测试运行器
pub async fn run_permission_tests() -> Result<(), Box<dyn std::error::Error>> {
    // 从环境变量获取配置
    let base_url = env::var("TEST_API_BASE_URL")
        .unwrap_or_else(|_| "http://localhost:8080".to_string());
    
    println!("🚀 启动权限测试套件");
    println!("使用API地址: {}", base_url);
    
    // 创建权限测试器
    let mut tester = PermissionTester::new(Some(base_url)).await?;
    
    // 运行完整的权限测试
    tester.run_full_permission_tests().await?;
    
    Ok(())
}

/// 快速权限验证（仅数据库测试）
pub async fn quick_permission_check() -> Result<(), Box<dyn std::error::Error>> {
    println!("⚡ 运行快速权限验证...");
    
    let tester = PermissionTester::new(None).await?;
    
    // 只运行数据库相关的权限检查
    println!("检查数据库连接...");
    tester.check_database_connection().await?;
    
    println!("验证跨租户权限隔离...");
    // 这里可以添加更多快速检查
    
    println!("✅ 快速权限验证完成");
    Ok(())
}

#[cfg(test)]
mod integration_tests {
    use super::*;
    use tokio_test;

    /// 集成测试示例：运行权限测试套件
    #[tokio::test]
    #[ignore] // 需要后端服务运行，默认忽略
    async fn integration_test_run_permission_suite() {
        // 设置测试环境变量
        env::set_var("TEST_API_BASE_URL", "http://localhost:8080");
        
        let result = run_permission_tests().await;
        
        match result {
            Ok(()) => println!("✅ 集成测试通过"),
            Err(e) => {
                println!("❌ 集成测试失败: {}", e);
                panic!("权限测试套件失败");
            }
        }
    }

    /// 快速验证测试
    #[tokio::test]
    async fn test_quick_permission_check() {
        let result = quick_permission_check().await;
        
        // 快速检查应该总是成功（只检查数据库连接）
        assert!(result.is_ok(), "快速权限验证失败: {:?}", result);
    }
}

/// 命令行工具示例
#[cfg(feature = "cli")]
pub mod cli {
    use super::*;
    use clap::{App, Arg, SubCommand};
    
    pub async fn main() -> Result<(), Box<dyn std::error::Error>> {
        let matches = App::new("权限测试工具")
            .version("1.0")
            .author("Deep-Mate Team")
            .about("多租户权限系统测试工具")
            .arg(
                Arg::with_name("url")
                    .short("u")
                    .long("url")
                    .value_name("BASE_URL")
                    .help("API服务器地址")
                    .takes_value(true)
            )
            .subcommand(
                SubCommand::with_name("full")
                    .about("运行完整的权限测试套件")
            )
            .subcommand(
                SubCommand::with_name("quick")
                    .about("运行快速权限验证")
            )
            .get_matches();
        
        let base_url = matches.value_of("url")
            .map(|s| s.to_string());
        
        match matches.subcommand() {
            ("full", _) => {
                let mut tester = PermissionTester::new(base_url).await?;
                tester.run_full_permission_tests().await?;
            }
            ("quick", _) => {
                quick_permission_check().await?;
            }
            _ => {
                println!("请指定子命令: full 或 quick");
                println!("使用 --help 查看帮助");
            }
        }
        
        Ok(())
    }
}

/// 性能基准测试工具
pub mod benchmarks {
    use super::*;
    use std::time::{Duration, Instant};
    
    pub struct PermissionBenchmark {
        tester: PermissionTester,
    }
    
    impl PermissionBenchmark {
        pub async fn new() -> Result<Self, Box<dyn std::error::Error>> {
            let tester = PermissionTester::new(None).await?;
            Ok(Self { tester })
        }
        
        /// 权限查询性能基准测试
        pub async fn benchmark_permission_queries(&self, iterations: usize) -> Result<BenchmarkResult, Box<dyn std::error::Error>> {
            println!("🔥 开始权限查询性能基准测试...");
            println!("迭代次数: {}", iterations);
            
            let start = Instant::now();
            let mut successful_queries = 0;
            
            for i in 0..iterations {
                let result = self.tester.test_db_permissions(
                    "550e8400-e29b-41d4-a716-446655440102", // 深圳校长
                    "550e8400-e29b-41d4-a716-446655440001", // 深圳实验学校
                    "student",
                    "read",
                    &format!("基准测试 #{}", i + 1)
                ).await;
                
                if result.is_ok() {
                    successful_queries += 1;
                }
            }
            
            let duration = start.elapsed();
            
            Ok(BenchmarkResult {
                total_iterations: iterations,
                successful_iterations: successful_queries,
                total_duration: duration,
                average_duration: duration / iterations as u32,
                queries_per_second: (iterations as f64) / duration.as_secs_f64(),
            })
        }
        
        /// 并发权限查询基准测试
        pub async fn benchmark_concurrent_queries(&self, concurrent_users: usize, queries_per_user: usize) -> Result<BenchmarkResult, Box<dyn std::error::Error>> {
            println!("🚀 开始并发权限查询基准测试...");
            println!("并发用户数: {}, 每用户查询数: {}", concurrent_users, queries_per_user);
            
            let start = Instant::now();
            let mut handles = Vec::new();
            
            for user_id in 0..concurrent_users {
                let tester = self.tester.clone();
                let handle = tokio::spawn(async move {
                    let mut successful = 0;
                    for query_id in 0..queries_per_user {
                        let result = tester.test_db_permissions(
                            "550e8400-e29b-41d4-a716-446655440102",
                            "550e8400-e29b-41d4-a716-446655440001",
                            "student",
                            "read",
                            &format!("并发用户{} 查询#{}", user_id, query_id + 1)
                        ).await;
                        
                        if result.is_ok() {
                            successful += 1;
                        }
                    }
                    successful
                });
                handles.push(handle);
            }
            
            let mut total_successful = 0;
            for handle in handles {
                total_successful += handle.await.unwrap_or(0);
            }
            
            let duration = start.elapsed();
            let total_queries = concurrent_users * queries_per_user;
            
            Ok(BenchmarkResult {
                total_iterations: total_queries,
                successful_iterations: total_successful,
                total_duration: duration,
                average_duration: duration / total_queries as u32,
                queries_per_second: (total_queries as f64) / duration.as_secs_f64(),
            })
        }
    }
    
    #[derive(Debug)]
    pub struct BenchmarkResult {
        pub total_iterations: usize,
        pub successful_iterations: usize,
        pub total_duration: Duration,
        pub average_duration: Duration,
        pub queries_per_second: f64,
    }
    
    impl BenchmarkResult {
        pub fn print_summary(&self) {
            println!("\n📊 基准测试结果:");
            println!("  总查询数: {}", self.total_iterations);
            println!("  成功查询数: {}", self.successful_iterations);
            println!("  成功率: {:.2}%", (self.successful_iterations as f64 / self.total_iterations as f64) * 100.0);
            println!("  总耗时: {:?}", self.total_duration);
            println!("  平均响应时间: {:?}", self.average_duration);
            println!("  QPS (查询/秒): {:.2}", self.queries_per_second);
            
            // 性能评估
            if self.average_duration.as_millis() < 1 {
                println!("  性能评级: 🚀 优秀 (<1ms)");
            } else if self.average_duration.as_millis() < 5 {
                println!("  性能评级: ✅ 良好 (<5ms)");
            } else if self.average_duration.as_millis() < 10 {
                println!("  性能评级: ⚠️ 一般 (<10ms)");
            } else {
                println!("  性能评级: ❌ 需要优化 (>10ms)");
            }
        }
    }
    
    #[cfg(test)]
    mod benchmark_tests {
        use super::*;
        
        #[tokio::test]
        async fn test_permission_query_benchmark() {
            let benchmark = PermissionBenchmark::new().await.unwrap();
            let result = benchmark.benchmark_permission_queries(100).await.unwrap();
            
            result.print_summary();
            
            // 基准要求：平均响应时间应该小于10ms
            assert!(result.average_duration.as_millis() < 10, 
                "权限查询性能不达标: {:?}", result.average_duration);
        }
        
        #[tokio::test]
        async fn test_concurrent_query_benchmark() {
            let benchmark = PermissionBenchmark::new().await.unwrap();
            let result = benchmark.benchmark_concurrent_queries(10, 20).await.unwrap();
            
            result.print_summary();
            
            // 并发基准要求：QPS应该大于100
            assert!(result.queries_per_second > 100.0, 
                "并发查询性能不达标: {:.2} QPS", result.queries_per_second);
        }
    }
}

/// 测试报告生成器
pub mod reporting {
    use super::*;
    use serde::{Serialize, Deserialize};
    use std::fs;
    use chrono::{DateTime, Utc};
    
    #[derive(Debug, Serialize, Deserialize)]
    pub struct TestReport {
        pub timestamp: DateTime<Utc>,
        pub environment: TestEnvironment,
        pub results: TestResults,
        pub performance: PerformanceMetrics,
        pub recommendations: Vec<String>,
    }
    
    #[derive(Debug, Serialize, Deserialize)]
    pub struct TestEnvironment {
        pub api_base_url: String,
        pub database_version: String,
        pub rust_version: String,
    }
    
    #[derive(Debug, Serialize, Deserialize)]
    pub struct TestResults {
        pub login_tests: (i32, i32),
        pub admin_tests: (i32, i32),
        pub tenant_isolation_tests: (i32, i32),
        pub role_hierarchy_tests: (i32, i32),
        pub db_permission_tests: (i32, i32),
        pub cross_tenant_tests: (i32, i32),
        pub role_inheritance_tests: (i32, i32),
        pub overall_pass_rate: f64,
    }
    
    #[derive(Debug, Serialize, Deserialize)]
    pub struct PerformanceMetrics {
        pub average_response_time_ms: f64,
        pub queries_per_second: f64,
        pub concurrent_user_capacity: usize,
    }
    
    pub async fn generate_test_report(base_url: Option<String>) -> Result<TestReport, Box<dyn std::error::Error>> {
        println!("📝 生成测试报告...");
        
        // 运行测试并收集结果
        let mut tester = PermissionTester::new(base_url.clone()).await?;
        tester.run_full_permission_tests().await?;
        
        // 运行性能基准测试
        let benchmark = benchmarks::PermissionBenchmark::new().await?;
        let perf_result = benchmark.benchmark_permission_queries(50).await?;
        
        // 生成建议
        let mut recommendations = Vec::new();
        if perf_result.average_duration.as_millis() > 5 {
            recommendations.push("考虑优化权限查询性能，当前响应时间较慢".to_string());
        }
        if perf_result.queries_per_second < 200.0 {
            recommendations.push("考虑增加数据库连接池大小以提高并发性能".to_string());
        }
        
        let total_passed = tester.stats.login_tests.0 + tester.stats.admin_tests.0 + 
                          tester.stats.tenant_isolation_tests.0 + tester.stats.role_hierarchy_tests.0 + 
                          tester.stats.db_permission_tests.0 + tester.stats.cross_tenant_tests.0 + 
                          tester.stats.role_inheritance_tests.0;
        let total_tests = tester.stats.login_tests.1 + tester.stats.admin_tests.1 + 
                         tester.stats.tenant_isolation_tests.1 + tester.stats.role_hierarchy_tests.1 + 
                         tester.stats.db_permission_tests.1 + tester.stats.cross_tenant_tests.1 + 
                         tester.stats.role_inheritance_tests.1;
        
        let pass_rate = if total_tests > 0 {
            (total_passed as f64 / total_tests as f64) * 100.0
        } else {
            0.0
        };
        
        let report = TestReport {
            timestamp: Utc::now(),
            environment: TestEnvironment {
                api_base_url: base_url.unwrap_or_else(|| "http://localhost:8080".to_string()),
                database_version: "PostgreSQL 18".to_string(),
                rust_version: "rust 1.88.0".to_string(),
            },
            results: TestResults {
                login_tests: tester.stats.login_tests,
                admin_tests: tester.stats.admin_tests,
                tenant_isolation_tests: tester.stats.tenant_isolation_tests,
                role_hierarchy_tests: tester.stats.role_hierarchy_tests,
                db_permission_tests: tester.stats.db_permission_tests,
                cross_tenant_tests: tester.stats.cross_tenant_tests,
                role_inheritance_tests: tester.stats.role_inheritance_tests,
                overall_pass_rate: pass_rate,
            },
            performance: PerformanceMetrics {
                average_response_time_ms: perf_result.average_duration.as_millis() as f64,
                queries_per_second: perf_result.queries_per_second,
                concurrent_user_capacity: 100, // 估算值
            },
            recommendations,
        };
        
        Ok(report)
    }
    
    pub fn save_report_to_file(report: &TestReport, filename: &str) -> Result<(), Box<dyn std::error::Error>> {
        let json_content = serde_json::to_string_pretty(report)?;
        fs::write(filename, json_content)?;
        println!("📄 测试报告已保存到: {}", filename);
        Ok(())
    }
    
    pub fn print_report_summary(report: &TestReport) {
        println!("\n📋 测试报告摘要");
        println!("==========================================");
        println!("测试时间: {}", report.timestamp.format("%Y-%m-%d %H:%M:%S UTC"));
        println!("API地址: {}", report.environment.api_base_url);
        println!("总体通过率: {:.1}%", report.results.overall_pass_rate);
        println!("平均响应时间: {:.2}ms", report.performance.average_response_time_ms);
        println!("查询性能: {:.1} QPS", report.performance.queries_per_second);
        
        if !report.recommendations.is_empty() {
            println!("\n建议:");
            for (i, rec) in report.recommendations.iter().enumerate() {
                println!("  {}. {}", i + 1, rec);
            }
        }
        
        println!("==========================================");
    }
}