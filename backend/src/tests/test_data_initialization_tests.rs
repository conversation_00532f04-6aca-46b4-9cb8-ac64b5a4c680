//! 多租户权限测试数据初始化集成测试
//! Multi-tenant Permission Test Data Initialization Integration Tests
//!
//! 提供了更好的错误处理、类型安全和测试集成

use crate::tests::test_utils::setup_test_db;
use sqlx::{PgPool, Row};
use std::collections::HashMap;
use tokio;
use uuid::Uuid;

/// 测试数据初始化主结构体
pub struct TestDataInitializer {
    pool: PgPool,
    stats: TestDataStats,
}

/// 测试数据统计信息
#[derive(Debug, Default)]
pub struct TestDataStats {
    pub tenants_created: i64,
    pub users_created: i64,
    pub teachers_created: i64,
    pub students_created: i64,
    pub classes_created: i64,
    pub exams_created: i64,
    pub policies_created: i64,
}

/// 权限验证结果
#[derive(Debug)]
pub struct PermissionVerificationResult {
    pub user_name: String,
    pub role: String,
    pub resource: String,
    pub action: String,
    pub effect: String,
}

impl TestDataInitializer {
    /// 创建新的测试数据初始化器
    pub async fn new() -> Result<Self, sqlx::Error> {
        let pool = setup_test_db().await;
        Ok(Self {
            pool,
            stats: TestDataStats::default(),
        })
    }

    /// 运行完整的测试数据初始化流程
    pub async fn run_full_initialization(&mut self) -> Result<(), sqlx::Error> {
        self.check_database_connection().await?;
        self.check_required_tables().await?;
        self.check_and_initialize_casbin_policies().await?;
        self.initialize_test_data().await?;
        self.generate_statistics().await?;
        Ok(())
    }

    /// 检查数据库连接
    pub async fn check_database_connection(&self) -> Result<(), sqlx::Error> {
        sqlx::query("SELECT 1")
            .fetch_one(&self.pool)
            .await?;
        println!("✅ 数据库连接正常");
        Ok(())
    }

    /// 检查必要的表是否存在
    pub async fn check_required_tables(&self) -> Result<(), sqlx::Error> {
        let required_tables = vec![
            "public.tenants",
            "public.users", 
            "public.roles",
            "public.casbin_policies"
        ];

        for table in required_tables {
            let result = sqlx::query(&format!("SELECT 1 FROM {} LIMIT 1", table))
                .fetch_optional(&self.pool)
                .await;
                
            match result {
                Ok(_) => println!("✅ 表 {} 存在", table),
                Err(e) => {
                    eprintln!("❌ 必要的表 {} 不存在！错误: {}", table, e);
                    return Err(e);
                }
            }
        }
        
        println!("✅ 必要的表结构检查通过");
        Ok(())
    }

    /// 检查并初始化Casbin权限策略
    pub async fn check_and_initialize_casbin_policies(&mut self) -> Result<(), sqlx::Error> {
        let policy_count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM public.casbin_policies WHERE tenant_id = 'template'"
        )
        .fetch_one(&self.pool)
        .await?;

        if policy_count == 0 {
            println!("⚠️  Casbin权限策略未初始化，正在初始化...");
            self.initialize_casbin_template_policies().await?;
        } else {
            println!("✅ Casbin权限策略已存在 ({} 条模板策略)", policy_count);
        }
        
        Ok(())
    }

    /// 初始化Casbin模板策略
    async fn initialize_casbin_template_policies(&mut self) -> Result<(), sqlx::Error> {
        // 这里应该插入基础的Casbin策略模板
        // 为了简化，我们添加一些基本的角色权限策略
        
        let template_policies = vec![
            // 校长权限
            ("p", "principal", "tenant_*", "student", "read", "allow", "template"),
            ("p", "principal", "tenant_*", "teacher", "read", "allow", "template"),
            ("p", "principal", "tenant_*", "exam", "create", "allow", "template"),
            ("p", "principal", "tenant_*", "exam", "read", "allow", "template"),
            ("p", "principal", "tenant_*", "exam", "update", "allow", "template"),
            ("p", "principal", "tenant_*", "grade", "read", "allow", "template"),
            
            // 教导主任权限
            ("p", "academic_director", "tenant_*", "exam", "create", "allow", "template"),
            ("p", "academic_director", "tenant_*", "exam", "read", "allow", "template"),
            ("p", "academic_director", "tenant_*", "teacher", "read", "allow", "template"),
            ("p", "academic_director", "tenant_*", "student", "read", "allow", "template"),
            
            // 学科组长权限
            ("p", "subject_leader", "tenant_*", "exam", "read", "allow", "template"),
            ("p", "subject_leader", "tenant_*", "grade", "read", "allow", "template"),
            
            // 班主任权限
            ("p", "class_teacher", "tenant_*", "student", "read", "allow", "template"),
            ("p", "class_teacher", "tenant_*", "grade", "read", "allow", "template"),
            
            // 任课老师权限
            ("p", "teacher", "tenant_*", "student", "read", "allow", "template"),
            ("p", "teacher", "tenant_*", "grade", "create", "allow", "template"),
            ("p", "teacher", "tenant_*", "grade", "read", "allow", "template"),
            
            // 学生权限
            ("p", "student", "tenant_*", "exam", "read", "allow", "template"),
            ("p", "student", "tenant_*", "grade", "read", "allow", "template"),
        ];

        for (ptype, v0, v1, v2, v3, v4, tenant_id) in template_policies {
            sqlx::query(
                "INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id) VALUES ($1, $2, $3, $4, $5, $6, $7)"
            )
            .bind(ptype)
            .bind(v0)
            .bind(v1)
            .bind(v2)
            .bind(v3)
            .bind(v4)
            .bind(tenant_id)
            .execute(&self.pool)
            .await?;
        }

        println!("✅ Casbin模板策略初始化完成");
        Ok(())
    }

    /// 初始化测试数据
    pub async fn initialize_test_data(&mut self) -> Result<(), sqlx::Error> {
        // 执行测试数据初始化SQL文件
        let sql_content = std::fs::read_to_string("migrations/20250803_test_data_initialization.sql")
            .expect("无法读取测试数据初始化SQL文件");

        // 分割SQL语句并逐个执行
        let statements: Vec<&str> = sql_content
            .split(';')
            .map(|s| s.trim())
            .filter(|s| !s.is_empty() && !s.starts_with("--"))
            .collect();

        for statement in statements {
            if !statement.trim().is_empty() {
                sqlx::query(statement)
                    .execute(&self.pool)
                    .await
                    .map_err(|e| {
                        eprintln!("执行SQL语句失败: {}", statement);
                        eprintln!("错误: {}", e);
                        e
                    })?;
            }
        }

        println!("✅ 测试数据初始化完成");
        Ok(())
    }

    /// 生成统计信息
    pub async fn generate_statistics(&mut self) -> Result<(), sqlx::Error> {
        // 租户统计
        self.stats.tenants_created = sqlx::query_scalar(
            "SELECT COUNT(*) FROM public.tenants WHERE id IN ('550e8400-e29b-41d4-a716-********0001', '550e8400-e29b-41d4-a716-********0002', '550e8400-e29b-41d4-a716-********0003')"
        ).fetch_one(&self.pool).await?;

        // 用户统计
        self.stats.users_created = sqlx::query_scalar(
            "SELECT COUNT(*) FROM public.users WHERE id::text LIKE '550e8400-e29b-41d4-a716-********%'"
        ).fetch_one(&self.pool).await?;

        // 深圳实验学校数据统计
        if let Ok(teachers) = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tenant_szsy_001.teachers"
        ).fetch_one(&self.pool).await {
            self.stats.teachers_created += teachers;
        }

        if let Ok(students) = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tenant_szsy_001.students"
        ).fetch_one(&self.pool).await {
            self.stats.students_created += students;
        }

        if let Ok(classes) = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tenant_szsy_001.administrative_classes"
        ).fetch_one(&self.pool).await {
            self.stats.classes_created += classes;
        }

        if let Ok(exams) = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tenant_szsy_001.exams"
        ).fetch_one(&self.pool).await {
            self.stats.exams_created += exams;
        }

        // 北京四中数据统计
        if let Ok(teachers) = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tenant_bjsz_002.teachers"
        ).fetch_one(&self.pool).await {
            self.stats.teachers_created += teachers;
        }

        if let Ok(students) = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tenant_bjsz_002.students"
        ).fetch_one(&self.pool).await {
            self.stats.students_created += students;
        }

        if let Ok(classes) = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tenant_bjsz_002.administrative_classes"
        ).fetch_one(&self.pool).await {
            self.stats.classes_created += classes;
        }

        // 权限策略统计
        self.stats.policies_created = sqlx::query_scalar(
            "SELECT COUNT(*) FROM public.casbin_policies"
        ).fetch_one(&self.pool).await?;

        println!("📊 统计信息生成完成");
        self.print_statistics();
        Ok(())
    }

    /// 打印统计信息
    pub fn print_statistics(&self) {
        println!("==========================================");
        println!("📊 初始化统计信息:");
        println!("==========================================");
        println!("🏢 创建租户数量: {}", self.stats.tenants_created);
        println!("👥 创建用户数量: {}", self.stats.users_created);
        println!("👨‍🏫 创建教师数量: {}", self.stats.teachers_created);
        println!("👨‍🎓 创建学生数量: {}", self.stats.students_created);
        println!("🏫 创建班级数量: {}", self.stats.classes_created);
        println!("📝 创建考试数量: {}", self.stats.exams_created);
        println!("🔐 创建策略数量: {}", self.stats.policies_created);
        println!("==========================================");
    }

    /// 获取租户信息
    pub async fn get_tenant_info(&self) -> Result<Vec<(String, String, String, String)>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT 
                name as "学校名称",
                COALESCE(tenant_type, 'unknown') as "学校代码", 
                schema_name as "模式名称",
                status as "状态"
            FROM public.tenants 
            WHERE id IN ('550e8400-e29b-41d4-a716-********0001', '550e8400-e29b-41d4-a716-********0002', '550e8400-e29b-41d4-a716-********0003')
            ORDER BY name
            "#
        ).fetch_all(&self.pool).await?;

        let mut results = Vec::new();
        for row in rows {
            results.push((
                row.get::<String, _>("学校名称"),
                row.get::<String, _>("学校代码"),
                row.get::<String, _>("模式名称"),
                row.get::<String, _>("状态"),
            ));
        }
        
        Ok(results)
    }

    /// 验证校长权限
    pub async fn verify_principal_permissions(&self) -> Result<Vec<PermissionVerificationResult>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT 
                cp.v0 as "角色",
                cp.v2 as "资源", 
                cp.v3 as "操作",
                cp.v4 as "效果"
            FROM public.casbin_policies cp
            WHERE cp.ptype = 'p' 
            AND cp.v0 = 'principal' 
            AND cp.tenant_id = '550e8400-e29b-41d4-a716-********0001'
            AND cp.v2 IN ('student', 'teacher', 'exam', 'grade')
            ORDER BY cp.v2, cp.v3
            LIMIT 10
            "#
        ).fetch_all(&self.pool).await?;

        let mut results = Vec::new();
        for row in rows {
            results.push(PermissionVerificationResult {
                user_name: "张校长".to_string(),
                role: row.get::<String, _>("角色"),
                resource: row.get::<String, _>("资源"),
                action: row.get::<String, _>("操作"),
                effect: row.get::<String, _>("效果"),
            });
        }

        Ok(results)
    }

    /// 验证学生权限
    pub async fn verify_student_permissions(&self) -> Result<Vec<PermissionVerificationResult>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT 
                cp.v0 as "角色",
                cp.v2 as "资源", 
                cp.v3 as "操作",
                cp.v4 as "效果"
            FROM public.casbin_policies cp
            WHERE cp.ptype = 'p' 
            AND cp.v0 = 'student' 
            AND cp.tenant_id = '550e8400-e29b-41d4-a716-********0001'
            ORDER BY cp.v2, cp.v3
            "#
        ).fetch_all(&self.pool).await?;

        let mut results = Vec::new();
        for row in rows {
            results.push(PermissionVerificationResult {
                user_name: "小明学生".to_string(),
                role: row.get::<String, _>("角色"),
                resource: row.get::<String, _>("资源"),
                action: row.get::<String, _>("操作"),
                effect: row.get::<String, _>("效果"),
            });
        }

        Ok(results)
    }

    /// 验证用户角色映射
    pub async fn verify_user_role_mappings(&self) -> Result<Vec<(String, String, String)>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT 
                COALESCE(u.username, 'Unknown') as "用户姓名",
                cp.v1 as "角色",
                CASE cp.v2
                    WHEN 'system' THEN '系统级'
                    WHEN '550e8400-e29b-41d4-a716-********0001' THEN '深圳实验学校'
                    WHEN '550e8400-e29b-41d4-a716-********0002' THEN '北京四中'
                    ELSE cp.v2
                END as "租户/域"
            FROM public.casbin_policies cp
            LEFT JOIN public.users u ON u.id::text = cp.v0
            WHERE cp.ptype = 'g'
            AND cp.v0 LIKE '550e8400-e29b-41d4-a716-********%'
            ORDER BY u.username
            "#
        ).fetch_all(&self.pool).await?;

        let mut results = Vec::new();
        for row in rows {
            results.push((
                row.get::<String, _>("用户姓名"),
                row.get::<String, _>("角色"),
                row.get::<String, _>("租户/域"),
            ));
        }

        Ok(results)
    }

    /// 打印测试账号信息
    pub fn print_test_account_info(&self) {
        println!("🎉 多租户权限测试数据初始化完成！");
        println!("==========================================");
        println!("📋 测试账号信息:");
        println!("系统管理员: admin / password123");
        println!("深圳实验学校校长: principal_sz / password123");
        println!("深圳实验学校教导主任: director_sz / password123");
        println!("深圳实验学校学科组长: math_leader_sz / password123");
        println!("深圳实验学校班主任: class_teacher_sz / password123");
        println!("深圳实验学校任课老师: teacher_sz / password123");
        println!("深圳实验学校学生: student_sz / password123");
        println!("北京四中校长: principal_bj / password123");
        println!();
        println!("📝 下一步操作建议:");
        println!("1. 启动后端服务: cd backend && cargo run");
        println!("2. 启动前端服务: cd frontend && npm run dev");
        println!("3. 使用测试账号登录系统验证权限");
        println!("4. 运行权限测试: cargo test test_permissions");
        println!();
        println!("⚠️  注意事项:");
        println!("- 所有测试用户的默认密码都是 'password123'");
        println!("- 测试数据仅用于开发和测试环境");
        println!("- 生产环境请删除所有测试数据");
        println!("==========================================");
    }
}

// 测试模块
#[cfg(test)]
mod tests {
    use super::*;

    /// 测试数据库连接
    #[tokio::test]
    async fn test_database_connection() {
        let initializer = TestDataInitializer::new().await.unwrap();
        let result = initializer.check_database_connection().await;
        assert!(result.is_ok(), "数据库连接失败");
    }

    /// 测试必要表结构检查
    #[tokio::test]
    async fn test_required_tables_check() {
        let initializer = TestDataInitializer::new().await.unwrap();
        let result = initializer.check_required_tables().await;
        assert!(result.is_ok(), "必要表结构检查失败");
    }

    /// 测试Casbin权限策略检查和初始化
    #[tokio::test]
    async fn test_casbin_policy_initialization() {
        let mut initializer = TestDataInitializer::new().await.unwrap();
        let result = initializer.check_and_initialize_casbin_policies().await;
        assert!(result.is_ok(), "Casbin权限策略初始化失败");
    }

    /// 测试完整的测试数据初始化流程
    #[tokio::test]
    async fn test_full_initialization() {
        let mut initializer = TestDataInitializer::new().await.unwrap();
        let result = initializer.run_full_initialization().await;
        assert!(result.is_ok(), "完整测试数据初始化失败");
        
        // 验证统计信息
        assert!(initializer.stats.tenants_created > 0, "未创建租户");
        assert!(initializer.stats.users_created > 0, "未创建用户");
        assert!(initializer.stats.policies_created > 0, "未创建权限策略");
    }

    /// 测试租户信息获取
    #[tokio::test]
    async fn test_get_tenant_info() {
        let mut initializer = TestDataInitializer::new().await.unwrap();
        let _ = initializer.run_full_initialization().await;
        
        let tenant_info = initializer.get_tenant_info().await.unwrap();
        assert!(!tenant_info.is_empty(), "未获取到租户信息");
        
        println!("租户信息:");
        for (name, code, schema, status) in tenant_info {
            println!("学校: {}, 代码: {}, 模式: {}, 状态: {}", name, code, schema, status);
        }
    }

    /// 测试校长权限验证
    #[tokio::test]
    async fn test_principal_permission_verification() {
        let mut initializer = TestDataInitializer::new().await.unwrap();
        let _ = initializer.run_full_initialization().await;
        
        let permissions = initializer.verify_principal_permissions().await.unwrap();
        assert!(!permissions.is_empty(), "校长权限验证失败");
        
        println!("校长权限:");
        for perm in permissions {
            println!("角色: {}, 资源: {}, 操作: {}, 效果: {}", 
                perm.role, perm.resource, perm.action, perm.effect);
        }
    }

    /// 测试学生权限验证
    #[tokio::test]
    async fn test_student_permission_verification() {
        let mut initializer = TestDataInitializer::new().await.unwrap();
        let _ = initializer.run_full_initialization().await;
        
        let permissions = initializer.verify_student_permissions().await.unwrap();
        assert!(!permissions.is_empty(), "学生权限验证失败");
        
        println!("学生权限:");
        for perm in permissions {
            println!("角色: {}, 资源: {}, 操作: {}, 效果: {}", 
                perm.role, perm.resource, perm.action, perm.effect);
        }
    }

    /// 测试用户角色映射验证
    #[tokio::test]
    async fn test_user_role_mapping_verification() {
        let mut initializer = TestDataInitializer::new().await.unwrap();
        let _ = initializer.run_full_initialization().await;
        
        let mappings = initializer.verify_user_role_mappings().await.unwrap();
        assert!(!mappings.is_empty(), "用户角色映射验证失败");
        
        println!("用户角色映射:");
        for (user, role, tenant) in mappings {
            println!("用户: {}, 角色: {}, 租户: {}", user, role, tenant);
        }
    }

    /// 集成测试 - 完整权限测试流程
    #[tokio::test]
    async fn test_complete_permission_workflow() {
        let mut initializer = TestDataInitializer::new().await.unwrap();
        
        // 执行完整初始化
        initializer.run_full_initialization().await.unwrap();
        
        // 验证各种权限
        let principal_perms = initializer.verify_principal_permissions().await.unwrap();
        let student_perms = initializer.verify_student_permissions().await.unwrap();
        let user_mappings = initializer.verify_user_role_mappings().await.unwrap();
        
        // 断言权限存在
        assert!(!principal_perms.is_empty(), "校长权限为空");
        assert!(!student_perms.is_empty(), "学生权限为空");
        assert!(!user_mappings.is_empty(), "用户角色映射为空");
        
        // 打印完整报告
        initializer.print_statistics();
        initializer.print_test_account_info();
        
        println!("🧪 权限验证报告:");
        println!("==========================================");
        println!("校长权限数量: {}", principal_perms.len());
        println!("学生权限数量: {}", student_perms.len());
        println!("用户角色映射数量: {}", user_mappings.len());
        println!("==========================================");
    }
}