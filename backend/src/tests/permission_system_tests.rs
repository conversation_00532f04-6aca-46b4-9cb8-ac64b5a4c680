//! 多租户权限系统集成测试
//! Multi-tenant Permission System Integration Tests
//! 
//! 这个测试模块替代了原来的bash脚本test_permissions.sh
//! 提供了完整的权限系统测试，包括用户认证、API访问、数据库权限验证等

use crate::tests::test_utils::setup_test_db;
use sqlx::{PgPool, Row};
use std::collections::HashMap;
use tokio;
use uuid::Uuid;
use reqwest::{Client, Response};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};

/// 测试用户信息结构体
#[derive(Debug, Clone)]
pub struct TestUser {
    pub username: String,
    pub phone: String,
    pub password: String,
    pub display_name: String,
    pub user_id: Option<Uuid>,
}

/// 登录响应结构体
#[derive(Debug, Deserialize)]
struct LoginResponse {
    success: bool,
    code: i32,
    message: String,
    data: Option<LoginData>,
}

#[derive(Debug, Deserialize)]
struct LoginData {
    token: String,
    user: UserInfo,
}

#[derive(Debug, Deserialize)]
struct UserInfo {
    id: String,
    username: Option<String>,
    phone_number: String,
}

/// API测试结果
#[derive(Debug)]
pub struct ApiTestResult {
    pub endpoint: String,
    pub description: String,
    pub expected_success: bool,
    pub actual_success: bool,
    pub status_code: u16,
    pub passed: bool,
}

/// 权限测试统计
#[derive(Debug, Default,Clone)]
pub struct PermissionTestStats {
    pub login_tests: (i32, i32), // (成功, 总数)
    pub admin_tests: (i32, i32),
    pub tenant_isolation_tests: (i32, i32),
    pub role_hierarchy_tests: (i32, i32),
    pub db_permission_tests: (i32, i32),
    pub cross_tenant_tests: (i32, i32),
    pub role_inheritance_tests: (i32, i32),
}

/// 多租户权限测试器
#[derive(Debug,Clone)]
pub struct PermissionTester {
    pool: PgPool,
    client: Client,
    base_url: String,
    test_users: HashMap<String, TestUser>,
    jwt_tokens: HashMap<String, String>,
    pub(crate) stats: PermissionTestStats,
}

impl PermissionTester {
    /// 创建新的权限测试器
    pub async fn new(base_url: Option<String>) -> Result<Self, Box<dyn std::error::Error>> {
        let pool = setup_test_db().await;
        let client = Client::new();
        let base_url = base_url.unwrap_or_else(|| "http://localhost:8080".to_string());
        
        let mut test_users = HashMap::new();
        
        // 初始化测试用户
        test_users.insert("admin".to_string(), TestUser {
            username: "admin".to_string(),
            phone: "13800000001".to_string(),
            password: "password123".to_string(),
            display_name: "系统管理员".to_string(),
            user_id: Some(Uuid::parse_str("550e8400-e29b-41d4-a716-************")?),
        });
        
        test_users.insert("principal_sz".to_string(), TestUser {
            username: "principal_sz".to_string(),
            phone: "13800000002".to_string(),
            password: "password123".to_string(),
            display_name: "深圳校长".to_string(),
            user_id: Some(Uuid::parse_str("550e8400-e29b-41d4-a716-************")?),
        });
        
        test_users.insert("director_sz".to_string(), TestUser {
            username: "director_sz".to_string(),
            phone: "13800000003".to_string(),
            password: "password123".to_string(),
            display_name: "深圳教导主任".to_string(),
            user_id: Some(Uuid::parse_str("550e8400-e29b-41d4-a716-************")?),
        });
        
        test_users.insert("math_leader_sz".to_string(), TestUser {
            username: "math_leader_sz".to_string(),
            phone: "13800000004".to_string(),
            password: "password123".to_string(),
            display_name: "深圳数学组长".to_string(),
            user_id: Some(Uuid::parse_str("550e8400-e29b-41d4-a716-************")?),
        });
        
        test_users.insert("class_teacher_sz".to_string(), TestUser {
            username: "class_teacher_sz".to_string(),
            phone: "13800000006".to_string(),
            password: "password123".to_string(),
            display_name: "深圳班主任".to_string(),
            user_id: Some(Uuid::parse_str("550e8400-e29b-41d4-a716-************")?),
        });
        
        test_users.insert("teacher_sz".to_string(), TestUser {
            username: "teacher_sz".to_string(),
            phone: "13800000007".to_string(),
            password: "password123".to_string(),
            display_name: "深圳任课老师".to_string(),
            user_id: Some(Uuid::parse_str("550e8400-e29b-41d4-a716-************")?),
        });
        
        test_users.insert("student_sz".to_string(), TestUser {
            username: "student_sz".to_string(),
            phone: "13800000008".to_string(),
            password: "password123".to_string(),
            display_name: "深圳学生".to_string(),
            user_id: Some(Uuid::parse_str("550e8400-e29b-41d4-a716-************")?),
        });
        
        test_users.insert("principal_bj".to_string(), TestUser {
            username: "principal_bj".to_string(),
            phone: "13800000010".to_string(),
            password: "password123".to_string(),
            display_name: "北京校长".to_string(),
            user_id: Some(Uuid::parse_str("550e8400-e29b-41d4-a716-************")?),
        });

        Ok(Self {
            pool,
            client,
            base_url,
            test_users,
            jwt_tokens: HashMap::new(),
            stats: PermissionTestStats::default(),
        })
    }

    /// 运行完整的权限测试套件
    pub async fn run_full_permission_tests(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🧪 开始多租户权限系统测试...");
        println!("API Base URL: {}", self.base_url);
        println!();

        // 检查服务器状态
        self.check_server_health().await?;
        
        // 检查数据库连接
        self.check_database_connection().await?;
        
        // 第一阶段：用户登录测试
        println!("\n========================================");
        println!("第一阶段: 用户登录测试");
        println!("========================================");
        self.test_user_logins().await?;

        // 第二阶段：系统管理员权限测试
        println!("\n========================================");
        println!("第二阶段: 系统管理员权限测试");
        println!("========================================");
        self.test_admin_permissions().await?;

        // 第三阶段：租户权限隔离测试
        println!("\n========================================");
        println!("第三阶段: 租户权限隔离测试");
        println!("========================================");
        self.test_tenant_isolation().await?;

        // 第四阶段：角色层次权限测试
        println!("\n========================================");
        println!("第四阶段: 角色层次权限测试");
        println!("========================================");
        self.test_role_hierarchy().await?;

        // 第五阶段：数据库权限策略验证
        println!("\n========================================");
        println!("第五阶段: 数据库权限策略验证");
        println!("========================================");
        self.test_database_permissions().await?;

        // 第六阶段：跨租户权限隔离验证
        println!("\n========================================");
        println!("第六阶段: 跨租户权限隔离验证");
        println!("========================================");
        self.test_cross_tenant_isolation().await?;

        // 第七阶段：角色继承关系验证
        println!("\n========================================");
        println!("第七阶段: 角色继承关系验证");
        println!("========================================");
        self.test_role_inheritance().await?;

        // 测试总结
        self.print_test_summary().await?;

        Ok(())
    }

    /// 检查服务器健康状态
    async fn check_server_health(&self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🔍 检查API服务器状态...");
        
        let health_url = format!("{}/health", self.base_url);
        let response = self.client.get(&health_url).send().await;
        
        match response {
            Ok(resp) if resp.status().is_success() => {
                println!("✅ API服务器运行正常");
                Ok(())
            }
            Ok(resp) => {
                println!("❌ API服务器响应异常，状态码: {}", resp.status());
                Err("API服务器健康检查失败".into())
            }
            Err(e) => {
                println!("❌ API服务器未运行或无法访问！");
                println!("请确保后端服务正在运行: cd backend && cargo run");
                Err(format!("API服务器连接失败: {}", e).into())
            }
        }
    }

    /// 检查数据库连接
    pub(crate) async fn check_database_connection(&self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🔍 检查数据库连接...");
        
        sqlx::query("SELECT 1")
            .fetch_one(&self.pool)
            .await?;
            
        println!("✅ 数据库连接正常");
        Ok(())
    }

    /// 获取JWT令牌
    async fn get_jwt_token(&mut self, username: &str) -> Result<String, Box<dyn std::error::Error>> {
        let user = self.test_users.get(username)
            .ok_or("用户不存在")?;
        
        println!("🔐 获取 {} 的JWT令牌...", user.display_name);
        
        let login_url = format!("{}/api/v1/auth/login", self.base_url);
        let login_payload = json!({
            "phone": user.phone,
            "password": user.password
        });
        
        let response = self.client
            .post(&login_url)
            .json(&login_payload)
            .send()
            .await?;
        
        let status = response.status();
        let body: Value = response.json().await?;
        
        if status.is_success() {
            if let Some(data) = body.get("data") {
                if let Some(token) = data.get("token") {
                    let token_str = token.as_str()
                        .ok_or("令牌格式错误")?
                        .to_string();
                    
                    self.jwt_tokens.insert(username.to_string(), token_str.clone());
                    println!("✅ {} 登录成功", user.display_name);
                    return Ok(token_str);
                }
            }
        }
        
        println!("❌ {} 登录失败 (状态码: {})", user.display_name, status);
        println!("响应: {}", body);
        Err(format!("{} 登录失败", user.display_name).into())
    }

    /// 测试API访问权限
    async fn test_api_access(
        &self,
        username: &str,
        endpoint: &str,
        description: &str,
        expected_success: bool,
    ) -> Result<ApiTestResult, Box<dyn std::error::Error>> {
        let token = self.jwt_tokens.get(username)
            .ok_or("用户没有有效的JWT令牌")?;
        
        println!("  测试: {}", description);
        
        let url = format!("{}{}", self.base_url, endpoint);
        let response = self.client
            .get(&url)
            .header("Authorization", format!("Bearer {}", token))
            .header("Content-Type", "application/json")
            .send()
            .await?;
        
        let status_code = response.status().as_u16();
        let actual_success = response.status().is_success();
        
        let passed = if expected_success {
            if actual_success {
                println!("  ✅ 访问成功 (状态码: {})", status_code);
                true
            } else {
                println!("  ❌ 访问失败 (状态码: {}) - 期望成功", status_code);
                false
            }
        } else {
            if status_code == 403 || status_code == 401 {
                println!("  ✅ 正确拒绝访问 (状态码: {})", status_code);
                true
            } else {
                println!("  ⚠️  意外的响应 (状态码: {}) - 期望拒绝", status_code);
                false
            }
        };
        
        Ok(ApiTestResult {
            endpoint: endpoint.to_string(),
            description: description.to_string(),
            expected_success,
            actual_success,
            status_code,
            passed,
        })
    }

    /// 测试数据库权限查询
    pub(crate) async fn test_db_permissions(
        &self,
        user_id: &str,
        tenant_id: &str,
        resource: &str,
        action: &str,
        display_name: &str,
    ) -> Result<bool, Box<dyn std::error::Error>> {
        println!("  数据库权限测试: {} 访问 {}:{}", display_name, resource, action);
        
        let query = r#"
            SELECT CASE WHEN EXISTS (
                SELECT 1 FROM public.casbin_policies cp1
                JOIN public.casbin_policies cp2 ON cp1.v1 = cp2.v0 AND cp2.ptype = 'p'
                WHERE cp1.ptype = 'g' 
                AND cp1.v0 = $1
                AND cp2.v1 = $2
                AND cp2.v2 = $3
                AND cp2.v3 = $4
                AND cp2.v4 = 'allow'
            ) THEN 'ALLOW' ELSE 'DENY' END as result
        "#;
        
        let row = sqlx::query(query)
            .bind(user_id)
            .bind(tenant_id)
            .bind(resource)
            .bind(action)
            .fetch_one(&self.pool)
            .await?;
        
        let result: String = row.try_get("result")?;
        let allowed = result == "ALLOW";
        
        if allowed {
            println!("  ✅ 数据库权限: 允许访问");
        } else {
            println!("  ❌ 数据库权限: 拒绝访问");
        }
        
        Ok(allowed)
    }

    /// 测试用户登录
    async fn test_user_logins(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let mut success_count = 0;
        let total_count = self.test_users.len();
        
        for username in self.test_users.keys().cloned().collect::<Vec<_>>() {
            if self.get_jwt_token(&username).await.is_ok() {
                success_count += 1;
            }
        }
        
        self.stats.login_tests = (success_count, total_count as i32);
        println!("\n登录测试结果: {}/{} 用户成功登录", success_count, total_count);
        
        if success_count == 0 {
            return Err("没有用户成功登录，无法继续权限测试".into());
        }
        
        Ok(())
    }

    /// 测试系统管理员权限
    async fn test_admin_permissions(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let mut passed = 0;
        let mut total = 0;
        
        if self.jwt_tokens.contains_key("admin") {
            println!("测试系统管理员权限...");
            
            let tests = vec![
                ("/api/admin/tenants", "系统管理员访问租户管理", true),
                ("/api/v1/users", "系统管理员访问用户列表", true),
                ("/api/v1/roles", "系统管理员访问角色管理", true),
            ];
            
            for (endpoint, description, expected) in tests {
                total += 1;
                if let Ok(result) = self.test_api_access("admin", endpoint, description, expected).await {
                    if result.passed {
                        passed += 1;
                    }
                }
            }
        } else {
            println!("❌ 系统管理员未成功登录，跳过测试");
        }
        
        self.stats.admin_tests = (passed, total);
        Ok(())
    }

    /// 测试租户权限隔离
    async fn test_tenant_isolation(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let mut passed = 0;
        let mut total = 0;
        
        // 测试深圳实验学校校长权限
        if self.jwt_tokens.contains_key("principal_sz") {
            println!("测试深圳实验学校校长权限...");
            
            let tests = vec![
                ("/api/v1/students", "校长访问学生管理", true),
                ("/api/v1/teachers", "校长访问教师管理", true),
                ("/api/v1/exams", "校长访问考试管理", true),
                ("/api/v1/grades", "校长访问成绩管理", true),
                ("/api/admin/tenants", "校长访问租户管理(应拒绝)", false),
            ];
            
            for (endpoint, description, expected) in tests {
                total += 1;
                if let Ok(result) = self.test_api_access("principal_sz", endpoint, description, expected).await {
                    if result.passed {
                        passed += 1;
                    }
                }
            }
        }
        
        // 测试北京四中校长权限
        if self.jwt_tokens.contains_key("principal_bj") {
            println!("测试北京四中校长权限...");
            
            let tests = vec![
                ("/api/v1/students", "校长访问学生管理", true),
                ("/api/v1/teachers", "校长访问教师管理", true),
                ("/api/admin/tenants", "校长访问租户管理(应拒绝)", false),
            ];
            
            for (endpoint, description, expected) in tests {
                total += 1;
                if let Ok(result) = self.test_api_access("principal_bj", endpoint, description, expected).await {
                    if result.passed {
                        passed += 1;
                    }
                }
            }
        }
        
        self.stats.tenant_isolation_tests = (passed, total);
        Ok(())
    }

    /// 测试角色层次权限
    async fn test_role_hierarchy(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let mut passed = 0;
        let mut total = 0;
        
        // 测试教导主任权限
        if self.jwt_tokens.contains_key("director_sz") {
            println!("测试教导主任权限...");
            let tests = vec![
                ("/api/v1/students", "教导主任访问学生管理", true),
                ("/api/v1/exams", "教导主任访问考试管理", true),
                ("/api/v1/teachers", "教导主任访问教师管理", true),
                ("/api/admin/tenants", "教导主任访问租户管理(应拒绝)", false),
            ];
            
            for (endpoint, description, expected) in tests {
                total += 1;
                if let Ok(result) = self.test_api_access("director_sz", endpoint, description, expected).await {
                    if result.passed {
                        passed += 1;
                    }
                }
            }
        }
        
        // 测试学科组长权限
        if self.jwt_tokens.contains_key("math_leader_sz") {
            println!("测试学科组长权限...");
            let tests = vec![
                ("/api/v1/exams", "学科组长访问考试管理", true),
                ("/api/v1/grades", "学科组长访问成绩管理", true),
                ("/api/v1/students", "学科组长访问学生管理(应受限)", true),
                ("/api/admin/tenants", "学科组长访问租户管理(应拒绝)", false),
            ];
            
            for (endpoint, description, expected) in tests {
                total += 1;
                if let Ok(result) = self.test_api_access("math_leader_sz", endpoint, description, expected).await {
                    if result.passed {
                        passed += 1;
                    }
                }
            }
        }
        
        // 测试班主任权限
        if self.jwt_tokens.contains_key("class_teacher_sz") {
            println!("测试班主任权限...");
            let tests = vec![
                ("/api/v1/students", "班主任访问学生管理", true),
                ("/api/v1/grades", "班主任访问成绩管理", true),
                ("/api/v1/exams", "班主任访问考试管理(查看)", true),
                ("/api/admin/tenants", "班主任访问租户管理(应拒绝)", false),
            ];
            
            for (endpoint, description, expected) in tests {
                total += 1;
                if let Ok(result) = self.test_api_access("class_teacher_sz", endpoint, description, expected).await {
                    if result.passed {
                        passed += 1;
                    }
                }
            }
        }
        
        // 测试任课老师权限
        if self.jwt_tokens.contains_key("teacher_sz") {
            println!("测试任课老师权限...");
            let tests = vec![
                ("/api/v1/grades", "任课老师访问成绩管理", true),
                ("/api/v1/students", "任课老师访问学生管理(应受限)", true),
                ("/api/v1/exams", "任课老师访问考试管理(查看)", true),
                ("/api/admin/tenants", "任课老师访问租户管理(应拒绝)", false),
            ];
            
            for (endpoint, description, expected) in tests {
                total += 1;
                if let Ok(result) = self.test_api_access("teacher_sz", endpoint, description, expected).await {
                    if result.passed {
                        passed += 1;
                    }
                }
            }
        }
        
        // 测试学生权限
        if self.jwt_tokens.contains_key("student_sz") {
            println!("测试学生权限...");
            let tests = vec![
                ("/api/v1/personal/grades", "学生访问个人成绩", true),
                ("/api/v1/personal/profile", "学生访问个人信息", true),
                ("/api/v1/students", "学生访问学生管理(应拒绝)", false),
                ("/api/v1/teachers", "学生访问教师管理(应拒绝)", false),
                ("/api/admin/tenants", "学生访问租户管理(应拒绝)", false),
            ];
            
            for (endpoint, description, expected) in tests {
                total += 1;
                if let Ok(result) = self.test_api_access("student_sz", endpoint, description, expected).await {
                    if result.passed {
                        passed += 1;
                    }
                }
            }
        }
        
        self.stats.role_hierarchy_tests = (passed, total);
        Ok(())
    }

    /// 测试数据库权限策略
    async fn test_database_permissions(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("验证数据库中的权限策略...");
        
        let mut passed = 0;
        let mut total = 0;
        
        // 校长权限验证
        total += 2;
        if self.test_db_permissions(
            "550e8400-e29b-41d4-a716-************",
            "550e8400-e29b-41d4-a716-446655440001",
            "student",
            "read",
            "深圳校长"
        ).await.unwrap_or(false) {
            passed += 1;
        }
        
        if self.test_db_permissions(
            "550e8400-e29b-41d4-a716-************",
            "550e8400-e29b-41d4-a716-446655440001",
            "exam",
            "manage",
            "深圳校长"
        ).await.unwrap_or(false) {
            passed += 1;
        }
        
        // 学生权限验证
        total += 2;
        if self.test_db_permissions(
            "550e8400-e29b-41d4-a716-************",
            "550e8400-e29b-41d4-a716-446655440001",
            "student_grade",
            "read",
            "深圳学生"
        ).await.unwrap_or(false) {
            passed += 1;
        }
        
        // 这个应该失败（学生不能管理教师）
        if !self.test_db_permissions(
            "550e8400-e29b-41d4-a716-************",
            "550e8400-e29b-41d4-a716-446655440001",
            "teacher",
            "manage",
            "深圳学生(应拒绝)"
        ).await.unwrap_or(true) {
            passed += 1; // 拒绝是正确的结果
        }
        
        self.stats.db_permission_tests = (passed, total);
        Ok(())
    }

    /// 测试跨租户权限隔离
    async fn test_cross_tenant_isolation(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("验证跨租户权限隔离...");
        
        let mut passed = 0;
        let mut total = 1;
        
        // 查询深圳校长在北京四中的权限（应该没有）
        println!("  测试: 深圳校长访问北京四中权限");
        
        let query = r#"
            SELECT COUNT(*) as count FROM public.casbin_policies 
            WHERE ptype = 'g' 
            AND v0 = '550e8400-e29b-41d4-a716-************' 
            AND v2 = '550e8400-e29b-41d4-a716-446655440002'
        "#;
        
        let row = sqlx::query(query)
            .fetch_one(&self.pool)
            .await?;
        
        let count: i64 = row.try_get("count")?;
        
        if count == 0 {
            println!("  ✅ 跨租户权限隔离正常");
            passed += 1;
        } else {
            println!("  ❌ 跨租户权限隔离异常: 发现 {} 条跨租户权限", count);
        }
        
        self.stats.cross_tenant_tests = (passed, total);
        Ok(())
    }

    /// 测试角色继承关系
    async fn test_role_inheritance(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("验证角色继承关系...");
        
        let mut passed = 0;
        let mut total = 1;
        
        // 验证角色继承策略
        println!("  验证角色继承策略存在性...");
        
        let query = r#"
            SELECT COUNT(*) as count FROM public.casbin_policies 
            WHERE ptype = 'g2' 
            AND tenant_id = 'template'
        "#;
        
        let row = sqlx::query(query)
            .fetch_one(&self.pool)
            .await?;
        
        let count: i64 = row.try_get("count")?;
        
        if count > 0 {
            println!("  ✅ 发现 {} 条角色继承关系", count);
            passed += 1;
            
            // 显示角色继承关系详情
            println!("  角色继承关系详情:");
            let inheritance_rows = sqlx::query(
                r#"
                SELECT 
                    v0 as "上级角色",
                    v1 as "下级角色"
                FROM public.casbin_policies 
                WHERE ptype = 'g2' 
                AND tenant_id = 'template'
                ORDER BY v0
                "#
            ).fetch_all(&self.pool).await?;
            
            for row in inheritance_rows {
                let parent_role: String = row.try_get("上级角色")?;
                let child_role: String = row.try_get("下级角色")?;
                println!("    {} -> {}", parent_role, child_role);
            }
        } else {
            println!("  ❌ 未发现角色继承关系");
        }
        
        self.stats.role_inheritance_tests = (passed, total);
        Ok(())
    }

    /// 打印测试总结
    async fn print_test_summary(&self) -> Result<(), Box<dyn std::error::Error>> {
        println!("\n========================================");
        println!("测试总结");
        println!("========================================");
        
        // 统计权限策略
        println!("📊 权限策略统计:");
        let policy_stats = sqlx::query(
            r#"
            SELECT 
                CASE 
                    WHEN tenant_id = 'system' THEN '系统级策略'
                    WHEN tenant_id = 'template' THEN '模板策略'
                    WHEN tenant_id LIKE '550e8400-e29b-41d4-a716-44665544%' THEN '测试租户策略'
                    ELSE '其他策略'
                END as "策略类型",
                ptype as "策略分类",
                COUNT(*) as "数量"
            FROM public.casbin_policies 
            GROUP BY 
                CASE 
                    WHEN tenant_id = 'system' THEN '系统级策略'
                    WHEN tenant_id = 'template' THEN '模板策略'
                    WHEN tenant_id LIKE '550e8400-e29b-41d4-a716-44665544%' THEN '测试租户策略'
                    ELSE '其他策略'
                END,
                ptype
            ORDER BY "策略类型", ptype
            "#
        ).fetch_all(&self.pool).await?;
        
        for row in policy_stats {
            let policy_type: String = row.try_get("策略类型")?;
            let policy_class: String = row.try_get("策略分类")?;
            let count: i64 = row.try_get("数量")?;
            println!("  {}: {} - {} 条", policy_type, policy_class, count);
        }
        
        println!("\n👥 用户角色映射统计:");
        let role_mappings = sqlx::query(
            r#"
            SELECT 
                cp.v1 as "角色",
                COUNT(*) as "用户数量"
            FROM public.casbin_policies cp
            WHERE cp.ptype = 'g'
            AND cp.v0 LIKE '550e8400-e29b-41d4-a716-44665544%'
            GROUP BY cp.v1
            ORDER BY cp.v1
            "#
        ).fetch_all(&self.pool).await?;
        
        for row in role_mappings {
            let role: String = row.try_get("角色")?;
            let count: i64 = row.try_get("用户数量")?;
            println!("  {}: {} 个用户", role, count);
        }
        
        // 打印测试结果统计
        println!("\n🧪 测试结果统计:");
        println!("  登录测试: {}/{}", self.stats.login_tests.0, self.stats.login_tests.1);
        println!("  管理员权限测试: {}/{}", self.stats.admin_tests.0, self.stats.admin_tests.1);
        println!("  租户隔离测试: {}/{}", self.stats.tenant_isolation_tests.0, self.stats.tenant_isolation_tests.1);
        println!("  角色层次测试: {}/{}", self.stats.role_hierarchy_tests.0, self.stats.role_hierarchy_tests.1);
        println!("  数据库权限测试: {}/{}", self.stats.db_permission_tests.0, self.stats.db_permission_tests.1);
        println!("  跨租户隔离测试: {}/{}", self.stats.cross_tenant_tests.0, self.stats.cross_tenant_tests.1);
        println!("  角色继承测试: {}/{}", self.stats.role_inheritance_tests.0, self.stats.role_inheritance_tests.1);
        
        let total_passed = self.stats.login_tests.0 + self.stats.admin_tests.0 + 
                          self.stats.tenant_isolation_tests.0 + self.stats.role_hierarchy_tests.0 + 
                          self.stats.db_permission_tests.0 + self.stats.cross_tenant_tests.0 + 
                          self.stats.role_inheritance_tests.0;
        let total_tests = self.stats.login_tests.1 + self.stats.admin_tests.1 + 
                         self.stats.tenant_isolation_tests.1 + self.stats.role_hierarchy_tests.1 + 
                         self.stats.db_permission_tests.1 + self.stats.cross_tenant_tests.1 + 
                         self.stats.role_inheritance_tests.1;
        
        println!("\n总计: {}/{} 测试通过", total_passed, total_tests);
        
        println!("\n🎉 多租户权限系统测试完成！");
        println!("==========================================");
        println!("测试要点总结:");
        println!("✅ 用户登录认证");
        println!("✅ 系统管理员权限");
        println!("✅ 租户权限隔离");
        println!("✅ 角色层次权限");
        println!("✅ 数据库权限策略");
        println!("✅ 跨租户隔离验证");
        println!("✅ 角色继承关系");
        println!();
        println!("注意事项:");
        println!("- 如果API测试失败，请检查后端服务是否正常运行");
        println!("- 如果数据库测试失败，请检查权限策略是否正确初始化");
        println!("- 部分API端点可能尚未实现，测试失败属正常情况");
        println!("- 建议结合前端界面进行完整的用户体验测试");
        println!("==========================================");
        
        Ok(())
    }
}

// 测试模块
#[cfg(test)]
mod tests {
    use super::*;

    /// 测试服务器健康检查
    #[tokio::test]
    async fn test_server_health_check() {
        let tester = PermissionTester::new(Some("http://localhost:8080".to_string()))
            .await
            .unwrap();
        
        // 注意：这个测试需要后端服务运行，在CI环境中可能会失败
        let result = tester.check_server_health().await;
        if result.is_err() {
            println!("⚠️ 服务器健康检查失败，可能后端服务未运行");
        }
    }

    /// 测试数据库连接
    #[tokio::test]
    async fn test_database_connection_check() {
        let tester = PermissionTester::new(None).await.unwrap();
        let result = tester.check_database_connection().await;
        assert!(result.is_ok(), "数据库连接失败");
    }

    /// 测试用户登录功能（模拟）
    #[tokio::test]
    async fn test_user_login_simulation() {
        let mut tester = PermissionTester::new(None).await.unwrap();
        
        // 验证测试用户数据
        assert!(tester.test_users.contains_key("admin"));
        assert!(tester.test_users.contains_key("principal_sz"));
        assert!(tester.test_users.contains_key("student_sz"));
        
        // 验证用户信息
        let admin_user = &tester.test_users["admin"];
        assert_eq!(admin_user.username, "admin");
        assert_eq!(admin_user.phone, "13800000001");
        assert_eq!(admin_user.password, "password123");
    }

    /// 测试数据库权限查询
    #[tokio::test]
    async fn test_database_permission_query() {
        let tester = PermissionTester::new(None).await.unwrap();
        
        // 测试权限查询功能（注意：这需要测试数据已经初始化）
        let result = tester.test_db_permissions(
            "550e8400-e29b-41d4-a716-************", // 深圳校长ID
            "550e8400-e29b-41d4-a716-446655440001", // 深圳实验学校租户ID
            "student",
            "read",
            "深圳校长"
        ).await;
        
        // 如果测试数据未初始化，这个测试可能会失败
        match result {
            Ok(allowed) => println!("权限查询结果: {}", if allowed { "允许" } else { "拒绝" }),
            Err(e) => println!("权限查询失败: {}，可能测试数据未初始化", e),
        }
    }

    /// 测试跨租户权限隔离查询
    #[tokio::test]
    async fn test_cross_tenant_isolation_query() {
        let tester = PermissionTester::new(None).await.unwrap();
        
        let query = r#"
            SELECT COUNT(*) as count FROM public.casbin_policies 
            WHERE ptype = 'g' 
            AND v0 = '550e8400-e29b-41d4-a716-************' 
            AND v2 = '550e8400-e29b-41d4-a716-446655440002'
        "#;
        
        let result = sqlx::query(query)
            .fetch_one(&tester.pool)
            .await;
        
        match result {
            Ok(row) => {
                let count: i64 = row.try_get("count").unwrap_or(0);
                println!("跨租户权限数量: {}", count);
                assert_eq!(count, 0, "不应该存在跨租户权限");
            }
            Err(e) => {
                println!("跨租户权限查询失败: {}", e);
            }
        }
    }

    /// 集成测试 - 完整权限测试流程（需要后端服务运行）
    #[tokio::test]
    #[ignore] // 默认忽略，需要手动运行
    async fn test_full_permission_workflow() {
        let mut tester = PermissionTester::new(Some("http://localhost:8080".to_string()))
            .await
            .unwrap();
        
        // 运行完整的权限测试
        let result = tester.run_full_permission_tests().await;
        
        match result {
            Ok(()) => {
                println!("✅ 完整权限测试流程成功");
                
                // 验证测试统计
                assert!(tester.stats.login_tests.1 > 0, "应该有登录测试");
                
                // 打印测试统计
                println!("测试统计:");
                println!("  登录测试: {}/{}", tester.stats.login_tests.0, tester.stats.login_tests.1);
                println!("  管理员测试: {}/{}", tester.stats.admin_tests.0, tester.stats.admin_tests.1);
                println!("  租户隔离测试: {}/{}", tester.stats.tenant_isolation_tests.0, tester.stats.tenant_isolation_tests.1);
                println!("  角色层次测试: {}/{}", tester.stats.role_hierarchy_tests.0, tester.stats.role_hierarchy_tests.1);
            }
            Err(e) => {
                println!("❌ 完整权限测试流程失败: {}", e);
                println!("请确保：");
                println!("1. 后端服务正在运行 (cargo run)");
                println!("2. 测试数据已经初始化");
                println!("3. 数据库连接正常");
            }
        }
    }

    /// 性能测试 - 权限查询性能基准
    #[tokio::test]
    async fn test_permission_query_performance() {
        let tester = PermissionTester::new(None).await.unwrap();
        
        let start = std::time::Instant::now();
        let iterations = 100;
        
        for _ in 0..iterations {
            let _ = tester.test_db_permissions(
                "550e8400-e29b-41d4-a716-************",
                "550e8400-e29b-41d4-a716-446655440001",
                "student",
                "read",
                "性能测试"
            ).await;
        }
        
        let duration = start.elapsed();
        let avg_time = duration / iterations;
        
        println!("权限查询性能基准:");
        println!("  总时间: {:?}", duration);
        println!("  平均时间: {:?}", avg_time);
        println!("  QPS: {:.2}", 1000.0 / avg_time.as_millis() as f64);
        
        // 基准：平均查询时间应该小于10ms
        assert!(avg_time.as_millis() < 10, "权限查询性能不符合要求");
    }
}