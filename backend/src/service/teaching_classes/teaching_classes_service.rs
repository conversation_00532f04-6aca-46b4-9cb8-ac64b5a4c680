use std::collections::{HashMap, HashSet};

use sqlx::{postgres::PgRow, PgPool, Row};
use uuid::Uuid;

use crate::{
    middleware::auth_middleware::AuthContext,
    model::{
        teaching_classes::teaching_classes::{
            CreateTeachingClassesParams, DeleteTeachingClassesParams, FindAllStudentInClassParams,
            MoveStudentToTeachingClassesParams, RemoveStudentFromTeachingClassesParams,
            TeachingClasses, TeachingClassesStatistics, UpdateTeachingClassesParams,
        },
        Student,
    },
};

#[derive(Clone)]
pub struct TeachingClassesService {
    db_pool: PgPool,
}

//教学班级管理服务
impl TeachingClassesService {
    pub fn new(pool: PgPool) -> Self {
        Self { db_pool: pool }
    }

    /**
     * 作者：张瀚
     * 说明：获取用户能查看的教学班列表
     */
    pub async fn get_user_class_list(
        &self,
        schema_name: &String,
        user_id: &Uuid,
    ) -> Result<Vec<TeachingClasses>, String> {
        let mut result: Vec<TeachingClasses> = vec![];
        //自己担任认任课老师的班级列表
        let mut teacher_class_list = self.find_teacher_class_list(schema_name, user_id).await?;
        result.append(&mut teacher_class_list);
        //自己担任临时班主任的班级列表 TODO
        Ok(result)
    }

    /**
     * 作者：张瀚
     * 说明：获取所有的教学班（管理员功能）
     */
    pub async fn get_all_class_list(
        &self,
        schema_name: &String,
    ) -> Result<Vec<TeachingClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.teaching_classes tc order by tc.is_active desc , tc.created_at desc",
            schema_name
        ));
        builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：查询自己担任班主任的班级列表
     */
    pub async fn find_teacher_class_list(
        &self,
        schema_name: &String,
        teacher_id: &Uuid,
    ) -> Result<Vec<TeachingClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.teaching_classes tc where tc.teacher_id = ",
            schema_name
        ));
        builder.push_bind(teacher_id);
        builder.push(" order by tc.is_active desc , tc.created_at desc");
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：查询指定的多个教学班内所有学生ID列表（去重）
     */
    pub async fn find_all_student_id_set_in_classes(
        &self,
        schema_name: &String,
        classes_id_list: &Vec<Uuid>,
    ) -> Result<HashSet<Uuid>, String> {
        if classes_id_list.len() == 0 {
            return Ok(HashSet::<Uuid>::new());
        }
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select distinct stc.student_id from {}.student_teaching_classes stc where stc.class_id in (",
            schema_name
        ));
        for (index, item) in classes_id_list.iter().enumerate() {
            builder.push_bind(item);
            if index < classes_id_list.len() - 1 {
                builder.push(" , ");
            } else {
                builder.push(" ) ");
            }
        }
        Ok(builder
            .build_query_scalar()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?
            .into_iter()
            .collect())
    }

    /**
     * 作者：张瀚
     * 说明：查询指定编码的班级
     */
    pub async fn find_by_code(
        &self,
        schema_name: &String,
        code: &String,
    ) -> Result<TeachingClasses, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.teaching_classes tc where tc.code =  ",
            schema_name
        ));
        builder.push_bind(code);
        Ok(builder
            .build_query_as()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：获取教学班统计数据
     */
    pub async fn get_statistics(
        &self,
        context: &AuthContext,
        tenant_id: &Option<Uuid>,
        schema_name: &String,
    ) -> Result<TeachingClassesStatistics, String> {
        //获取能查看的班级列表
        let class_list = match context.is_admin_in_tenant(tenant_id.clone()) {
            true => self.get_all_class_list(schema_name).await?,
            false => {
                self.get_user_class_list(schema_name, &context.user_id)
                    .await?
            }
        };
        //统计
        let mut teacher_id_set = HashSet::<Uuid>::new();
        let mut class_id_set = HashSet::<Uuid>::new();
        for classes in &class_list {
            if classes.teacher_id.is_some() {
                teacher_id_set.insert(classes.teacher_id.unwrap().clone());
            }
            class_id_set.insert(classes.id.clone());
        }
        let student_id_set = self
            .find_all_student_id_set_in_classes(schema_name, &class_id_set.into_iter().collect())
            .await
            .map_err(|e| e.to_string())?;
        let data = TeachingClassesStatistics {
            total_classes: class_list.len() as i32,
            total_teacher: teacher_id_set.len() as i32,
            total_students: student_id_set.len() as i32,
        };
        Ok(data)
    }

    /**
     * 作者：张瀚
     * 说明：创建行政班
     */
    pub async fn create_classes(
        &self,
        schema_name: &String,
        params: &CreateTeachingClassesParams,
    ) -> Result<TeachingClasses, String> {
        //权限校验TODO
        //创建班级
        let CreateTeachingClassesParams {
            class_name,
            code,
            academic_year,
            subject_group_id,
            teacher_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "INSERT INTO {}.teaching_classes (class_name,code,academic_year,subject_group_id,teacher_id) VALUES (",
            schema_name
        ));
        builder
            .push_bind(class_name)
            .push(" , ")
            .push_bind(code)
            .push(" , ")
            .push_bind(academic_year)
            .push(" , ")
            .push_bind(subject_group_id)
            .push(" , ")
            .push_bind(teacher_id)
            .push(" ) returning *");
        Ok(builder
            .build_query_as()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：更新班级
     */
    pub async fn update_classes(
        &self,
        schema_name: &String,
        params: &UpdateTeachingClassesParams,
    ) -> Result<TeachingClasses, String> {
        let UpdateTeachingClassesParams {
            id,
            class_name,
            code,
            academic_year,
            subject_group_id,
            teacher_id,
            is_active,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "UPDATE {}.teaching_classes SET class_name = ",
            schema_name
        ));
        builder
            .push_bind(class_name)
            .push(" , code = ")
            .push_bind(code)
            .push(" , academic_year = ")
            .push_bind(academic_year)
            .push(" , subject_group_id = ")
            .push_bind(subject_group_id)
            .push(" , teacher_id = ")
            .push_bind(teacher_id)
            .push(" , is_active = ")
            .push_bind(is_active)
            .push(" , updated_at = now() where id = ")
            .push_bind(id)
            .push("  returning * ");
        builder
            .build_query_as()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：删除班级
     */
    pub async fn delete_class(
        &self,
        schema_name: &String,
        params: &DeleteTeachingClassesParams,
    ) -> Result<(), String> {
        let DeleteTeachingClassesParams { class_id } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "update {}.teaching_classes set is_active = false where id = ",
            schema_name
        ));
        builder.push_bind(class_id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：查询教学班内所有学生信息
     */
    pub async fn find_all_student_in_class(
        &self,
        schema_name: &String,
        params: &FindAllStudentInClassParams,
    ) -> Result<Vec<Student>, String> {
        let FindAllStudentInClassParams { class_id } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.* from {}.students s ,{}.student_teaching_classes stc where stc.class_id = ",
            schema_name, schema_name
        ));
        builder
            .push_bind(class_id)
            .push("  and stc.student_id = s.id order by s.created_at desc ");
        builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：把学生移动到行政班内
     */
    pub async fn move_student_to_teaching_classes(
        &self,
        schema_name: &String,
        params: &MoveStudentToTeachingClassesParams,
    ) -> Result<(), String> {
        let MoveStudentToTeachingClassesParams {
            class_id,
            student_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "INSERT INTO {}.student_teaching_classes (student_id,class_id) VALUES ( ",
            schema_name
        ));
        builder
            .push_bind(student_id)
            .push(" , ")
            .push_bind(class_id)
            .push(" ) ");
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：把学生从行政班中移除
     */
    pub async fn remove_student_from_teaching_classes(
        &self,
        schema_name: &String,
        params: &RemoveStudentFromTeachingClassesParams,
    ) -> Result<(), String> {
        let RemoveStudentFromTeachingClassesParams {
            class_id,
            student_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "DELETE FROM {}.student_teaching_classes WHERE student_id = ",
            schema_name
        ));
        builder
            .push_bind(student_id)
            .push(" and class_id = ")
            .push_bind(class_id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：批量统计指定教学班级中的学生数量
     */
    pub async fn batch_count_by_class(
        &self,
        schema_name: &str,
        class_ids: &Vec<Uuid>,
    ) -> Result<HashMap<Uuid, i64>, String> {
        if class_ids.len() == 0 {
            return Ok(HashMap::<Uuid, i64>::new());
        }
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.class_id,count(*) from {}.student_teaching_classes s where s.class_id in (",
            schema_name
        ));
        for (index, id) in class_ids.iter().enumerate() {
            builder.push_bind(id);
            if index < class_ids.len() - 1 {
                builder.push(" , ");
            } else {
                builder.push(" ) group by s.class_id");
            }
        }
        let list: Vec<PgRow> = builder
            .build()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?;
        let mut map = HashMap::<Uuid, i64>::new();
        for ele in list.iter() {
            let id: Uuid = ele.try_get("class_id").map_err(|e| e.to_string())?;
            let count: i64 = ele.try_get("count").map_err(|e| e.to_string())?;
            map.insert(id, count);
        }
        Ok(map)
    }
}
