use crate::model::paper::paper::CreatePaperRequest;
use crate::model::teaching_aids::textbook_paper::CreateTextbookPaperRequest;
use crate::model::textbooks::*;
use crate::service::paper::paper::PaperService;
use crate::service::storage::StorageService;
use crate::service::teaching_aids::textbook_paper_service::TextbookPaperService;
use crate::utils::error_handler::AppResult;
use chrono::Utc;
use serde_json::Value;
use sqlx::{PgPool, Postgres, Transaction};
use std::sync::Arc;
use uuid::Uuid;

#[derive(Clone)]
pub struct TeachingAidsService {
    pool: PgPool,
    storage_service: Arc<dyn StorageService>,
    paper_service: Arc<PaperService>,
    textbook_paper_service: Arc<TextbookPaperService>,
}

impl TeachingAidsService {
    pub fn new(
        pool: PgPool,
        storage_service: Arc<dyn StorageService>,
        paper_service: Arc<PaperService>,
        textbook_paper_service: Arc<TextbookPaperService>,
    ) -> Self {
        Self {
            pool,
            storage_service,
            paper_service,
            textbook_paper_service,
        }
    }

    pub async fn create_textbook(&self, request: Textbook) -> AppResult<Textbook> {
        let textbook = sqlx::query_as!(
            Textbook,
            r#"
            INSERT INTO public.textbooks (id, title, subject_id, grade_level_id, publisher, publication_year, isbn, version, status, creator_id, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
            "#,
            request.id,
            request.title,
            request.subject_id,
            request.grade_level_id,
            request.publisher,
            request.publication_year,
            request.isbn,
            request.version,
            request.status,
            request.creator_id,
            request.created_at,
            request.updated_at
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(textbook)
    }

    pub async fn get_textbook(&self, id: Uuid) -> AppResult<TextbookVO> {
        let textbook = sqlx::query_as!(
            TextbookVO,
            r#"
                    SELECT t.*, s.name AS subject, g.name AS grade_level
                    FROM public.textbooks AS t
                    LEFT JOIN public.subjects AS s ON t.subject_id = s.id
                    LEFT JOIN public.grade_levels AS g ON t.grade_level_id = g.id
                    WHERE t.id = $1"#,
            id
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or_else(|| anyhow::anyhow!("Textbook not found"))?;

        Ok(textbook)
    }

    pub async fn get_textbooks(&self) -> AppResult<Vec<TextbookVO>> {
        // 直接映射到TextbookVO，通过SQL别名确保字段与结构体字段匹配
        let textbooks = sqlx::query_as!(
                TextbookVO,
                r#"
                SELECT
                    t.id AS "id!: Uuid",
                    t.title AS "title!: String",
                    t.subject_id,
                    s.name AS subject,
                    t.grade_level_id,
                    g.name AS grade_level,
                    t.publisher,
                    t.publication_year,
                    t.isbn,
                    t.version,
                    t.status,
                    t.creator_id,
                    t.created_at,
                    t.updated_at
                FROM public.textbooks AS t
                LEFT JOIN public.subjects AS s ON t.subject_id = s.id
                LEFT JOIN public.grade_levels AS g ON t.grade_level_id = g.id
                "#
            )
            .fetch_all(&self.pool)
            .await?;

        Ok(textbooks)
    }


    pub async fn update_textbook(&self, id: Uuid, request: Textbook) -> AppResult<Textbook> {
        let now = Utc::now();

        let textbook = sqlx::query_as!(
            Textbook,
            r#"
            UPDATE public.textbooks
            SET title = $2, subject_id = $3, grade_level_id = $4, publisher = $5, publication_year = $6, isbn = $7, version = $8, status = $9, creator_id = $10, updated_at = $11
            WHERE id = $1
            RETURNING *
            "#,
            id,
            request.title,
            request.subject_id,
            request.grade_level_id,
            request.publisher,
            request.publication_year,
            request.isbn,
            request.version,
            request.status,
            request.creator_id,
            now
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or_else(|| anyhow::anyhow!("Textbook not found"))?;

        Ok(textbook)
    }

    pub async fn delete_textbook(&self, id: Uuid) -> AppResult<()> {
        let result = sqlx::query!("DELETE FROM public.textbooks WHERE id = $1", id)
            .execute(&self.pool)
            .await?;

        if result.rows_affected() == 0 {
            return Err(anyhow::anyhow!("Textbook not found"));
        }

        Ok(())
    }

    pub async fn create_textbook_with_chapters(
        &self,
        textbook: Textbook,
        chapters: Vec<TeachingAidChapter2>,
        answer_sheets: Vec<(String, Value)>,
    ) -> AppResult<Textbook> {
        let mut tx = self.pool.begin().await?;

        let created_textbook = sqlx::query_as!(
            Textbook,
            r#"
            INSERT INTO public.textbooks (id, title, subject_id, grade_level_id, publisher, publication_year, isbn, version, status, creator_id, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
            "#,
            textbook.id,
            textbook.title,
            textbook.subject_id,
            textbook.grade_level_id,
            textbook.publisher,
            textbook.publication_year,
            textbook.isbn,
            textbook.version,
            textbook.status,
            textbook.creator_id,
            textbook.created_at,
            textbook.updated_at
        )
            .fetch_one(&mut *tx)
            .await?;

        for chapter in chapters {
            sqlx::query!(
                r#"
                INSERT INTO public.chapters (id, textbook_id, parent_id, chapter_number, title, content, metadata, creator_id, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                "#,
                chapter.id,
                textbook.id,
                chapter.parent_id,
                chapter.chapter_number,
                chapter.title,
                chapter.content,
                chapter.metadata,
                chapter.creator_id,
                chapter.created_at,
                chapter.updated_at,
            )
                .execute(&mut *tx)
                .await?;
        }

        let mut couter = 0;
        for (title, content) in answer_sheets {
            let create_paper_req = CreatePaperRequest {
                paper_name: title,
                paper_content: Some(content),
            };
            let new_paper = self.paper_service.create_paper_tx(&mut tx, create_paper_req).await?;

            let create_textbook_paper_req = CreateTextbookPaperRequest {
                paper_id: new_paper.id,
                textbook_id: created_textbook.id,
                serial_number: couter,
            };
            self.textbook_paper_service
                .create_textbook_paper_tx(&mut tx, create_textbook_paper_req)
                .await?;
            couter += 1;
        }

        tx.commit().await?;

        Ok(created_textbook)
    }

    pub async fn get_chapters_by_textbook_id(
        &self,
        textbook_id: Uuid,
    ) -> AppResult<Vec<TeachingAidChapter2>> {
        let chapters = sqlx::query_as!(
            TeachingAidChapter2, "SELECT id, textbook_id, parent_id, chapter_number, title, description, content, metadata, creator_id, created_at, updated_at FROM public.chapters WHERE textbook_id = $1",
            textbook_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(chapters)
    }
}
