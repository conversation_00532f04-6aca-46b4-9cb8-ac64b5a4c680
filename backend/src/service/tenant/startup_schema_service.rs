use anyhow::Result;
use sqlx::PgPool;
use tracing::{info, warn, error};
use std::collections::HashSet;

/// 启动时的 Schema 服务
/// 专门处理程序启动时 schema 不存在的问题
pub struct StartupSchemaService {
    pool: PgPool,
    template_path: String,
}

impl StartupSchemaService {
    pub fn new(pool: PgPool, template_path: Option<String>) -> Self {
        Self {
            pool,
            template_path: template_path.unwrap_or_else(|| "tenants/template/init_tenant_schema.migrations_temp".to_string()),
        }
    }

    /// 启动时检查和创建必要的 schema
    /// 这个方法在程序启动时调用，确保编译时需要的 schema 存在
    pub async fn ensure_startup_schemas(&self) -> Result<()> {
        info!("🔍 Checking startup schemas...");
        
        // 1. 获取所有已注册的租户
        let tenants = self.get_all_tenants().await?;
        info!("Found {} registered tenants", tenants.len());
        
        if tenants.is_empty() {
            warn!("No tenants found in database. Creating default tenant schema for development...");
            self.create_default_tenant_for_development().await?;
            return Ok(());
        }
        
        // 2. 检查每个租户的 schema 是否存在
        let existing_schemas = self.get_existing_schemas().await?;
        let mut missing_schemas = Vec::new();
        
        for tenant in &tenants {
            if !existing_schemas.contains(&tenant.schema_name) {
                missing_schemas.push(tenant.schema_name.clone());
            }
        }
        
        if missing_schemas.is_empty() {
            info!("✅ All tenant schemas exist");
            return Ok(());
        }
        
        // 3. 创建缺失的 schema
        warn!("Found {} missing schemas: {:?}", missing_schemas.len(), missing_schemas);
        
        for schema_name in missing_schemas {
            match self.create_schema_from_template(&schema_name).await {
                Ok(_) => {
                    info!("✅ Created schema: {}", schema_name);
                }
                Err(e) => {
                    error!("❌ Failed to create schema {}: {}", schema_name, e);
                    // 继续处理其他 schema，不因为一个失败而停止
                }
            }
        }
        
        info!("✅ Startup schema check completed");
        Ok(())
    }

    /// 为开发环境创建默认租户
    async fn create_default_tenant_for_development(&self) -> Result<()> {
        let default_schema = "tenant_dev";
        
        // 检查是否已存在
        if self.schema_exists(default_schema).await? {
            info!("Default development schema already exists: {}", default_schema);
            return Ok(());
        }
        
        info!("Creating default development tenant schema: {}", default_schema);
        
        let mut tx = self.pool.begin().await?;
        
        // 1. 创建租户记录
        let tenant_id = uuid::Uuid::new_v4();
        sqlx::query!(
            r#"
            INSERT INTO public.tenants (id, name, tenant_type, schema_name, created_at)
            VALUES ($1, $2, $3, $4, NOW())
            ON CONFLICT (schema_name) DO NOTHING
            "#,
            tenant_id,
            "Development Tenant",
            "development",
            default_schema
        )
        .execute(&mut *tx)
        .await?;
        
        // 2. 创建 schema
        sqlx::query(&format!("CREATE SCHEMA IF NOT EXISTS \"{}\"", default_schema))
            .execute(&mut *tx)
            .await?;
        
        // 3. 初始化表结构
        self.execute_template_sql(&mut tx, default_schema).await?;
        
        tx.commit().await?;
        
        info!("✅ Created default development tenant: {}", default_schema);
        Ok(())
    }

    /// 从模板创建 schema
    async fn create_schema_from_template(&self, schema_name: &str) -> Result<()> {
        self.validate_schema_name(schema_name)?;
        
        let mut tx = self.pool.begin().await?;
        
        // 1. 创建 schema
        sqlx::query(&format!("CREATE SCHEMA IF NOT EXISTS \"{}\"", schema_name))
            .execute(&mut *tx)
            .await?;
        
        // 2. 执行模板 SQL
        self.execute_template_sql(&mut tx, schema_name).await?;
        
        tx.commit().await?;
        Ok(())
    }

    /// 执行模板 SQL
    async fn execute_template_sql(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        schema_name: &str,
    ) -> Result<()> {
        // 读取模板文件
        let template_content = tokio::fs::read_to_string(&self.template_path).await
            .map_err(|e| anyhow::anyhow!("Failed to read template file {}: {}", self.template_path, e))?;
        
        // 替换占位符
        let sql = template_content.replace("{schema}", schema_name);
        
        // 分割并执行多个 SQL 语句
        for statement in sql.split(';') {
            let trimmed = statement.trim();
            if !trimmed.is_empty() && !trimmed.starts_with("--") {
                sqlx::query(trimmed).execute(&mut **tx).await
                    .map_err(|e| anyhow::anyhow!("Failed to execute SQL statement '{}': {}", trimmed, e))?;
            }
        }
        
        Ok(())
    }

    /// 获取所有租户信息
    async fn get_all_tenants(&self) -> Result<Vec<TenantInfo>> {
        let tenants = sqlx::query_as!(
            TenantInfo,
            "SELECT name, schema_name FROM public.tenants WHERE schema_name IS NOT NULL"
        )
        .fetch_all(&self.pool)
        .await
        .unwrap_or_else(|e| {
            warn!("Failed to fetch tenants, assuming empty: {}", e);
            Vec::new()
        });
        
        Ok(tenants)
    }

    /// 获取现有的 schema 列表
    async fn get_existing_schemas(&self) -> Result<HashSet<String>> {
        let rows = sqlx::query!(
            r#"
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'public')
            AND schema_name LIKE 'tenant_%'
            "#
        )
        .fetch_all(&self.pool)
        .await?;
        
        let schemas = rows.into_iter()
            .filter_map(|row| row.schema_name)
            .collect();
        
        Ok(schemas)
    }

    /// 检查单个 schema 是否存在
    async fn schema_exists(&self, schema_name: &str) -> Result<bool> {
        let exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = $1)",
            schema_name
        )
        .fetch_one(&self.pool)
        .await?;
        
        Ok(exists.unwrap_or(false))
    }

    /// 验证 schema 名称
    fn validate_schema_name(&self, schema_name: &str) -> Result<()> {
        use regex::Regex;
        use once_cell::sync::Lazy;
        
        static SCHEMA_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"^[a-z0-9_]+$").unwrap());
        
        if !SCHEMA_REGEX.is_match(schema_name) {
            return Err(anyhow::anyhow!("Invalid schema name: {}", schema_name));
        }
        Ok(())
    }

    /// 创建编译时需要的测试 schema
    /// 这个方法专门用于解决编译时 sqlx::query! 宏的问题
    pub async fn create_compile_time_schemas(&self) -> Result<()> {
        info!("🔧 Creating compile-time schemas for sqlx macro validation...");
        
        let compile_schemas = vec![
            "tenant_compile_test",  // 用于编译时验证的测试 schema
            "tenant_dev",           // 开发环境默认 schema
        ];
        
        for schema_name in compile_schemas {
            if !self.schema_exists(schema_name).await? {
                info!("Creating compile-time schema: {}", schema_name);
                self.create_schema_from_template(schema_name).await?;
            }
        }
        
        info!("✅ Compile-time schemas ready");
        Ok(())
    }

    /// 清理开发环境的测试数据
    pub async fn cleanup_development_data(&self) -> Result<()> {
        if std::env::var("RUST_ENV").unwrap_or_default() != "development" {
            return Ok(());
        }
        
        info!("🧹 Cleaning up development test data...");
        
        // 清理测试 schema 中的数据，但保留表结构
        let test_schemas = vec!["tenant_compile_test", "tenant_dev"];
        
        for schema_name in test_schemas {
            if self.schema_exists(schema_name).await? {
                let tables = vec!["students", "teachers", "courses", "classes", "exams", "exam_results", "learning_records"];
                
                for table in tables {
                    let sql = format!("TRUNCATE TABLE \"{}\".\"{}\" RESTART IDENTITY CASCADE", schema_name, table);
                    sqlx::query(&sql).execute(&self.pool).await.ok(); // 忽略错误
                }
            }
        }
        
        info!("✅ Development data cleanup completed");
        Ok(())
    }
}

#[derive(Debug)]
struct TenantInfo {
    name: String,
    schema_name: String,
}

/// 启动时的 Schema 初始化器
pub struct StartupSchemaInitializer;

impl StartupSchemaInitializer {
    /// 在程序启动时调用，确保编译时需要的 schema 存在
    pub async fn initialize_for_compilation(pool: &PgPool) -> Result<()> {
        let service = StartupSchemaService::new(pool.clone(), None);
        
        // 1. 创建编译时需要的 schema
        service.create_compile_time_schemas().await?;
        
        // 2. 确保启动时的 schema 存在
        service.ensure_startup_schemas().await?;
        
        // 3. 在开发环境清理测试数据
        service.cleanup_development_data().await?;
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_schema_validation() {
        let pool = PgPool::connect("postgresql://test").await.unwrap();
        let service = StartupSchemaService::new(pool, None);
        
        assert!(service.validate_schema_name("tenant_001").is_ok());
        assert!(service.validate_schema_name("test_schema").is_ok());
        assert!(service.validate_schema_name("tenant-001").is_err());
        assert!(service.validate_schema_name("Tenant_001").is_err());
    }
}
