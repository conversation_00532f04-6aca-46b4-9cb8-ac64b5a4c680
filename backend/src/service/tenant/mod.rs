pub mod tenant_service;
pub mod tenant_data_service;
pub mod startup_schema_service;
pub mod member;
// Re-export commonly used services
pub use tenant_service::TenantService;

// Create placeholder modules for missing services
pub mod compile_safe_query_service {
    use anyhow::Result;

    pub struct CompileSafeQueryService;
    pub struct StudentRow;

    impl CompileSafeQueryService {
        pub fn new() -> Self {
            Self
        }
    }
}

pub mod dynamic_query_service {
    use anyhow::Result;

    pub struct DynamicQueryService;

    impl DynamicQueryService {
        pub fn new() -> Self {
            Self
        }
    }
}
