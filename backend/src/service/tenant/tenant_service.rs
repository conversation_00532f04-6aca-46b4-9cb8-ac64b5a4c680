use crate::model::tenant::tenant::{CreateTenantRequest, Tenant, TenantResponse, UpdateTenantRequest};
use crate::utils::error::{AppError, Result};
use sqlx::{PgPool};
use uuid::Uuid;
use crate::utils::schema::validate_schema_name;

#[derive(Clone)]
pub struct TenantService {
    pool: PgPool,
}

impl TenantService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 创建新租户并初始化其 schema
    pub async fn create_tenant(
        &self,
        request: CreateTenantRequest,
        creator_id: Uuid,
    ) -> Result<TenantResponse> {
        let tenant_id = Uuid::new_v4();
        let schema_name = self.generate_schema_name(&request.schema_name)?;

        // 1. 创建租户记录
        let mut tx = self.pool.begin().await?;
        let tenant_record = sqlx::query!(
            r#"
            INSERT INTO public.tenants (
                id, name, tenant_type, schema_name, domain, status, settings, created_by
            ) VALUES ($1, $2, $3, $4, $5, 'active', $6, $7)
            RETURNING id, name, tenant_type, schema_name, domain, status, settings, created_by, created_at, updated_at
            "#,
            tenant_id,
            request.name,
            request.tenant_type,
            schema_name,
            request.domain,
            request.settings,
            creator_id
        )
        .fetch_one(&mut *tx)
        .await?;

        // 2. 创建租户 schema (在独立的事务中)
        self.create_tenant_schema(&mut tx, &schema_name).await?;

        // 3. 初始化租户表结构 (在独立的事务中)
        self.initialize_tenant_tables(&mut tx, &schema_name).await?;

        tx.commit().await?;

        // 创建 Tenant 结构体
        let tenant = Tenant {
            id: tenant_record.id,
            name: tenant_record.name,
            tenant_type: tenant_record.tenant_type.unwrap_or_default(),
            schema_name: tenant_record.schema_name,
            domain: tenant_record.domain,
            status: tenant_record.status,
            settings: tenant_record.settings,
            created_by: tenant_record.created_by,
            created_at: tenant_record.created_at,
            updated_at: tenant_record.updated_at,
        };

        Ok(tenant.into())
    }

    /// 获取租户列表
    pub async fn get_tenants(&self) -> Result<Vec<TenantResponse>> {
        let tenant_records = sqlx::query!(
            "SELECT id, name, tenant_type, schema_name, domain, status, settings, created_by, created_at, updated_at FROM public.tenants ORDER BY created_at DESC"
        )
        .fetch_all(&self.pool)
        .await?;

        let tenants: Vec<_> = tenant_records
            .into_iter()
            .map(|record| Tenant {
                id: record.id,
                name: record.name,
                tenant_type: record.tenant_type.unwrap_or_default(),
                schema_name: record.schema_name,
                domain: record.domain,
                status: record.status,
                settings: record.settings,
                created_by: record.created_by,
                created_at: record.created_at,
                updated_at: record.updated_at,
            })
            .collect();

        Ok(tenants.into_iter().map(|t| t.into()).collect())
    }

    /// 根据 ID 获取租户
    pub async fn get_tenant_by_id(&self, tenant_id: Uuid) -> Result<TenantResponse> {
        let tenant_record = sqlx::query!(
            "SELECT id, name, tenant_type, schema_name, domain, status, settings, created_by, created_at, updated_at FROM public.tenants WHERE id = $1",
            tenant_id
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or(AppError::NotFound("Tenant not found".to_string()))?;

        let tenant = Tenant {
            id: tenant_record.id,
            name: tenant_record.name,
            tenant_type: tenant_record.tenant_type.unwrap_or_default(),
            schema_name: tenant_record.schema_name,
            domain: tenant_record.domain,
            status: tenant_record.status,
            settings: tenant_record.settings,
            created_by: tenant_record.created_by,
            created_at: tenant_record.created_at,
            updated_at: tenant_record.updated_at,
        };

        Ok(tenant.into())
    }

    /// 根据 schema 名称获取租户
    pub async fn get_tenant_by_schema(&self, schema_name: &str) -> Result<TenantResponse> {
        let tenant_record = sqlx::query!(
            "SELECT id, name, tenant_type, schema_name, domain, status, settings, created_by, created_at, updated_at FROM public.tenants WHERE schema_name = $1",
            schema_name
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or(AppError::NotFound("Tenant not found".to_string()))?;

        let tenant = Tenant {
            id: tenant_record.id,
            name: tenant_record.name,
            tenant_type: tenant_record.tenant_type.unwrap_or_default(),
            schema_name: tenant_record.schema_name,
            domain: tenant_record.domain,
            status: tenant_record.status,
            settings: tenant_record.settings,
            created_by: tenant_record.created_by,
            created_at: tenant_record.created_at,
            updated_at: tenant_record.updated_at,
        };

        Ok(tenant.into())
    }

    /// 更新租户
    pub async fn update_tenant(
        &self,
        tenant_id: Uuid,
        request: UpdateTenantRequest,
    ) -> Result<TenantResponse> {
        let tenant_record = sqlx::query!(
            r#"
            UPDATE public.tenants
            SET name = COALESCE($2, name),
                domain = COALESCE($3, domain),
                status = COALESCE($4, status),
                settings = COALESCE($5, settings),
                tenant_type = COALESCE($6, tenant_type),
                updated_at = NOW()
            WHERE id = $1
            RETURNING id, name, tenant_type, schema_name, domain, status, settings, created_by, created_at, updated_at
            "#,
            tenant_id,
            request.name,
            request.domain,
            request.status,
            request.settings,
            request.tenant_type
        )
        .fetch_optional(&self.pool)
        .await?
        .ok_or(AppError::NotFound("Tenant not found".to_string()))?;

        let tenant = Tenant {
            id: tenant_record.id,
            name: tenant_record.name,
            tenant_type: tenant_record.tenant_type.unwrap_or_default(),
            schema_name: tenant_record.schema_name,
            domain: tenant_record.domain,
            status: tenant_record.status,
            settings: tenant_record.settings,
            created_by: tenant_record.created_by,
            created_at: tenant_record.created_at,
            updated_at: tenant_record.updated_at,
        };

        Ok(tenant.into())
    }

    /// 删除租户（软删除）
    pub async fn delete_tenant(&self, tenant_id: Uuid) -> Result<()> {
        let result = sqlx::query!(
            "UPDATE public.tenants SET status = 'deleted', updated_at = NOW() WHERE id = $1",
            tenant_id
        )
        .execute(&self.pool)
        .await?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound("Tenant not found".to_string()).into());
        }

        Ok(())
    }

    /// 生成 schema 名称
    fn generate_schema_name(&self, tenant_code: &str) -> Result<String> {
        validate_schema_name(tenant_code)?;
        Ok(format!("tenant_{}", tenant_code))
    }

    /// 验证schema名称是否安全
    fn is_valid_schema_name(schema_name: &str) -> bool {
        // 检查长度
        if schema_name.len() < 3 || schema_name.len() > 63 {
            return false;
        }

        // 检查字符：只允许小写字母、数字、下划线
        if !schema_name.chars().all(|c| c.is_ascii_lowercase() || c.is_ascii_digit() || c == '_') {
            return false;
        }

        // 必须以字母开头
        if !schema_name.chars().next().map_or(false, |c| c.is_ascii_lowercase()) {
            return false;
        }

        // 不能以下划线结尾
        if schema_name.ends_with('_') {
            return false;
        }

        // 不能包含连续的下划线
        if schema_name.contains("__") {
            return false;
        }

        true
    }

    /// 创建租户 schema
    async fn create_tenant_schema(
        &self,
        conn: &mut sqlx::PgConnection,
        schema_name: &str,
    ) -> Result<()> {
        // 验证schema名称安全性
        if !Self::is_valid_schema_name(schema_name) {
            return Err(AppError::BadRequest("Invalid schema name".to_string()).into());
        }

        // 检查schema是否已存在
        let exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = $1)",
            schema_name
        )
        .fetch_one(&mut *conn)
        .await?;

        if exists.unwrap_or(false) {
            return Err(AppError::Conflict(format!("Schema '{}' already exists", schema_name)).into());
        }

        // 使用参数化查询创建schema（虽然schema名称不能参数化，但我们已经验证了安全性）
        let create_schema_sql = format!(r#"CREATE SCHEMA {}"#, schema_name);

        sqlx::query(&create_schema_sql)
            .execute(&mut *conn)
            .await
            .map_err(|e| anyhow::anyhow!("Failed to create schema: {}", e))?;

        Ok(())
    }

    /// 初始化租户表结构
    async fn initialize_tenant_tables(
        &self,
        conn: &mut sqlx::PgConnection,
        schema_name: &str,
    ) -> Result<()> {
        // 读取租户模板SQL文件
        let template_path = "tenants/template/init_tenant_schema.sql";
        let raw_sql = std::fs::read_to_string(template_path)
            .map_err(|e| AppError::InternalServerError(format!("Failed to read tenant template: {}", e)))?;

        // 替换模板中的schema占位符
        let replaced_sql = raw_sql.replace("{schema}", schema_name);

        // 执行SQL批处理
        self.execute_sql_batch(conn, &replaced_sql).await?;

        Ok(())
    }

    /// 执行SQL批处理（在事务中）
    async fn execute_sql_batch(
        &self,
        conn: &mut sqlx::PgConnection,
        sql: &str,
    ) -> Result<()> {
        // 按分号分割SQL语句
        let statements: Vec<&str> = sql
            .split(';')
            .map(|s| s.trim())
            .filter(|s| !s.is_empty())
            .collect();

        for statement in statements {
            // dbg!(&statement);
            if !statement.trim().is_empty() {
                sqlx::query(statement)
                    .execute(&mut *conn)
                    .await
                    .map_err(|e| anyhow::anyhow!("Failed to execute SQL: {} - Error: {}", statement, e))?;
            }
        }

        Ok(())
    }
}
