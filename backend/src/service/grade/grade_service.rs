use anyhow::bail;
use anyhow::Result;
use crate::model::grade::grade::{
    GradeLevel, CreateGradeLevel, UpdateGradeLevel, GradeLevelVO, GradeLevelSummary,
    GradeLevelQueryParams, GradeLevelStatistics, GradeLevelUsageStats, CheckCodeResponse
};
use crate::model::base::PageResult;
use crate::utils::error::AppError;
use sqlx::{PgPool, Postgres, QueryBuilder};
use uuid::Uuid;
use crate::model::{PageParams, SubjectQueryParams, SubjectVO};

#[derive(Clone)]
pub struct GradeService {
    db_pool: PgPool,
}

impl GradeService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    pub async fn get_all_grades(&self) -> Result<Vec<GradeLevel>, AppError> {
        let grades = sqlx::query_as::<_, GradeLevel>(
            "SELECT id, code, name, description, order_level, is_active, created_at, updated_at FROM public.grade_levels WHERE is_active = true ORDER BY order_level"
        )
        .fetch_all(&self.db_pool)
        .await?;
        Ok(grades)
    }

    pub async fn get_grade_by_id(&self, id: Uuid) -> Result<Option<GradeLevelVO>, AppError> {
        let grade = sqlx::query_as::<_, GradeLevelVO>(
            "SELECT 
                g.id, g.code, g.name, g.description, g.order_level, g.is_active, 
                g.created_at, g.updated_at, NULLIF(g.education_stage_code, '') as education_stage_code
            FROM public.grade_levels g WHERE g.id = $1"
        )
        .bind(id)
        .fetch_optional(&self.db_pool)
        .await?;
        Ok(grade)
    }

    pub async fn get_grade_by_code(&self, code: &str) -> Result<Option<GradeLevel>, AppError> {
        let grade = sqlx::query_as::<_, GradeLevel>(
            "SELECT id, code, name, description, order_level, is_active, created_at, updated_at FROM public.grade_levels WHERE code = $1"
        )
        .bind(code)
        .fetch_optional(&self.db_pool)
        .await?;
        Ok(grade)
    }


    pub async fn get_grade_by_name(&self, name: &str) -> Result<Option<GradeLevel>, AppError> {
        let grade = sqlx::query_as::<_, GradeLevel>(
            "SELECT id, code, name, description, order_level, is_active, created_at, updated_at FROM public.grade_levels WHERE name = $1"
        )
        .bind(name)
        .fetch_optional(&self.db_pool)
        .await?;
        Ok(grade)
    }

    pub async fn create_grade(&self, payload: CreateGradeLevel) -> Result<GradeLevel> {
        // Check if code already exists
        if let Some(_) = self.get_grade_by_code(&payload.code).await? {
            bail!("年级编码已经存在");
        }

        let grade = sqlx::query_as::<_, GradeLevel>(
            "INSERT INTO public.grade_levels (code, name, description, order_level, education_stage_code) 
             VALUES ($1, $2, $3, $4, $5) 
             RETURNING id, code, name, description, order_level, is_active, created_at, updated_at"
        )
        .bind(payload.code)
        .bind(payload.name)
        .bind(payload.description)
        .bind(payload.order_level)
        .bind(payload.education_stage_code)
        .fetch_one(&self.db_pool)
        .await?;
        Ok(grade)
    }

    pub async fn update_grade(&self, id: Uuid, payload: UpdateGradeLevel) -> Result<GradeLevel> {
        let mut query = "
            UPDATE public.grade_levels 
            SET name = COALESCE($1, name), 
                description = COALESCE($2, description), 
                order_level = COALESCE($3, order_level), 
                is_active = COALESCE($4, is_active), 
                education_stage_code = CASE WHEN $5::text IS DISTINCT FROM 'NULL' THEN $5::text::varchar ELSE education_stage_code END,
                updated_at = NOW() 
            WHERE id = $6 
            RETURNING id, code, name, description, order_level, is_active, created_at, updated_at";

        let grade = sqlx::query_as::<_, GradeLevel>(query)
            .bind(payload.name)
            .bind(payload.description)
            .bind(payload.order_level)
            .bind(payload.is_active)
            .bind(payload.education_stage_code)
            .bind(id)
            .fetch_one(&self.db_pool)
            .await?;
        Ok(grade)
    }

    pub async fn delete_grade(&self, id: Uuid) -> Result<(), AppError> {
        // Soft delete by setting is_active to false
        sqlx::query("UPDATE public.grade_levels SET is_active = false, updated_at = NOW() WHERE id = $1")
            .bind(id)
            .execute(&self.db_pool)
            .await?;
        Ok(())
    }

    // New methods required by the controller

    /// 获取年级列表（支持分页和查询）
    pub async fn get_grades(&self, params: GradeLevelQueryParams) -> Result<PageResult<GradeLevelVO>, AppError> {
        // 内部函数 构造公共的查询内容，传入的是build引用
        fn build_where_clause(
            builder: &mut QueryBuilder<Postgres>,
            params: &GradeLevelQueryParams,
        ) {
            let mut conditions = Vec::new();
            
            if let Some(is_active) = params.is_active {
                conditions.push(format!("g.is_active = {}", is_active));
            }
            
            if let Some(search) = &params.search {
                if !search.trim().is_empty() {
                    let pattern = format!("%{}%", search.trim());
                    conditions.push(format!("(g.name ILIKE '{}' OR g.code ILIKE '{}')", pattern, pattern));
                }
            }
            
            if let Some(stage_code) = &params.stage_code {
                if !stage_code.trim().is_empty() {
                    conditions.push(format!("g.education_stage_code = '{}'", stage_code.trim()));
                }
            }
            
            if !conditions.is_empty() {
                builder.push(" WHERE ");
                builder.push(conditions.join(" AND "));
            }
        }

        // 构建 COUNT 查询
        let mut count_builder =
            QueryBuilder::new("SELECT COUNT(*) as total FROM public.grade_levels g");
        build_where_clause(&mut count_builder, &params);

        // 构建主查询
        let mut query_builder = QueryBuilder::new(
            "SELECT
                g.id, g.code, g.name, g.description, g.order_level, g.is_active,
                g.created_at, g.updated_at, 
                NULLIF(g.education_stage_code, '') as education_stage_code
            FROM public.grade_levels g",
        );
        build_where_clause(&mut query_builder, &params);

        // 添加排序和分页
        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();
        
        let order_direction = params.order_direction.as_deref().unwrap_or("asc");
        query_builder
            .push(" ORDER BY g.order_level ")
            .push(order_direction)
            .push(" LIMIT ")
            .push_bind(page_size)
            .push(" OFFSET ")
            .push_bind(offset);
        
        // 执行查询
        let total: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await?;
        let grades: Vec<GradeLevelVO> = query_builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await?;

        Ok(PageResult {
            data: grades,
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
        })
    }

    /// 获取年级简要信息列表（用于下拉选择）
    pub async fn get_grade_summaries(&self, is_active: Option<bool>) -> Result<Vec<GradeLevelSummary>> {
        let query = match is_active {
            Some(active) => {
                sqlx::query_as::<_, GradeLevelSummary>(
                    "SELECT id, code, name, is_active FROM public.grade_levels WHERE is_active = $1 ORDER BY order_level"
                )
                .bind(active)
            },
            None => {
                sqlx::query_as::<_, GradeLevelSummary>(
                    "SELECT id, code, name, is_active FROM public.grade_levels ORDER BY order_level"
                )
            }
        };

        let grades = query.fetch_all(&self.db_pool).await?;
        Ok(grades)
    }

    /// 获取年级统计信息
    pub async fn get_grade_statistics(&self) -> Result<GradeLevelStatistics> {
        let stats = sqlx::query!(
            "SELECT 
                COUNT(*) as total_grades,
                COUNT(CASE WHEN is_active = true THEN 1 END) as active_grades,
                COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_grades
            FROM public.grade_levels"
        )
        .fetch_one(&self.db_pool)
        .await?;

        // Get usage stats for each grade level
        let usage_stats = sqlx::query!(
            "SELECT 
                g.id as grade_id,
                g.name as grade_name,
                0 as student_count,  -- TODO: Add actual count when student table is implemented
                0 as class_count     -- TODO: Add actual count when class table is implemented
            FROM public.grade_levels g
            WHERE g.is_active = true
            ORDER BY g.order_level"
        )
        .fetch_all(&self.db_pool)
        .await?;

        let usage_stats = usage_stats.into_iter().map(|row| {
            GradeLevelUsageStats {
                grade_id: row.grade_id,
                grade_name: row.grade_name,
                student_count: row.student_count.unwrap_or(0) as i64,
                class_count: row.class_count.unwrap_or(0) as i64,
                exam_count: Some(0), // TODO: Add actual exam count when implemented
            }
        }).collect();

        Ok(GradeLevelStatistics {
            total_grades: stats.total_grades.unwrap_or(0),
            active_grades: stats.active_grades.unwrap_or(0),
            inactive_grades: stats.inactive_grades.unwrap_or(0),
            usage_stats,
        })
    }

    /// 批量更新年级排序
    pub async fn update_grade_orders(&self, orders: Vec<(Uuid, i32)>) -> Result<(), AppError> {
        let mut tx = self.db_pool.begin().await?;

        for (grade_id, order_level) in orders {
            sqlx::query("UPDATE public.grade_levels SET order_level = $2, updated_at = NOW() WHERE id = $1")
                .bind(grade_id)
                .bind(order_level)
                .execute(&mut *tx)
                .await?;
        }

        tx.commit().await?;
        Ok(())
    }

    /// 检查年级代码是否可用
    pub async fn is_code_available(&self, code: &str, exclude_id: Option<Uuid>) -> Result<bool, AppError> {
        let query = match exclude_id {
            Some(id) => {
                sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM public.grade_levels WHERE code = $1 AND id != $2")
                    .bind(code)
                    .bind(id)
            },
            None => {
                sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM public.grade_levels WHERE code = $1")
                    .bind(code)
            }
        };

        let count = query.fetch_one(&self.db_pool).await?;
        Ok(count == 0)
    }
}