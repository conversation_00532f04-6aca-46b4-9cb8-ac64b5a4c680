use std::collections::HashSet;

use sqlx::PgPool;
use tower::builder;
use uuid::Uuid;

use crate::{
    middleware::auth_middleware::AuthContext,
    model::{
        administrative_classes::administrative_classes::{
            AdministrativeClasses, AdministrativeClassesStatistics,
            CreateAdministrativeClassesParams, DeleteAdministrativeClassesParams,
            FindAllStudentInClassParams, MoveStudentToAdministrativeClassesParams,
            RemoveStudentFromAdministrativeClassesParams, UpdateAdministrativeClassesParams,
        },
        Student,
    },
};

#[derive(Clone)]
pub struct AdministrativeClassesService {
    db_pool: PgPool,
}

//行政班级管理服务
impl AdministrativeClassesService {
    pub fn new(pool: PgPool) -> Self {
        Self { db_pool: pool }
    }

    /**
     * 作者：张瀚
     * 说明：获取用户能查看的行政班列表(不包含管理员权限)
     */
    pub async fn get_user_class_list(
        &self,
        schema_name: &String,
        user_id: &Uuid,
    ) -> Result<Vec<AdministrativeClasses>, String> {
        let mut result: Vec<AdministrativeClasses> = vec![];
        //自己担任班主任的班级列表
        let mut head_teacher_class_list = self
            .find_head_teacher_class_list(schema_name, user_id)
            .await?;
        result.append(&mut head_teacher_class_list);
        //自己担任临时班主任的班级列表 TODO
        Ok(result)
    }

    /**
     * 作者：张瀚
     * 说明：查询所有行政班（管理员功能）
     */
    pub async fn get_all_class_list(
        &self,
        schema_name: &String,
    ) -> Result<Vec<AdministrativeClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac order by ac.is_active desc , ac.created_at desc ",
            schema_name
        ));
        builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：查询自己担任班主任的班级列表
     */
    pub async fn find_head_teacher_class_list(
        &self,
        schema_name: &String,
        teacher_id: &Uuid,
    ) -> Result<Vec<AdministrativeClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac where ac.teacher_id = ",
            schema_name
        ));
        builder.push_bind(teacher_id);
        builder.push(" order by ac.is_active desc , ac.created_at desc ");
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：查询班级内的学生的ID列表
     */
    pub async fn find_student_id_list_in_classes(
        &self,
        schema_name: &String,
        classes_id: &Uuid,
    ) -> Result<Vec<Uuid>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.id from {}.students s where s.administrative_class_id = ",
            schema_name
        ));
        builder.push_bind(classes_id);
        Ok(builder
            .build_query_scalar()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：查询指定编码的班级
     */
    pub async fn find_all_by_code(
        &self,
        schema_name: &String,
        code: &String,
    ) -> Result<Vec<AdministrativeClasses>, String> {
        let mut builder = sqlx::QueryBuilder::new(format!(
            "SELECT * from {}.administrative_classes ac where ac.code =  ",
            schema_name
        ));
        builder.push_bind(code);
        Ok(builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：获取行政班统计数据
     */
    pub async fn get_statistics(
        &self,
        context: &AuthContext,
        tenant_id: &Option<Uuid>,
        schema_name: &String,
        user_id: &Uuid,
    ) -> Result<AdministrativeClassesStatistics, String> {
        //获取能查看的班级列表
        let class_list = match context.is_admin_in_tenant(tenant_id.clone()) {
            true => self.get_all_class_list(schema_name).await?,
            false => self.get_user_class_list(schema_name, user_id).await?,
        };
        //统计班主任ID
        let mut teacher_id_set = HashSet::<Uuid>::new();
        let mut student_id_set = HashSet::<String>::new();
        for classes in &class_list {
            if classes.teacher_id.is_some() {
                teacher_id_set.insert(classes.teacher_id.clone().unwrap());
            }
            //查询班内学生ID列表
            let student_id_list = self
                .find_student_id_list_in_classes(schema_name, &classes.id)
                .await?;
            student_id_list.iter().for_each(|student_id| {
                student_id_set.insert(student_id.clone().to_string());
            });
        }
        let data = AdministrativeClassesStatistics {
            total_classes: class_list.len() as i32,
            total_teacher: teacher_id_set.len() as i32,
            total_students: student_id_set.len() as i32,
        };
        Ok(data)
    }
    /**
     * 作者：张瀚
     * 说明：创建行政班
     */
    pub async fn create_classes(
        &self,
        schema_name: &String,
        user_id: &Uuid,
        params: &CreateAdministrativeClassesParams,
    ) -> Result<AdministrativeClasses, String> {
        //权限校验TODO
        //创建班级
        let CreateAdministrativeClassesParams {
            class_name,
            code,
            academic_year,
            grade_level_code,
            teacher_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "INSERT INTO {}.administrative_classes (class_name,code,academic_year,grade_level_code,teacher_id) VALUES (",
            schema_name
        ));
        builder
            .push_bind(class_name)
            .push(" , ")
            .push_bind(code)
            .push(" , ")
            .push_bind(academic_year)
            .push(" , ")
            .push_bind(grade_level_code)
            .push(" , ")
            .push_bind(teacher_id)
            .push(" ) returning *");
        Ok(builder
            .build_query_as()
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| e.to_string())?)
    }

    /**
     * 作者：张瀚
     * 说明：查询行政班内所有学生信息
     */
    pub async fn find_all_student_in_class(
        &self,
        schema_name: &String,
        params: &FindAllStudentInClassParams,
    ) -> Result<Vec<Student>, String> {
        let FindAllStudentInClassParams { class_id } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "select s.* from {}.students s where s.administrative_class_id = ",
            schema_name
        ));
        builder
            .push_bind(class_id)
            .push(" order by s.created_at desc ");
        builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：编辑班级
     */
    pub async fn update_classes(
        &self,
        schema_name: &String,
        params: &UpdateAdministrativeClassesParams,
    ) -> Result<(), String> {
        let UpdateAdministrativeClassesParams {
            id,
            class_name,
            code,
            academic_year,
            grade_level_code,
            teacher_id,
            is_active,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "update {}.administrative_classes ac set ",
            schema_name
        ));
        builder
            .push("class_name = ")
            .push_bind(class_name)
            .push(" , code = ")
            .push_bind(code)
            .push(" , academic_year = ")
            .push_bind(academic_year)
            .push(" , grade_level_code =")
            .push_bind(grade_level_code)
            .push(" , teacher_id = ")
            .push_bind(teacher_id)
            .push(" , is_active = ")
            .push_bind(is_active)
            .push(" , updated_at = now() where id = ")
            .push_bind(id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：把学生移动到行政班内
     */
    pub async fn move_student_to_administrative_classes(
        &self,
        schema_name: &String,
        params: &MoveStudentToAdministrativeClassesParams,
    ) -> Result<(), String> {
        let MoveStudentToAdministrativeClassesParams {
            class_id,
            student_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "update {}.students set administrative_class_id = ",
            schema_name
        ));
        builder
            .push_bind(class_id)
            .push(" where id = ")
            .push_bind(student_id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：把学生从行政班中移除
     */
    pub async fn remove_student_from_administrative_classes(
        &self,
        schema_name: &String,
        params: &RemoveStudentFromAdministrativeClassesParams,
    ) -> Result<(), String> {
        let RemoveStudentFromAdministrativeClassesParams {
            class_id,
            student_id,
        } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "update {}.students set administrative_class_id = null ",
            schema_name
        ));
        builder
            .push(" where id = ")
            .push_bind(student_id)
            .push(" and administrative_class_id = ")
            .push_bind(class_id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }

    /**
     * 作者：张瀚
     * 说明：删除班级
     */
    pub async fn delete_class(
        &self,
        schema_name: &String,
        params: &DeleteAdministrativeClassesParams,
    ) -> Result<(), String> {
        let DeleteAdministrativeClassesParams { class_id } = params;
        let mut builder = sqlx::QueryBuilder::new(format!(
            "update {}.administrative_class set is_active = false where id = ",
            schema_name
        ));
        builder.push_bind(class_id);
        builder
            .build()
            .execute(&self.db_pool)
            .await
            .map_err(|e| e.to_string())
            .map(|_| ())
    }
}
