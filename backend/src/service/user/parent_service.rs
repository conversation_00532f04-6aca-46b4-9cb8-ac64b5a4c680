use crate::model::user::auth::*;
use chrono::{DateTime, Utc};
use sqlx::{PgPool, Row};
use tracing::{info, warn};
use uuid::Uuid;

pub struct ParentService {
    db: PgPool,
}

impl ParentService {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    pub async fn link_student(
        &self,
        parent_user_id: Uuid,
        request: LinkStudentRequest,
    ) -> AuthResult<LinkStudentResponse> {
        // Find student by phone number
        let student_user = self.find_user_by_phone(&request.student_phone_number).await?
            .ok_or(AuthError::UserNotFound)?;

        // Find student's identity in the specified tenant
        let student_identity = self.find_student_identity(
            student_user.id,
            request.tenant_id,
        ).await?
        .ok_or(AuthError::IdentityNotFound)?;

        // Check if relationship already exists
        if self.relationship_exists(parent_user_id, student_user.id, request.tenant_id).await? {
            return Err(AuthError::IdentityAlreadyBound);
        }

        // Create parent-student relationship
        let relation_id = Uuid::new_v4();
        let now = Utc::now();

        // Default access permissions for parents
        let default_permissions = serde_json::json!({
            "view_grades": true,
            "view_attendance": true,
            "receive_notifications": true,
            "contact_teachers": false,
            "view_homework": true,
            "view_exam_results": true
        });

        sqlx::query!(
            r#"
            INSERT INTO public.parent_student_relations 
            (id, parent_user_id, student_user_id, student_tenant_id, student_identity_id,
             relationship_type, verification_status, verification_method, verification_data,
             access_permissions, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            "#,
            relation_id,
            parent_user_id,
            student_user.id,
            request.tenant_id,
            student_identity.id,
            request.relationship_type,
            "pending",
            request.verification_method,
            request.additional_info,
            default_permissions,
            now,
            now
        )
        .execute(&self.db)
        .await?;

        // Create verification steps based on method
        let verification_steps = self.create_verification_steps(&request.verification_method);

        info!(
            "Parent-student relationship created: parent={}, student={}, relation_id={}",
            parent_user_id, student_user.id, relation_id
        );

        Ok(LinkStudentResponse {
            success: true,
            message: "Student link request submitted".to_string(),
            data: LinkStudentData {
                relation_id,
                verification_status: "pending".to_string(),
                verification_steps,
            },
        })
    }

    pub async fn get_linked_students(&self, parent_user_id: Uuid) -> AuthResult<LinkedStudentsResponse> {
        let relations = sqlx::query!(
            r#"
            SELECT 
                psr.id as relation_id,
                psr.student_user_id,
                psr.student_tenant_id as tenant_id,
                psr.relationship_type,
                psr.verification_status,
                psr.access_permissions,
                psr.verified_at,
                u.phone_number as student_phone,
                u.username as student_name,
                t.name as tenant_name
            FROM public.parent_student_relations psr
            JOIN public.users u ON psr.student_user_id = u.id
            JOIN public.tenants t ON psr.student_tenant_id = t.id
            WHERE psr.parent_user_id = $1 AND psr.is_active = true
            ORDER BY psr.created_at DESC
            "#,
            parent_user_id
        )
        .fetch_all(&self.db)
        .await?;

        let students: Vec<LinkedStudent> = relations
            .into_iter()
            .map(|row| LinkedStudent {
                relation_id: row.relation_id,
                student_user_id: row.student_user_id,
                student_name: row.student_name,
                tenant_id: row.tenant_id,
                tenant_name: row.tenant_name,
                relationship_type: row.relationship_type,
                verification_status: row.verification_status.unwrap_or_default(),
                access_permissions: row.access_permissions,
                verified_at: row.verified_at,
            })
            .collect();

        Ok(LinkedStudentsResponse {
            success: true,
            data: LinkedStudentsData { students },
        })
    }

    pub async fn verify_parent_student_relationship(
        &self,
        relation_id: Uuid,
        verified_by: Uuid,
        verification_method: &str,
    ) -> AuthResult<()> {
        let now = Utc::now();

        sqlx::query!(
            r#"
            UPDATE public.parent_student_relations 
            SET verification_status = 'verified', verified_by = $1, verified_at = $2, 
                verification_method = $3, updated_at = $4
            WHERE id = $5
            "#,
            verified_by,
            now,
            verification_method,
            now,
            relation_id
        )
        .execute(&self.db)
        .await?;

        info!("Parent-student relationship verified: relation_id={}, verified_by={}", 
              relation_id, verified_by);

        Ok(())
    }

    pub async fn update_access_permissions(
        &self,
        relation_id: Uuid,
        permissions: serde_json::Value,
    ) -> AuthResult<()> {
        sqlx::query!(
            r#"
            UPDATE public.parent_student_relations 
            SET access_permissions = $1, updated_at = $2
            WHERE id = $3
            "#,
            permissions,
            Utc::now(),
            relation_id
        )
        .execute(&self.db)
        .await?;

        Ok(())
    }

    pub async fn get_parent_permissions(
        &self,
        parent_user_id: Uuid,
        student_user_id: Uuid,
        tenant_id: Uuid,
    ) -> AuthResult<Option<serde_json::Value>> {
        let relation = sqlx::query!(
            r#"
            SELECT access_permissions FROM public.parent_student_relations 
            WHERE parent_user_id = $1 AND student_user_id = $2 AND student_tenant_id = $3 
            AND verification_status = 'verified' AND is_active = true
            "#,
            parent_user_id,
            student_user_id,
            tenant_id
        )
        .fetch_optional(&self.db)
        .await?;

        Ok(relation.map(|r| r.access_permissions).flatten())
    }

    pub async fn deactivate_relationship(&self, relation_id: Uuid) -> AuthResult<()> {
        sqlx::query!(
            r#"
            UPDATE public.parent_student_relations 
            SET is_active = false, updated_at = $1
            WHERE id = $2
            "#,
            Utc::now(),
            relation_id
        )
        .execute(&self.db)
        .await?;

        info!("Parent-student relationship deactivated: relation_id={}", relation_id);
        Ok(())
    }

    pub async fn get_student_parents(
        &self,
        student_user_id: Uuid,
        tenant_id: Uuid,
    ) -> AuthResult<Vec<ParentInfo>> {
        let parents = sqlx::query!(
            r#"
            SELECT 
                psr.id as relation_id,
                psr.parent_user_id,
                psr.relationship_type,
                psr.verification_status,
                psr.verified_at,
                u.phone_number as parent_phone
            FROM public.parent_student_relations psr
            JOIN public.users u ON psr.parent_user_id = u.id
            WHERE psr.student_user_id = $1 AND psr.student_tenant_id = $2 
            AND psr.is_active = true AND psr.verification_status = 'verified'
            ORDER BY psr.created_at ASC
            "#,
            student_user_id,
            tenant_id
        )
        .fetch_all(&self.db)
        .await?;

        let parent_info: Vec<ParentInfo> = parents
            .into_iter()
            .map(|row| ParentInfo {
                relation_id: row.relation_id,
                parent_user_id: row.parent_user_id,
                parent_phone: row.parent_phone,
                relationship_type: row.relationship_type,
                verification_status: row.verification_status.unwrap_or_default(),
                verified_at: row.verified_at,
            })
            .collect();

        Ok(parent_info)
    }

    // Helper methods
    async fn find_user_by_phone(&self, phone: &str) -> AuthResult<Option<User>> {
        let user = sqlx::query(
            "SELECT * FROM public.users WHERE phone_number = $1"
        )
        .bind(phone)
        .fetch_optional(&self.db)
        .await?;

        if let Some(row) = user {
            let user = User {
                id: row.get("id"),
                username: row.get("username"),
                phone_number: row.get("phone_number"),
                phone_verified: row.get("phone_verified"),
                phone_verified_at: row.get("phone_verified_at"),
                password_hash: row.get("password_hash"),
                salt: row.get("salt"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
                last_login_at: row.get("last_login_at"),
                is_active: row.get("is_active"),
                failed_login_attempts: row.get("failed_login_attempts"),
                locked_until: row.get("locked_until"),
            };
            Ok(Some(user))
        } else {
            Ok(None)
        }
    }

    async fn find_student_identity(
        &self,
        user_id: Uuid,
        tenant_id: Uuid,
    ) -> AuthResult<Option<UserIdentity>> {
        // First get the tenant schema name
        let tenant_schema: Option<String> = sqlx::query_scalar(
            "SELECT schema_name FROM public.tenants WHERE id = $1"
        )
        .bind(tenant_id)
        .fetch_optional(&self.db)
        .await?;

        let schema_name = tenant_schema.ok_or(AuthError::IdentityNotFound)?;

        // Query from the tenant schema using PRD-compliant structure
        let query = format!(
            r#"
            SELECT ui.id, ui.user_id, ui.role_id, ui.target_type, ui.target_id, ui.subject,
                   ui.created_at, ui.updated_at
            FROM "{schema_name}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $1 AND r.code = 'student'
            LIMIT 1
            "#
        );

        let identity = sqlx::query(&query)
            .bind(user_id)
            .fetch_optional(&self.db)
            .await?;

        if let Some(row) = identity {
            let identity = UserIdentity {
                id: row.get("id"),
                user_id: row.get("user_id"),
                role_id: row.get("role_id"),
                target_type: row.get("target_type"),
                target_id: row.get("target_id"),
                subject: row.get("subject"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            };
            Ok(Some(identity))
        } else {
            Ok(None)
        }
    }

    async fn relationship_exists(
        &self,
        parent_user_id: Uuid,
        student_user_id: Uuid,
        tenant_id: Uuid,
    ) -> AuthResult<bool> {
        let count: Option<i64> = sqlx::query_scalar!(
            r#"
            SELECT COUNT(*) FROM public.parent_student_relations
            WHERE parent_user_id = $1 AND student_user_id = $2 AND student_tenant_id = $3 AND is_active = true
            "#,
            parent_user_id,
            student_user_id,
            tenant_id
        )
        .fetch_one(&self.db)
        .await?;

        Ok(count.unwrap_or(0) > 0)
    }

    fn create_verification_steps(&self, verification_method: &str) -> Vec<VerificationStep> {
        match verification_method {
            "phone_verification" => vec![
                VerificationStep {
                    step: "student_confirmation".to_string(),
                    description: "Student needs to confirm the relationship".to_string(),
                    status: "pending".to_string(),
                },
                VerificationStep {
                    step: "phone_verification".to_string(),
                    description: "Phone number verification required".to_string(),
                    status: "waiting".to_string(),
                },
            ],
            "admin_approval" => vec![
                VerificationStep {
                    step: "admin_review".to_string(),
                    description: "School administrator approval required".to_string(),
                    status: "pending".to_string(),
                },
            ],
            "document_upload" => vec![
                VerificationStep {
                    step: "document_upload".to_string(),
                    description: "Upload relationship verification documents".to_string(),
                    status: "pending".to_string(),
                },
                VerificationStep {
                    step: "document_review".to_string(),
                    description: "Document review by school administrator".to_string(),
                    status: "waiting".to_string(),
                },
            ],
            _ => vec![
                VerificationStep {
                    step: "manual_verification".to_string(),
                    description: "Manual verification required".to_string(),
                    status: "pending".to_string(),
                },
            ],
        }
    }
}

#[derive(Debug, serde::Serialize)]
pub struct ParentInfo {
    pub relation_id: Uuid,
    pub parent_user_id: Uuid,
    pub parent_phone: String,
    pub relationship_type: String,
    pub verification_status: String,
    pub verified_at: Option<DateTime<Utc>>,
}