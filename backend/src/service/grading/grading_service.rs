use std::path::Path;
use crate::model::tenant::tenant::Tenant;
use anyhow::{Context, Result};
use axum::extract::{Multipart};
use axum::Json;
use chrono::{DateTime, Utc};
use serde_json::Value;
use sqlx::{ PgPool};
use uuid::Uuid;
use crate::utils::http_client::{get_request, post_json, HttpClientError,parse_json_response};
use crate::model::grading::grading::{AIGradingRecord, AIGradingRecordResponse, AnswerCardBlock, AnswerCardBlockResponse, CardBlockGradingRecord, CardBlockGradingRecordResponse, CardBlockQuestionLink, CreateAnswerCardBlocksRequest, GradingAssignment, GradingAssignmentListResponse, GradingAssignmentQueryParams, GradingAssignmentRequest, GradingAssignmentResponse, GradingControlRequest, GradingControlState, GradingControlStateResponse, GradingRecord, GradingRecordResponse, GradingStatistics, GradingStatisticsQueryParams, GradingStatisticsResponse, HandleScanExceptionRequest, OverallGradingStatistics, PaperScan, PaperScanBatchRequest, PaperScanException, PaperScanPagesResponse, PaperScanListResponse, PaperScanPathRequest, PaperScanQueryParams, PaperScanResponse, ProcessAIGradingRequest, ScanExceptionResponse, StudentIdException, SubmitCardBlockGradingRequest, SubmitGradingRecordRequest, PaperScanPages, RecognizeRequest, SheetRequest};
use crate::service::storage::FileInfo;
use crate::service::storage::storage_service::UploadOptions;
use crate::utils::api_response::ApiResponse;
use crate::web_server::AppState;

#[derive(Clone)]
pub struct GradingService {
    pool: PgPool,
}

impl GradingService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    async fn get_schema_name(&self, tenant_id: Uuid) -> Result<String> {
        let tenant = sqlx::query_as::<_, Tenant>("SELECT * FROM public.tenants WHERE id = $1")
            .bind(tenant_id)
            .fetch_one(&self.pool)
            .await?;
        Ok(tenant.schema_name)
    }

    async fn get_tenant_id(&self, schema_name:String) -> Result<Uuid> {
        let tenant = sqlx::query_as::<_, Tenant>("SELECT * FROM public.tenants WHERE schema_name = $1")
            .bind(schema_name)
            .fetch_one(&self.pool)
            .await?;
        Ok(tenant.id)
    }

    /// 将当前文件上传到 Minio 服务端
    pub async fn upload_to_minio(&self,state: AppState, params:PaperScanPathRequest, mut payload: Multipart) -> Result<Vec<FileInfo>,String> {
        let mut uploaded_files:Vec<FileInfo> = Vec::new();
        while let Some(field) = payload.next_field().await
            .map_err(|e|format!("Failed reading multipart field: {}", e))?
        {
            let field_name = field.name().unwrap_or_default().to_string();
            if field_name == "file" {
                // 处理文件字段
                if let Some(file_name) = field.file_name().map(|s| s.to_string()) {
                    let file_id = Uuid::new_v4();
                    let file_name_key = format!("{}_{}",file_id, file_name);
                    let content_type = field.content_type().unwrap_or("application/octet-stream").to_string();
                    let prefix = format!("{}/{}/{}/{}/{}/{}","tenants",params.tenant_name,"grading","paper_scans",params.exam_id,params.batch_no);
                    let bytes = field.bytes().await.map_err(|e| format!("Failed to read file field: {}", e))?;

                    // 上传到 MinIO
                    let  files= state.storage_service.upload(
                        &file_name_key,
                        bytes,
                        UploadOptions { preserve_filename: false, prefix: Some(prefix) },
                    ).await.map_err(|e| format!("Failed to upload file to MinIO: {}", e))?;

                    uploaded_files.push(files);
                }
            }
        }
        Ok(uploaded_files)
    }

    /// 从文件名解析页面类型 (1=正面, 2=背面)
    /// 支持格式: Doc1754030229_17.jpg (正面) 或 Doc1754030229_-17.jpg (背面)
    pub fn parse_page_type_from_filename(&self,filename: &str) -> Result<i32> {
        let path = Path::new(filename);
        let stem = path.file_stem()
            .and_then(|s| s.to_str())
            .context("Invalid filename format: no stem")?;

        // 提取下划线后的数字部分
        let num_str = stem.rsplit('_').next()
            .context("Filename must contain '_' followed by number")?;

        let page_num: i32 = num_str.parse()
            .context("Failed to parse number after '_'")?;

        match page_num {
            n if n > 0 => Ok(1),  // 正数 -> 正面
            n if n < 0 => Ok(2),  // 负数 -> 背面
            _ => anyhow::bail!("Page number cannot be zero") // 0 是非法值
        }
    }

    /// 识别纸张中的作答信息并且提取
    pub async fn recognize_paper_scans(&self,state:AppState,payload: &PaperScanBatchRequest) -> Result<String> {
        let bucket = state.config.minio_config().default_bucket.clone();

        let config = state.config.grading_config();
        let url = format!("{}/sheet/recognize",config.get_endpoint_url());
        println!("url:{}",url);
        let mut sheets:Vec<SheetRequest> = Vec::new();

        let mut sheet:SheetRequest = SheetRequest {
            uuid: payload.id.to_string(),
            bucket,
            front_page_url: "".to_string(),
            back_page_url: None,
        };
        let scan_pages = payload.scans.clone();
        for item in scan_pages {
            //-- 1=正面, 2=反面
            match item.paper_num {
                1 => sheet.front_page_url = item.file_url.clone(),
                2 => sheet.back_page_url = Some(item.file_url.clone()),
                invalid_num => {
                    return Err(anyhow::anyhow!("Invalid page number: {} (must be 1 or 2)", invalid_num));
                }
            }
        }
        sheets.push(sheet);
        let payload:RecognizeRequest = RecognizeRequest {
            mark:"{\"dpi\":200,\"size\":[1653,2338],\"page_num\":2,\"location\":{\"mode\":\"block\",\"data\":{\"anchor\":{\"margin\":[34,74],\"size\":[69,38],\"center_size\":[59,28],\"offset\":[40,40]},\"page\":{\"col\":3,\"size\":[59,59],\"center_size\":[39,39],\"offset\":[40,40],\"pos\":[530,2205]}}},\"number\":{\"mode\":\"fill\",\"data\":[{\"horizontal\":true,\"digits\":7,\"pos\":[900,470],\"page\":1}]},\"fill_question\":{\"mode\":\"fill_block\",\"data\":[{\"block_id\":1,\"contents\":[\"A\",\"B\",\"C\",\"D\"],\"pos\":[145,930],\"page\":1,\"question_item_ids\":[1,2]},{\"block_id\":2,\"contents\":[\"A\",\"B\",\"C\",\"D\"],\"pos\":[450,970],\"page\":1,\"question_item_ids\":[3,4]},{\"block_id\":3,\"contents\":[\"A\",\"B\",\"C\",\"D\"],\"pos\":[730,970],\"page\":1,\"question_item_ids\":[5,6]},{\"block_id\":4,\"contents\":[\"A\",\"B\",\"C\",\"D\"],\"pos\":[1000,970],\"page\":1,\"question_item_ids\":[7,8]},{\"block_id\":5,\"contents\":[\"A\",\"B\",\"C\",\"D\"],\"pos\":[1280,970],\"page\":1,\"question_item_ids\":[9,10]}]},\"handwrite_question\":{\"mode\":\"answer_area\",\"data\":[{\"block_id\":6,\"pos\":[130,1170],\"page\":1,\"question_item_ids\":[11],\"size\":[1360,110]},{\"block_id\":7,\"pos\":[130,1285],\"page\":1,\"question_item_ids\":[12],\"size\":[1360,110]},{\"block_id\":8,\"pos\":[130,1400],\"page\":1,\"question_item_ids\":[13,14],\"size\":[1360,230]},{\"block_id\":9,\"pos\":[130,1515],\"page\":1,\"question_item_ids\":[15],\"size\":[1360,110]},{\"block_id\":12,\"pos\":[120,310],\"page\":2,\"question_item_ids\":[16],\"size\":[1400,210]},{\"block_id\":13,\"pos\":[120,510],\"page\":2,\"question_item_ids\":[17],\"size\":[1400,210]},{\"block_id\":14,\"pos\":[120,720],\"page\":2,\"question_item_ids\":[18],\"size\":[1400,210]},{\"block_id\":15,\"pos\":[120,1360],\"page\":2,\"question_item_ids\":[19],\"size\":[1400,790]}]}}".to_string(),
            sheets,
        };


        let response = post_json(url.as_str(),&payload).await?;
        let create_data:Value = parse_json_response(response).await?;
        println!("create_data:{}",create_data);

        Ok("success".to_string())
    }

    pub async fn save_upload_paper_scans(
        &self,
        schema_name: String,
        request: &PaperScanBatchRequest,
    ) -> Result<Vec<PaperScanPagesResponse>> {
        let mut request = request;
        let tenant_id = self.get_tenant_id(schema_name.clone()).await?;
        let mut tx = self.pool.begin().await?;
        let mut responses = Vec::new();

        // 1. 插入纸张主表信息
        let query = format!(
            r#"
                INSERT INTO {}.paper_scans (
                    id, exam_id, scan_method, scan_device,
                    status, duplicate_num, blank_num,
                    is_abnormal
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8
                ) RETURNING *
                "#,
            schema_name
        );

        let paper_scan = sqlx::query_as::<_, PaperScan>(&query)
            .bind(request.id)
            .bind(request.exam_id)
            .bind(request.scan_method.clone())
            .bind(request.scan_device.clone())
            .bind(request.status.clone())
            .bind(0)
            .bind(0)
            .bind(false)
            .fetch_one(&mut *tx)
            .await?;

        let scans = request.scans.iter().clone();
        for scan_file in scans {

            let query = format!(
                r#"
                INSERT INTO {}.paper_scan_pages (
                    id,paper_scan_id,page_num,file_name, file_url,minio_bucket,minio_object_key, file_size, is_duplicate, is_blank,
                    is_abnormal, abnormal_reason, result,
                    created_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
                ) RETURNING *
                "#,
                schema_name
            );

            let scan = sqlx::query_as::<_, PaperScanPages>(&query)
                .bind(scan_file.id)
                .bind(scan_file.paper_scan_id)
                .bind(scan_file.paper_num)
                .bind(scan_file.file_name.clone())
                .bind(scan_file.file_url.clone())
                .bind(scan_file.minio_bucket.clone())
                .bind(scan_file.minio_object_key.clone())
                .bind(scan_file.file_size)
                .bind(scan_file.is_duplicate)
                .bind(scan_file.is_blank)
                .bind(scan_file.is_abnormal)
                .bind(scan_file.abnormal_reason.clone())
                .bind(scan_file.result.clone())
                .bind(scan_file.create_at)
                .fetch_one(&mut *tx)
                .await?;

            responses.push(self.paper_scan_pages_to_response(tenant_id, scan).await?);
        }

        tx.commit().await?;
        Ok(responses)
    }

    pub async fn get_paper_scan(
        &self,
        tenant_id: Uuid,
        scan_id: Uuid,
    ) -> Result<Option<PaperScanResponse>> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let query = format!("SELECT * FROM {}.paper_scans WHERE id = $1", schema_name);
        let scan = sqlx::query_as::<_, PaperScan>(&query)
            .bind(scan_id)
            .fetch_optional(&self.pool)
            .await?;

        if let Some(scan) = scan {
            Ok(Some(self.paper_scan_to_response(tenant_id, scan).await?))
        } else {
            Ok(None)
        }
    }

    pub async fn list_paper_scans(
        &self,
        tenant_id: Uuid,
        params: PaperScanQueryParams,
    ) -> Result<PaperScanListResponse> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(20);
        let offset = (page - 1) * page_size;

        let mut query = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.paper_scans WHERE 1=1", schema_name));
        let mut count_query = sqlx::QueryBuilder::new(format!("SELECT COUNT(*) FROM {}.paper_scans WHERE 1=1", schema_name));

        if let Some(exam_id) = &params.exam_id {
            query.push(" AND exam_id = ");
            query.push_bind(exam_id);
            count_query.push(" AND exam_id = ");
            count_query.push_bind(exam_id);
        }

        if let Some(student_id) = &params.student_id {
            query.push(" AND student_id = ");
            query.push_bind(student_id);
            count_query.push(" AND student_id = ");
            count_query.push_bind(student_id);
        }

        if let Some(is_abnormal) = &params.is_abnormal {
            query.push(" AND is_abnormal = ");
            query.push_bind(is_abnormal);
            count_query.push(" AND is_abnormal = ");
            count_query.push_bind(is_abnormal);
        }

        if let Some(needs_review) = &params.needs_review {
            query.push(" AND needs_review = ");
            query.push_bind(needs_review);
            count_query.push(" AND needs_review = ");
            count_query.push_bind(needs_review);
        }

        if let Some(min_quality) = &params.min_quality {
            query.push(" AND scan_quality >= ");
            query.push_bind(min_quality);
            count_query.push(" AND scan_quality >= ");
            count_query.push_bind(min_quality);
        }

        query.push(" ORDER BY scan_timestamp DESC LIMIT ");
        query.push_bind(page_size);
        query.push(" OFFSET ");
        query.push_bind(offset);

        let scans = query
            .build_query_as::<PaperScan>()
            .fetch_all(&self.pool)
            .await?;

        let total = count_query
            .build_query_scalar::<i64>()
            .fetch_one(&self.pool)
            .await?;

        let mut scan_responses = Vec::new();
        for scan in scans {
            scan_responses.push(self.paper_scan_to_response(tenant_id, scan).await?);
        }

        Ok(PaperScanListResponse {
            scans: scan_responses,
            total,
            page,
            page_size,
        })
    }

    pub async fn create_answer_card_blocks(
        &self,
        tenant_id: Uuid,
        request: CreateAnswerCardBlocksRequest,
    ) -> Result<Vec<AnswerCardBlockResponse>> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let mut tx = self.pool.begin().await?;
        let mut responses = Vec::new();

        for block_req in request.blocks {
            let block_id = Uuid::new_v4();
            let block_query = format!(
                r#"
                INSERT INTO {}.answer_card_blocks (
                    id, exam_id, block_name, block_type, position_info,
                    template_version, max_score, is_active
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *
                "#,
                schema_name
            );
            let block = sqlx::query_as::<_, AnswerCardBlock>(&block_query)
                .bind(block_id)
                .bind(block_req.exam_id)
                .bind(block_req.block_name)
                .bind(block_req.block_type)
                .bind(block_req.position_info)
                //.bind(block_req.template_version)
                .bind(block_req.max_score)
                //.bind(block_req.is_active)
                .fetch_one(&mut *tx)
                .await?;

            for link_req in &block_req.question_links {
                let link_query = format!(
                    r#"
                    INSERT INTO {}.card_block_question_links (
                        id, card_block_id, question_id, link_type, weight_ratio,
                        score_mapping, is_primary
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                    "#,
                    schema_name
                );
                sqlx::query(&link_query)
                    .bind(Uuid::new_v4())
                    .bind(block_id)
                    .bind(link_req.question_id)
                    //.bind(link_req.link_type)
                    .bind(link_req.weight_ratio)
                    //.bind(link_req.score_mapping)
                    .bind(link_req.is_primary)
                    .execute(&mut *tx)
                    .await?;
            }

            responses.push(self.answer_card_block_to_response(tenant_id, block).await?);
        }

        tx.commit().await?;
        Ok(responses)
    }

    pub async fn assign_grading_tasks(
        &self,
        tenant_id: Uuid,
        request: GradingAssignmentRequest,
    ) -> Result<Vec<GradingAssignmentResponse>> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let mut tx = self.pool.begin().await?;
        let mut responses = Vec::new();

        for assignment_req in request.assignments {
            let assignment_id = Uuid::new_v4();
            let query = format!(
                r#"
                INSERT INTO {}.grading_assignments (
                    id, exam_id, question_id, student_id, grader_user_id,
                    assignment_type, assignment_method, priority_level,
                    estimated_time, status
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 'assigned') RETURNING *
                "#,
                schema_name
            );
            let assignment = sqlx::query_as::<_, GradingAssignment>(&query)
                .bind(assignment_id)
                //.bind(assignment_req.exam_id)
                .bind(assignment_req.question_id)
                //.bind(assignment_req.student_id)
                //.bind(assignment_req.grader_user_id)
                //.bind(assignment_req.assignment_type)
                //.bind(assignment_req.assignment_method)
                //.bind(assignment_req.priority_level)
                //.bind(assignment_req.estimated_time)
                .fetch_one(&mut *tx)
                .await?;

            responses.push(self.grading_assignment_to_response(assignment).await?);
        }

        tx.commit().await?;
        Ok(responses)
    }

    pub async fn get_grading_assignments(
        &self,
        tenant_id: Uuid,
        params: GradingAssignmentQueryParams,
    ) -> Result<GradingAssignmentListResponse> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(20);
        let offset = (page - 1) * page_size;

        let mut query = sqlx::QueryBuilder::new(format!("SELECT * FROM {}.grading_assignments WHERE 1=1", schema_name));
        let mut count_query = sqlx::QueryBuilder::new(format!("SELECT COUNT(*) FROM {}.grading_assignments WHERE 1=1", schema_name));

        if let Some(exam_id) = &params.exam_id {
            query.push(" AND exam_id = ");
            query.push_bind(exam_id);
            count_query.push(" AND exam_id = ");
            count_query.push_bind(exam_id);
        }

        if let Some(grader_user_id) = &params.grader_user_id {
            query.push(" AND grader_user_id = ");
            query.push_bind(grader_user_id);
            count_query.push(" AND grader_user_id = ");
            count_query.push_bind(grader_user_id);
        }

        if let Some(status) = &params.status {
            query.push(" AND status = ");
            query.push_bind(status);
            count_query.push(" AND status = ");
            count_query.push_bind(status);
        }

        if let Some(priority_level) = &params.priority_level {
            query.push(" AND priority_level = ");
            query.push_bind(priority_level);
            count_query.push(" AND priority_level = ");
            count_query.push_bind(priority_level);
        }

        query.push(" ORDER BY priority_level DESC, assigned_at ASC LIMIT ");
        query.push_bind(page_size);
        query.push(" OFFSET ");
        query.push_bind(offset);

        let assignments = query
            .build_query_as::<GradingAssignment>()
            .fetch_all(&self.pool)
            .await?;

        let total = count_query
            .build_query_scalar::<i64>()
            .fetch_one(&self.pool)
            .await?;

        let mut assignment_responses = Vec::new();
        for assignment in assignments {
            assignment_responses.push(self.grading_assignment_to_response(assignment).await?);
        }

        Ok(GradingAssignmentListResponse {
            assignments: assignment_responses,
            total,
            page,
            page_size,
        })
    }

    pub async fn submit_grading_record(
        &self,
        tenant_id: Uuid,
        request: SubmitGradingRecordRequest,
    ) -> Result<GradingRecordResponse> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let mut tx = self.pool.begin().await?;

        let record_id = Uuid::new_v4();
        let record_query = format!(
            r#"
            INSERT INTO {}.grading_records (
                id, exam_id, student_id, question_id, grader_user_id,
                original_score, final_score, grading_method, grading_time,
                grading_notes, is_reviewed
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING *
            "#,
            schema_name
        );
        let record = sqlx::query_as::<_, GradingRecord>(&record_query)
            .bind(record_id)
            //.bind(request.exam_id)
            .bind(request.student_id)
            .bind(request.question_id)
            //.bind(request.grader_user_id)
            //.bind(request.original_score)
            //.bind(request.final_score)
            .bind(request.grading_method)
            //.bind(request.grading_time)
            //.bind(request.grading_notes)
            //.bind(request.is_reviewed)
            .fetch_one(&mut *tx)
            .await?;

        let update_query = format!(
            r#"
            UPDATE {}.grading_assignments
            SET status = 'completed', completed_at = NOW(), actual_time = $1
            WHERE exam_id = $2 AND question_id = $3 AND student_id = $4 AND grader_user_id = $5
            "#,
            schema_name
        );
        sqlx::query(&update_query)
            //.bind(request.grading_time)
            //.bind(request.exam_id)
            .bind(request.question_id)
            .bind(request.student_id)
            //.bind(request.grader_user_id)
            .execute(&mut *tx)
            .await?;

        tx.commit().await?;
        Ok(self.grading_record_to_response(record).await?)
    }

    pub async fn submit_card_block_grading(
        &self,
        tenant_id: Uuid,
        request: SubmitCardBlockGradingRequest,
    ) -> Result<CardBlockGradingRecordResponse> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let mut tx = self.pool.begin().await?;

        let record_id = Uuid::new_v4();
        let query = format!(
            r#"
            INSERT INTO {}.card_block_grading_records (
                id, exam_id, student_id, card_block_id, grader_user_id,
                raw_score, adjusted_score, grading_method, confidence_score,
                grading_notes, quality_level, grading_duration
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12) RETURNING *
            "#,
            schema_name
        );
        let record = sqlx::query_as::<_, CardBlockGradingRecord>(&query)
            .bind(record_id)
            //.bind(request.exam_id)
            .bind(request.student_id)
            .bind(request.card_block_id)
            //.bind(request.grader_user_id)
            //.bind(request.raw_score)
            //.bind(request.adjusted_score)
            .bind(request.grading_method)
            //.bind(request.confidence_score)
            //.bind(request.grading_notes)
            //.bind(request.quality_level)
            //.bind(request.grading_duration)
            .fetch_one(&mut *tx)
            .await?;

        tx.commit().await?;
        Ok(self.card_block_grading_record_to_response(record).await?)
    }

    pub async fn process_ai_grading(
        &self,
        tenant_id: Uuid,
        request: ProcessAIGradingRequest,
    ) -> Result<Vec<AIGradingRecordResponse>> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let mut tx = self.pool.begin().await?;
        let mut responses = Vec::new();

        for ai_req in request.ai_gradings {
            let ai_record_id = Uuid::new_v4();
            let query = format!(
                r#"
                INSERT INTO {}.ai_grading_records (
                    id, exam_id, student_id, question_id, ai_agent_id,
                    ai_model_version, ai_model_result, confidence_score,
                    processing_time, error_message, reviewed_by_human
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING *
                "#,
                schema_name
            );
            let ai_record = sqlx::query_as::<_, AIGradingRecord>(&query)
                .bind(ai_record_id)
                //.bind(ai_req.exam_id)
                .bind(ai_req.student_id)
                .bind(ai_req.question_id)
                //.bind(ai_req.ai_agent_id)
                //.bind(ai_req.ai_model_version)
                //.bind(ai_req.ai_model_result)
                //.bind(ai_req.confidence_score)
                //.bind(ai_req.processing_time)
                //.bind(ai_req.error_message)
                //.bind(ai_req.reviewed_by_human)
                .fetch_one(&mut *tx)
                .await?;

            responses.push(self.ai_grading_record_to_response(ai_record).await?);
        }

        tx.commit().await?;
        Ok(responses)
    }

    pub async fn control_grading_process(
        &self,
        tenant_id: Uuid,
        request: GradingControlRequest,
    ) -> Result<GradingControlStateResponse> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let control_id = Uuid::new_v4();
        let query = format!(
            r#"
            INSERT INTO {}.grading_control_states (
                id, exam_id, question_id, grader_user_id, control_level,
                control_action, control_reason, controlled_by, is_active
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *
            "#,
            schema_name
        );
        let control_state = sqlx::query_as::<_, GradingControlState>(&query)
            .bind(control_id)
            .bind(request.exam_id)
            //.bind(request.question_id)
            //.bind(request.grader_user_id)
            //.bind(request.control_level)
            //.bind(request.control_action)
            //.bind(request.control_reason)
            //.bind(request.controlled_by)
            //.bind(request.is_active)
            .fetch_one(&self.pool)
            .await?;

        Ok(self.grading_control_state_to_response(control_state).await?)
    }

    pub async fn handle_scan_exception(
        &self,
        tenant_id: Uuid,
        request: HandleScanExceptionRequest,
    ) -> Result<ScanExceptionResponse> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let mut tx = self.pool.begin().await?;

        match request.exception_type.as_str() {
            "student_id" => {
                let exception_id = Uuid::new_v4();
                let query = format!(
                    r#"
                    INSERT INTO {}.student_id_exceptions (
                        id, scan_id, detected_student_id, exception_type,
                        suggested_students, confirmed_student_id, confirmed_by,
                        resolution_method, processing_notes
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *
                    "#,
                    schema_name
                );
                let exception = sqlx::query_as::<_, StudentIdException>(&query)
                    .bind(exception_id)
                    .bind(request.scan_id)
                    //.bind(request.detected_student_id)
                    .bind(request.exception_type)
                    //.bind(request.suggested_students)
                    .bind(request.confirmed_student_id)
                    //.bind(request.confirmed_by)
                    //.bind(request.resolution_method)
                    //.bind(request.processing_notes)
                    .fetch_one(&mut *tx)
                    .await?;

                if let Some(confirmed_student_id) = request.confirmed_student_id {
                    let update_query = format!(
                        "UPDATE {}.paper_scans SET student_id = $1 WHERE id = $2",
                        schema_name
                    );
                    sqlx::query(&update_query)
                        .bind(confirmed_student_id)
                        .bind(request.scan_id)
                        .execute(&mut *tx)
                        .await?;
                }

                tx.commit().await?;
                Ok(ScanExceptionResponse {
                    id: exception.id,
                    exception_id: exception.id,
                    scan_id: exception.scan_id,
                    exception_type: exception.exception_type,
                    description: "".to_string() ,//exception.exception_type.clone(),
                    resolution_status: "resolved".to_string(),
                    resolution_notes: exception.processing_notes,
                    resolved_at: Some(Utc::now()),
                    resolved_by: None,
                })
            }
            "paper_scan" => {
                let exception_id = Uuid::new_v4();
                let query = format!(
                    r#"
                    INSERT INTO {}.paper_scan_exceptions (
                        id, scan_id, exception_type, exception_description,
                        auto_detected, confidence_score, resolution_status,
                        resolved_by, resolution_notes
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING *
                    "#,
                    schema_name
                );
                let exception = sqlx::query_as::<_, PaperScanException>(&query)
                    .bind(exception_id)
                    .bind(request.scan_id)
                    .bind(request.exception_type)
                    //.bind(request.exception_description)
                    //.bind(request.auto_detected)
                    //.bind(request.confidence_score)
                    .bind("resolved")
                    //.bind(request.confirmed_by)
                    //.bind(request.processing_notes)
                    .fetch_one(&mut *tx)
                    .await?;

                tx.commit().await?;
                Ok(ScanExceptionResponse {
                    id: exception.id,
                    exception_id: exception.id,
                    scan_id: exception.scan_id,
                    exception_type: exception.exception_type,
                    description: "".to_string(),//exception.exception_type.clone(),
                    resolution_status: exception.resolution_status,
                    resolution_notes: exception.resolution_notes,
                    resolved_at: Some(Utc::now()),
                    resolved_by: None,
                })
            }
            _ => Err(anyhow::anyhow!("Invalid exception type")),
        }
    }

    pub async fn get_grading_statistics(
        &self,
        tenant_id: Uuid,
        params: GradingStatisticsQueryParams,
    ) -> Result<GradingStatisticsResponse> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let mut query = sqlx::QueryBuilder::new(format!(
            "SELECT * FROM {}.grading_statistics WHERE 1=1",
            schema_name
        ));

        if let Some(exam_id) = &params.exam_id {
            query.push(" AND exam_id = ");
            query.push_bind(exam_id);
        }

        if let Some(grader_user_id) = &params.grader_user_id {
            query.push(" AND grader_user_id = ");
            query.push_bind(grader_user_id);
        }

        if let Some(question_id) = &params.question_id {
            query.push(" AND question_id = ");
            query.push_bind(question_id);
        }

        let statistics = query
            .build_query_as::<GradingStatistics>()
            .fetch_all(&self.pool)
            .await?;

        let overall_stats_query = format!(
            r#"
            SELECT
                COUNT(*) as total_assignments,
                COUNT(*) FILTER (WHERE status = 'completed') as completed_assignments,
                COUNT(*) FILTER (WHERE status = 'in_progress') as in_progress_assignments,
                COUNT(*) FILTER (WHERE status = 'assigned') as pending_assignments,
                AVG(actual_time) as avg_grading_time,
                AVG(CASE WHEN actual_time > 0 THEN estimated_time::float / actual_time::float ELSE NULL END) as time_accuracy
            FROM {}.grading_assignments
            WHERE exam_id = $1
            "#,
            schema_name
        );
        let overall_stats: (Option<i64>, Option<i64>, Option<i64>, Option<i64>, Option<f64>, Option<f64>) = sqlx::query_as(&overall_stats_query)
            .bind(params.exam_id)
            .fetch_one(&self.pool)
            .await?;

        Ok(GradingStatisticsResponse {
            exam_id: params.exam_id.unwrap_or(Uuid::new_v4()),
            overall_statistics: OverallGradingStatistics {
                total_questions: overall_stats.0.unwrap_or(0),
                total_graded: overall_stats.1.unwrap_or(0),
                average_consistency: overall_stats.5.unwrap_or(0.0) as f32,
                quality_distribution: serde_json::json!({}),
                grading_method_distribution: serde_json::json!({}),
                time_statistics: serde_json::json!({
                    "avg_grading_time": overall_stats.4.unwrap_or(0.0)
                }),
            },
            question_statistics: vec![], // Convert GradingStatistics to QuestionStatistics
            grader_statistics: vec![], // TODO: Implement grader statistics
        })
    }


    async fn paper_scan_to_response(&self, tenant_id: Uuid, scan: PaperScan)->Result<PaperScanResponse> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let query = format!(
            "SELECT COUNT(*) > 0 FROM {}.paper_scan_exceptions WHERE scan_id = $1",
            schema_name
        );
        let has_exceptions = sqlx::query_scalar::<_, bool>(&query)
            .bind(scan.id)
            .fetch_one(&self.pool)
            .await?;
        // .unwrap_or(false);
        Ok(PaperScanResponse {
            id: scan.id,
            exam_id: scan.exam_id,
            student_id: scan.student_id.unwrap_or_default(),
            student_name: "Unknown".to_string(), // TODO: Load actual student name
            front_page_url: None,
            back_page_url: None,
            scan_quality: 1,
            scan_method: scan.scan_method,
            scan_device: scan.scan_device,
            duplicate_num: scan.duplicate_num,
            blank_num: scan.blank_num,
            is_abnormal: scan.is_abnormal,
            abnormal_reason: scan.abnormal_reason,
            needs_review: scan.needs_review,
            reviewed_by: scan.reviewed_by,
            reviewed_at: scan.reviewed_at,
            file_size: 1,
            scan_timestamp:DateTime::default() ,
            created_at: scan.created_at,
            updated_at: scan.updated_at,
            exceptions: vec![], // TODO: Load actual exceptions
            student_id_exceptions: vec![], // TODO: Load actual student ID exceptions
        })
    }

    async fn paper_scan_pages_to_response(&self, tenant_id: Uuid, paper_scan_pages: PaperScanPages) -> Result<PaperScanPagesResponse> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let query = format!(
            "SELECT COUNT(*) > 0 FROM {}.paper_scan_exceptions WHERE scan_id = $1",
            schema_name
        );

        Ok(PaperScanPagesResponse {
            id: paper_scan_pages.id,
            paper_scan_id: paper_scan_pages.paper_scan_id,
            page_num: paper_scan_pages.page_num,
            file_name:paper_scan_pages.file_name,
            file_url: paper_scan_pages.file_url,
            rectify_url:paper_scan_pages.rectify_url,
            minio_bucket: paper_scan_pages.minio_bucket,
            minio_object_key: paper_scan_pages.minio_object_key,
            file_size: paper_scan_pages.file_size,
            scan_quality: paper_scan_pages.scan_quality,
            is_duplicate: paper_scan_pages.is_duplicate,
            is_blank: paper_scan_pages.is_blank,
            is_abnormal: paper_scan_pages.is_abnormal,
            abnormal_reason: paper_scan_pages.abnormal_reason,
            result: paper_scan_pages.result,
            created_at: paper_scan_pages.created_at,
            updated_at: paper_scan_pages.updated_at,
        })
    }

    async fn answer_card_block_to_response(
        &self,
        tenant_id: Uuid,
        block: AnswerCardBlock,
    ) -> Result<AnswerCardBlockResponse> {
        let schema_name = self.get_schema_name(tenant_id).await?;
        let query = format!(
            "SELECT * FROM {}.card_block_question_links WHERE card_block_id = $1",
            schema_name
        );
        let question_links = sqlx::query_as::<_, CardBlockQuestionLink>(&query)
            .bind(block.id)
            .fetch_all(&self.pool)
            .await?;

        Ok(AnswerCardBlockResponse {
            id: block.id,
            exam_id: block.exam_id,
            block_name: block.block_name,
            block_type: block.block_type,
            position_info: block.position_info,
            template_version: block.template_version,
            max_score: block.max_score,
            is_active: block.is_active,
            created_at: block.created_at,
            updated_at: block.updated_at,
            questions: question_links,
            grading_records: vec![], // TODO: Load actual grading records
        })
    }

    async fn grading_assignment_to_response(
        &self,
        assignment: GradingAssignment,
    ) -> Result<GradingAssignmentResponse> {
        // Parse question_ids from JSON to get count
        let question_count = if let Ok(ids) =
            serde_json::from_value::<Vec<Uuid>>(assignment.question_ids.clone())
        {
            ids.len() as i32
        } else {
            0
        };

        Ok(GradingAssignmentResponse {
            id: assignment.id,
            exam_id: assignment.exam_id,
            grader_id: assignment.grader_id,
            grader_name: "Unknown".to_string(), // TODO: Load actual grader name
            question_count,
            completed_count: 0, // TODO: Calculate actual completed count
            status: assignment.status,
            assigned_at: assignment.assigned_at,
            deadline: assignment.deadline,
        })
    }

    async fn grading_record_to_response(
        &self,
        record: GradingRecord,
    ) -> Result<GradingRecordResponse> {
        Ok(GradingRecordResponse {
            id: record.id,
            assignment_id: record.assignment_id,
            student_id: record.student_id,
            student_name: "Unknown".to_string(), // TODO: Load actual student name
            question_id: record.question_id,
            score: record.score,
            max_score: record.max_score,
            comments: record.comments,
            grading_method: record.grading_method,
            graded_at: record.graded_at,
            graded_by: record.graded_by,
        })
    }

    async fn card_block_grading_record_to_response(
        &self,
        record: CardBlockGradingRecord,
    ) -> Result<CardBlockGradingRecordResponse> {
        Ok(CardBlockGradingRecordResponse {
            id: record.id,
            card_block_id: record.card_block_id,
            student_id: record.student_id,
            student_name: "Unknown".to_string(), // TODO: Load actual student name
            score: record.raw_score,
            max_score: record.raw_score, // Using raw_score as max_score for now
            comments: record.grading_notes,
            grading_method: record.grading_method,
            graded_at: record.created_at,
            graded_by: record.grader_user_id,
        })
    }

    async fn ai_grading_record_to_response(
        &self,
        record: AIGradingRecord,
    ) -> Result<AIGradingRecordResponse> {
        Ok(AIGradingRecordResponse {
            id: record.id,
            question_id: record.question_id,
            student_id: record.student_id,
            ai_score: 0.0, // Default value since field doesn't exist
            confidence: record.confidence_score,
            ai_model: record.ai_model_version.clone(),
            needs_review: record.reviewed_by_human,
            processed_at: record.created_at,
            ai_agent_id: record.ai_agent_id,
            ai_model_version: record.ai_model_version,
            ai_model_result: record.ai_model_result,
            confidence_score: record.confidence_score,
            processing_time: record.processing_time,
            error_message: record.error_message,
            reviewed_by_human: record.reviewed_by_human,
            human_reviewer_id: record.human_reviewer_id,
        })
    }

    async fn grading_control_state_to_response(
        &self,
        state: GradingControlState,
    ) -> Result<GradingControlStateResponse> {
        Ok(GradingControlStateResponse {
            exam_id: state.exam_id,
            current_state: state.control_action.clone(),
            grading_mode: "manual".to_string(), // TODO: Determine actual grading mode
            progress: 0.0, // TODO: Calculate actual progress
            started_at: Some(state.created_at),
            estimated_completion: None, // TODO: Calculate estimated completion
        })
    }
}
