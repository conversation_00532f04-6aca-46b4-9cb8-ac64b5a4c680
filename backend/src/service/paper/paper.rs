use crate::model::paper::paper::{CreatePaperRequest, Paper, UpdatePaperRequest};
use crate::utils::error_handler::AppResult;
use sqlx::{PgPool, Postgres, Transaction};
use uuid::Uuid;

#[derive(Clone)]
pub struct PaperService {
    pool: PgPool,
}

impl PaperService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn get_papers(&self) -> AppResult<Vec<Paper>> {
        let papers = sqlx::query_as!(Paper, "SELECT * FROM public.papers")
            .fetch_all(&self.pool)
            .await?;

        Ok(papers)
    }

    // GET /papers/{id}
    pub async fn get_paper_by_id(&self, id: Uuid) -> AppResult<Paper> {
        let paper = sqlx::query_as!(Paper, "SELECT * FROM public.papers WHERE id = $1", id)
            .fetch_one(&self.pool)
            .await?;
        Ok(paper)
    }

    // POST /papers
    pub async fn create_paper(&self, data: CreatePaperRequest) -> AppResult<Paper> {
        let paper = sqlx::query_as!(
            Paper,
            "INSERT INTO public.papers (paper_name, paper_content) VALUES ($1, $2) RETURNING *",
            data.paper_name,
            data.paper_content,
        )
        .fetch_one(&self.pool)
        .await?;
        Ok(paper)
    }

    pub async fn create_paper_tx<'a>(
        &self,
        tx: &mut Transaction<'a, Postgres>,
        data: CreatePaperRequest,
    ) -> AppResult<Paper> {
        let paper = sqlx::query_as!(
            Paper,
            "INSERT INTO public.papers (paper_name, paper_content) VALUES ($1, $2) RETURNING *",
            data.paper_name,
            data.paper_content,
        )
        .fetch_one(&mut **tx)
        .await?;
        Ok(paper)
    }

    // PUT /papers/{id}
    pub async fn update_paper(&self, id: Uuid, data: UpdatePaperRequest) -> AppResult<Paper> {
        let paper = sqlx::query_as!(
            Paper,
            "UPDATE public.papers SET paper_name = COALESCE($1, paper_name), paper_content = COALESCE($2, paper_content), updated_at = now() WHERE id = $3 RETURNING *",
            data.paper_name,
            data.paper_content,
            id
        )
        .fetch_one(&self.pool)
        .await?;
        Ok(paper)
    }

    // DELETE /papers/{id}
    pub async fn delete_paper(&self, id: Uuid) -> AppResult<()> {
        sqlx::query!("DELETE FROM public.papers WHERE id = $1", id)
            .execute(&self.pool)
            .await?;
        Ok(())
    }
}
