use crate::model::base::PageResult;
use crate::model::subject::{
    CreateSubjectRequest, Subject, SubjectQueryParams, SubjectStatistics, SubjectSummary,
    SubjectUsageStats, SubjectVO, UpdateSubjectRequest,
};
use crate::model::PageParams;
use crate::utils::api_response::{ApiResponse, PaginatedApiResponse};
use anyhow::bail;
use anyhow::Result;
use serde::Deserialize;
use sqlx::{PgPool, Postgres, QueryBuilder, Row};
use uuid::Uuid;

#[derive(Clone)]
pub struct SubjectService {
    db_pool: PgPool,
}

impl SubjectService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }
    /**
     * 作者：张瀚
     * 说明：分页查询学科列表
     */
    pub async fn page_subject(
        &self,
        params: PageSubjectParams,
    ) -> Result<PaginatedApiResponse<Subject>, ApiResponse<()>> {
        let PageSubjectParams {
            page,
            page_size,
            name_or_code_ilike,
            is_active,
            order_by,
            order_direction,
        } = params;
        //初始化参数
        let offset = (page - 1) * page_size;
        let is_active_value = is_active.unwrap_or(true);
        let name_or_code_ilike_temp = name_or_code_ilike.unwrap_or("".to_string());
        let name_or_code_ilike_value = name_or_code_ilike_temp.trim();
        let order_by_temp = order_by.unwrap_or("order_level".to_string());
        let order_by_value = order_by_temp.trim();
        let order_direction_value = order_direction.unwrap_or("asc".to_string());
        // 构建基本查询条件
        let mut where_conditions = vec![format!("s.is_active = {}", is_active_value)];
        // 名字或者编码模糊查询
        if !name_or_code_ilike_value.is_empty() {
            where_conditions.push(format!(
                "(s.name ILIKE '%{}%' OR s.code ILIKE '%{}%')",
                name_or_code_ilike_value, name_or_code_ilike_value
            ));
        }
        let where_clause = if where_conditions.is_empty() {
            "".to_string()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };
        //查询总数
        let total = sqlx::query("SELECT COUNT(*) as total FROM public.subjects s")
            .fetch_one(&self.db_pool)
            .await
            .map_err(|e| {
                ApiResponse::error(format!("总数统计失败，原因：{}", e.to_string()), None)
            })?
            .get("total");
        // 构建排序条件
        let order_clause = match order_by_value {
            "name" => format!("ORDER BY s.name {}", order_direction_value),
            "code" => format!("ORDER BY s.code {}", order_direction_value),
            "created_at" => format!("ORDER BY s.created_at {}", order_direction_value),
            _ => format!("ORDER BY s.order_level {}", order_direction_value),
        };
        //构建分页条件
        let limit_clause = format!("LIMIT {} OFFSET {}", page_size, offset);
        //查询数据
        let data = sqlx::query(&format!(
            "SELECT s.* FROM public.subjects s {} {} {}",
            where_clause, order_clause, limit_clause
        ))
        .fetch_all(&self.db_pool)
        .await
        .map_err(|e| ApiResponse::error(format!("查询数据失败，原因：{}", e.to_string()), None))?
        .into_iter()
        .map(|row| Subject {
            id: row.get("id"),
            code: row.get("code"),
            name: row.get("name"),
            description: row.get("description"),
            order_level: row.get("order_level"),
            is_active: row.get("is_active"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        })
        .collect();
        Ok(PaginatedApiResponse::success(
            data, page, page_size, total, None,
        ))
    }

    /// 获取所有学科（支持分页和查询）
    /**
     * 作者：tuip
     * 说明：分页查询学科列表，采用更安全的builder构筑器，避免SQL注入攻击
     */
    pub async fn get_subjects(&self, params: SubjectQueryParams) -> Result<PageResult<SubjectVO>> {
        // 内部函数 构造公共的查询内容，传入的是build引用
        fn build_where_clause(
            builder: &mut QueryBuilder<Postgres>, // 改为可变引用
            params: &SubjectQueryParams,
        ) {
            let mut where_count = 0;
            if let Some(is_active) = params.is_active {
                if where_count > 0 {
                    builder.push(" AND ");
                }
                if where_count == 0 {
                    builder.push(" WHERE ");
                }
                builder.push(" s.is_active = ").push_bind(is_active);
                where_count+=1;
            }

            if let Some(search) = &params.search {
                if !search.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    let pattern = format!("%{}%", search.trim());
                    builder
                        .push(" (s.name ILIKE ")
                        .push_bind(pattern.clone())
                        .push(" OR s.code ILIKE ")
                        .push_bind(pattern)
                        .push(")");
                    where_count+=1;
                }
            }
        }

        // 构建 COUNT 查询
        let mut count_builder =
            QueryBuilder::new("SELECT COUNT(*) as total FROM public.subjects s");
        build_where_clause(&mut count_builder, &params);

        // 构建主查询
        let mut query_builder = QueryBuilder::new(
            "SELECT s.id, s.code, s.name, s.description, s.order_level, 
         s.is_active, s.created_at, s.updated_at FROM public.subjects s",
        );
        build_where_clause(&mut query_builder, &params);
        // 添加排序和分页
        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();

        let order_by = params.order_by.as_deref().unwrap_or("order_level");
        let order_direction = params.order_direction.as_deref().unwrap_or("asc");
        query_builder
            .push(" ORDER BY ")
            .push(match order_by {
                "name" => "s.name",
                "code" => "s.code",
                "created_at" => "s.created_at",
                _ => "s.order_level",
            })
            .push(" ")
            .push(order_direction)
            .push(" LIMIT ")
            .push_bind(page_size)
            .push(" OFFSET ")
            .push_bind(offset);

        // 执行查询
        let total: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await?;
        let subjects: Vec<SubjectVO> = query_builder
            .build_query_as()
            .fetch_all(&self.db_pool)
            .await?;

        Ok(PageResult {
            data: subjects,
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
        })
    }

    /// 获取学科简要信息列表（用于下拉选择）
    pub async fn get_subject_summaries(
        &self,
        is_active: Option<bool>,
    ) -> Result<Vec<SubjectSummary>> {
        let query = match is_active {
            Some(active) => {
                sqlx::query_as::<_, SubjectSummary>(
                    "SELECT id, code, name, is_active FROM public.subjects WHERE is_active = $1 ORDER BY order_level"
                )
                .bind(active)
            },
            None => {
                sqlx::query_as::<_, SubjectSummary>(
                    "SELECT id, code, name, is_active FROM public.subjects ORDER BY order_level"
                )
            }
        };

        let subjects = query.fetch_all(&self.db_pool).await?;
        Ok(subjects)
    }

    /// 根据ID获取学科详情
    pub async fn get_subject_by_id(&self, id: Uuid) -> Result<Option<SubjectVO>> {
        let subject = sqlx::query_as::<_, SubjectVO>(
            r#"
        SELECT 
            id,
            code,
            name,
            description,
            order_level,
            is_active,
            created_at,
            updated_at
        FROM public.subjects
        WHERE id = $1
        "#,
        )
        .bind(id)
        .fetch_optional(&self.db_pool)
        .await?;
        Ok(subject)
    }

    /// 根据代码获取学科
    pub async fn get_subject_by_code(&self, code: &str) -> Result<Option<SubjectVO>> {
        let subject = sqlx::query_as::<_, SubjectVO>("SELECT * FROM public.subjects WHERE code = $1")
            .bind(code)
            .fetch_optional(&self.db_pool)
            .await?;

        Ok(subject)
    }

    /// 根据名称获取学科
    pub async fn get_subject_by_name(&self, name: &str) -> Result<Option<SubjectVO>> {
        let subject = sqlx::query_as::<_, SubjectVO>("SELECT * FROM public.subjects WHERE name = $1")
            .bind(name)
            .fetch_optional(&self.db_pool)
            .await?;

        Ok(subject)
    }

    /// 创建学科
    pub async fn create_subject(&self, payload: CreateSubjectRequest) -> Result<SubjectVO> {
        // 检查代码是否已存在
        if let Some(_) = self.get_subject_by_code(&payload.code).await? {
            bail!("学科编码已经存在");
        }

        let subject = sqlx::query_as::<_, SubjectVO>(
            r#"
            INSERT INTO public.subjects (code, name, description, order_level)
            VALUES ($1, $2, $3, $4)
            RETURNING *
            "#,
        )
        .bind(&payload.code)
        .bind(&payload.name)
        .bind(&payload.description)
        .bind(payload.order_level)
        .fetch_one(&self.db_pool)
        .await?;

        Ok(subject)
    }

    /// 更新学科
    pub async fn update_subject(&self, id: Uuid, payload: UpdateSubjectRequest) -> Result<SubjectVO> {
        let subject = sqlx::query_as::<_, SubjectVO>(
            r#"
            UPDATE public.subjects 
            SET 
                name = COALESCE($2, name),
                description = COALESCE($3, description),
                order_level = COALESCE($4, order_level),
                is_active = COALESCE($5, is_active),
                updated_at = NOW()
            WHERE id = $1
            RETURNING *
            "#,
        )
        .bind(id)
        .bind(&payload.name)
        .bind(&payload.description)
        .bind(payload.order_level)
        .bind(payload.is_active)
        .fetch_one(&self.db_pool)
        .await?;

        Ok(subject)
    }

    /// 删除学科（软删除，设置为不活跃）
    pub async fn delete_subject(&self, id: Uuid) -> Result<()> {
        // 检查是否有关联数据（题目或试卷）
        let usage_check = sqlx::query(
            r#"
            SELECT 
                (SELECT COUNT(*) FROM public.question_bank WHERE subject = (SELECT code FROM public.subjects WHERE id = $1)) as question_count,
                (SELECT COUNT(*) FROM public.exam_papers WHERE subject = (SELECT code FROM public.subjects WHERE id = $1)) as paper_count
            "#
        )
        .bind(id)
        .fetch_one(&self.db_pool)
        .await?;

        let question_count: i64 = usage_check.get("question_count");
        let paper_count: i64 = usage_check.get("paper_count");

        if question_count > 0 || paper_count > 0 {
            bail!(format!(
                "仍有 {} 试题和 {} 试卷关联在该学科",
                question_count, paper_count
            ));
        }

        sqlx::query(
            "UPDATE public.subjects SET is_active = false, updated_at = NOW() WHERE id = $1",
        )
        .bind(id)
        .execute(&self.db_pool)
        .await?;

        Ok(())
    }

    /// 获取学科统计信息
    pub async fn get_subject_statistics(&self) -> Result<SubjectStatistics> {
        // 基本统计
        let basic_stats = sqlx::query(
            r#"
            SELECT 
                COUNT(*) as total_subjects,
                COUNT(CASE WHEN is_active = true THEN 1 END) as active_subjects,
                COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_subjects
            FROM public.subjects
            "#,
        )
        .fetch_one(&self.db_pool)
        .await?;

        // 使用统计
        let usage_stats = sqlx::query(
            r#"
            SELECT 
                s.id as subject_id,
                s.name as subject_name,
                COALESCE(q.question_count, 0) as question_count,
                COALESCE(p.paper_count, 0) as paper_count,
                0 as exam_count  -- TODO: 当exam表实现后，添加真实的考试数量统计
            FROM public.subjects s
            LEFT JOIN (
                SELECT subject, COUNT(*) as question_count
                FROM public.question_bank
                WHERE status != 'archived'
                GROUP BY subject
            ) q ON s.code = q.subject
            LEFT JOIN (
                SELECT subject, COUNT(*) as paper_count
                FROM public.exam_papers
                WHERE status != 'archived'
                GROUP BY subject
            ) p ON s.code = p.subject
            WHERE s.is_active = true
            ORDER BY s.order_level
            "#,
        )
        .fetch_all(&self.db_pool)
        .await?;

        let usage_stats: Vec<SubjectUsageStats> = usage_stats
            .into_iter()
            .map(|row| SubjectUsageStats {
                subject_id: row.get("subject_id"),
                subject_name: row.get("subject_name"),
                question_count: row.get("question_count"),
                paper_count: row.get("paper_count"),
                exam_count: Some(row.get("exam_count")),
            })
            .collect();

        Ok(SubjectStatistics {
            total_subjects: basic_stats.get("total_subjects"),
            active_subjects: basic_stats.get("active_subjects"),
            inactive_subjects: basic_stats.get("inactive_subjects"),
            usage_stats,
        })
    }

    /// 批量更新学科排序
    pub async fn update_subject_orders(&self, orders: Vec<(Uuid, i32)>) -> Result<()> {
        let mut tx = self.db_pool.begin().await?;

        for (subject_id, order_level) in orders {
            sqlx::query(
                "UPDATE public.subjects SET order_level = $2, updated_at = NOW() WHERE id = $1",
            )
            .bind(subject_id)
            .bind(order_level)
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(())
    }

    /// 检查学科代码是否可用
    pub async fn is_code_available(&self, code: &str, exclude_id: Option<Uuid>) -> Result<bool> {
        let query = match exclude_id {
            Some(id) => sqlx::query(
                "SELECT COUNT(*) as count FROM public.subjects WHERE code = $1 AND id != $2",
            )
            .bind(code)
            .bind(id),
            None => sqlx::query("SELECT COUNT(*) as count FROM public.subjects WHERE code = $1")
                .bind(code),
        };

        let row = query.fetch_one(&self.db_pool).await?;
        let count: i64 = row.get("count");
        Ok(count == 0)
    }
}

/// 学科查询参数
#[derive(Debug, Deserialize)]
pub struct PageSubjectParams {
    pub page: i32,
    pub page_size: i32,
    pub name_or_code_ilike: Option<String>,
    pub is_active: Option<bool>,
    pub order_by: Option<String>, // 排序字段：name, code, order_level, created_at
    pub order_direction: Option<String>, // 排序方向：asc, desc
}
