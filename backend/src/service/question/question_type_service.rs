use crate::model::question::question_type::{ComposeBindRequest, ComposeCodeRequest, ComposeQuestionType, ComposeQuestionTypeQueryParams, ComposeQuestionTypeVO, ComposeSubjectGradeRequest, CreateQuestionTypeRequest, QuestionType, QuestionTypeQueryParams, QuestionTypeSummary, QuestionTypeVO, UpdateQuestionTypeRequest};
use crate::model::{PageParams, PageResult, SubjectSummary};
use anyhow::{bail, Result};
use chrono::Utc;
use sqlx::{PgPool, Postgres, QueryBuilder, Row};
use uuid::Uuid;

pub struct QuestionTypeService {
    db: PgPool,
}

impl QuestionTypeService {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    /// 获取题目类型分页列表
    ///
    /// # 参数
    /// * `params` - 查询参数，包含分页、搜索、排序等条件
    ///
    /// # 返回值
    /// 返回包含题目类型信息的分页结果
    ///
    /// # 错误处理
    /// 如果数据库查询失败，将返回相应的错误信息
    pub async fn get_question_types(
        &self,
        params: QuestionTypeQueryParams,
    ) -> Result<PageResult<QuestionTypeVO>> {
        // 内部函数 构造公共的查询内容，传入的是build引用
        fn build_where_clause(
            builder: &mut QueryBuilder<Postgres>, // 改为可变引用
            params: &QuestionTypeQueryParams,
        ) {
            let mut where_count = 0;
            if let Some(is_active) = params.is_active {
                if where_count > 0 {
                    builder.push(" AND ");
                }
                if where_count == 0 {
                    builder.push(" WHERE ");
                }
                builder.push(" qt.is_active = ").push_bind(is_active);
                where_count += 1;
            }

            if let Some(search) = &params.search {
                if !search.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    let pattern = format!("%{}%", search.trim());
                    builder
                        .push(" (qt.type_name ILIKE ")
                        .push_bind(pattern.clone())
                        .push(" OR qt.code ILIKE ")
                        .push_bind(pattern)
                        .push(")");
                    where_count += 1;
                }
            }
        }

        // 构建 COUNT 查询
        let mut count_builder =
            QueryBuilder::new("SELECT COUNT(*) as total FROM public.question_types qt");
        build_where_clause(&mut count_builder, &params);

        // 构建主查询
        let mut query_builder = QueryBuilder::new(
            "SELECT qt.code, qt.type_name, qt.description, qt.is_active,
            qt.created_at, qt.updated_at FROM public.question_types qt",
        );
        build_where_clause(&mut query_builder, &params);
        // 添加排序和分页
        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();

        let order_by = params.order_by.as_deref().unwrap_or("created_at");
        let order_direction = params.order_direction.as_deref().unwrap_or("asc");
        query_builder
            .push(" ORDER BY ")
            .push(match order_by {
                "type_name" => "qt.type_name",
                "code" => "qt.code",
                "created_at" => "qt.created_at",
                _ => "qt.created_at",
            })
            .push(" ")
            .push(order_direction)
            .push(" LIMIT ")
            .push_bind(page_size)
            .push(" OFFSET ")
            .push_bind(offset);

        // 执行查询
        let total: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db)
            .await?;

        let question_types: Vec<QuestionTypeVO> =
            query_builder.build_query_as().fetch_all(&self.db).await?;

        Ok(PageResult {
            data: question_types,
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
        })
    }


    pub async fn get_question_type_summaries(&self, is_active: Option<bool>)-> Result<Vec<QuestionTypeSummary>>{
        let query = match is_active {
            Some(active) => {
                sqlx::query_as::<_, QuestionTypeSummary>(
                    "SELECT code, type_name, is_active FROM public.question_types WHERE is_active = $1"
                )
                    .bind(active)
            },
            None => {
                sqlx::query_as::<_, QuestionTypeSummary>(
                    "SELECT code, type_name, is_active FROM public.question_types "
                )
            }
        };

        let subjects = query.fetch_all(&self.db).await?;
        Ok(subjects)
    }

    /// 创建新的题目类型
    pub async fn create_question_type(
        &self,
        payload: CreateQuestionTypeRequest,
    ) -> Result<QuestionTypeVO> {
        let now = Utc::now();
        let id = Uuid::new_v4();

        let rec = sqlx::query_as!(
            QuestionTypeVO,
            r#"
            INSERT INTO public.question_types (id, code, type_name, description, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $5)
            RETURNING code, type_name, description, is_active, created_at, updated_at
            "#,
            id,
            payload.code,
            payload.type_name,
            payload.description,
            now
        )
            .fetch_one(&self.db)
            .await?;

        Ok(rec)
    }

    /// 更新题目类型信息
    pub async fn update_question_type(&self, payload: UpdateQuestionTypeRequest) -> Result<()> {
        let mut conn = self.db.acquire().await?;

        let qt_exists: bool = sqlx::query_scalar(
            r#"
                SELECT EXISTS (SELECT 1 FROM public.question_types WHERE code = $1)"#,
        )
        .bind(payload.code.clone())
        .fetch_one(&mut *conn)
        .await?;

        if !qt_exists {
            bail!("题目类型不存在");
        }

        let mut builder = QueryBuilder::<Postgres>::new("UPDATE public.question_types SET ");
        let mut update_count = 0;

        if let Some(name) = &payload.type_name {
            if update_count > 0 {
                builder.push(", ");
            }
            builder.push("type_name = ").push_bind(name);
            update_count += 1;
        }

        if let Some(description) = &payload.description {
            if update_count > 0 {
                builder.push(", ");
            }
            builder.push("description = ").push_bind(description);
            update_count += 1;
        }

        if let Some(is_active) = payload.is_active {
            if update_count > 0 {
                builder.push(", ");
            }
            builder.push("is_active = ").push_bind(is_active);
            update_count += 1;
        }

        // 没有任何字段要更新？
        if update_count == 0 {
            bail!("未提供任何可更新的字段（type_name、description、is_active）");
        }

        builder.push(" WHERE code = ").push_bind(payload.code);

        let query = builder.build();
        query.execute(&mut *conn).await?;

        Ok(())
    }

    pub async fn is_code_available(&self, code: &str) -> Result<bool> {
        let row =
            sqlx::query("SELECT COUNT(*) as count FROM public.question_types WHERE code = $1")
                .bind(code)
                .fetch_one(&self.db)
                .await?;
        let count: i64 = row.get("count");
        Ok(count == 0)
    }

    pub async fn get_all_compose(
        &self,
        params: ComposeQuestionTypeQueryParams,
    ) -> Result<PageResult<ComposeQuestionTypeVO>> {
        // 添加排序和分页
        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();

        // 构建 COUNT 查询
        let mut count_builder =
            QueryBuilder::new(r#"SELECT COUNT(*) as total FROM public.compose_question_types cqt"#);

        let mut query_builder = QueryBuilder::new(
            r#"SELECT
                qt.type_name as question_type_name,
                cqt.question_type_code,
                cqt.subject_code,
                s.name as "subject_name",
                cqt.grade_level_code,
                gl.name as "grade_level_name",
                es.name as "education_stage_name",
                es.code as "education_stage_code"
                FROM public.compose_question_types cqt
                LEFT JOIN question_types qt ON qt.code = cqt.question_type_code
                LEFT JOIN subjects s ON cqt.subject_code = s.code
                LEFT JOIN grade_levels gl ON cqt.grade_level_code = gl.code
                LEFT JOIN education_stages es on gl.education_stage_code = es.code"#,
        );
        query_builder.push(" LIMIT ")
        .push_bind(page_size)
        .push(" OFFSET ")
        .push_bind(offset);

        // 执行查询
        let total: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db)
            .await?;

        let cqt: Vec<ComposeQuestionTypeVO> =
            query_builder.build_query_as().fetch_all(&self.db).await?;

        Ok(PageResult {
            data: cqt,
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
        })
    }

    /// 根据科目和年级筛选题目类型
    ///
    /// # 参数
    /// * `params` - 包含科目代码和年级代码的查询参数，均为可选参数
    ///
    /// # 返回值
    /// 返回符合科目和年级条件的题目类型列表
    pub async fn list_by_subject_grade(
        &self,
        params: ComposeSubjectGradeRequest,
    ) -> Result<Vec<QuestionTypeVO>> {
        let mut builder = QueryBuilder::new(
            r#"
        SELECT * FROM public.question_types WHERE code IN (
            SELECT question_type_code FROM public.compose_question_types WHERE
        "#,
        );
        let mut build_count = 0;

        if let Some(subject) = params.subject_code {
            builder.push("subject_code = ").push_bind(subject);
            build_count += 1;
        }

        if let Some(grade) = params.grade_level_code {
            if build_count > 0 {
                builder.push(" AND ");
            }
            builder.push("grade_level_code = ").push_bind(grade);
            build_count += 1;
        }

        builder.push(")");

        if build_count == 0 {
            bail!("未提供任何参数");
        }

        let rows: Vec<QuestionType> = builder.build_query_as().fetch_all(&self.db).await?;

        let question_types: Vec<QuestionTypeVO> = rows
            .into_iter()
            .map(|row| QuestionTypeVO {
                code: row.code,
                type_name: row.type_name,
                description: row.description,
                is_active: row.is_active,
                created_at: row.created_at,
                updated_at: row.updated_at,
            })
            .collect();

        Ok(question_types)
    }

    /// 根据题目类型代码获取关联的科目、年级和教育阶段信息
    pub async fn list_by_question_type_code(
        &self,
        params: ComposeCodeRequest,
    ) -> Result<Vec<ComposeQuestionTypeVO>> {
        let rows = sqlx::query_as!(
            ComposeQuestionTypeVO,
            r#"
        SELECT
                qt.type_name as "question_type_name?",
                cqt.question_type_code,
               cqt.subject_code,
               s.name as "subject_name?",
               cqt.grade_level_code,
               gl.name as "grade_level_name?",
               es.name as "education_stage_name?",
               es.code as "education_stage_code?"
        FROM public.compose_question_types cqt
        LEFT JOIN question_types qt ON qt.code = cqt.question_type_code
        LEFT JOIN subjects s ON cqt.subject_code = s.code
        LEFT JOIN grade_levels gl ON cqt.grade_level_code = gl.code
        LEFT JOIN education_stages es on gl.education_stage_code = es.code
        WHERE cqt.question_type_code = $1
        "#,
            params.question_type_code,
        )
        .fetch_all(&self.db)
        .await?;
        Ok(rows)
    }

    /// 绑定题目类型与科目、年级的组合关系
    pub async fn bind_compose(&self, request: ComposeBindRequest) -> Result<ComposeQuestionType> {
        let record = sqlx::query_as!(
            ComposeQuestionType,
            r#"
            INSERT INTO public.compose_question_types
                (question_type_code, subject_code, grade_level_code)
            VALUES ($1, $2, $3)
            ON CONFLICT (question_type_code, subject_code, grade_level_code)
            DO UPDATE SET
                -- 使用虚拟更新避免实际数据变更
                question_type_code = CASE
                    WHEN compose_question_types.question_type_code = EXCLUDED.question_type_code
                    THEN compose_question_types.question_type_code
                    ELSE EXCLUDED.question_type_code
                END
            RETURNING *
            "#,
            request.question_type_code,
            request.subject_code,
            request.grade_level_code,
        )
        .fetch_one(&self.db)
        .await?;

        Ok(record)
    }

    /// 解绑题目类型与科目、年级的组合关系
    pub async fn unbind_compose(&self, request: ComposeBindRequest) -> Result<()> {
        let result = sqlx::query!(
            r#"
        DELETE FROM compose_question_types
        WHERE question_type_code = $1
          AND subject_code = $2
          AND grade_level_code = $3
        "#,
            request.question_type_code,
            request.subject_code,
            request.grade_level_code,
        )
        .execute(&self.db)
        .await?;
        Ok(())
    }
}
