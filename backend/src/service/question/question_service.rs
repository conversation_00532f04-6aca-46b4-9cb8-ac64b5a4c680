use crate::model::question::*;
use crate::utils::db::get_tenant_schema;
use crate::utils::error::AppError;
use crate::utils::error_handler::{SqlxResultExt, errors, AppResult};
use anyhow::{Result, Context};
use sqlx::PgPool;
use uuid::Uuid;

pub struct QuestionService {
    pool: PgPool,
}

impl QuestionService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_question(
        &self,
        creator_id: Uuid,
        tenant_id: Uuid,
        request: CreateQuestionRequest,
    ) -> AppResult<QuestionResponse> {
        let question = sqlx::query_as!(
            QuestionBank,
            r#"
            INSERT INTO public.question_bank (
                id, source_type, source_id, question_type, question_content,
                answer_content, difficulty_level, knowledge_points, subject,
                grade_level, creator_id, status
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'draft'
            ) RETURNING *
            "#,
            Uuid::new_v4(),
            request.source_type,
            request.source_id,
            request.question_type,
            request.question_content,
            request.answer_content,
            request.difficulty_level,
            request.knowledge_points,
            request.subject,
            request.grade_level,
            creator_id
        )
        .fetch_one(&self.pool)
        .await
        .with_db_context("create question")?;

        Ok(self.question_to_response(question, tenant_id).await?)
    }

    pub async fn get_question(&self, question_id: Uuid, tenant_id: Uuid) -> Result<Option<QuestionResponse>> {
        let question = sqlx::query_as!(
            QuestionBank,
            "SELECT * FROM public.question_bank WHERE id = $1",
            question_id
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(question) = question {
            Ok(Some(self.question_to_response(question, tenant_id).await?))
        } else {
            Ok(None)
        }
    }

    pub async fn update_question(
        &self,
        question_id: Uuid,
        tenant_id: Uuid,
        request: UpdateQuestionRequest,
    ) -> Result<Option<QuestionResponse>> {
        let mut query = sqlx::QueryBuilder::new("UPDATE public.question_bank SET updated_at = NOW(), version = version + 1");
        let mut has_updates = false;

        if let Some(question_content) = &request.question_content {
            query.push(", question_content = ");
            query.push_bind(question_content);
            has_updates = true;
        }

        if let Some(answer_content) = &request.answer_content {
            query.push(", answer_content = ");
            query.push_bind(answer_content);
            has_updates = true;
        }

        if let Some(difficulty_level) = &request.difficulty_level {
            query.push(", difficulty_level = ");
            query.push_bind(difficulty_level);
            has_updates = true;
        }

        if let Some(knowledge_points) = &request.knowledge_points {
            query.push(", knowledge_points = ");
            query.push_bind(knowledge_points);
            has_updates = true;
        }

        if let Some(status) = &request.status {
            query.push(", status = ");
            query.push_bind(status);
            has_updates = true;
        }

        if !has_updates {
            return self.get_question(question_id, tenant_id).await;
        }

        query.push(" WHERE id = ");
        query.push_bind(question_id);
        query.push(" RETURNING *");

        let question = query
            .build_query_as::<QuestionBank>()
            .fetch_optional(&self.pool)
            .await?;

        if let Some(question) = question {
            Ok(Some(self.question_to_response(question, tenant_id).await?))
        } else {
            Ok(None)
        }
    }

    pub async fn delete_question(&self, question_id: Uuid) -> Result<bool> {
        let result = sqlx::query!(
            "UPDATE public.question_bank SET status = 'archived' WHERE id = $1",
            question_id
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn list_questions(&self, params: QuestionQueryParams, tenant_id: Uuid) -> Result<QuestionListResponse> {
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(20);
        let offset = (page - 1) * page_size;

        let mut query = sqlx::QueryBuilder::new("SELECT * FROM public.question_bank WHERE status != 'archived'");
        let mut count_query = sqlx::QueryBuilder::new("SELECT COUNT(*) FROM public.question_bank WHERE status != 'archived'");

        if let Some(subject) = &params.subject {
            query.push(" AND subject = ");
            query.push_bind(subject);
            count_query.push(" AND subject = ");
            count_query.push_bind(subject);
        }

        if let Some(grade_level) = &params.grade_level {
            query.push(" AND grade_level = ");
            query.push_bind(grade_level);
            count_query.push(" AND grade_level = ");
            count_query.push_bind(grade_level);
        }

        if let Some(difficulty_level) = &params.difficulty_level {
            query.push(" AND difficulty_level = ");
            query.push_bind(difficulty_level);
            count_query.push(" AND difficulty_level = ");
            count_query.push_bind(difficulty_level);
        }

        if let Some(question_type) = &params.question_type {
            query.push(" AND question_type = ");
            query.push_bind(question_type);
            count_query.push(" AND question_type = ");
            count_query.push_bind(question_type);
        }

        if let Some(status) = &params.status {
            query.push(" AND status = ");
            query.push_bind(status);
            count_query.push(" AND status = ");
            count_query.push_bind(status);
        }

        if let Some(keyword) = &params.keyword {
            query.push(" AND (question_content ILIKE ");
            query.push_bind(format!("%{}%", keyword));
            query.push(" OR answer_content ILIKE ");
            query.push_bind(format!("%{}%", keyword));
            query.push(")");
            count_query.push(" AND (question_content ILIKE ");
            count_query.push_bind(format!("%{}%", keyword));
            count_query.push(" OR answer_content ILIKE ");
            count_query.push_bind(format!("%{}%", keyword));
            count_query.push(")");
        }

        query.push(" ORDER BY created_at DESC LIMIT ");
        query.push_bind(page_size);
        query.push(" OFFSET ");
        query.push_bind(offset);

        let questions = query
            .build_query_as::<QuestionBank>()
            .fetch_all(&self.pool)
            .await?;

        let total = count_query
            .build_query_scalar::<i64>()
            .fetch_one(&self.pool)
            .await?;

        let mut question_responses = Vec::new();
        for question in questions {
            question_responses.push(self.question_to_response(question, tenant_id).await?);
        }

        Ok(QuestionListResponse {
            questions: question_responses,
            total,
            page,
            page_size,
        })
    }

    pub async fn create_paper(
        &self,
        creator_id: Uuid,
        tenant_id: Uuid,
        request: CreatePaperRequest,
    ) -> Result<PaperResponse> {
        let mut tx = self.pool.begin().await?;

        let paper_id = Uuid::new_v4();
        let paper = sqlx::query_as!(
            ExamPaper,
            r#"
            INSERT INTO public.exam_papers (
                id, title, subject, grade_level, total_score, description,
                structure, creator_id, status
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, 'draft'
            ) RETURNING *
            "#,
            paper_id,
            request.title,
            request.subject,
            request.grade_level,
            request.total_score,
            request.description,
            request.structure,
            creator_id
        )
        .fetch_one(&mut *tx)
        .await?;

        let mut questions = Vec::new();
        for question_req in &request.questions {
            let question = sqlx::query_as!(
                ExamPaperQuestion,
                r#"
                INSERT INTO public.exam_paper_questions (
                    id, exam_paper_id, question_id, question_number, score,
                    section_name, display_order
                ) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *
                "#,
                Uuid::new_v4(),
                paper_id,
                question_req.question_id,
                question_req.question_number,
                question_req.score,
                question_req.section_name,
                question_req.display_order
            )
            .fetch_one(&mut *tx)
            .await?;
            questions.push(question);
        }

        tx.commit().await?;

        Ok(self.paper_to_response(paper, questions, tenant_id).await?)
    }

    pub async fn get_paper(&self, paper_id: Uuid, tenant_id: Uuid) -> Result<Option<PaperResponse>> {
        let paper = sqlx::query_as!(
            ExamPaper,
            "SELECT * FROM public.exam_papers WHERE id = $1",
            paper_id
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(paper) = paper {
            let questions = sqlx::query_as!(
                ExamPaperQuestion,
                "SELECT * FROM public.exam_paper_questions WHERE exam_paper_id = $1 ORDER BY display_order",
                paper_id
            )
            .fetch_all(&self.pool)
            .await?;

            Ok(Some(self.paper_to_response(paper, questions, tenant_id).await?))
        } else {
            Ok(None)
        }
    }

    pub async fn list_papers(&self, params: PaperQueryParams, tenant_id: Uuid) -> Result<PaperListResponse> {
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(20);
        let offset = (page - 1) * page_size;

        let mut query = sqlx::QueryBuilder::new("SELECT * FROM public.exam_papers WHERE status != 'archived'");
        let mut count_query = sqlx::QueryBuilder::new("SELECT COUNT(*) FROM public.exam_papers WHERE status != 'archived'");

        if let Some(subject) = &params.subject {
            query.push(" AND subject = ");
            query.push_bind(subject);
            count_query.push(" AND subject = ");
            count_query.push_bind(subject);
        }

        if let Some(grade_level) = &params.grade_level {
            query.push(" AND grade_level = ");
            query.push_bind(grade_level);
            count_query.push(" AND grade_level = ");
            count_query.push_bind(grade_level);
        }

        if let Some(status) = &params.status {
            query.push(" AND status = ");
            query.push_bind(status);
            count_query.push(" AND status = ");
            count_query.push_bind(status);
        }

        if let Some(keyword) = &params.keyword {
            query.push(" AND (title ILIKE ");
            query.push_bind(format!("%{}%", keyword));
            query.push(" OR description ILIKE ");
            query.push_bind(format!("%{}%", keyword));
            query.push(")");
            count_query.push(" AND (title ILIKE ");
            count_query.push_bind(format!("%{}%", keyword));
            count_query.push(" OR description ILIKE ");
            count_query.push_bind(format!("%{}%", keyword));
            count_query.push(")");
        }

        query.push(" ORDER BY created_at DESC LIMIT ");
        query.push_bind(page_size);
        query.push(" OFFSET ");
        query.push_bind(offset);

        let papers = query
            .build_query_as::<ExamPaper>()
            .fetch_all(&self.pool)
            .await?;

        let total = count_query
            .build_query_scalar::<i64>()
            .fetch_one(&self.pool)
            .await?;

        let mut paper_responses = Vec::new();
        for paper in papers {
            if let Some(paper_response) = self.get_paper(paper.id, tenant_id).await? {
                paper_responses.push(paper_response);
            }
        }

        Ok(PaperListResponse {
            papers: paper_responses,
            total,
            page,
            page_size,
        })
    }

    pub async fn create_textbook(
        &self,
        creator_id: Uuid,
        tenant_id: Uuid,
        request: CreateTextbookRequest,
    ) -> Result<TextbookResponse> {
        let mut tx = self.pool.begin().await?;

        let textbook_id = Uuid::new_v4();
        let textbook = sqlx::query_as!(
            Textbook,
            r#"
            INSERT INTO public.textbooks (
                id, title, subject_id, grade_level_id, publisher,
                publication_year, isbn, version, creator_id, status
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, 'draft'
            ) RETURNING *
            "#,
            textbook_id,
            request.title,
            request.subject_id,
            request.grade_level_id,
            request.publisher,
            request.publication_year,
            request.isbn,
            request.version,
            creator_id
        )
        .fetch_one(&mut *tx)
        .await?;

        for chapter_req in &request.chapters {
            let chapter_id = Uuid::new_v4();
            sqlx::query!(
                r#"
                INSERT INTO public.textbook_chapters (
                    id, textbook_id, chapter_number, title, content, knowledge_points
                ) VALUES ($1, $2, $3, $4, $5, $6)
                "#,
                chapter_id,
                textbook_id,
                chapter_req.chapter_number,
                chapter_req.title,
                chapter_req.content,
                chapter_req.knowledge_points
            )
            .execute(&mut *tx)
            .await?;

            for exercise_req in &chapter_req.exercises {
                sqlx::query!(
                    r#"
                    INSERT INTO public.textbook_exercises (
                        id, textbook_id, chapter_id, question_content, answer_content,
                        difficulty_level, knowledge_points
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                    "#,
                    Uuid::new_v4(),
                    textbook_id,
                    chapter_id,
                    exercise_req.question_content,
                    exercise_req.answer_content,
                    exercise_req.difficulty_level,
                    exercise_req.knowledge_points
                )
                .execute(&mut *tx)
                .await?;
            }
        }

        tx.commit().await?;
        Ok(self.textbook_to_response(textbook, tenant_id).await?)
    }

    pub async fn import_textbook_exercises_to_question_bank(
        &self,
        textbook_id: Uuid,
        creator_id: Uuid,
    ) -> Result<i64> {
        let imported_count = sqlx::query_scalar!(
            r#"
            INSERT INTO public.question_bank (
                id, source_type, source_id, question_type, question_content,
                answer_content, difficulty_level, knowledge_points, subject,
                grade_level, creator_id, status
            )
            SELECT 
                gen_random_uuid(),
                'textbook_exercise',
                te.id,
                CASE 
                    WHEN te.question_content ILIKE '%选择%' THEN 'choice'
                    WHEN te.question_content ILIKE '%填空%' THEN 'blank'
                    WHEN te.question_content ILIKE '%计算%' THEN 'calculation'
                    ELSE 'essay'
                END,
                te.question_content,
                te.answer_content,
                te.difficulty_level,
                te.knowledge_points,
                s.code,
                gl.level_order,
                $2,
                'published'
            FROM public.textbook_exercises te
            JOIN public.textbooks t ON te.textbook_id = t.id
            JOIN public.subjects s ON t.subject_id = s.id
            JOIN public.grade_levels gl ON t.grade_level_id = gl.id
            WHERE te.textbook_id = $1
            AND NOT EXISTS (
                SELECT 1 FROM public.question_bank qb 
                WHERE qb.source_type = 'textbook_exercise' 
                AND qb.source_id = te.id
            )
            "#,
            textbook_id,
            creator_id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(imported_count.unwrap_or(0))
    }

    pub async fn grant_textbook_access(
        &self,
        textbook_id: Uuid,
        tenant_ids: Vec<Uuid>,
        granted_by: Uuid,
    ) -> Result<i64> {
        let mut granted_count = 0;
        for tenant_id in tenant_ids {
            let result = sqlx::query!(
                r#"
                INSERT INTO public.textbook_tenant_access (
                    id, textbook_id, tenant_id, granted_by
                ) VALUES ($1, $2, $3, $4)
                ON CONFLICT (textbook_id, tenant_id) DO NOTHING
                "#,
                Uuid::new_v4(),
                textbook_id,
                tenant_id,
                granted_by
            )
            .execute(&self.pool)
            .await?;

            if result.rows_affected() > 0 {
                granted_count += 1;
            }
        }

        Ok(granted_count)
    }

    pub async fn bulk_import_questions(
        &self,
        creator_id: Uuid,
        request: BulkImportRequest,
    ) -> Result<BulkImportResponse> {
        let mut tx = self.pool.begin().await?;
        let mut successful_imports = Vec::new();
        let mut failed_imports = Vec::new();

        for (index, question_req) in request.questions.iter().enumerate() {
            let question_id = Uuid::new_v4();
            
            // Check for duplicates if validation is enabled
            if request.validate_duplicates {
                let existing = sqlx::query_scalar!(
                    "SELECT id FROM public.question_bank WHERE question_content = $1 AND answer_content = $2",
                    question_req.question_content,
                    question_req.correct_answer
                )
                .fetch_optional(&mut *tx)
                .await?;

                if existing.is_some() {
                    failed_imports.push(ImportError {
                        line_number: index as i32 + 1,
                        error_message: "Duplicate question found".to_string(),
                        question_content: Some(question_req.question_content.clone()),
                    });
                    continue;
                }
            }

            match sqlx::query_as!(
                Question,
                r#"
                INSERT INTO public.question_bank (
                    id, source_type, question_type, question_content, options,
                    correct_answer, explanation, difficulty_level, knowledge_points,
                    tags, estimated_time, creator_id, status
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, 'draft'
                ) RETURNING id, source_type, question_type, question_content, options,
                    correct_answer, explanation, difficulty_level, knowledge_points,
                    tags, estimated_time, creator_id, status, created_at, updated_at
                "#,
                question_id,
                question_req.source_type,
                question_req.question_type,
                question_req.question_content,
                question_req.options,
                question_req.correct_answer,
                question_req.explanation,
                question_req.difficulty_level,
                question_req.knowledge_points,
                question_req.tags,
                question_req.estimated_time,
                creator_id
            )
            .fetch_one(&mut *tx)
            .await {
                Ok(question) => {
                    successful_imports.push(question);
                },
                Err(e) => {
                    failed_imports.push(ImportError {
                        line_number: index as i32 + 1,
                        error_message: e.to_string(),
                        question_content: Some(question_req.question_content.clone()),
                    });
                }
            }
        }

        if failed_imports.is_empty() || successful_imports.len() > failed_imports.len() {
            tx.commit().await?;
        } else {
            tx.rollback().await?;
        }

        Ok(BulkImportResponse {
            total_questions: request.questions.len() as i32,
            successful_imports: successful_imports.len() as i32,
            failed_imports: failed_imports.len() as i32,
            imported_questions: successful_imports,
            errors: failed_imports,
        })
    }

    pub async fn search_questions(&self, params: QuestionQueryParams, tenant_id: Uuid) -> Result<QuestionSearchResponse> {
        let page = params.page.unwrap_or(1);
        let page_size = params.page_size.unwrap_or(20);
        let offset = (page - 1) * page_size;

        let mut query = sqlx::QueryBuilder::new(
            "SELECT * FROM public.question_bank WHERE status != 'archived'"
        );
        let mut count_query = sqlx::QueryBuilder::new(
            "SELECT COUNT(*) FROM public.question_bank WHERE status != 'archived'"
        );

        // Apply filters
        if let Some(source_type) = &params.source_type {
            query.push(" AND source_type = ");
            query.push_bind(source_type);
            count_query.push(" AND source_type = ");
            count_query.push_bind(source_type);
        }

        if let Some(question_type) = &params.question_type {
            query.push(" AND question_type = ");
            query.push_bind(question_type);
            count_query.push(" AND question_type = ");
            count_query.push_bind(question_type);
        }

        if let Some(difficulty_level) = &params.difficulty_level {
            query.push(" AND difficulty_level = ");
            query.push_bind(difficulty_level);
            count_query.push(" AND difficulty_level = ");
            count_query.push_bind(difficulty_level);
        }

        if let Some(subject) = &params.subject {
            query.push(" AND subject = ");
            query.push_bind(subject);
            count_query.push(" AND subject = ");
            count_query.push_bind(subject);
        }

        if let Some(status) = &params.status {
            query.push(" AND status = ");
            query.push_bind(status);
            count_query.push(" AND status = ");
            count_query.push_bind(status);
        }

        if let Some(search) = &params.search {
            query.push(" AND (question_content ILIKE ");
            query.push_bind(format!("%{}%", search));
            query.push(" OR correct_answer ILIKE ");
            query.push_bind(format!("%{}%", search));
            query.push(" OR explanation ILIKE ");
            query.push_bind(format!("%{}%", search));
            query.push(")");
            count_query.push(" AND (question_content ILIKE ");
            count_query.push_bind(format!("%{}%", search));
            count_query.push(" OR correct_answer ILIKE ");
            count_query.push_bind(format!("%{}%", search));
            count_query.push(" OR explanation ILIKE ");
            count_query.push_bind(format!("%{}%", search));
            count_query.push(")");
        }

        query.push(" ORDER BY created_at DESC LIMIT ");
        query.push_bind(page_size);
        query.push(" OFFSET ");
        query.push_bind(offset);

        let questions = query
            .build_query_as::<Question>()
            .fetch_all(&self.pool)
            .await?;

        let total = count_query
            .build_query_scalar::<i64>()
            .fetch_one(&self.pool)
            .await?;

        let mut question_responses = Vec::new();
        for question in questions {
            question_responses.push(self.question_to_response_from_question(question, tenant_id).await?);
        }

        Ok(QuestionSearchResponse {
            questions: question_responses,
            total,
            page,
            page_size,
            filters_applied: serde_json::json!({
                "source_type": params.source_type,
                "question_type": params.question_type,
                "difficulty_level": params.difficulty_level,
                "subject": params.subject,
                "status": params.status,
                "search": params.search
            }),
        })
    }

    pub async fn get_question_statistics(&self) -> Result<QuestionStatisticsResponse> {
        // 定义查询结果结构
        #[derive(sqlx::FromRow)]
        struct StatsResult {
            total_questions: Option<i64>,
            published_questions: Option<i64>,
            draft_questions: Option<i64>,
        }

        #[derive(sqlx::FromRow)]
        struct JsonResult {
            by_subject: Option<serde_json::Value>,
        }

        #[derive(sqlx::FromRow)]
        struct DifficultyResult {
            by_difficulty: Option<serde_json::Value>,
        }

        #[derive(sqlx::FromRow)]
        struct TypeResult {
            by_type: Option<serde_json::Value>,
        }

        let stats = sqlx::query_as!(
            StatsResult,
            r#"
            SELECT
                COUNT(*) as total_questions,
                COUNT(*) FILTER (WHERE status = 'published') as published_questions,
                COUNT(*) FILTER (WHERE status = 'draft') as draft_questions
            FROM public.question_bank
            WHERE status != 'archived'
            "#
        )
        .fetch_one(&self.pool)
        .await?;

        let by_subject = sqlx::query_as!(
            JsonResult,
            r#"
            SELECT
                jsonb_object_agg(subject, count) as by_subject
            FROM (
                SELECT subject, COUNT(*) as count
                FROM public.question_bank
                WHERE status != 'archived'
                GROUP BY subject
            ) t
            "#
        )
        .fetch_one(&self.pool)
        .await?;

        let by_difficulty = sqlx::query_as!(
            DifficultyResult,
            r#"
            SELECT
                jsonb_object_agg(difficulty_level::text, count) as by_difficulty
            FROM (
                SELECT difficulty_level, COUNT(*) as count
                FROM public.question_bank
                WHERE status != 'archived'
                GROUP BY difficulty_level
            ) t
            "#
        )
        .fetch_one(&self.pool)
        .await?;

        let by_type = sqlx::query_as!(
            TypeResult,
            r#"
            SELECT
                jsonb_object_agg(question_type, count) as by_type
            FROM (
                SELECT question_type, COUNT(*) as count
                FROM public.question_bank
                WHERE status != 'archived'
                GROUP BY question_type
            ) t
            "#
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(QuestionStatisticsResponse {
            total_questions: stats.total_questions.unwrap_or(0),
            published_questions: stats.published_questions.unwrap_or(0),
            draft_questions: stats.draft_questions.unwrap_or(0),
            by_subject: by_subject.by_subject.unwrap_or(serde_json::json!({})),
            by_difficulty: by_difficulty.by_difficulty.unwrap_or(serde_json::json!({})),
            by_type: by_type.by_type.unwrap_or(serde_json::json!({})),
        })
    }

    async fn question_to_response_from_question(&self, question: Question, tenant_id: Uuid) -> Result<QuestionResponse> {
        let usage_count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*)::bigint FROM public.exam_paper_questions WHERE question_id = $1"
        )
        .bind(question.id)
        .fetch_one(&self.pool)
        .await?;

        let schema = get_tenant_schema(&self.pool, tenant_id).await?;
        let query = format!("SELECT AVG(actual_score) FROM {}.question_scores WHERE question_id = $1", schema);
        let average_score = sqlx::query_scalar::<_, f64>(&query)
            .bind(question.id)
            .fetch_one(&self.pool)
            .await?;

        Ok(QuestionResponse {
            id: question.id,
            source_type: question.source_type,
            source_id: None, // Question model doesn't have source_id
            question_type: question.question_type,
            question_content: question.question_content,
            answer_content: Some(question.correct_answer),
            difficulty_level: question.difficulty_level.parse().unwrap_or(1),
            knowledge_points: serde_json::json!(question.knowledge_points),
            subject: "".to_string(), // Question model doesn't have subject
            grade_level: 1, // Question model doesn't have grade_level
            creator_id: question.creator_id,
            version: 1, // Question model doesn't have version
            status: question.status,
            created_at: question.created_at,
            updated_at: question.updated_at,
            usage_count,
            average_score: Some(average_score.unwrap_or(0.0) as f32),
        })
    }

    async fn question_to_response(&self, question: QuestionBank, tenant_id: Uuid) -> Result<QuestionResponse> {
        let usage_count = sqlx::query_scalar!(
            "SELECT COUNT(*)::bigint FROM public.exam_paper_questions WHERE question_id = $1",
            question.id
        )
        .fetch_one(&self.pool)
        .await?
        .unwrap_or(0);

        let schema = get_tenant_schema(&self.pool, tenant_id).await?;
        let query = format!("SELECT AVG(actual_score) FROM {}.question_scores WHERE question_id = $1", schema);
        let average_score = sqlx::query_scalar::<_, f64>(&query)
            .bind(question.id)
            .fetch_one(&self.pool)
            .await?;

        Ok(QuestionResponse {
            id: question.id,
            source_type: question.source_type,
            source_id: question.source_id,
            question_type: question.question_type,
            question_content: question.question_content,
            answer_content: question.answer_content,
            difficulty_level: question.difficulty_level,
            knowledge_points: question.knowledge_points,
            subject: question.subject,
            grade_level: question.grade_level,
            creator_id: question.creator_id,
            version: question.version,
            status: question.status,
            created_at: question.created_at,
            updated_at: question.updated_at,
            usage_count,
            average_score: Some(average_score.unwrap_or(0.0) as f32),
        })
    }

    async fn paper_to_response(&self, paper: ExamPaper, questions: Vec<ExamPaperQuestion>, tenant_id: Uuid) -> Result<PaperResponse> {
        let schema = get_tenant_schema(&self.pool, tenant_id).await?;
        let query = format!("SELECT COUNT(DISTINCT exam_id)::bigint FROM {}.exam_subjects WHERE paper_template_id = $1", schema);
        let usage_count = sqlx::query_scalar::<_, i64>(&query)
            .bind(paper.id)
            .fetch_one(&self.pool)
            .await?
            .unwrap_or(0);

        Ok(PaperResponse {
            id: paper.id,
            title: paper.title,
            subject: paper.subject,
            grade_level: paper.grade_level,
            total_score: paper.total_score,
            description: paper.description,
            structure: paper.structure,
            creator_id: paper.creator_id,
            version: paper.version,
            status: paper.status,
            created_at: paper.created_at,
            updated_at: paper.updated_at,
            questions,
            usage_count,
        })
    }

    async fn textbook_to_response(&self, textbook: Textbook, tenant_id: Uuid) -> Result<TextbookResponse> {
        let chapter_count = sqlx::query_scalar!(
            "SELECT COUNT(*)::bigint FROM public.textbook_chapters WHERE textbook_id = $1",
            textbook.id
        )
        .fetch_one(&self.pool)
        .await?
        .unwrap_or(0);

        let exercise_count = sqlx::query_scalar!(
            "SELECT COUNT(*)::bigint FROM public.textbook_exercises WHERE textbook_id = $1",
            textbook.id
        )
        .fetch_one(&self.pool)
        .await?
        .unwrap_or(0);

        let access_tenants = sqlx::query_scalar!(
            "SELECT ARRAY_AGG(tenant_id) FROM public.textbook_tenant_access WHERE textbook_id = $1",
            textbook.id
        )
        .fetch_one(&self.pool)
        .await?
        .unwrap_or(vec![]);

        Ok(TextbookResponse {
            id: textbook.id,
            title: textbook.title,
            subject_id: textbook.subject_id,
            grade_level_id: textbook.grade_level_id,
            publisher: textbook.publisher,
            publication_year: textbook.publication_year,
            isbn: textbook.isbn,
            version: textbook.version,
            status: textbook.status,
            creator_id: textbook.creator_id,
            created_at: textbook.created_at,
            updated_at: textbook.updated_at,
            chapter_count,
            exercise_count,
            access_tenants,
        })
    }
}