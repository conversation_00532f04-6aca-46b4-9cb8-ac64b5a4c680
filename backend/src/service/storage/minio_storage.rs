use crate::service::storage::storage_service::{StorageService, StorageResult, FileInfo, UploadOptions};
use crate::config::MinioConfig;
use anyhow::{Result, anyhow};
use bytes::Bytes;
use async_trait::async_trait;
use mime_guess;
use minio::s3::client::Client;
use minio::s3::creds::StaticProvider;
use axum::http::Method;
use minio::s3::error::ErrorCode;
use minio::s3::http::BaseUrl;
use minio::s3::segmented_bytes::SegmentedBytes;
use minio::s3::types::S3Api;
use tracing::debug;
use tracing::log::info;
use uuid::Uuid;

/// MinIO存储服务实现
pub struct MinioStorageService {
    client: Client,
    default_bucket: String,
}

impl MinioStorageService {
    /// 创建新的MinIO存储服务实例
    pub async fn new(config: MinioConfig) -> Result<Self> {
        // 验证配置
        config.validate()?;
        // 创建认证提供者
        let provider = StaticProvider::new(&config.access_key, &config.secret_key, None);
        
        // 创建MinIO客户端
        let client = Client::new(
            config.get_endpoint_url().parse::<BaseUrl>().unwrap(),
            Some(Box::new(provider)),
            None,
            None,
        )?;
        
        // 确保默认bucket存在
        match client.bucket_exists(&config.default_bucket).send().await {
            Ok(response) => {
                match response.exists {
                    true => {},
                    false => {
                        client.create_bucket(&config.default_bucket).send().await?;
                    }
                }
            },
            Err(e) => {
                return Err(anyhow!("Failed to check bucket existence: {}", e));
            }
        }
        
        Ok(Self {
            client,
            default_bucket: config.default_bucket,
        })
    }
    
    /// 生成唯一的文件键
    fn generate_file_key(&self, filename: &str, prefix: Option<String>, preserve_filename: bool) -> String {
        if preserve_filename {
            if let Some(prefix) = prefix {
                format!("{}/{}", prefix, filename)
            } else {
                filename.to_string()
            }
        } else {
            let extension = std::path::Path::new(filename)
                .extension()
                .and_then(|ext| ext.to_str())
                .map(|ext| format!(".{}", ext))
                .unwrap_or_default();
            
            let unique_id = Uuid::new_v4().to_string();
            
            if let Some(prefix) = prefix {
                format!("{}/{}{}", prefix, unique_id, extension)
            } else {
                format!("{}{}", unique_id, extension)
            }
        }
    }
}

#[async_trait]
impl StorageService for MinioStorageService {
    async fn upload(
        &self,
        filename: &str,
        content: Bytes,
        options: UploadOptions
    ) -> StorageResult<FileInfo> {
        // 生成文件键
        let key = self.generate_file_key(filename, options.prefix, options.preserve_filename);

        // 执行上传
        let _upload_result = self.client.put_object(&self.default_bucket, &key, SegmentedBytes::from(content)).send().await
            .map_err(|e| anyhow!("Failed to upload file: {}", e))?;
        let file_info = self.get_info(key.as_str()).await?;
        Ok(file_info)
    }

    async fn download(&self, key: &str) -> StorageResult<Bytes> {

        let object_response = self.client.get_object(&self.default_bucket, key).send().await
            .map_err(|e| anyhow!("Failed to download file: {}", e))?;
        let content_bytes = object_response.content.to_segmented_bytes().await?.to_bytes();
        Ok(Bytes::from(content_bytes))
    }

    async fn delete(&self, key: &str) -> StorageResult<()> {
        self.client.delete_object(&self.default_bucket, key).send().await
            .map_err(|e| anyhow!("Failed to delete file: {}", e))?;
        Ok(())
    }

    async fn delete_batch(&self, keys: Vec<&str>) -> StorageResult<Vec<String>> {
        let mut deleted_files = Vec::new();
        
        for key in keys {
            match self.delete(key).await {
                Ok(_) => deleted_files.push(key.to_string()),
                Err(e) => {
                    // 记录错误但继续处理其他文件
                    tracing::warn!("Failed to delete file {}: {}", key, e);
                }
            }
        }
        
        Ok(deleted_files)
    }

    async fn exists(&self, key: &str) -> StorageResult<bool> {
        match self.client.stat_object(&self.default_bucket, key).send().await {
            Ok(_) => Ok(true),
            Err(minio::s3::error::Error::S3Error(ref s3_error)) 
                if s3_error.code == ErrorCode::NoSuchKey => Ok(false),
            Err(e) => Err(anyhow!("Failed to check file existence: {}", e)),
        }
    }

    async fn get_info(&self, key: &str) -> StorageResult<FileInfo> {
        let stat_result = self.client.stat_object(&self.default_bucket, key).send().await
            .map_err(|e| anyhow!("Failed to get file info: {}", e))?;
        
        // 从文件扩展名推断内容类型
        let content_type = mime_guess::from_path(stat_result.object)
                .first_or_octet_stream()
                .to_string();
        
        // 生成文件URL
        let url = self.generate_presigned_url(key).await?;
        
        Ok(FileInfo {
            key: key.to_string(),
            size: stat_result.size,
            content_type,
            url,
        })
    }

    async fn generate_presigned_url(&self, key: &str) -> StorageResult<String> {
        let presigned_object_url_response = self.client.get_presigned_object_url(&self.default_bucket,key,Method::GET).send().await
            .map_err(|e| anyhow!("Failed to generate presigned URL: {}", e))?;
        Ok(presigned_object_url_response. url)
    }
}