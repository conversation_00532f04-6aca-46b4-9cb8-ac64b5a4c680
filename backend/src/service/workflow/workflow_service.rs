use std::collections::{HashMap, HashSet};
use std::env;
use crate::model::workflow::workflow::{InfoObject, WorkflowApiResponseItem, WorkflowQueryParams, WorkflowSettingQueryParams, WorkflowSettingSummaryQueryParams, WorkflowSettingVO, WorkflowSummaryVO};
use crate::model::{PageParams, PageResult};
use anyhow::Result;
use reqwest::Client;
use sqlx::{PgPool, Postgres, QueryBuilder};
use uuid::Uuid;

pub struct WorkflowService {
    db: PgPool,
}

impl WorkflowService {
    pub fn new(db: PgPool) -> Self {
        Self { db }
    }

    pub async fn get_workflow_summaries(
        &self,
        params: WorkflowQueryParams,
    ) -> Result<Vec<WorkflowSummaryVO>, anyhow::Error> {
        // 1. 读取配置
        let base_url = env::var("WORKFLOW_SERVICE_ENDPOINT")?;
        let api_path = env::var("WORKFLOW_SERVICE_API")?; // /api/workflows/category
        let category = params.workflow_type.to_string();

        // 拼接 URL
        let full_url = format!("http://{}{}{}", base_url, api_path, category);

        // 2. 请求 AI 服务
        let client = Client::new();
        let raw_response = client
            .get(&full_url)
            .send()
            .await?
            .error_for_status()?
            .json::<Vec<WorkflowApiResponseItem>>()
            .await?;

        // 3. 构造响应数据（增加错误处理）
        let result: Vec<WorkflowSummaryVO> = raw_response
            .into_iter()
            .map(|item| {
                serde_json::from_str::<InfoObject>(&item.info)
                    .map_err(|e| {
                        anyhow::anyhow!(
                        "解析 info 字段失败，工作流: {}，错误: {}",
                        item.name,
                        e
                    )
                    })
                    .map(|info_obj| WorkflowSummaryVO {
                        workflow_id: item.id,
                        workflow_name: item.name,
                        workflow_type: item.category,
                        description: info_obj.description,
                    })
            })
            .collect::<Result<Vec<_>, _>>()?; // 这里会提前返回错误

        // 4. 数据库操作（使用事务批量处理）
        let mut tx = self.db.begin().await?; // 开启事务

        // 获取数据库现有数据（包含更多字段）
        #[derive(sqlx::FromRow)]
        struct DbRecord {
            id: Uuid,
            workflow_name: String,
            description: String,
        }

        let db_existing: Vec<DbRecord> = sqlx::query_as(
            r#"SELECT id, workflow_name, description FROM public.workflows
                    WHERE workflow_type = $1"#,
        )
            .bind(category.clone()) // 直接使用引用，避免 clone
            .fetch_all(&mut *tx)
            .await?;


        // 创建快速查找结构
        let db_map: HashMap<Uuid, DbRecord> = db_existing
            .into_iter()
            .map(|r| (r.id, r))
            .collect();

        // 准备批量操作容器
        let mut to_insert = Vec::new();
        let mut to_update = Vec::new();
        let mut active_ids = HashSet::new();

        // 遍历API结果
        for item in &result {
            active_ids.insert(item.workflow_id);

            match db_map.get(&item.workflow_id) {
                None => {
                    // 新记录需要插入
                    to_insert.push(item);
                }
                Some(db_record) => {
                    // 检查是否需要更新
                    if db_record.workflow_name != item.workflow_name
                        || db_record.description != item.description
                    {
                        to_update.push(item);
                    }
                }
            }
        }

        // 批量插入新记录
        if !to_insert.is_empty() {
            let mut builder = QueryBuilder::new(
                // 添加 is_active 字段
                "INSERT INTO public.workflows (id, workflow_name, workflow_type, description, is_active) "
            );
            builder.push_values(to_insert, |mut b, item| {
                b.push_bind(item.workflow_id)
                    .push_bind(&item.workflow_name)
                    .push_bind(category.clone()) // 直接使用引用
                    .push_bind(&item.description)
                    .push_bind(true); // 设置 is_active = TRUE
            });
            builder.build().execute(&mut *tx).await?;
        }

        // 批量更新变化记录 - 使用 QueryBuilder
        if !to_update.is_empty() {
            let mut builder = QueryBuilder::new(
                "UPDATE public.workflows AS w SET
                workflow_name = u.workflow_name,
                description = u.description,
                is_active = TRUE,
                updated_at = NOW()
             FROM (VALUES "
            );

            builder.push_values(to_update, |mut b, item| {
                b.push_bind(item.workflow_id)
                    .push_bind(&item.workflow_name)
                    .push_bind(&item.description);
            });

            builder.push(") AS u(id, workflow_name, description) WHERE w.id = u.id");

            builder.build().execute(&mut *tx).await?;
        }

        // 停用不存在的记录（使用NOT IN优化）
        let deactivate_query = "
            UPDATE public.workflows
            SET is_active = FALSE,
                updated_at = NOW()
            WHERE workflow_type = $1
              AND id <> ALL($2)";  // 使用 <> ALL() 过滤

        let active_ids_vec: Vec<Uuid> = active_ids.into_iter().collect();
        sqlx::query(deactivate_query)
            .bind(category)
            .bind(&active_ids_vec)
            .execute(&mut *tx)
            .await?;
        
        tx.commit().await?; // 提交事务

        // 5. 返回给前端
        Ok(result)
    }

    pub async fn get_workflow_settings(
        &self,
        params: WorkflowSettingQueryParams,
    ) -> Result<PageResult<WorkflowSettingVO>> {
        fn build_where_clause<'a>(
            builder: &mut QueryBuilder<'a, Postgres>,
            params: &'a WorkflowSettingQueryParams,
        ) {
            let mut where_count = 0;

            if let Some(subject_code) = &params.subject_code {
                if !subject_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.subject_codes @> to_jsonb(");
                    builder.push_bind(vec![subject_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.subject_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(grade_level_code) = &params.grade_level_code {
                if !grade_level_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.grade_level_codes @> to_jsonb(");
                    builder.push_bind(vec![grade_level_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.grade_level_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(question_type_code) = &params.question_type_code {
                if !question_type_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.question_type_codes @> to_jsonb(");
                    builder.push_bind(vec![question_type_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.question_type_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(schema_name) = &params.schema_name {
                if !schema_name.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.schema_names @> to_jsonb(");
                    builder.push_bind(vec![schema_name.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.schema_names) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(workflow_type) = &params.workflow_type {
                if where_count > 0 {
                    builder.push(" AND ");
                }
                if where_count == 0 {
                    builder.push(" WHERE ");
                }
                builder.push(" w.workflow_type = ").push_bind(workflow_type);
                where_count += 1;
            }
        }

        // 构建 COUNT 查询
        let mut count_builder =
            QueryBuilder::new("SELECT COUNT(*) as total FROM public.workflow_settings ws");
        build_where_clause(&mut count_builder, &params);

        // 构建主查询
        let mut query_builder = QueryBuilder::new(
            "SELECT ws.workflow_id, w.workflow_name, w.workflow_type, w.description, 
                        ws.subject_codes, ws.grade_level_codes, ws.question_type_codes, 
                        ws.schema_names, ws.created_at, ws.updated_at
                 FROM public.workflow_settings ws
                 LEFT JOIN public.workflows w ON ws.workflow_id = w.id",
        );
        build_where_clause(&mut query_builder, &params);

        // 添加排序和分页
        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();
        query_builder
            .push(" LIMIT ")
            .push_bind(page_size)
            .push(" OFFSET ")
            .push_bind(offset);

        // 执行查询
        let total: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db)
            .await?;

        let question_types: Vec<WorkflowSettingVO> =
            query_builder.build_query_as().fetch_all(&self.db).await?;

        Ok(PageResult {
            data: question_types,
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
        })
    }

    pub async fn get_workflow_summaries_in_setting(
        &self,
        params: WorkflowSettingSummaryQueryParams,
    ) -> Result<Vec<WorkflowSummaryVO>> {
        fn build_where_clause<'a>(
            builder: &mut QueryBuilder<'a, Postgres>,
            params: &'a WorkflowSettingSummaryQueryParams,
        ) {
            let mut where_count = 0;

            if let Some(subject_code) = &params.subject_code {
                if !subject_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.subject_codes @> to_jsonb(");
                    builder.push_bind(vec![subject_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.subject_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(grade_level_code) = &params.grade_level_code {
                if !grade_level_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.grade_level_codes @> to_jsonb(");
                    builder.push_bind(vec![grade_level_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.grade_level_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(question_type_code) = &params.question_type_code {
                if !question_type_code.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.question_type_codes @> to_jsonb(");
                    builder.push_bind(vec![question_type_code.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.question_type_codes) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(schema_name) = &params.schema_name {
                if !schema_name.trim().is_empty() {
                    if where_count > 0 {
                        builder.push(" AND ");
                    }
                    if where_count == 0 {
                        builder.push(" WHERE ");
                    }
                    builder.push(" (ws.schema_names @> to_jsonb(");
                    builder.push_bind(vec![schema_name.clone()]);
                    builder.push("::text[]) OR jsonb_array_length(ws.schema_names) = 0) ");
                    where_count += 1;
                }
            }

            if let Some(workflow_type) = &params.workflow_type {
                if where_count > 0 {
                    builder.push(" AND ");
                }
                if where_count == 0 {
                    builder.push(" WHERE ");
                }
                builder.push(" w.workflow_type = ").push_bind(workflow_type);
                where_count += 1;
            }
        }

        // 构建主查询
        let mut query_builder = QueryBuilder::new(
            "SELECT ws.workflow_id, w.workflow_name, w.workflow_type, w.description
                 FROM public.workflow_settings ws
                 LEFT JOIN public.workflows w ON ws.workflow_id = w.id",
        );
        build_where_clause(&mut query_builder, &params);

        let question_types: Vec<WorkflowSummaryVO> =
            query_builder.build_query_as().fetch_all(&self.db).await?;

        Ok(question_types)
    }
}
