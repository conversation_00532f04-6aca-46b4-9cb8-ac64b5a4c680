use std::collections::HashSet;

use sqlx::PgPool;
use uuid::Uuid;

use crate::{
    middleware::auth_middleware::AuthContext,
    model::{
        auth::IdentitySwitchLog,
        homework_students::homework_students::{
            BatchBindStudentsToHomeworkParams, BatchUnbindStudentsFromHomeworkParams,
            HomeworkStudents,
        },
    },
    utils::schema::connect_with_schema,
};

#[derive(Clone)]
pub struct HomeworkStudentsService {
    db_pool: PgPool,
}

impl HomeworkStudentsService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }
}

impl HomeworkStudentsService {
    /**
     * 作者：张瀚
     * 说明：批量绑定学生到作业中
     */
    pub async fn batch_bind_students_to_homework(
        &self,
        schema_name: &String,
        params: &BatchBindStudentsToHomeworkParams,
    ) -> Result<Vec<HomeworkStudents>, String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e| e.to_string())?;
        let BatchBindStudentsToHomeworkParams {
            homework_id,
            student_id_list,
        } = params;
        //先查询已有关联
        let refs = self
            .find_all_by_homework_id(schema_name, &homework_id)
            .await?;
        //已经有的关联不要重复，所以要去掉
        let mut student_id_set: HashSet<Uuid> = student_id_list.clone().into_iter().collect();
        for ele in refs {
            student_id_set.remove(&ele.student_id);
        }
        //新建剩余的
        if student_id_set.len() == 0 {
            return Ok(vec![]);
        }
        let mut builder = sqlx::QueryBuilder::new(
            "INSERT INTO homework_students (homework_id,student_id) VALUES ",
        );
        for (i, ele) in student_id_set.iter().enumerate() {
            builder
                .push(" ( ")
                .push_bind(homework_id)
                .push(" , ")
                .push_bind(ele)
                .push(" ) ");
            if i < student_id_set.len() - 1 {
                builder.push(" , ");
            }
        }
        builder.push("RETURNING *");
        builder
            .build_query_as()
            .fetch_all(&mut *conn)
            .await
            .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：查询某个作业涉及的所有学生列表
     */
    pub async fn find_all_by_homework_id(
        &self,
        schema_name: &String,
        homework_id: &Uuid,
    ) -> Result<Vec<HomeworkStudents>, String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e| e.to_string())?;
        sqlx::query_as::<_, HomeworkStudents>(
            "SELECT * FROM homework_students WHERE homework_id = $1 order by created_at desc",
        )
        .bind(homework_id)
        .fetch_all(&mut *conn)
        .await
        .map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：从作业中批量解绑学生
     */
    pub async fn batch_unbind_students_from_homework(
        &self,
        schema_name: &String,
        params: &BatchUnbindStudentsFromHomeworkParams,
    ) -> Result<(), String> {
        let mut conn = connect_with_schema(&self.db_pool, schema_name)
            .await
            .map_err(|e| e.to_string())?;
        let BatchUnbindStudentsFromHomeworkParams {
            homework_id,
            student_id_list,
        } = params;
        if student_id_list.len() == 0 {
            return Ok(());
        }
        let mut builder =
            sqlx::QueryBuilder::new("DELETE FROM homework_students WHERE homework_id = ");
        builder.push_bind(homework_id).push(" and student_id in (");
        for (i, id) in student_id_list.iter().enumerate() {
            builder.push_bind(id);
            if i < student_id_list.len() - 1 {
                builder.push(" , ");
            }
        }
        builder
            .push(" )")
            .build()
            .execute(&mut *conn)
            .await
            .map_err(|e| e.to_string())?;
        Ok(())
    }
}
