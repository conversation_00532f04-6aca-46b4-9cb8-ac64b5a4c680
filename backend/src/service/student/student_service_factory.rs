use std::sync::Arc;
use sqlx::PgPool;

use crate::service::permission::{
    DataFilterManager, StudentDataFilter
};
use super::StudentService;

/// 学生服务工厂
pub struct StudentServiceFactory;

impl StudentServiceFactory {
    /// 创建带有数据过滤器的学生服务
    pub fn create_with_data_filter(db_pool: PgPool) -> StudentService {
        let mut filter_manager = DataFilterManager::new();
        
        // 注册学生数据过滤器
        let student_filter = Arc::new(StudentDataFilter::new(db_pool.clone()));
        filter_manager.register_filter("student".to_string(), student_filter);
        
        StudentService::with_data_filter_manager(
            db_pool,
            Arc::new(filter_manager)
        )
    }
    
    /// 创建不带数据过滤器的学生服务（向后兼容）
    pub fn create_legacy(db_pool: PgPool) -> StudentService {
        StudentService::new(db_pool)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::PgPool;
    
    // 注意：这些测试需要数据库连接，在实际环境中运行
    
    #[tokio::test]
    #[ignore] // 需要数据库连接
    async fn test_create_with_data_filter() {
        // 这里需要一个测试数据库连接
        // let pool = PgPool::connect("postgresql://...").await.unwrap();
        // let service = StudentServiceFactory::create_with_data_filter(pool);
        // assert!(service.data_filter_manager.is_some());
    }
    
    #[tokio::test]
    #[ignore] // 需要数据库连接
    async fn test_create_legacy() {
        // 这里需要一个测试数据库连接
        // let pool = PgPool::connect("postgresql://...").await.unwrap();
        // let service = StudentServiceFactory::create_legacy(pool);
        // assert!(service.data_filter_manager.is_none());
    }
}
