use crate::{
    middleware::auth_middleware::AuthContext,
    model::homework::homework::{
        CreateHomeworkParams, Homework, HomeworkStatistics, PageHomeworkParams,
        UpdateHomeworkParams, HOMEWORK_STATUS_DONE, HOMEWORK_STATUS_DRAFT,
    },
    utils::{api_response::ApiResponse, schema::connect_with_schema},
};
use sqlx::{PgPool, Result};
use tower::builder;
use uuid::Uuid;
#[derive(Clone)]
pub struct HomeworkService {
    db_pool: PgPool,
}

impl HomeworkService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }
}

impl HomeworkService {
    /**
     * 作者：张瀚
     * 说明：获取作业管理统计数据
     */
    pub async fn get_statistics(&self, schema_name: &String) -> Result<HomeworkStatistics, String> {
        let mut conn = connect_with_schema(&self.db_pool, &schema_name)
            .await
            .map_err(|e| e.to_string())?;
        //查询能看的作业列表
        let list =
            sqlx::query_as::<_, Homework>("SELECT * FROM homework h order by h.created_at desc")
                .fetch_all(&mut *conn)
                .await
                .map_err(|e| e.to_string())?;

        //统计
        let total_homeworks = list.len() as i32;
        let mut completed_homeworks = 0;
        let mut in_progress_homeworks = 0;
        let mut total_students = 0;
        let mut homework_id_list: Vec<Uuid> = vec![];
        for ele in list {
            if ele.homework_status == HOMEWORK_STATUS_DONE {
                completed_homeworks += 1;
            } else if ele.homework_status != HOMEWORK_STATUS_DRAFT {
                //非完成和草稿的都是进行中
                in_progress_homeworks += 1;
            }
            homework_id_list.push(ele.id);
        }
        if homework_id_list.len() > 0 {
            let mut builder = sqlx::QueryBuilder::new("select distinct hs.student_id from homework_students hs where hs.homework_id in ( ");
            for (i, id) in homework_id_list.iter().enumerate() {
                builder.push_bind(id);
                if i < homework_id_list.len() - 1 {
                    builder.push(" , ");
                }
            }
            let student_id_list: Vec<Uuid> = builder
                .push(" )")
                .build_query_scalar()
                .fetch_all(&mut *conn)
                .await
                .map_err(|e| e.to_string())?;
            total_students = student_id_list.len() as i32;
        }
        Ok(HomeworkStatistics {
            total_homeworks,
            completed_homeworks,
            in_progress_homeworks,
            total_students,
        })
    }

    pub async fn create_homework(
        &self,
        context: &AuthContext,
        schema_name: &String,
        params: &CreateHomeworkParams,
    ) -> Result<Homework, String> {
        let CreateHomeworkParams {
            homework_name,
            homework_status,
            subject_group_id,
            description,
        } = params;
        let bean =sqlx::query_as::<_, Homework>(&format!(
            "INSERT INTO {}.homework (homework_name, homework_status, subject_group_id, description ) VALUES ($1, $2, $3, $4) RETURNING *",schema_name)
        )
        .bind(homework_name)
        .bind(homework_status)
        .bind(subject_group_id)
        .bind(description)
        .fetch_one(&self.db_pool)
        .await.map_err(|e| e.to_string())?;
        //TODO:创建关联关系
        Ok(bean)
    }

    pub async fn update_homework(
        &self,
        context: &AuthContext,
        schema_name: &String,
        params: &UpdateHomeworkParams,
    ) -> Result<Homework, String> {
        let UpdateHomeworkParams {
            id,
            homework_name,
            homework_status,
            subject_group_id,
            description,
        } = params;
        sqlx::query_as::<_, Homework>(&format!(
            "UPDATE {}.homework SET homework_name = $1, homework_status = $2, subject_group_id = $3, description = $4, updated_at = NOW() WHERE ID = $5 RETURNING *",schema_name)
        )
        .bind(homework_name)
        .bind(homework_status)
        .bind(subject_group_id)
        .bind(description)
        .bind(id)
        .fetch_one(&self.db_pool)
        .await.map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：分页查询作业
     */
    pub async fn page_homework(
        &self,
        context: &AuthContext,
        schema_name: &String,
        params: &PageHomeworkParams,
    ) -> Result<(Vec<Homework>, i64), String> {
        let PageHomeworkParams { page_params } = params;
        let list = sqlx::query_as::<_, Homework>(&format!(
            "SELECT * FROM {}.homework h order by h.created_at desc limit $1 offset $2",
            schema_name
        ))
        .bind(page_params.get_limit())
        .bind(page_params.get_offset())
        .fetch_all(&self.db_pool)
        .await
        .map_err(|e| e.to_string())?;
        let count = sqlx::query_scalar::<_, i64>(&format!(
            "SELECT count(*) FROM {}.homework h ",
            schema_name
        ))
        .fetch_one(&self.db_pool)
        .await
        .map_err(|e| e.to_string())?;
        Ok((list, count))
    }
}
