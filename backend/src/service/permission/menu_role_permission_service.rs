use std::collections::HashMap;
use anyhow::Result;
use tracing::{info, debug, error};
use uuid::Uuid;
use serde::{Serialize, Deserialize};
use super::casbin_service::{MultiTenantCasbinService, PermissionPolicy, CasbinPermissionService};

/// 菜单角色权限请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MenuRolePermissionRequest {
    pub menu_id: String,
    pub role_ids: Vec<String>,
    pub tenant_id: String,
}

/// 菜单角色权限响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MenuRolePermissionResponse {
    pub menu_id: String,
    pub roles: Vec<RoleInfo>,
}

/// 角色信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleInfo {
    pub role_id: String,
    pub role_name: String,
    pub role_code: String,
}

/// 菜单角色权限服务
pub struct MenuRolePermissionService {
    casbin_service: std::sync::Arc<MultiTenantCasbinService>,
}

impl MenuRolePermissionService {
    pub fn new(casbin_service: std::sync::Arc<MultiTenantCasbinService>) -> Self {
        Self { casbin_service }
    }

    /// 设置菜单的角色权限
    /// 这会替换菜单的所有角色权限配置
    pub async fn set_menu_role_permissions(
        &self,
        request: &MenuRolePermissionRequest,
    ) -> Result<()> {
        info!(
            "设置菜单角色权限: menu_id={}, roles={:?}, tenant_id={}",
            request.menu_id, request.role_ids, request.tenant_id
        );

        // 1. 先删除该菜单的所有现有角色权限策略
        self.remove_all_menu_role_permissions(&request.menu_id, &request.tenant_id).await?;

        // 2. 为每个角色添加菜单访问权限策略
        for role_id in &request.role_ids {
            let policy = PermissionPolicy {
                subject: role_id.clone(),
                domain: request.tenant_id.clone(),
                object: format!("menu:{}", request.menu_id),
                action: "access".to_string(),
                effect: "allow".to_string(),
            };

            match self.casbin_service.add_policy(&policy).await {
                Ok(true) => {
                    debug!("成功添加角色菜单权限策略: {} -> {}", role_id, request.menu_id);
                }
                Ok(false) => {
                    debug!("角色菜单权限策略已存在: {} -> {}", role_id, request.menu_id);
                }
                Err(e) => {
                    error!("添加角色菜单权限策略失败: {} -> {}, error: {}", role_id, request.menu_id, e);
                    return Err(e);
                }
            }
        }

        info!("菜单角色权限设置完成: menu_id={}", request.menu_id);
        Ok(())
    }

    /// 获取菜单的角色权限配置
    pub async fn get_menu_role_permissions(
        &self,
        menu_id: &str,
        tenant_id: &str,
    ) -> Result<Vec<String>> {
        debug!("获取菜单角色权限: menu_id={}, tenant_id={}", menu_id, tenant_id);

        // 从casbin策略中查询该菜单的角色权限
        let policies = self.casbin_service.get_policies_for_object(
            &format!("menu:{}", menu_id),
            tenant_id,
        ).await?;

        let mut role_ids = Vec::new();
        for policy in policies {
            // 策略格式: [role_id, tenant_id, menu:menu_id, access, allow]
            if policy.len() >= 4 && policy[2] == format!("menu:{}", menu_id) && policy[3] == "access" {
                role_ids.push(policy[0].clone());
            }
        }

        debug!("菜单角色权限查询结果: menu_id={}, roles={:?}", menu_id, role_ids);
        Ok(role_ids)
    }

    /// 添加单个角色的菜单权限
    pub async fn add_role_menu_permission(
        &self,
        menu_id: &str,
        role_id: &str,
        tenant_id: &str,
    ) -> Result<bool> {
        debug!("添加角色菜单权限: role_id={}, menu_id={}, tenant_id={}", role_id, menu_id, tenant_id);

        let policy = PermissionPolicy {
            subject: role_id.to_string(),
            domain: tenant_id.to_string(),
            object: format!("menu:{}", menu_id),
            action: "access".to_string(),
            effect: "allow".to_string(),
        };

        self.casbin_service.add_policy(&policy).await
    }

    /// 移除单个角色的菜单权限
    pub async fn remove_role_menu_permission(
        &self,
        menu_id: &str,
        role_id: &str,
        tenant_id: &str,
    ) -> Result<bool> {
        debug!("移除角色菜单权限: role_id={}, menu_id={}, tenant_id={}", role_id, menu_id, tenant_id);

        let policy = PermissionPolicy {
            subject: role_id.to_string(),
            domain: tenant_id.to_string(),
            object: format!("menu:{}", menu_id),
            action: "access".to_string(),
            effect: "allow".to_string(),
        };

        self.casbin_service.remove_policy(&policy).await
    }

    /// 移除菜单的所有角色权限
    pub async fn remove_all_menu_role_permissions(
        &self,
        menu_id: &str,
        tenant_id: &str,
    ) -> Result<()> {
        debug!("移除菜单所有角色权限: menu_id={}, tenant_id={}", menu_id, tenant_id);

        // 获取该菜单的所有角色权限策略
        let role_ids = self.get_menu_role_permissions(menu_id, tenant_id).await?;

        // 逐个删除策略
        for role_id in role_ids {
            if let Err(e) = self.remove_role_menu_permission(menu_id, &role_id, tenant_id).await {
                error!("删除角色菜单权限失败: role_id={}, menu_id={}, error={}", role_id, menu_id, e);
            }
        }

        Ok(())
    }

    /// 检查角色是否有菜单访问权限
    pub async fn check_role_menu_permission(
        &self,
        role_id: &str,
        menu_id: &str,
        tenant_id: &str,
    ) -> Result<bool> {
        debug!("检查角色菜单权限: role_id={}, menu_id={}, tenant_id={}", role_id, menu_id, tenant_id);

        use super::casbin_service::PermissionRequest;

        let request = PermissionRequest {
            subject: role_id.to_string(),
            domain: tenant_id.to_string(),
            object: format!("menu:{}", menu_id),
            action: "access".to_string(),
        };

        self.casbin_service.enforce(&request).await
    }

    /// 获取角色的所有菜单权限
    pub async fn get_role_menu_permissions(
        &self,
        role_id: &str,
        tenant_id: &str,
    ) -> Result<Vec<String>> {
        debug!("获取角色菜单权限: role_id={}, tenant_id={}", role_id, tenant_id);

        let policies = self.casbin_service.get_policies_for_subject(role_id, tenant_id).await?;

        let mut menu_ids = Vec::new();
        for policy in policies {
            // 策略格式: [role_id, tenant_id, menu:menu_id, access, allow]
            if policy.len() >= 4 && policy[2].starts_with("menu:") && policy[3] == "access" {
                if let Some(menu_id) = policy[2].strip_prefix("menu:") {
                    menu_ids.push(menu_id.to_string());
                }
            }
        }

        debug!("角色菜单权限查询结果: role_id={}, menus={:?}", role_id, menu_ids);
        Ok(menu_ids)
    }

    /// 批量设置多个菜单的角色权限
    pub async fn batch_set_menu_role_permissions(
        &self,
        requests: Vec<MenuRolePermissionRequest>,
    ) -> Result<HashMap<String, Result<(), String>>> {
        info!("批量设置菜单角色权限: count={}", requests.len());

        let mut results = HashMap::new();

        for request in requests {
            let menu_id = request.menu_id.clone();
            match self.set_menu_role_permissions(&request).await {
                Ok(()) => {
                    results.insert(menu_id, Ok(()));
                }
                Err(e) => {
                    results.insert(menu_id, Err(e.to_string()));
                }
            }
        }

        Ok(results)
    }
}
