pub mod casbin_service;
pub mod role_sync_service;
pub mod menu_role_permission_service;
pub mod postgres_adapter;
pub mod data_filter;
pub mod data_scope_sync_service;
pub mod optimized_student_filter;
pub mod class_teacher_permission_manager;
pub mod menu_migration_service;

pub use casbin_service::{
    CasbinPermissionService,
    MultiTenantCasbinService,
    PermissionRequest,
    PermissionPolicy,
    RoleRelation,
    MenuPermission,
    DataScope,
};

pub use role_sync_service::{
    RolePermissionSyncService,
    SyncConfig,
    SyncResult,
};

pub use postgres_adapter::{
    PostgresAdapter,
    CasbinRule,
};

pub use data_filter::{
    Data<PERSON><PERSON>er,
    DataFilterManager,
    FilterContext,
    FilterCondition,
    FilterParam,
    BaseDataFilter,
    StudentDataFilter,
    AdministrativeClassDataFilter,
};

pub use data_scope_sync_service::{
    DataScopePermissionSyncService,
    DataScopeSyncConfig,
    DataScopeSyncResult,
};

pub use optimized_student_filter::OptimizedStudentDataFilter;

pub use class_teacher_permission_manager::{
    ClassTeacherPermissionManager,
    ClassTeacherPermission,
    PermissionSyncResult,
};

pub use menu_migration_service::{
    MenuMigrationService,
    MigrationReport,
};

