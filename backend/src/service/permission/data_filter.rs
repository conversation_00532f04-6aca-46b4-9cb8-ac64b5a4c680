use std::collections::HashMap;
use std::sync::Arc;
use anyhow::{Result, anyhow};
use async_trait::async_trait;
use sqlx::{PgPool, QueryBuilder, Postgres};
use uuid::Uuid;
use serde::{Serialize, Deserialize};
use tracing::{debug, error, info, warn};
use super::{CasbinPermissionService, DataScope};

/// 数据过滤条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilterCondition {
    /// SQL WHERE 条件
    pub sql_condition: String,
    /// 绑定参数
    pub bind_params: Vec<FilterParam>,
}

/// 过滤参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterParam {
    Uuid(Uuid),
    String(String),
    Int(i64),
    Bool(bool),
}

/// 数据过滤上下文
#[derive(Debug, <PERSON>lone)]
pub struct FilterContext {
    /// 用户ID
    pub user_id: Uuid,
    /// 租户ID
    pub tenant_id: String,
    /// 用户身份标识（用于Casbin）
    pub user_identity: String,
    /// 资源类型
    pub resource: String,
    /// 操作类型
    pub action: String,
    /// 数据库schema名称
    pub schema_name: String,
}

/// 通用数据过滤器trait
#[async_trait]
pub trait DataFilter: Send + Sync {
    /// 获取过滤条件
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>>;
    
    /// 应用过滤条件到查询构建器
    fn apply_filter_to_query<'a>(
        &self,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()>;

    /// 应用过滤条件到计数查询构建器
    fn apply_filter_to_count_query<'a>(
        &self,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()>;

    /// 直接应用过滤条件到查询构建器（新方法）
    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool>;
}

/// 数据过滤器管理器
pub struct DataFilterManager {
    filters: HashMap<String, Arc<dyn DataFilter>>,
}

impl DataFilterManager {
    pub fn new() -> Self {
        Self {
            filters: HashMap::new(),
        }
    }
    
    /// 注册数据过滤器
    pub fn register_filter(&mut self, resource: String, filter: Arc<dyn DataFilter>) {
        self.filters.insert(resource, filter);
    }
    
    /// 获取数据过滤器
    pub fn get_filter(&self, resource: &str) -> Option<&Arc<dyn DataFilter>> {
        self.filters.get(resource)
    }
    
    /// 应用数据过滤
    pub async fn apply_data_filter<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        if let Some(filter) = self.get_filter(&context.resource) {
            // 直接在QueryBuilder上应用过滤条件
            return filter.apply_filter_to_builders(context, query_builder, count_builder, casbin_service).await;
        }

        debug!("No data filter applied for resource: {}", context.resource);
        Ok(false)
    }
}

/// 基础数据过滤器实现
pub struct BaseDataFilter;

impl BaseDataFilter {
    /// 解析数据范围为过滤条件
    pub fn parse_data_scopes_to_condition(
        data_scopes: &[DataScope],
        resource: &str,
        table_alias: &str,
    ) -> Result<Option<FilterCondition>> {
        if data_scopes.is_empty() {
            return Ok(None);
        }
        
        let mut conditions = Vec::new();
        let mut bind_params = Vec::new();
        
        for scope in data_scopes {
            if scope.resource != resource {
                continue;
            }
            
            match scope.scope_type.as_str() {
                "class" => {
                    if scope.scope_value != "*" {
                        if let Ok(class_id) = Uuid::parse_str(&scope.scope_value) {
                            conditions.push(format!("{}.administrative_class_id = ${}", 
                                                   table_alias, bind_params.len() + 1));
                            bind_params.push(FilterParam::Uuid(class_id));
                        }
                    }
                },
                "grade" => {
                    if scope.scope_value != "*" {
                        conditions.push(format!("{}.grade_level_code = ${}", 
                                               table_alias, bind_params.len() + 1));
                        bind_params.push(FilterParam::String(scope.scope_value.clone()));
                    }
                },
                "subject_group" => {
                    if scope.scope_value != "*" {
                        // 需要通过教师表关联查询
                        warn!("Subject group filtering not implemented yet");
                    }
                },
                "school" => {
                    // 学校级别权限，通常不需要额外过滤（租户隔离已处理）
                    debug!("School level permission detected, no additional filtering needed");
                },
                _ => {
                    warn!("Unknown scope type: {}", scope.scope_type);
                }
            }
        }
        
        if conditions.is_empty() {
            return Ok(None);
        }
        
        let sql_condition = if conditions.len() == 1 {
            conditions[0].clone()
        } else {
            format!("({})", conditions.join(" OR "))
        };
        
        Ok(Some(FilterCondition {
            sql_condition,
            bind_params,
        }))
    }
    
    /// 检查用户是否为系统管理员
    pub async fn is_system_admin(
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        use crate::service::permission::PermissionRequest;
        
        let admin_request = PermissionRequest {
            subject: context.user_identity.clone(),
            domain: context.tenant_id.clone(),
            object: "system:admin".to_string(),
            action: "manage".to_string(),
        };
        
        casbin_service.enforce(&admin_request).await
    }
}

/// 学生数据过滤器
pub struct StudentDataFilter {
    db_pool: PgPool,
}

impl StudentDataFilter {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 获取班主任负责的班级ID列表
    async fn get_class_teacher_class_ids(
        &self,
        context: &FilterContext,
    ) -> Result<Vec<Uuid>> {
        info!("Getting class teacher class IDs for user_id: {} in schema: {}",
              context.user_id, context.schema_name);

        // 1. 根据user_id查找教师记录
        let teacher_query = format!(
            "SELECT id FROM {}.teachers WHERE user_id = $1 AND is_active = true",
            context.schema_name
        );
        info!("Teacher query: {}", teacher_query);
        let teacher_id: Option<Uuid> = sqlx::query_scalar(&teacher_query)
            .bind(context.user_id)
            .fetch_optional(&self.db_pool)
            .await?;

        if let Some(teacher_id) = teacher_id {
            info!("Found teacher_id: {} for user_id: {}", teacher_id, context.user_id);
            // 2. 根据教师ID查找其负责的行政班
            let class_query = format!(
                "SELECT id FROM {}.administrative_classes WHERE teacher_id = $1 AND is_active = true",
                context.schema_name
            );
            info!("Class query: {}", class_query);
            let class_ids: Vec<Uuid> = sqlx::query_scalar(&class_query)
                .bind(teacher_id)
                .fetch_all(&self.db_pool)
                .await?;

            info!("Found class_ids: {:?} for teacher_id: {}", class_ids, teacher_id);
            Ok(class_ids)
        } else {
            info!("No teacher found for user_id: {}", context.user_id);
            Ok(Vec::new())
        }
    }

    /// 获取教师任教的年级列表（从user_identities表获取）
    async fn get_teacher_grade_codes(
        &self,
        context: &FilterContext,
    ) -> Result<Vec<String>> {
        // 从user_identities表查询target_type='grade'的身份记录
        let grade_query = format!(
            "SELECT DISTINCT gl.code
             FROM {}.user_identities ui
             JOIN public.grade_levels gl ON ui.target_id = gl.id
             WHERE ui.user_id = $1 AND ui.target_type = 'grade'",
            context.schema_name
        );

        let grade_codes: Vec<String> = sqlx::query_scalar(&grade_query)
            .bind(context.user_id)
            .fetch_all(&self.db_pool)
            .await?;

        info!("Found grade codes for user {}: {:?}", context.user_id, grade_codes);
        Ok(grade_codes)
    }

    /// 获取教师所在学科组的学生（通过教学班关联）
    async fn get_subject_group_student_ids(
        &self,
        context: &FilterContext,
    ) -> Result<Vec<Uuid>> {
        // 这里需要根据具体的学科组和教学班关系来实现
        // 暂时返回空，后续可以扩展
        warn!("Subject group student filtering not implemented yet");
        Ok(Vec::new())
    }
}

/// 行政班数据过滤器
pub struct AdministrativeClassDataFilter {
    db_pool: PgPool,
}

impl AdministrativeClassDataFilter {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }
}

#[async_trait]
impl DataFilter for AdministrativeClassDataFilter {
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            return Ok(None);
        }

        // 2. 获取用户的数据权限范围
        let data_scopes = casbin_service
            .get_user_data_scopes(&context.user_identity, &context.tenant_id, &context.resource)
            .await?;

        // 3. 解析数据范围为过滤条件
        if let Some(condition) = BaseDataFilter::parse_data_scopes_to_condition(
            &data_scopes, &context.resource, "ac"
        )? {
            return Ok(Some(condition));
        }

        // 4. 回退到基于角色的特殊处理
        // 班主任只能看到自己负责的班级
        let teacher_query = format!(
            "SELECT id FROM {}.teachers WHERE user_id = $1 AND is_active = true",
            context.schema_name
        );
        let teacher_id: Option<Uuid> = sqlx::query_scalar(&teacher_query)
            .bind(context.user_id)
            .fetch_optional(&self.db_pool)
            .await?;

        if let Some(teacher_id) = teacher_id {
            return Ok(Some(FilterCondition {
                sql_condition: "ac.teacher_id = $1".to_string(),
                bind_params: vec![FilterParam::Uuid(teacher_id)],
            }));
        }

        // 5. 无权限访问
        Ok(Some(FilterCondition {
            sql_condition: "1 = 0".to_string(),
            bind_params: Vec::new(),
        }))
    }

    fn apply_filter_to_query<'a>(
        &self,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()> {
        query_builder.push(" AND (").push(&condition.sql_condition).push(")");

        for param in &condition.bind_params {
            match param {
                FilterParam::Uuid(uuid) => query_builder.push_bind(uuid),
                FilterParam::String(s) => query_builder.push_bind(s),
                FilterParam::Int(i) => query_builder.push_bind(i),
                FilterParam::Bool(b) => query_builder.push_bind(b),
            };
        }

        Ok(())
    }

    fn apply_filter_to_count_query<'a>(
        &self,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()> {
        count_builder.push(" AND (").push(&condition.sql_condition).push(")");

        for param in &condition.bind_params {
            match param {
                FilterParam::Uuid(uuid) => count_builder.push_bind(uuid),
                FilterParam::String(s) => count_builder.push_bind(s),
                FilterParam::Int(i) => count_builder.push_bind(i),
                FilterParam::Bool(b) => count_builder.push_bind(b),
            };
        }

        Ok(())
    }

    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            return Ok(false);
        }

        // 2. 获取用户的数据权限范围
        let data_scopes = casbin_service
            .get_user_data_scopes(&context.user_identity, &context.tenant_id, &context.resource)
            .await?;

        // 3. 解析数据范围为过滤条件
        if let Some(condition) = BaseDataFilter::parse_data_scopes_to_condition(
            &data_scopes, &context.resource, "ac"
        )? {
            query_builder.push(" AND (").push(&condition.sql_condition).push(")");
            count_builder.push(" AND (").push(&condition.sql_condition).push(")");

            for param in &condition.bind_params {
                match param {
                    FilterParam::Uuid(uuid) => {
                        query_builder.push_bind(*uuid);
                        count_builder.push_bind(*uuid);
                    },
                    FilterParam::String(s) => {
                        query_builder.push_bind(s.clone());
                        count_builder.push_bind(s.clone());
                    },
                    FilterParam::Int(i) => {
                        query_builder.push_bind(*i);
                        count_builder.push_bind(*i);
                    },
                    FilterParam::Bool(b) => {
                        query_builder.push_bind(*b);
                        count_builder.push_bind(*b);
                    },
                }
            }
            return Ok(true);
        }

        // 4. 回退到基于角色的特殊处理
        // 班主任只能看到自己负责的班级
        let teacher_query = format!(
            "SELECT id FROM {}.teachers WHERE user_id = $1 AND is_active = true",
            context.schema_name
        );
        let teacher_id: Option<Uuid> = sqlx::query_scalar(&teacher_query)
            .bind(context.user_id)
            .fetch_optional(&self.db_pool)
            .await?;

        if let Some(teacher_id) = teacher_id {
            query_builder.push(" AND ac.teacher_id = ").push_bind(teacher_id);
            count_builder.push(" AND ac.teacher_id = ").push_bind(teacher_id);
            return Ok(true);
        }

        // 5. 无权限访问
        query_builder.push(" AND 1 = 0");
        count_builder.push(" AND 1 = 0");
        Ok(true)
    }
}

#[async_trait]
impl DataFilter for StudentDataFilter {
    async fn get_filter_condition(
        &self,
        context: &FilterContext,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<Option<FilterCondition>> {
        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            debug!("User is system admin, no data filtering applied");
            return Ok(None);
        }

        // 2. 获取用户的数据权限范围
        let data_scopes = casbin_service
            .get_user_data_scopes(&context.user_identity, &context.tenant_id, &context.resource)
            .await?;

        // 3. 如果有明确的数据范围权限，使用Casbin数据范围
        if let Some(condition) = BaseDataFilter::parse_data_scopes_to_condition(
            &data_scopes, &context.resource, "s"
        )? {
            return Ok(Some(condition));
        }

        // 4. 回退到基于角色的特殊处理（兼容现有逻辑）
        let mut all_conditions = Vec::new();
        let mut all_bind_params = Vec::new();

        // 4.1 班主任权限：可以查看自己负责班级的学生
        let class_ids = self.get_class_teacher_class_ids(context).await?;
        if !class_ids.is_empty() {
            if class_ids.len() == 1 {
                all_conditions.push("s.administrative_class_id = ?".to_string());
                all_bind_params.push(FilterParam::Uuid(class_ids[0]));
            } else {
                let placeholders = vec!["?"; class_ids.len()].join(", ");
                all_conditions.push(format!("s.administrative_class_id IN ({})", placeholders));
                for class_id in class_ids {
                    all_bind_params.push(FilterParam::Uuid(class_id));
                }
            }
        }

        // 4.2 年级权限：可以查看任教年级的学生
        let grade_codes = self.get_teacher_grade_codes(context).await?;
        if !grade_codes.is_empty() {
            let mut grade_conditions = Vec::new();
            for grade_code in grade_codes {
                // 需要通过行政班表关联查询年级
                grade_conditions.push(format!(
                    "EXISTS (SELECT 1 FROM {}.administrative_classes ac WHERE ac.id = s.administrative_class_id AND ac.grade_level_code = ?)",
                    context.schema_name
                ));
                all_bind_params.push(FilterParam::String(grade_code));
            }
            all_conditions.push(format!("({})", grade_conditions.join(" OR ")));
        }

        // 4.3 学科组权限：可以查看教学班的学生（暂未实现）
        // let subject_student_ids = self.get_subject_group_student_ids(context).await?;

        if !all_conditions.is_empty() {
            let sql_condition = if all_conditions.len() == 1 {
                all_conditions[0].clone()
            } else {
                format!("({})", all_conditions.join(" OR "))
            };

            return Ok(Some(FilterCondition {
                sql_condition,
                bind_params: all_bind_params,
            }));
        }

        // 5. 如果没有任何权限，返回空结果条件
        Ok(Some(FilterCondition {
            sql_condition: "1 = 0".to_string(), // 永远为false的条件
            bind_params: Vec::new(),
        }))
    }

    fn apply_filter_to_query<'a>(
        &self,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()> {
        query_builder.push(" AND (").push(&condition.sql_condition).push(")");

        for param in &condition.bind_params {
            match param {
                FilterParam::Uuid(uuid) => query_builder.push_bind(uuid),
                FilterParam::String(s) => query_builder.push_bind(s),
                FilterParam::Int(i) => query_builder.push_bind(i),
                FilterParam::Bool(b) => query_builder.push_bind(b),
            };
        }

        Ok(())
    }

    fn apply_filter_to_count_query<'a>(
        &self,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        condition: &'a FilterCondition,
    ) -> Result<()> {
        count_builder.push(" AND (").push(&condition.sql_condition).push(")");

        for param in &condition.bind_params {
            match param {
                FilterParam::Uuid(uuid) => count_builder.push_bind(uuid),
                FilterParam::String(s) => count_builder.push_bind(s),
                FilterParam::Int(i) => count_builder.push_bind(i),
                FilterParam::Bool(b) => count_builder.push_bind(b),
            };
        }

        Ok(())
    }

    async fn apply_filter_to_builders<'a>(
        &self,
        context: &FilterContext,
        query_builder: &mut QueryBuilder<'a, Postgres>,
        count_builder: &mut QueryBuilder<'a, Postgres>,
        casbin_service: &dyn CasbinPermissionService,
    ) -> Result<bool> {
        info!("StudentDataFilter::apply_filter_to_builders called for user_id: {}, tenant_id: {}",
              context.user_id, context.tenant_id);

        // 1. 检查是否为系统管理员
        if BaseDataFilter::is_system_admin(context, casbin_service).await? {
            info!("User {} is system admin, no data filtering applied", context.user_id);
            return Ok(false);
        }

        // 2. 获取用户的数据权限范围
        let data_scopes = casbin_service
            .get_user_data_scopes(&context.user_identity, &context.tenant_id, &context.resource)
            .await?;

        // 3. 如果有明确的数据范围权限，使用Casbin数据范围
        if let Some(condition) = BaseDataFilter::parse_data_scopes_to_condition(
            &data_scopes, &context.resource, "s"
        )? {
            query_builder.push(" AND (").push(&condition.sql_condition).push(")");
            count_builder.push(" AND (").push(&condition.sql_condition).push(")");

            for param in &condition.bind_params {
                match param {
                    FilterParam::Uuid(uuid) => {
                        query_builder.push_bind(*uuid);
                        count_builder.push_bind(*uuid);
                    },
                    FilterParam::String(s) => {
                        query_builder.push_bind(s.clone());
                        count_builder.push_bind(s.clone());
                    },
                    FilterParam::Int(i) => {
                        query_builder.push_bind(*i);
                        count_builder.push_bind(*i);
                    },
                    FilterParam::Bool(b) => {
                        query_builder.push_bind(*b);
                        count_builder.push_bind(*b);
                    },
                }
            }
            return Ok(true);
        }

        // 4. 回退到基于角色的特殊处理（兼容现有逻辑）
        let mut has_conditions = false;

        // 4.1 班主任权限：可以查看自己负责班级的学生
        let class_ids = self.get_class_teacher_class_ids(context).await?;
        info!("Found class_ids for user {}: {:?}", context.user_id, class_ids);
        if !class_ids.is_empty() {
            if !has_conditions {
                query_builder.push(" AND (");
                count_builder.push(" AND (");
            } else {
                query_builder.push(" OR ");
                count_builder.push(" OR ");
            }

            if class_ids.len() == 1 {
                query_builder.push("s.administrative_class_id = ").push_bind(class_ids[0]);
                count_builder.push("s.administrative_class_id = ").push_bind(class_ids[0]);
            } else {
                query_builder.push("s.administrative_class_id IN (");
                count_builder.push("s.administrative_class_id IN (");

                for (i, class_id) in class_ids.iter().enumerate() {
                    if i > 0 {
                        query_builder.push(", ");
                        count_builder.push(", ");
                    }
                    query_builder.push_bind(*class_id);
                    count_builder.push_bind(*class_id);
                }

                query_builder.push(")");
                count_builder.push(")");
            }
            has_conditions = true;
        }

        // 4.2 年级权限：可以查看任教年级的学生
        let grade_codes = self.get_teacher_grade_codes(context).await?;
        if !grade_codes.is_empty() {
            if !has_conditions {
                query_builder.push(" AND (");
                count_builder.push(" AND (");
            } else {
                query_builder.push(" OR ");
                count_builder.push(" OR ");
            }

            query_builder.push("(");
            count_builder.push("(");

            for (i, grade_code) in grade_codes.iter().enumerate() {
                if i > 0 {
                    query_builder.push(" OR ");
                    count_builder.push(" OR ");
                }

                let exists_query = format!(
                    "EXISTS (SELECT 1 FROM {}.administrative_classes ac WHERE ac.id = s.administrative_class_id AND ac.grade_level_code = ",
                    context.schema_name
                );

                query_builder.push(&exists_query).push_bind(grade_code.clone()).push(")");
                count_builder.push(&exists_query).push_bind(grade_code.clone()).push(")");
            }

            query_builder.push(")");
            count_builder.push(")");
            has_conditions = true;
        }

        if has_conditions {
            query_builder.push(")");
            count_builder.push(")");
            debug!("Applied data filter for user {} in tenant {}", context.user_id, context.tenant_id);
            return Ok(true);
        }

        // 5. 如果没有任何权限，返回空结果条件
        query_builder.push(" AND 1 = 0");
        count_builder.push(" AND 1 = 0");
        debug!("No permissions found, applied restrictive filter");
        Ok(true)
    }
}



// 包含测试模块

