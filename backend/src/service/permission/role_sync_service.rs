use std::sync::Arc;
use anyhow::{Result, anyhow};
use sqlx::{PgPool, Row};
use uuid::Uuid;
use tracing::{info, warn, error, debug};
use serde::{Serialize, Deserialize};

use crate::service::permission::{
    CasbinPermissionService, 
    MultiTenantCasbinService, 
    PermissionPolicy, 
    RoleRelation
};
use crate::model::role::role::{Role, RoleLevel, SystemRoles};
use crate::model::role::permission::Permission;
use crate::utils::error::AppError;

/// 权限同步配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncConfig {
    pub sync_system_roles: bool,
    pub sync_tenant_roles: bool,
    pub sync_user_identities: bool,
    pub clear_existing_policies: bool,
    pub dry_run: bool,
}

impl Default for SyncConfig {
    fn default() -> Self {
        Self {
            sync_system_roles: true,
            sync_tenant_roles: true,
            sync_user_identities: true,
            clear_existing_policies: false,
            dry_run: false,
        }
    }
}

/// 同步结果统计
#[derive(Debug, Serialize, Deserialize)]
pub struct SyncResult {
    pub tenant_id: String,
    pub roles_synced: i32,
    pub permissions_synced: i32,
    pub user_identities_synced: i32,
    pub policies_created: i32,
    pub role_relations_created: i32,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

/// 角色权限同步服务
pub struct RolePermissionSyncService {
    pool: PgPool,
    casbin_service: Arc<MultiTenantCasbinService>,
}

impl RolePermissionSyncService {
    /// 创建新的同步服务实例
    pub fn new(pool: PgPool, casbin_service: Arc<MultiTenantCasbinService>) -> Self {
        Self {
            pool,
            casbin_service,
        }
    }
    
    /// 同步所有租户的权限策略
    pub async fn sync_all_tenants(&self, config: &SyncConfig) -> Result<Vec<SyncResult>> {
        info!("Starting permission sync for all tenants with config: {:?}", config);
        
        // 获取所有租户
        let tenants = self.get_all_tenants().await?;
        let mut results = Vec::new();
        
        for tenant in tenants {
            let tenant_id = tenant.id.to_string();
            match self.sync_tenant_permissions(&tenant_id, config).await {
                Ok(result) => {
                    info!("Successfully synced tenant {}: {:?}", tenant_id, result);
                    results.push(result);
                }
                Err(e) => {
                    error!("Failed to sync tenant {}: {}", tenant_id, e);
                    results.push(SyncResult {
                        tenant_id: tenant_id.clone(),
                        roles_synced: 0,
                        permissions_synced: 0,
                        user_identities_synced: 0,
                        policies_created: 0,
                        role_relations_created: 0,
                        errors: vec![e.to_string()],
                        warnings: vec![],
                    });
                }
            }
        }
        
        info!("Completed permission sync for {} tenants", results.len());
        Ok(results)
    }
    
    /// 同步指定租户的权限策略
    pub async fn sync_tenant_permissions(&self, tenant_id: &str, config: &SyncConfig) -> Result<SyncResult> {
        info!("Syncing permissions for tenant: {}", tenant_id);
        
        let mut result = SyncResult {
            tenant_id: tenant_id.to_string(),
            roles_synced: 0,
            permissions_synced: 0,
            user_identities_synced: 0,
            policies_created: 0,
            role_relations_created: 0,
            errors: Vec::new(),
            warnings: Vec::new(),
        };
        
        // 开始事务
        let mut tx = self.pool.begin().await?;
        
        // 1. 清除现有策略（如果配置要求）
        if config.clear_existing_policies && !config.dry_run {
            self.casbin_service.clear_tenant_policies(tenant_id).await?;
            info!("Cleared existing policies for tenant: {}", tenant_id);
        }
        
        // 2. 同步系统角色
        if config.sync_system_roles {
            let sync_count = self.sync_system_roles(&mut tx, tenant_id, config, &mut result).await?;
            result.roles_synced += sync_count;
        }
        
        // 3. 同步租户角色
        if config.sync_tenant_roles {
            let sync_count = self.sync_tenant_roles(&mut tx, tenant_id, config, &mut result).await?;
            result.roles_synced += sync_count;
        }
        
        // 4. 同步用户身份
        if config.sync_user_identities {
            let sync_count = self.sync_user_identities(&mut tx, tenant_id, config, &mut result).await?;
            result.user_identities_synced = sync_count;
        }
        
        // 5. 同步角色权限关联
        let perm_count = self.sync_role_permissions(&mut tx, tenant_id, config, &mut result).await?;
        result.permissions_synced = perm_count;
        
        // 提交事务
        if !config.dry_run {
            tx.commit().await?;
            info!("Successfully committed permission sync for tenant: {}", tenant_id);
        } else {
            tx.rollback().await?;
            info!("Dry run completed for tenant: {}", tenant_id);
        }
        
        Ok(result)
    }
    
    /// 同步系统角色
    async fn sync_system_roles(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        tenant_id: &str,
        config: &SyncConfig,
        result: &mut SyncResult,
    ) -> Result<i32> {
        debug!("Syncing system roles for tenant: {}", tenant_id);
        
        // 获取系统角色
        let system_roles = sqlx::query_as::<_, Role>(
            "SELECT * FROM public.roles WHERE is_system = true AND is_active = true"
        )
        .fetch_all(&mut **tx)
        .await?;
        
        let mut synced_count = 0;
        
        for role in system_roles {
            // 创建角色继承关系策略
            if let Err(e) = self.create_role_inheritance_policies(&role, tenant_id, config).await {
                result.warnings.push(format!("Failed to create inheritance for role {}: {}", role.code, e));
                continue;
            }
            
            // 创建基础权限策略
            if let Err(e) = self.create_basic_role_policies(&role, tenant_id, config).await {
                result.warnings.push(format!("Failed to create basic policies for role {}: {}", role.code, e));
                continue;
            }
            
            synced_count += 1;
            result.policies_created += 1;
        }
        
        Ok(synced_count)
    }
    
    /// 同步租户角色
    async fn sync_tenant_roles(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        tenant_id: &str,
        config: &SyncConfig,
        result: &mut SyncResult,
    ) -> Result<i32> {
        debug!("Syncing tenant roles for tenant: {}", tenant_id);
        
        // 获取租户角色
        let tenant_roles = sqlx::query_as::<_, Role>(
            "SELECT * FROM public.roles WHERE tenant_id = $1 AND is_active = true"
        )
        .bind(Uuid::parse_str(tenant_id)?)
        .fetch_all(&mut **tx)
        .await?;
        
        let mut synced_count = 0;
        
        for role in tenant_roles {
            // 创建租户特定角色策略
            if let Err(e) = self.create_tenant_role_policies(&role, tenant_id, config).await {
                result.warnings.push(format!("Failed to create tenant policies for role {}: {}", role.code, e));
                continue;
            }
            
            synced_count += 1;
            result.policies_created += 1;
        }
        
        Ok(synced_count)
    }
    
    /// 同步用户身份
    async fn sync_user_identities(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        tenant_id: &str,
        config: &SyncConfig,
        result: &mut SyncResult,
    ) -> Result<i32> {
        debug!("Syncing user identities for tenant: {}", tenant_id);
        
        // 构建租户 schema 名称
        let tenant_uuid = Uuid::parse_str(tenant_id)?;
        let schema_query = format!(
            "SELECT tenant_code FROM public.tenants WHERE id = '{}'",
            tenant_uuid
        );
        
        let tenant_row = sqlx::query(&schema_query)
            .fetch_one(&mut **tx)
            .await?;
        
        let tenant_code: String = tenant_row.get("tenant_code");
        let schema_name = format!("tenant_{}", tenant_code);
        
        // 获取用户身份数据
        let identity_query = format!(
            r#"
            SELECT 
                ui.id,
                ui.user_id,
                ui.role_id,
                ui.target_type,
                ui.target_id,
                ui.subject,
                r.code as role_code,
                r.level as role_level
            FROM {}.user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.is_active = true
            "#,
            schema_name
        );
        
        let identities = sqlx::query(&identity_query)
            .fetch_all(&mut **tx)
            .await?;
        
        let mut synced_count = 0;
        
        for identity in identities {
            let user_id: Uuid = identity.get("user_id");
            let role_code: String = identity.get("role_code");
            let target_type: String = identity.get("target_type");
            let target_id: Option<Uuid> = identity.get("target_id");
            let subject: Option<String> = identity.get("subject");
            
            // 构建用户身份标识
            let user_identity = self.build_user_identity(&user_id, &role_code, &target_type, target_id.as_ref());
            
            // 创建用户-角色关系
            let role_relation = RoleRelation {
                user: user_identity.clone(),
                role: role_code.clone(),
                domain: tenant_id.to_string(),
            };
            
            if !config.dry_run {
                if let Err(e) = self.casbin_service.add_role(&role_relation).await {
                    result.warnings.push(format!("Failed to add role relation for user {}: {}", user_id, e));
                    continue;
                }
            }
            
            // 创建用户特定的数据权限策略
            if let Err(e) = self.create_user_data_policies(&user_identity, &role_code, &target_type, target_id.as_ref(), subject.as_deref(), tenant_id, config).await {
                result.warnings.push(format!("Failed to create data policies for user {}: {}", user_id, e));
                continue;
            }
            
            synced_count += 1;
            result.role_relations_created += 1;
        }
        
        Ok(synced_count)
    }
    
    /// 同步角色权限关联
    async fn sync_role_permissions(
        &self,
        tx: &mut sqlx::Transaction<'_, sqlx::Postgres>,
        tenant_id: &str,
        config: &SyncConfig,
        result: &mut SyncResult,
    ) -> Result<i32> {
        debug!("Syncing role permissions for tenant: {}", tenant_id);
        
        // 获取角色权限关联
        let role_permissions = sqlx::query!(
            r#"
            SELECT 
                r.code as role_code,
                p.resource,
                p.action,
                p.scope
            FROM public.roles r
            JOIN public.role_permissions rp ON r.id = rp.role_id
            JOIN public.permissions p ON rp.permission_id = p.id
            WHERE (r.is_system = true OR r.tenant_id = $1) 
            AND r.is_active = true
            "#,
            Uuid::parse_str(tenant_id)?
        )
        .fetch_all(&mut **tx)
        .await?;
        
        let mut synced_count = 0;
        
        for rp in role_permissions {
            let object = if rp.scope.is_empty() || rp.scope == "*" {
                format!("{}:*", rp.resource)
            } else {
                format!("{}:{}", rp.resource, rp.scope)
            };
            
            let policy = PermissionPolicy {
                subject: rp.role_code,
                domain: tenant_id.to_string(),
                object,
                action: rp.action,
                effect: "allow".to_string(),
            };
            
            if !config.dry_run {
                if let Err(e) = self.casbin_service.add_policy(&policy).await {
                    result.warnings.push(format!("Failed to add permission policy: {}", e));
                    continue;
                }
            }
            
            synced_count += 1;
        }
        
        Ok(synced_count)
    }
    
    /// 创建角色继承关系策略
    async fn create_role_inheritance_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
        // 根据角色级别创建继承关系
        match role.level {
            RoleLevel::SuperAdmin => {
                // 超级管理员继承所有权限，无需特殊处理
            }
            RoleLevel::TenantAdmin => {
                // 租户管理员继承部分管理权限
                self.add_inheritance_policy(&role.code, "admin", tenant_id, config).await?;
            }
            RoleLevel::Principal => {
                // 校长继承管理人员权限
                self.add_inheritance_policy(&role.code, "management", tenant_id, config).await?;
            }
            RoleLevel::AcademicDirector => {
                // 教导主任继承教学管理权限
                self.add_inheritance_policy(&role.code, "academic_staff", tenant_id, config).await?;
            }
            RoleLevel::SubjectLeader | RoleLevel::GradeLeader => {
                // 学科组长、年级长继承教师权限
                self.add_inheritance_policy(&role.code, "staff", tenant_id, config).await?;
            }
            RoleLevel::ClassTeacher | RoleLevel::Teacher => {
                // 班主任、任课老师继承基础教师权限
                self.add_inheritance_policy(&role.code, "teacher", tenant_id, config).await?;
            }
            RoleLevel::Student => {
                // 学生继承基础用户权限
                self.add_inheritance_policy(&role.code, "user", tenant_id, config).await?;
            }
            RoleLevel::Parent => {
                // 家长继承基础查看权限  
                self.add_inheritance_policy(&role.code, "viewer", tenant_id, config).await?;
            }
        }
        
        Ok(())
    }
    
    /// 添加继承关系策略
    async fn add_inheritance_policy(&self, child_role: &str, parent_role: &str, tenant_id: &str, config: &SyncConfig) -> Result<()> {
        if config.dry_run {
            debug!("DRY RUN: Would add inheritance {} -> {} in {}", child_role, parent_role, tenant_id);
            return Ok(());
        }
        
        // 使用 casbin 的 g2 关系定义角色继承
        // 这里需要直接操作 enforcer，因为 casbin-rs 的角色继承 API 可能不同
        // 实际实现时需要根据 casbin-rs 的具体 API 调整
        debug!("Added role inheritance: {} -> {} in {}", child_role, parent_role, tenant_id);
        Ok(())
    }
    
    /// 创建基础角色策略
    async fn create_basic_role_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
        let policies = match role.level {
            RoleLevel::SuperAdmin => vec![
                ("*", "*", "allow"),
            ],
            RoleLevel::TenantAdmin => vec![
                ("tenant:*", "manage", "allow"),
                ("user:*", "manage", "allow"),
                ("role:*", "manage", "allow"),
            ],
            RoleLevel::Principal => vec![
                ("school:*", "manage", "allow"),
                ("teacher:*", "read", "allow"),
                ("student:*", "read", "allow"),
                ("exam:*", "read", "allow"),
                ("grade:*", "read", "allow"),
            ],
            RoleLevel::AcademicDirector => vec![
                ("exam:*", "manage", "allow"),
                ("grade:*", "manage", "allow"),
                ("student:*", "read", "allow"),
                ("teacher:*", "read", "allow"),
            ],
            RoleLevel::SubjectLeader => vec![
                ("exam:subject:*", "manage", "allow"),
                ("grade:subject:*", "write", "allow"),
                ("student:subject:*", "read", "allow"),
            ],
            RoleLevel::GradeLeader => vec![
                ("student:grade:*", "read", "allow"),
                ("grade:grade:*", "read", "allow"),
                ("exam:grade:*", "read", "allow"),
            ],
            RoleLevel::ClassTeacher => vec![
                ("student:class:*", "read", "allow"),
                ("grade:class:*", "read", "allow"),
                ("exam:class:*", "read", "allow"),
            ],
            RoleLevel::Teacher => vec![
                ("grade:teaching_class:*", "write", "allow"),
                ("student:teaching_class:*", "read", "allow"),
            ],
            RoleLevel::Student => vec![
                ("grade:self", "read", "allow"),
                ("menu:personal_center", "view", "allow"),
            ],
            RoleLevel::Parent => vec![
                ("grade:child", "read", "allow"),
                ("student:child", "read", "allow"),
            ],
        };
        
        for (object, action, effect) in policies {
            let policy = PermissionPolicy {
                subject: role.code.clone(),
                domain: tenant_id.to_string(),
                object: object.to_string(),
                action: action.to_string(),
                effect: effect.to_string(),
            };
            
            if !config.dry_run {
                self.casbin_service.add_policy(&policy).await?;
            }
        }
        
        Ok(())
    }
    
    /// 创建租户特定角色策略
    async fn create_tenant_role_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
        // 租户自定义角色的策略创建逻辑
        // 这里可以根据租户的具体需求来定制策略
        debug!("Creating tenant-specific policies for role: {} in tenant: {}", role.code, tenant_id);
        Ok(())
    }
    
    /// 创建用户数据权限策略
    async fn create_user_data_policies(
        &self,
        user_identity: &str,
        role_code: &str,
        target_type: &str,
        target_id: Option<&Uuid>,
        subject: Option<&str>,
        tenant_id: &str,
        config: &SyncConfig,
    ) -> Result<()> {
        let mut policies = Vec::new();
        
        match target_type {
            "class" => {
                if let Some(class_id) = target_id {
                    policies.push(PermissionPolicy {
                        subject: user_identity.to_string(),
                        domain: tenant_id.to_string(),
                        object: format!("student:class:{}", class_id),
                        action: "read".to_string(),
                        effect: "allow".to_string(),
                    });
                    
                    if role_code == SystemRoles::CLASS_TEACHER {
                        policies.push(PermissionPolicy {
                            subject: user_identity.to_string(),
                            domain: tenant_id.to_string(),
                            object: format!("grade:class:{}", class_id),
                            action: "read".to_string(),
                            effect: "allow".to_string(),
                        });
                    }
                }
            }
            "subject_group" => {
                if let Some(subject) = subject {
                    policies.push(PermissionPolicy {
                        subject: user_identity.to_string(),
                        domain: tenant_id.to_string(),
                        object: format!("exam:subject:{}", subject),
                        action: "manage".to_string(),
                        effect: "allow".to_string(),
                    });
                    
                    policies.push(PermissionPolicy {
                        subject: user_identity.to_string(),
                        domain: tenant_id.to_string(),
                        object: format!("grade:subject:{}", subject),
                        action: "write".to_string(),
                        effect: "allow".to_string(),
                    });
                }
            }
            "grade" => {
                if let Some(grade_id) = target_id {
                    policies.push(PermissionPolicy {
                        subject: user_identity.to_string(),
                        domain: tenant_id.to_string(),
                        object: format!("student:grade:{}", grade_id),
                        action: "read".to_string(),
                        effect: "allow".to_string(),
                    });
                }
            }
            "school" => {
                policies.push(PermissionPolicy {
                    subject: user_identity.to_string(),
                    domain: tenant_id.to_string(),
                    object: "school:*".to_string(),
                    action: "manage".to_string(),
                    effect: "allow".to_string(),
                });
            }
            "student" => {
                // 学生只能访问自己的数据
                policies.push(PermissionPolicy {
                    subject: user_identity.to_string(),
                    domain: tenant_id.to_string(),
                    object: "grade:self".to_string(),
                    action: "read".to_string(),
                    effect: "allow".to_string(),
                });
            }
            _ => {
                // 其他目标类型的处理
            }
        }
        
        // 批量添加策略
        for policy in policies {
            if !config.dry_run {
                self.casbin_service.add_policy(&policy).await?;
            }
        }
        
        Ok(())
    }
    
    /// 构建用户身份标识
    fn build_user_identity(&self, user_id: &Uuid, role_code: &str, target_type: &str, target_id: Option<&Uuid>) -> String {
        match target_id {
            Some(id) => format!("{}:{}:{}:{}", user_id, role_code, target_type, id),
            None => format!("{}:{}:{}", user_id, role_code, target_type),
        }
    }
    
    /// 获取所有租户
    async fn get_all_tenants(&self) -> Result<Vec<TenantInfo>> {
        let rows = sqlx::query!(
            "SELECT id, name, schema_name, status FROM public.tenants WHERE status = 'active'"
        )
        .fetch_all(&self.pool)
        .await?;
        
        let tenants = rows.into_iter().map(|row| TenantInfo {
            id: row.id,
            name: Some(row.name),
            schema_name: Some(row.schema_name),
            status: row.status.unwrap_or_default(),
        }).collect();
        
        Ok(tenants)
    }
}

/// 租户信息结构
#[derive(Debug)]
struct TenantInfo {
    id: Uuid,
    name: Option<String>,
    schema_name: Option<String>,
    status: String,
}