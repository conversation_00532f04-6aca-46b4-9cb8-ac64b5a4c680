use anyhow::Result;
use sqlx::{PgPool, Row};
use std::collections::HashMap;
use tracing::{info, warn, error};
use uuid::Uuid;

use super::{CasbinPermissionService, PermissionRequest, PermissionPolicy};

/// 菜单权限迁移服务
pub struct MenuMigrationService {
    db_pool: PgPool,
}

impl MenuMigrationService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 执行完整权限迁移到Casbin
    pub async fn migrate_permissions_to_casbin(
        &self,
        casbin_service: &dyn CasbinPermissionService,
        tenant_id: &str,
        dry_run: bool,
    ) -> Result<MigrationReport> {
        info!("开始权限迁移 - tenant_id: {}, dry_run: {}", tenant_id, dry_run);
        
        let mut report = MigrationReport::new();
        
        // 1. 迁移菜单访问权限
        let menu_report = self.migrate_menu_access_permissions(casbin_service, tenant_id, dry_run).await?;
        report.merge_menu_report(menu_report);
        
        // 2. 迁移数据范围权限（独立于菜单）
        let data_report = self.migrate_data_scope_permissions(casbin_service, tenant_id, dry_run).await?;
        report.merge_data_report(data_report);
        
        info!("权限迁移完成: {}", report.summary());
        Ok(report)
    }
    
    /// 迁移菜单访问权限
    async fn migrate_menu_access_permissions(
        &self,
        casbin_service: &dyn CasbinPermissionService,
        tenant_id: &str,
        dry_run: bool,
    ) -> Result<MenuMigrationReport> {
        info!("迁移菜单访问权限");
        
        let mut report = MenuMigrationReport::new();
        let menu_permissions = self.get_menu_permissions().await?;
        
        for menu in menu_permissions {
            // 为每个required_permission创建菜单访问策略
            for perm_role in &menu.required_permissions {
                let policy = vec![
                    format!("role:{}", perm_role),
                    "menu".to_string(),
                    "access".to_string(),
                    menu.menu_id.clone(),
                ];
                
                if !dry_run {
                    let permission_policy = PermissionPolicy {
                        subject: policy[0].clone(),
                        domain: tenant_id.to_string(),
                        object: policy[2].clone(),
                        action: policy[3].clone(),
                        effect: policy[4].clone(),
                    };
                    casbin_service.add_policy(&permission_policy).await?;
                }
                
                report.add_menu_policy(menu.menu_id.clone(), policy);
                info!("添加菜单访问策略: role:{} -> menu:{}", perm_role, menu.menu_id);
            }
        }
        
        Ok(report)
    }
    
    /// 迁移数据范围权限（独立于菜单）
    async fn migrate_data_scope_permissions(
        &self,
        casbin_service: &dyn CasbinPermissionService,
        tenant_id: &str,
        dry_run: bool,
    ) -> Result<DataScopeMigrationReport> {
        info!("迁移数据范围权限");
        
        let mut report = DataScopeMigrationReport::new();
        
        // 获取现有的数据范围配置
        let data_scope_configs = self.get_data_scope_configurations().await?;
        
        for config in data_scope_configs {
            let policies = self.convert_data_scope_to_policies(&config);
            
            for policy in policies {
                if !dry_run {
                    let permission_policy = PermissionPolicy {
                        subject: policy[0].clone(),
                        domain: tenant_id.to_string(),
                        object: policy[2].clone(),
                        action: policy[3].clone(),
                        effect: policy[4].clone(),
                    };
                    casbin_service.add_policy(&permission_policy).await?;
                }
                
                report.add_data_policy(config.resource.clone(), policy);
                info!("添加数据范围策略: {} -> {}", config.resource, config.scope_definition);
            }
        }
        
        Ok(report)
    }
    
    /// 获取数据范围配置（从现有菜单和角色权限中推导）
    async fn get_data_scope_configurations(&self) -> Result<Vec<DataScopeConfig>> {
        // 基于业务逻辑预定义数据范围规则
        let configs = vec![
            // 学生数据范围
            DataScopeConfig {
                resource: "student".to_string(),
                roles: vec!["班主任".to_string()],
                scope_definition: "scope:class:own".to_string(),
                actions: vec!["read".to_string(), "write".to_string()],
            },
            DataScopeConfig {
                resource: "student".to_string(),
                roles: vec!["教导主任".to_string(), "校长".to_string()],
                scope_definition: "scope:school:*".to_string(),
                actions: vec!["read".to_string(), "write".to_string()],
            },
            // 班级数据范围
            DataScopeConfig {
                resource: "class".to_string(),
                roles: vec!["班主任".to_string()],
                scope_definition: "scope:class:own".to_string(),
                actions: vec!["read".to_string(), "write".to_string()],
            },
            DataScopeConfig {
                resource: "class".to_string(),
                roles: vec!["教导主任".to_string(), "年级长".to_string(), "校长".to_string()],
                scope_definition: "scope:school:*".to_string(),
                actions: vec!["read".to_string(), "write".to_string()],
            },
            // 考试数据范围
            DataScopeConfig {
                resource: "exam".to_string(),
                roles: vec!["班主任".to_string(), "任课老师".to_string()],
                scope_definition: "scope:class:own".to_string(),
                actions: vec!["read".to_string()],
            },
            DataScopeConfig {
                resource: "exam".to_string(),
                roles: vec!["教导主任".to_string(), "校长".to_string()],
                scope_definition: "scope:school:*".to_string(),
                actions: vec!["read".to_string(), "write".to_string()],
            },
        ];
        
        Ok(configs)
    }
    
    /// 将数据范围配置转换为Casbin策略
    fn convert_data_scope_to_policies(&self, config: &DataScopeConfig) -> Vec<Vec<String>> {
        let mut policies = Vec::new();
        
        for role in &config.roles {
            for action in &config.actions {
                let policy = vec![
                    format!("role:{}", role),
                    config.resource.clone(),
                    action.clone(),
                    config.scope_definition.clone(),
                ];
                policies.push(policy);
            }
        }
        
        policies
    }
    
    /// 获取菜单权限配置（复用现有逻辑）
    async fn get_menu_permissions(&self) -> Result<Vec<MenuPermissionRecord>> {
        let query = r#"
            SELECT menu_id, name, required_permissions 
            FROM public.menu_permissions 
            WHERE is_active = true 
            AND required_permissions IS NOT NULL 
            AND array_length(required_permissions, 1) > 0
            ORDER BY menu_id
        "#;
        
        let rows = sqlx::query(query)
            .fetch_all(&self.db_pool)
            .await?;
            
        let mut records = Vec::new();
        for row in rows {
            let menu_id: String = row.get("menu_id");
            let name: String = row.get("name");
            let required_permissions: Vec<String> = row.get("required_permissions");
            
            records.push(MenuPermissionRecord {
                menu_id,
                name,
                required_permissions,
            });
        }
        
        Ok(records)
    }
    
    /// 验证迁移结果
    pub async fn validate_migration(
        &self,
        casbin_service: &dyn CasbinPermissionService,
        tenant_id: &str,
    ) -> Result<ValidationReport> {
        info!("验证权限迁移结果");
        
        let mut report = ValidationReport::new();
        let menu_permissions = self.get_menu_permissions().await?;
        
        for menu in menu_permissions {
            for role in &menu.required_permissions {
                let permission_request = PermissionRequest {
                    subject: format!("role:{}", role),
                    domain: tenant_id.to_string(),
                    object: format!("menu:{}", menu.menu_id),
                    action: "access".to_string(),
                };

                let has_access = casbin_service.enforce(&permission_request).await?;
                
                if has_access {
                    report.add_success(format!("菜单访问验证通过: {} -> {}", role, menu.menu_id));
                } else {
                    report.add_failure(format!("菜单访问验证失败: {} -> {}", role, menu.menu_id));
                }
            }
        }
        
        info!("验证完成: {}", report.summary());
        Ok(report)
    }
    
    /// 清理旧的菜单权限字段
    pub async fn cleanup_menu_permission_fields(&self, confirm: bool) -> Result<()> {
        if !confirm {
            warn!("清理操作需要确认，请设置 confirm=true");
            return Ok(());
        }
        
        info!("开始清理菜单权限表中的冗余字段");
        
        let queries = vec![
            "ALTER TABLE public.menu_permissions DROP COLUMN IF EXISTS required_permissions",
            "ALTER TABLE public.menu_permissions DROP COLUMN IF EXISTS data_scopes",
            "ALTER TABLE public.menu_permissions DROP COLUMN IF EXISTS permission_mode",
        ];
        
        for query in queries {
            sqlx::query(query)
                .execute(&self.db_pool)
                .await?;
            info!("执行清理SQL: {}", query);
        }
        
        Ok(())
    }
}

/// 菜单权限记录
#[derive(Debug, Clone)]
struct MenuPermissionRecord {
    menu_id: String,
    name: String,
    required_permissions: Vec<String>,
}

/// 数据范围配置
#[derive(Debug, Clone)]
struct DataScopeConfig {
    resource: String,
    roles: Vec<String>,
    scope_definition: String,
    actions: Vec<String>,
}

/// 迁移报告
#[derive(Debug)]
pub struct MigrationReport {
    menu_report: Option<MenuMigrationReport>,
    data_report: Option<DataScopeMigrationReport>,
}

impl MigrationReport {
    fn new() -> Self {
        Self {
            menu_report: None,
            data_report: None,
        }
    }
    
    fn merge_menu_report(&mut self, report: MenuMigrationReport) {
        self.menu_report = Some(report);
    }
    
    fn merge_data_report(&mut self, report: DataScopeMigrationReport) {
        self.data_report = Some(report);
    }
    
    pub fn summary(&self) -> String {
        let menu_summary = self.menu_report.as_ref()
            .map(|r| r.summary())
            .unwrap_or_default();
        let data_summary = self.data_report.as_ref()
            .map(|r| r.summary())
            .unwrap_or_default();
            
        format!("菜单权限: {}, 数据权限: {}", menu_summary, data_summary)
    }
    
    pub fn get_all_policies(&self) -> Vec<String> {
        let mut policies = Vec::new();
        
        if let Some(menu_report) = &self.menu_report {
            policies.extend(menu_report.get_all_policies());
        }
        
        if let Some(data_report) = &self.data_report {
            policies.extend(data_report.get_all_policies());
        }
        
        policies
    }
}

/// 菜单迁移报告
#[derive(Debug)]
struct MenuMigrationReport {
    menu_policies: HashMap<String, Vec<Vec<String>>>,
}

impl MenuMigrationReport {
    fn new() -> Self {
        Self {
            menu_policies: HashMap::new(),
        }
    }
    
    fn add_menu_policy(&mut self, menu_id: String, policy: Vec<String>) {
        self.menu_policies.entry(menu_id)
            .or_insert_with(Vec::new)
            .push(policy);
    }
    
    fn summary(&self) -> String {
        let total_menus = self.menu_policies.len();
        let total_policies: usize = self.menu_policies.values()
            .map(|policies| policies.len())
            .sum();
        format!("{} 个菜单，{} 个策略", total_menus, total_policies)
    }
    
    fn get_all_policies(&self) -> Vec<String> {
        self.menu_policies.values()
            .flatten()
            .map(|policy| policy.join("|"))
            .collect()
    }
}

/// 数据范围迁移报告
#[derive(Debug)]
struct DataScopeMigrationReport {
    data_policies: HashMap<String, Vec<Vec<String>>>,
}

impl DataScopeMigrationReport {
    fn new() -> Self {
        Self {
            data_policies: HashMap::new(),
        }
    }
    
    fn add_data_policy(&mut self, resource: String, policy: Vec<String>) {
        self.data_policies.entry(resource)
            .or_insert_with(Vec::new)
            .push(policy);
    }
    
    fn summary(&self) -> String {
        let total_resources = self.data_policies.len();
        let total_policies: usize = self.data_policies.values()
            .map(|policies| policies.len())
            .sum();
        format!("{} 个资源，{} 个策略", total_resources, total_policies)
    }
    
    fn get_all_policies(&self) -> Vec<String> {
        self.data_policies.values()
            .flatten()
            .map(|policy| policy.join("|"))
            .collect()
    }
}

/// 验证报告
#[derive(Debug)]
pub struct ValidationReport {
    successes: Vec<String>,
    failures: Vec<String>,
}

impl ValidationReport {
    fn new() -> Self {
        Self {
            successes: Vec::new(),
            failures: Vec::new(),
        }
    }
    
    fn add_success(&mut self, message: String) {
        self.successes.push(message);
    }
    
    fn add_failure(&mut self, message: String) {
        self.failures.push(message);
    }
    
    pub fn summary(&self) -> String {
        format!(
            "验证通过: {}, 验证失败: {}", 
            self.successes.len(), 
            self.failures.len()
        )
    }
    
    pub fn has_failures(&self) -> bool {
        !self.failures.is_empty()
    }
    
    pub fn get_failures(&self) -> &[String] {
        &self.failures
    }
    
    pub fn get_successes(&self) -> &[String] {
        &self.successes
    }
}