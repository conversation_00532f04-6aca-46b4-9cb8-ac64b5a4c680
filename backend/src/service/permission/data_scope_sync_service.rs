use std::collections::HashMap;
use anyhow::{Result, anyhow};
use sqlx::{PgPool, Row};
use uuid::Uuid;
use tracing::{info, warn, error, debug};
use serde::{Serialize, Deserialize};

use super::{CasbinPermissionService, PermissionPolicy};

/// 数据范围同步配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataScopeSyncConfig {
    /// 是否同步班主任权限
    pub sync_class_teacher_permissions: bool,
    /// 是否同步年级主任权限
    pub sync_grade_director_permissions: bool,
    /// 是否同步学科组长权限
    pub sync_subject_leader_permissions: bool,
    /// 是否清除现有的数据范围权限
    pub clear_existing_data_scope_policies: bool,
    /// 是否为试运行模式
    pub dry_run: bool,
}

impl Default for DataScopeSyncConfig {
    fn default() -> Self {
        Self {
            sync_class_teacher_permissions: true,
            sync_grade_director_permissions: true,
            sync_subject_leader_permissions: false, // 暂未实现
            clear_existing_data_scope_policies: false,
            dry_run: false,
        }
    }
}

/// 数据范围同步结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataScopeSyncResult {
    /// 同步的权限数量
    pub synced_permissions: usize,
    /// 清除的权限数量
    pub cleared_permissions: usize,
    /// 错误信息
    pub errors: Vec<String>,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 是否成功
    pub success: bool,
}

/// 数据范围权限同步服务
pub struct DataScopePermissionSyncService {
    pool: PgPool,
}

impl DataScopePermissionSyncService {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
    
    /// 同步租户的数据范围权限
    pub async fn sync_tenant_data_scope_permissions(
        &self,
        tenant_id: &str,
        schema_name: &str,
        casbin_service: &dyn CasbinPermissionService,
        config: &DataScopeSyncConfig,
    ) -> Result<DataScopeSyncResult> {
        let mut result = DataScopeSyncResult {
            synced_permissions: 0,
            cleared_permissions: 0,
            errors: Vec::new(),
            warnings: Vec::new(),
            success: true,
        };
        
        info!("Starting data scope permission sync for tenant: {}", tenant_id);
        
        // 1. 清除现有的数据范围权限（如果配置了）
        if config.clear_existing_data_scope_policies {
            match self.clear_data_scope_policies(tenant_id, casbin_service, config.dry_run).await {
                Ok(cleared_count) => {
                    result.cleared_permissions = cleared_count;
                    info!("Cleared {} existing data scope policies", cleared_count);
                },
                Err(e) => {
                    let error_msg = format!("Failed to clear existing policies: {}", e);
                    error!("{}", error_msg);
                    result.errors.push(error_msg);
                    result.success = false;
                }
            }
        }
        
        // 2. 同步班主任权限
        if config.sync_class_teacher_permissions {
            match self.sync_class_teacher_permissions(tenant_id, schema_name, casbin_service, config.dry_run).await {
                Ok(synced_count) => {
                    result.synced_permissions += synced_count;
                    info!("Synced {} class teacher permissions", synced_count);
                },
                Err(e) => {
                    let error_msg = format!("Failed to sync class teacher permissions: {}", e);
                    error!("{}", error_msg);
                    result.errors.push(error_msg);
                    result.success = false;
                }
            }
        }
        
        // 3. 同步年级主任权限
        if config.sync_grade_director_permissions {
            match self.sync_grade_director_permissions(tenant_id, schema_name, casbin_service, config.dry_run).await {
                Ok(synced_count) => {
                    result.synced_permissions += synced_count;
                    info!("Synced {} grade director permissions", synced_count);
                },
                Err(e) => {
                    let error_msg = format!("Failed to sync grade director permissions: {}", e);
                    error!("{}", error_msg);
                    result.errors.push(error_msg);
                    result.success = false;
                }
            }
        }
        
        // 4. 同步学科组长权限（暂未实现）
        if config.sync_subject_leader_permissions {
            result.warnings.push("Subject leader permission sync not implemented yet".to_string());
        }
        
        info!("Data scope permission sync completed for tenant: {}, synced: {}, cleared: {}, errors: {}", 
              tenant_id, result.synced_permissions, result.cleared_permissions, result.errors.len());
        
        Ok(result)
    }
    
    /// 清除数据范围权限策略
    async fn clear_data_scope_policies(
        &self,
        tenant_id: &str,
        casbin_service: &dyn CasbinPermissionService,
        dry_run: bool,
    ) -> Result<usize> {
        // 查询现有的数据范围权限策略
        let query = "
            SELECT v0, v1, v2, v3, v4 
            FROM public.casbin_policies 
            WHERE tenant_id = $1 
            AND ptype = 'p' 
            AND (v2 LIKE '%:class:%' OR v2 LIKE '%:grade:%' OR v2 LIKE '%:subject_group:%')
        ";
        
        let rows = sqlx::query(query)
            .bind(tenant_id)
            .fetch_all(&self.pool)
            .await?;
        
        let mut cleared_count = 0;
        
        for row in rows {
            let subject: String = row.try_get("v0")?;
            let domain: String = row.try_get("v1")?;
            let object: String = row.try_get("v2")?;
            let action: String = row.try_get("v3")?;
            let effect: String = row.try_get("v4")?;
            
            if !dry_run {
                let policy = PermissionPolicy {
                    subject,
                    domain,
                    object,
                    action,
                    effect,
                };
                
                if let Err(e) = casbin_service.remove_policy(&policy).await {
                    warn!("Failed to remove policy: {:?}, error: {}", policy, e);
                } else {
                    cleared_count += 1;
                }
            } else {
                debug!("Would clear policy: {} -> {} @ {} | {}", subject, object, domain, action);
                cleared_count += 1;
            }
        }
        
        Ok(cleared_count)
    }
    
    /// 同步班主任权限
    async fn sync_class_teacher_permissions(
        &self,
        tenant_id: &str,
        schema_name: &str,
        casbin_service: &dyn CasbinPermissionService,
        dry_run: bool,
    ) -> Result<usize> {
        // 查询班主任和其负责的班级
        let query = format!(
            "SELECT t.user_id, ac.id as class_id, ac.class_name
             FROM {}.teachers t
             JOIN {}.administrative_classes ac ON t.id = ac.teacher_id
             WHERE t.is_active = true AND ac.is_active = true",
            schema_name, schema_name
        );
        
        let rows = sqlx::query(&query)
            .fetch_all(&self.pool)
            .await?;
        
        let mut synced_count = 0;
        
        for row in rows {
            let user_id: Uuid = row.try_get("user_id")?;
            let class_id: Uuid = row.try_get("class_id")?;
            let class_name: String = row.try_get("class_name")?;
            
            let user_identity = format!("user:{}", user_id);
            
            // 添加学生数据的班级范围读取权限
            let student_policy = PermissionPolicy {
                subject: user_identity.clone(),
                domain: tenant_id.to_string(),
                object: format!("student:class:{}", class_id),
                action: "read".to_string(),
                effect: "allow".to_string(),
            };
            
            // 添加行政班数据的读取权限
            let class_policy = PermissionPolicy {
                subject: user_identity.clone(),
                domain: tenant_id.to_string(),
                object: format!("administrative_class:class:{}", class_id),
                action: "read".to_string(),
                effect: "allow".to_string(),
            };
            
            if !dry_run {
                if let Err(e) = casbin_service.add_policy(&student_policy).await {
                    warn!("Failed to add student policy for teacher {}: {}", user_id, e);
                } else {
                    synced_count += 1;
                }
                
                if let Err(e) = casbin_service.add_policy(&class_policy).await {
                    warn!("Failed to add class policy for teacher {}: {}", user_id, e);
                } else {
                    synced_count += 1;
                }
            } else {
                debug!("Would add policies for teacher {} (class: {}): student:class:{}, administrative_class:class:{}", 
                       user_id, class_name, class_id, class_id);
                synced_count += 2;
            }
        }
        
        Ok(synced_count)
    }
    
    /// 同步年级主任权限
    async fn sync_grade_director_permissions(
        &self,
        tenant_id: &str,
        schema_name: &str,
        casbin_service: &dyn CasbinPermissionService,
        dry_run: bool,
    ) -> Result<usize> {
        // 查询年级主任和其负责的年级
        // 注意：这里假设teachers表中有grade_level_id字段来标识年级主任
        // 实际实现可能需要根据具体的数据模型调整
        let query = format!(
            "SELECT DISTINCT t.user_id, t.grade_level_id, gl.name as grade_name
             FROM {}.teachers t
             JOIN public.grade_levels gl ON t.grade_level_id = gl.id
             WHERE t.is_active = true AND t.grade_level_id IS NOT NULL",
            schema_name
        );
        
        let rows = sqlx::query(&query)
            .fetch_all(&self.pool)
            .await?;
        
        let mut synced_count = 0;
        
        for row in rows {
            let user_id: Uuid = row.try_get("user_id")?;
            let grade_level_id: i32 = row.try_get("grade_level_id")?;
            let grade_name: String = row.try_get("grade_name")?;
            
            let user_identity = format!("user:{}", user_id);
            
            // 添加学生数据的年级范围读取权限
            let student_policy = PermissionPolicy {
                subject: user_identity.clone(),
                domain: tenant_id.to_string(),
                object: format!("student:grade:{}", grade_level_id),
                action: "read".to_string(),
                effect: "allow".to_string(),
            };
            
            // 添加行政班数据的年级范围读取权限
            let class_policy = PermissionPolicy {
                subject: user_identity.clone(),
                domain: tenant_id.to_string(),
                object: format!("administrative_class:grade:{}", grade_level_id),
                action: "read".to_string(),
                effect: "allow".to_string(),
            };
            
            if !dry_run {
                if let Err(e) = casbin_service.add_policy(&student_policy).await {
                    warn!("Failed to add student policy for grade director {}: {}", user_id, e);
                } else {
                    synced_count += 1;
                }
                
                if let Err(e) = casbin_service.add_policy(&class_policy).await {
                    warn!("Failed to add class policy for grade director {}: {}", user_id, e);
                } else {
                    synced_count += 1;
                }
            } else {
                debug!("Would add policies for grade director {} (grade: {}): student:grade:{}, administrative_class:grade:{}", 
                       user_id, grade_name, grade_level_id, grade_level_id);
                synced_count += 2;
            }
        }
        
        Ok(synced_count)
    }
}
