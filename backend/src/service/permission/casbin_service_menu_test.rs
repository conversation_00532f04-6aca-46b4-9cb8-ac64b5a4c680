#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::PgPool;
    use std::sync::Arc;
    use uuid::Uuid;
    use crate::service::menu::MenuService;

    /// 创建测试数据库连接池
    async fn create_test_pool() -> PgPool {
        // 这里应该连接到测试数据库
        // 实际实现中需要配置测试数据库连接
        todo!("Configure test database connection")
    }

    /// 创建测试用的CasbinService
    async fn create_test_casbin_service() -> MultiTenantCasbinService {
        let pool = create_test_pool().await;
        let menu_service = Arc::new(MenuService::new(pool.clone()));
        
        MultiTenantCasbinService::new(
            pool,
            "test_model.conf".to_string(),
            menu_service,
        ).await.unwrap()
    }

    /// 设置测试数据
    async fn setup_test_data(pool: &PgPool) -> Result<(), sqlx::Error> {
        // 清理测试数据
        sqlx::query("DELETE FROM public.casbin_policies WHERE tenant_id = 'test'")
            .execute(pool)
            .await?;
        
        sqlx::query("DELETE FROM public.menu_permissions WHERE menu_id LIKE 'test_%'")
            .execute(pool)
            .await?;

        // 插入测试菜单数据
        sqlx::query!(
            r#"
            INSERT INTO public.menu_permissions (
                menu_id, name, path, icon, parent_id, sort_order, is_active
            ) VALUES 
            ($1, $2, $3, $4, $5, $6, $7),
            ($8, $9, $10, $11, $12, $13, $14)
            "#,
            "test_menu_1", "测试菜单1", "/test1", "test-icon", None::<String>, 100, true,
            "test_menu_2", "测试菜单2", "/test2", "test-icon2", Some("test_menu_1"), 101, true
        )
        .execute(pool)
        .await?;

        // 插入测试Casbin策略
        sqlx::query!(
            r#"
            INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id)
            VALUES 
            ($1, $2, $3, $4, $5, $6, $7, $8),
            ($9, $10, $11, $12, $13, $14, $15, $16),
            ($17, $18, $19, $20, $21, $22, $23, $24)
            "#,
            // 基础访问策略
            "p", "menu", "test", "menu:test_menu_1", "access", "allow", 
            r#"{"menu_name": "测试菜单1", "menu_path": "/test1"}"#, "test",
            // 权限要求策略
            "p", "menu", "test", "menu:test_menu_1:requirements", "check", "allow",
            r#"{"required_permissions": ["test:read", "test:write"]}"#, "test",
            // 数据范围策略
            "p", "menu", "test", "menu:test_menu_1:data_scope", "scope", "allow",
            r#"{"data_scopes": ["class:*", "school:own"]}"#, "test"
        )
        .execute(pool)
        .await?;

        Ok(())
    }

    /// 清理测试数据
    async fn cleanup_test_data(pool: &PgPool) -> Result<(), sqlx::Error> {
        sqlx::query("DELETE FROM public.casbin_policies WHERE tenant_id = 'test'")
            .execute(pool)
            .await?;
        
        sqlx::query("DELETE FROM public.menu_permissions WHERE menu_id LIKE 'test_%'")
            .execute(pool)
            .await?;

        Ok(())
    }

    #[tokio::test]
    async fn test_get_menu_required_permissions() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        let service = create_test_casbin_service().await;
        let result = service.get_menu_required_permissions("test_menu_1", "test").await;
        
        assert!(result.is_ok());
        let permissions = result.unwrap();
        assert_eq!(permissions.len(), 2);
        assert!(permissions.contains(&"test:read".to_string()));
        assert!(permissions.contains(&"test:write".to_string()));
        
        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_get_menu_data_scopes() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        let service = create_test_casbin_service().await;
        let result = service.get_menu_data_scopes("test_menu_1", "test").await;
        
        assert!(result.is_ok());
        let data_scopes = result.unwrap();
        assert!(data_scopes.is_some());
        
        let scopes = data_scopes.unwrap();
        assert_eq!(scopes.len(), 2);
        assert!(scopes.contains(&"class:*".to_string()));
        assert!(scopes.contains(&"school:own".to_string()));
        
        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_get_menu_permissions_from_policies() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        let service = create_test_casbin_service().await;
        let result = service.get_menu_permissions_from_policies("test").await;
        
        assert!(result.is_ok());
        let menus = result.unwrap();
        assert_eq!(menus.len(), 2);
        
        // 验证菜单1的权限信息
        let menu1 = menus.iter().find(|m| m.menu_id == "test_menu_1").unwrap();
        assert_eq!(menu1.name, "测试菜单1");
        assert_eq!(menu1.required_permissions.len(), 2);
        assert!(menu1.required_permissions.contains(&"test:read".to_string()));
        assert!(menu1.required_permissions.contains(&"test:write".to_string()));
        assert!(menu1.data_scopes.is_some());
        
        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_build_menu_tree() {
        let pool = create_test_pool().await;
        let service = create_test_casbin_service().await;
        
        let mut menus = vec![
            MenuPermission {
                menu_id: "parent".to_string(),
                name: "父菜单".to_string(),
                path: "/parent".to_string(),
                icon: None,
                parent_id: None,
                required_permissions: vec![],
                sort_order: Some(1),
                menu_type: "functional".to_string(),
                children: None,
            },
            MenuPermission {
                menu_id: "child".to_string(),
                name: "子菜单".to_string(),
                path: "/parent/child".to_string(),
                icon: None,
                parent_id: Some("parent".to_string()),
                required_permissions: vec![],
                sort_order: Some(2),
                menu_type: "functional".to_string(),
                children: None,
            },
        ];
        
        let tree = service.build_menu_tree(menus);
        
        assert_eq!(tree.len(), 1); // 只有一个根菜单
        let parent = &tree[0];
        assert_eq!(parent.menu_id, "parent");
        assert!(parent.children.is_some());
        
        let children = parent.children.as_ref().unwrap();
        assert_eq!(children.len(), 1);
        assert_eq!(children[0].menu_id, "child");
    }

    #[tokio::test]
    async fn test_check_menu_access() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        // 需要设置用户权限策略
        sqlx::query!(
            r#"
            INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id)
            VALUES 
            ($1, $2, $3, $4, $5, $6, $7),
            ($8, $9, $10, $11, $12, $13, $14),
            ($15, $16, $17, $18, $19, $20, $21)
            "#,
            // 用户访问菜单权限
            "p", "user:test_user", "test", "menu:test_menu_1", "access", "allow", "test",
            // 用户基础权限
            "p", "user:test_user", "test", "test:read", "allow", "allow", "test",
            "p", "user:test_user", "test", "test:write", "allow", "allow", "test"
        )
        .execute(&pool)
        .await
        .unwrap();
        
        let service = create_test_casbin_service().await;
        let result = service.check_menu_access("user:test_user", "test", "test_menu_1").await;
        
        assert!(result.is_ok());
        let has_access = result.unwrap();
        assert!(has_access);
        
        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_check_menu_access_denied() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        let service = create_test_casbin_service().await;
        let result = service.check_menu_access("user:unauthorized_user", "test", "test_menu_1").await;
        
        assert!(result.is_ok());
        let has_access = result.unwrap();
        assert!(!has_access);
        
        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_get_menu_data_access_scopes() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        // 设置用户权限
        sqlx::query!(
            r#"
            INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id)
            VALUES 
            ($1, $2, $3, $4, $5, $6, $7),
            ($8, $9, $10, $11, $12, $13, $14),
            ($15, $16, $17, $18, $19, $20, $21),
            ($22, $23, $24, $25, $26, $27, $28)
            "#,
            // 用户访问菜单权限
            "p", "user:test_user", "test", "menu:test_menu_1", "access", "allow", "test",
            // 用户基础权限
            "p", "user:test_user", "test", "test:read", "allow", "allow", "test",
            "p", "user:test_user", "test", "test:write", "allow", "allow", "test",
            // 用户数据范围权限
            "p", "user:test_user", "test", "class:*", "read", "allow", "test"
        )
        .execute(&pool)
        .await
        .unwrap();
        
        let service = create_test_casbin_service().await;
        let result = service.get_menu_data_access_scopes("user:test_user", "test", "test_menu_1").await;
        
        assert!(result.is_ok());
        let data_scopes = result.unwrap();
        assert!(!data_scopes.is_empty());
        
        // 验证数据范围
        let class_scope = data_scopes.iter().find(|s| s.resource == "class").unwrap();
        assert_eq!(class_scope.scope_type, "*");
        assert!(class_scope.actions.contains(&"read".to_string()));
        
        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_filter_menus_by_casbin_policies() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        // 设置用户权限（只能访问menu1）
        sqlx::query!(
            r#"
            INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, tenant_id)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            "#,
            "p", "user:test_user", "test", "menu:test_menu_1", "access", "allow", "test"
        )
        .execute(&pool)
        .await
        .unwrap();
        
        let menus = vec![
            MenuPermission {
                menu_id: "test_menu_1".to_string(),
                name: "测试菜单1".to_string(),
                path: "/test1".to_string(),
                icon: None,
                parent_id: None,
                required_permissions: vec![],
                data_scopes: None,
                sort_order: Some(1),
                menu_type: "functional".to_string(),
                children: None,
            },
            MenuPermission {
                menu_id: "test_menu_2".to_string(),
                name: "测试菜单2".to_string(),
                path: "/test2".to_string(),
                icon: None,
                parent_id: None,
                required_permissions: vec![],
                data_scopes: None,
                sort_order: Some(2),
                menu_type: "functional".to_string(),
                children: None,
            },
        ];
        
        let service = create_test_casbin_service().await;
        let result = service.filter_menus_by_casbin_policies(menus, "user:test_user", "test").await;
        
        assert!(result.is_ok());
        let filtered_menus = result.unwrap();
        assert_eq!(filtered_menus.len(), 1);
        assert_eq!(filtered_menus[0].menu_id, "test_menu_1");
        
        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_get_all_menu_permissions_from_policies() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();

        let service = create_test_casbin_service().await;
        let result = service.get_all_menu_permissions_from_policies().await;

        assert!(result.is_ok());
        let menus = result.unwrap();
        assert_eq!(menus.len(), 2);

        // 验证包含所有菜单（包括非激活的）
        let menu_ids: Vec<String> = menus.iter().map(|m| m.menu_id.clone()).collect();
        assert!(menu_ids.contains(&"test_menu_1".to_string()));
        assert!(menu_ids.contains(&"test_menu_2".to_string()));

        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_sort_menu_children() {
        let pool = create_test_pool().await;
        let service = create_test_casbin_service().await;

        let mut menu = MenuPermission {
            menu_id: "parent".to_string(),
            name: "父菜单".to_string(),
            path: "/parent".to_string(),
            icon: None,
            parent_id: None,
            required_permissions: vec![],
            data_scopes: None,
            sort_order: Some(1),
            menu_type: "functional".to_string(),
            children: Some(vec![
                MenuPermission {
                    menu_id: "child2".to_string(),
                    name: "子菜单2".to_string(),
                    path: "/parent/child2".to_string(),
                    icon: None,
                    parent_id: Some("parent".to_string()),
                    required_permissions: vec![],
                    data_scopes: None,
                    sort_order: Some(3),
                    menu_type: "functional".to_string(),
                    children: None,
                },
                MenuPermission {
                    menu_id: "child1".to_string(),
                    name: "子菜单1".to_string(),
                    path: "/parent/child1".to_string(),
                    icon: None,
                    parent_id: Some("parent".to_string()),
                    required_permissions: vec![],
                    data_scopes: None,
                    sort_order: Some(1),
                    menu_type: "functional".to_string(),
                    children: None,
                },
            ]),
        };

        service.sort_menu_children(&mut menu);

        let children = menu.children.unwrap();
        assert_eq!(children[0].menu_id, "child1");
        assert_eq!(children[1].menu_id, "child2");
    }
}
