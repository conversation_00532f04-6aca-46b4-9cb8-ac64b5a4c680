use anyhow::{Result, anyhow};
use async_trait::async_trait;
use casbin::{Adapt<PERSON>, Filter, Model, Result as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Error as <PERSON><PERSON>binError};
use sqlx::{PgPool, Row};
use tracing::{debug, info};

/// 将 sqlx 错误转换为 CasbinError
fn sqlx_error_to_casbin_error(msg: &str, e: sqlx::Error) -> CasbinError {
    CasbinError::from(std::io::Error::new(
        std::io::ErrorKind::Other, 
        format!("{}: {}", msg, e)
    ))
}

/// 自定义的PostgreSQL Casbin适配器
/// 实现与项目现有数据库连接池的集成
pub struct PostgresAdapter {
    pool: PgPool,
    table_name: String,
}

/// Casbin规则存储结构
#[derive(Debug, Clone)]
pub struct CasbinRule {
    pub id: Option<i32>,
    pub ptype: String,
    pub v0: String,
    pub v1: String,
    pub v2: String,
    pub v3: String,
    pub v4: String,
    pub v5: String,
}

impl PostgresAdapter {
    /// 创建新的PostgreSQL适配器
    pub async fn new(pool: PgPool) -> Result<Self> {
        let table_name = "casbin_policies".to_string();
        
        let adapter = Self {
            pool,
            table_name,
        };
        
        // 初始化表结构（如果不存在）
        adapter.create_table().await?;
        
        Ok(adapter)
    }
    
    /// 创建casbin_policies表（如果不存在）
    async fn create_table(&self) -> Result<()> {
        // 分别执行CREATE TABLE和索引创建语句
        let table_sql = format!(
            r#"
            CREATE TABLE IF NOT EXISTS public.{} (
                id SERIAL PRIMARY KEY,
                ptype VARCHAR(100) NOT NULL,
                v0 VARCHAR(100) DEFAULT '',
                v1 VARCHAR(100) DEFAULT '',
                v2 VARCHAR(100) DEFAULT '',
                v3 VARCHAR(100) DEFAULT '',
                v4 VARCHAR(100) DEFAULT '',
                v5 VARCHAR(100) DEFAULT '',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
            "#,
            self.table_name
        );
        
        // 创建表
        sqlx::query(&table_sql)
            .execute(&self.pool)
            .await
            .map_err(|e| anyhow!("Failed to create casbin table: {}", e))?;
        
        // 创建索引 - 每个索引单独执行
        let index_sqls = vec![
            format!("CREATE INDEX IF NOT EXISTS idx_{}_ptype ON public.{}(ptype)", self.table_name, self.table_name),
            format!("CREATE INDEX IF NOT EXISTS idx_{}_v0 ON public.{}(v0)", self.table_name, self.table_name),
            format!("CREATE INDEX IF NOT EXISTS idx_{}_v1 ON public.{}(v1)", self.table_name, self.table_name),
            format!("CREATE INDEX IF NOT EXISTS idx_{}_lookup ON public.{}(ptype, v0, v1)", self.table_name, self.table_name),
        ];
        
        for index_sql in index_sqls {
            sqlx::query(&index_sql)
                .execute(&self.pool)
                .await
                .map_err(|e| anyhow!("Failed to create casbin index: {}", e))?;
        }
        
        debug!("Casbin table {} and indexes ensured to exist", self.table_name);
        Ok(())
    }
    
    /// 从策略行构建数据库规则
    fn policy_line_to_rule(&self, policy: &[String]) -> CasbinRule {
        let mut rule = CasbinRule {
            id: None,
            ptype: policy.get(0).unwrap_or(&String::new()).clone(),
            v0: String::new(),
            v1: String::new(),
            v2: String::new(),
            v3: String::new(),
            v4: String::new(),
            v5: String::new(),
        };
        
        // 填充值
        if policy.len() > 1 { rule.v0 = policy[1].clone(); }
        if policy.len() > 2 { rule.v1 = policy[2].clone(); }
        if policy.len() > 3 { rule.v2 = policy[3].clone(); }
        if policy.len() > 4 { rule.v3 = policy[4].clone(); }
        if policy.len() > 5 { rule.v4 = policy[5].clone(); }
        if policy.len() > 6 { rule.v5 = policy[6].clone(); }
        
        rule
    }
}

#[async_trait]
impl Adapter for PostgresAdapter {
    /// 从数据库加载所有策略
    async fn load_policy(&mut self, model: &mut dyn Model) -> CasbinResult<()> {
        let sql = format!(
            "SELECT ptype, v0, v1, v2, v3, v4, v5 FROM public.{} ORDER BY id",
            self.table_name
        );
        
        let rows = sqlx::query(&sql)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| sqlx_error_to_casbin_error("Failed to load policies", e))?;
        
        let rows_count = rows.len();
        
        for row in &rows {
            let ptype: String = row.get("ptype");
            let v0: String = row.get("v0");
            let v1: String = row.get("v1");
            let v2: String = row.get("v2");
            let v3: Option<String> = row.get("v3");
            let v4: Option<String> = row.get("v4");
            let v5: Option<String> = row.get("v5");
            
            let mut policy = vec![ptype.clone()];
            
            // 添加非空值
            if !v0.is_empty() { policy.push(v0); }
            if !v1.is_empty() { policy.push(v1); }
            if !v2.is_empty() { policy.push(v2); }
            if let Some(v) = v3 { if !v.is_empty() { policy.push(v); } }
            if let Some(v) = v4 { if !v.is_empty() { policy.push(v); } }
            if let Some(v) = v5 { if !v.is_empty() { policy.push(v); } }
            
            model.add_policy(&ptype, &ptype, policy[1..].to_vec());
        }
        
        info!("Loaded {} policies", rows_count);
        
        Ok(())
    }

    /// 保存策略到数据库
    async fn save_policy(&mut self, model: &mut dyn Model) -> CasbinResult<()> {
        // 先清除现有策略
        self.remove_filtered_policy("", "", 0, vec![]).await?;
        
        // 保存所有策略 - 需要遍历所有策略类型
        let policy_types = ["p", "g"];
        for ptype in &policy_types {
            let policies = model.get_policy("", ptype);
            for policy in policies {
                let mut rule_policy = vec![ptype.to_string()];
                rule_policy.extend(policy.clone());
                self.add_policy("", "", rule_policy).await?;
            }
        }
        
        info!("Saved all policies");
        Ok(())
    }
    
    /// 添加策略
    async fn add_policy(&mut self, _sec: &str, _ptype: &str, rule: Vec<String>) -> CasbinResult<bool> {
        if rule.is_empty() {
            return Ok(false);
        }

        let casbin_rule = self.policy_line_to_rule(&rule);

        // 首先检查策略是否已存在
        let check_sql = format!(
            "SELECT COUNT(*) FROM public.{} WHERE ptype = $1 AND v0 = $2 AND v1 = $3 AND v2 = $4 AND v3 = $5 AND v4 = $6 AND v5 = $7",
            self.table_name
        );

        let count: (i64,) = sqlx::query_as(&check_sql)
            .bind(&casbin_rule.ptype)
            .bind(&casbin_rule.v0)
            .bind(&casbin_rule.v1)
            .bind(&casbin_rule.v2)
            .bind(&casbin_rule.v3)
            .bind(&casbin_rule.v4)
            .bind(&casbin_rule.v5)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| sqlx_error_to_casbin_error("Failed to check policy existence", e))?;

        if count.0 > 0 {
            debug!("Policy already exists: {:?}", rule);
            return Ok(false); // 策略已存在，返回false
        }

        // 插入新策略
        let sql = format!(
            "INSERT INTO public.{} (ptype, v0, v1, v2, v3, v4, v5) VALUES ($1, $2, $3, $4, $5, $6, $7)",
            self.table_name
        );

        let result = sqlx::query(&sql)
            .bind(&casbin_rule.ptype)
            .bind(&casbin_rule.v0)
            .bind(&casbin_rule.v1)
            .bind(&casbin_rule.v2)
            .bind(&casbin_rule.v3)
            .bind(&casbin_rule.v4)
            .bind(&casbin_rule.v5)
            .execute(&self.pool)
            .await
            .map_err(|e| sqlx_error_to_casbin_error("Failed to add policy", e))?;

        let affected = result.rows_affected() > 0;
        if affected {
            debug!("Added policy: {:?}", rule);
        }

        Ok(affected)
    }
    
    /// 删除策略
    async fn remove_policy(&mut self, _sec: &str, _ptype: &str, rule: Vec<String>) -> CasbinResult<bool> {
        if rule.is_empty() {
            return Ok(false);
        }
        
        let casbin_rule = self.policy_line_to_rule(&rule);
        
        let mut conditions = vec!["ptype = $1".to_string()];
        let mut params: Vec<&str> = vec![&casbin_rule.ptype];
        
        // 添加值条件
        let values = [&casbin_rule.v0, &casbin_rule.v1, &casbin_rule.v2, &casbin_rule.v3, &casbin_rule.v4, &casbin_rule.v5];
        let field_names = ["v0", "v1", "v2", "v3", "v4", "v5"];
        
        for (i, value) in values.iter().enumerate() {
            if !value.is_empty() {
                conditions.push(format!("{} = ${}", field_names[i], params.len() + 1));
                params.push(value);
            }
        }
        
        let sql = format!(
            "DELETE FROM public.{} WHERE {}",
            self.table_name,
            conditions.join(" AND ")
        );
        
        let mut query = sqlx::query(&sql);
        for param in params {
            query = query.bind(param);
        }
        
        let result = query
            .execute(&self.pool)
            .await
            .map_err(|e| sqlx_error_to_casbin_error("Failed to remove policy", e))?;
        
        let affected = result.rows_affected() > 0;
        if affected {
            debug!("Removed policy: {:?}", rule);
        }
        
        Ok(affected)
    }
    
    /// 删除过滤的策略
    async fn remove_filtered_policy(
        &mut self,
        _sec: &str,
        _ptype: &str,
        _field_index: usize,
        _field_values: Vec<String>,
    ) -> CasbinResult<bool> {
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        
        // 策略类型条件
        if !_ptype.is_empty() {
            conditions.push(format!("ptype = ${}", params.len() + 1));
            params.push(_ptype.to_string());
        }
        
        let sql = if conditions.is_empty() {
            format!("DELETE FROM public.{}", self.table_name)
        } else {
            format!(
                "DELETE FROM public.{} WHERE {}",
                self.table_name,
                conditions.join(" AND ")
            )
        };
        
        let mut query = sqlx::query(&sql);
        for param in params {
            query = query.bind(param);
        }
        
        let result = query
            .execute(&self.pool)
            .await
            .map_err(|e| sqlx_error_to_casbin_error("Failed to remove filtered policy", e))?;
        
        let affected = result.rows_affected() > 0;
        if affected {
            debug!(
                "Removed {} filtered policies",
                result.rows_affected()
            );
        }
        
        Ok(affected)
    }

    /// 加载过滤的策略 - 简单实现，直接调用load_policy
    async fn load_filtered_policy<'a>(&mut self, model: &mut dyn Model, _filter: Filter<'a>) -> CasbinResult<()> {
        self.load_policy(model).await
    }

    /// 清除所有策略
    async fn clear_policy(&mut self) -> CasbinResult<()> {
        Ok(())
    }

    /// 检查是否已过滤
    fn is_filtered(&self) -> bool {
        false
    }

    /// 批量添加策略
    async fn add_policies(&mut self, _sec: &str, _ptype: &str, _rules: Vec<Vec<String>>) -> CasbinResult<bool> {
        Ok(false)
    }

    /// 批量删除策略
    async fn remove_policies(&mut self, _sec: &str, _ptype: &str, _rules: Vec<Vec<String>>) -> CasbinResult<bool> {
        Ok(false)
    }
}

impl Clone for PostgresAdapter {
    fn clone(&self) -> Self {
        Self {
            pool: self.pool.clone(),
            table_name: self.table_name.clone(),
        }
    }
}