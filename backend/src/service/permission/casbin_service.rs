use std::{
    collections::HashMap,
    sync::Arc,
};
use anyhow::{Result, anyhow};
use async_trait::async_trait;
use casbin::{Enforcer, CoreApi, MgmtApi, RbacA<PERSON>, DefaultModel};
use dashmap::DashMap;
use sqlx::PgPool;
use uuid::Uuid;
use tracing::{info, debug, error};
use serde::{Serialize, Deserialize};
use super::postgres_adapter::PostgresAdapter;
use crate::service::menu::MenuService;
use crate::controller::menu::menu_controller::{DetailedMenuResponse, MenuQueryParams};

/// 权限检查请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionRequest {
    pub subject: String,      // 主体：user_id:role:target_id
    pub domain: String,       // 域：tenant_id
    pub object: String,       // 对象：resource:scope:target_id
    pub action: String,       // 动作：read, write, create, delete, manage
}

/// 权限策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionPolicy {
    pub subject: String,
    pub domain: String,
    pub object: String,
    pub action: String,
    pub effect: String,       // allow, deny
}

/// 角色关系
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleRelation {
    pub user: String,
    pub role: String,
    pub domain: String,
}

/// 菜单权限项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MenuPermission {
    pub menu_id: String,
    pub name: String,
    pub path: String,
    pub icon: Option<String>,
    pub parent_id: Option<String>,
    pub sort_order: Option<i32>,
    pub menu_type: String,
    pub children: Option<Vec<MenuPermission>>,
    pub required_permissions: Vec<String>,
}

/// 数据范围权限
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataScope {
    pub resource: String,
    pub scope_type: String,   // class, grade, subject_group, school
    pub scope_value: String,  // 具体的范围值
    pub actions: Vec<String>, // 允许的操作
}

/// Casbin 权限服务特征
#[async_trait]
pub trait CasbinPermissionService: Send + Sync {
    /// 检查权限
    async fn enforce(&self, req: &PermissionRequest) -> Result<bool>;
    
    /// 批量检查权限
    async fn batch_enforce(&self, requests: &[PermissionRequest]) -> Result<Vec<bool>>;
    
    /// 添加权限策略
    async fn add_policy(&self, policy: &PermissionPolicy) -> Result<bool>;
    
    /// 删除权限策略
    async fn remove_policy(&self, policy: &PermissionPolicy) -> Result<bool>;
    
    /// 添加角色关系
    async fn add_role(&self, relation: &RoleRelation) -> Result<bool>;
    
    /// 删除角色关系
    async fn remove_role(&self, relation: &RoleRelation) -> Result<bool>;
    
    /// 获取用户在指定域的所有角色
    async fn get_roles_for_user(&self, user: &str, domain: &str) -> Result<Vec<String>>;
    
    /// 获取用户权限菜单
    async fn get_user_menus(&self, user_identity: &str, tenant_id: &str) -> Result<Vec<MenuPermission>>;
    
    /// 获取所有菜单（不进行权限过滤，用于super_admin）
    async fn get_all_menus(&self) -> Result<Vec<MenuPermission>>;
    
    /// 获取用户数据权限范围
    async fn get_user_data_scopes(&self, user_identity: &str, tenant_id: &str, resource: &str) -> Result<Vec<DataScope>>;
    
    /// 同步角色和权限数据
    async fn sync_permissions(&self, tenant_id: &str) -> Result<()>;
    
    /// 清除租户所有权限数据
    async fn clear_tenant_policies(&self, tenant_id: &str) -> Result<()>;

    /// 从casbin_policies表中获取菜单权限信息
    async fn get_menu_permissions_from_policies(&self, tenant_id: &str) -> Result<Vec<MenuPermission>>;

    /// 从casbin_policies表中获取所有菜单权限信息（包括非激活的）
    async fn get_all_menu_permissions_from_policies(&self) -> Result<Vec<MenuPermission>>;

    /// 检查用户是否有访问特定菜单的权限
    async fn check_menu_access(&self, user_identity: &str, tenant_id: &str, menu_id: &str) -> Result<bool>;

    /// 获取用户对特定菜单的数据访问范围
    async fn get_menu_data_access_scopes(&self, user_identity: &str, tenant_id: &str, menu_id: &str) -> Result<Vec<DataScope>>;

    /// 获取指定对象的所有策略
    async fn get_policies_for_object(&self, object: &str, tenant_id: &str) -> Result<Vec<Vec<String>>>;

    /// 获取指定主体的所有策略
    async fn get_policies_for_subject(&self, subject: &str, tenant_id: &str) -> Result<Vec<Vec<String>>>;
}

/// Casbin 权限服务实现
pub struct MultiTenantCasbinService {
    /// 全局 enforcer
    enforcer: Arc<Enforcer>,
    /// 数据库连接池
    pool: PgPool,
    /// 菜单服务，用于从数据库获取菜单权限
    menu_service: Arc<MenuService>,
    /// 租户 enforcer 缓存
    enforcers: dashmap::DashMap<String, Arc<Enforcer>>,
}

impl MultiTenantCasbinService {
    /// 创建新的多租户 Casbin 服务
    pub async fn new(pool: PgPool, model_path: String, menu_service: Arc<MenuService>) -> Result<Self> {
        // 创建全局 adapter
        let adapter = PostgresAdapter::new(pool.clone()).await?;
        
        // 创建默认模型
        let model = DefaultModel::from_file(&model_path).await?;
        
        // 创建 enforcer
        let mut enforcer = Enforcer::new(model, adapter).await?;
        
        // 启用自动保存策略
        enforcer.enable_auto_save(true);
        
        // 启用自动构建角色链接
        enforcer.enable_auto_build_role_links(true);
        
        debug!("Created global enforcer with model: {}", model_path);
        
        Ok(Self {
            enforcer: Arc::new(enforcer),
            pool,
            menu_service,
            enforcers: DashMap::new(),
        })
    }
    
    /// 获取全局 enforcer
    fn get_enforcer(&self) -> Arc<Enforcer> {
        self.enforcer.clone()
    }
    
    /// 创建租户专用的 enforcer
    async fn create_tenant_enforcer(&self, tenant_id: &str) -> Result<Enforcer> {
        // 创建租户专用的 adapter
        let adapter = PostgresAdapter::new(self.pool.clone()).await?;
        
        // 从全局 enforcer 复制模型
        let model = self.enforcer.get_model();
        
        // 创建新的 enforcer
        let mut enforcer = Enforcer::new(model, adapter).await?;
        
        // 启用自动保存策略
        enforcer.enable_auto_save(true);
        
        // 启用自动构建角色链接
        enforcer.enable_auto_build_role_links(true);
        
        debug!("Created tenant enforcer for tenant: {}", tenant_id);
        
        Ok(enforcer)
    }
    
    /// 获取租户专用的 enforcer
    async fn get_tenant_enforcer(&self, tenant_id: &str) -> Result<Arc<Enforcer>> {
        // 检查缓存中是否存在
        if let Some(enforcer) = self.enforcers.get(tenant_id) {
            return Ok(enforcer.clone());
        }
        
        // 创建新的租户 enforcer
        let enforcer = self.create_tenant_enforcer(tenant_id).await?;
        let enforcer_arc = Arc::new(enforcer);
        
        // 添加到缓存
        self.enforcers.insert(tenant_id.to_string(), enforcer_arc.clone());
        
        Ok(enforcer_arc)
    }
    
    /// 重新加载权限策略
    pub async fn reload_policies(&self) -> Result<()> {
        let mut enforcer = self.enforcer.as_ref().clone();
        enforcer.load_policy().await.map_err(|e| anyhow!("Failed to reload policies: {}", e))?;
        info!("Reloaded all policies");
        Ok(())
    }
    
    /// 从数据库加载菜单权限配置
    async fn load_menu_permissions(&self) -> Result<HashMap<String, MenuPermission>> {
        let mut menus = HashMap::new();

        // 从数据库获取菜单权限数据
        let default_params = MenuQueryParams {
            menu_type: None,
            parent_id: None,
            is_active: None,
            include_children: Some(true),
            include_metadata: Some(true),
            search: None,
            page: None,
            page_size: None,
        };
        match self.menu_service.get_menu_tree(&default_params).await {
            Ok(menu_tree) => {
                // 将菜单树转换为权限配置
                self.convert_menu_tree_to_permissions(&mut menus, menu_tree);
                debug!("Successfully loaded {} menu permissions from database", menus.len());
            }
            Err(e) => {
                error!("Failed to load menu permissions from database: {}", e);
                // 如果数据库加载失败，返回空的权限配置
                // 这样系统仍然可以运行，但菜单权限检查会失败
                return Err(anyhow!("Failed to load menu permissions: {}", e));
            }
        }

        Ok(menus)
    }

    /// 将菜单树转换为权限配置（用于缓存）
    fn convert_menu_tree_to_permissions(
        &self,
        menus: &mut HashMap<String, MenuPermission>,
        menu_tree: Vec<DetailedMenuResponse>
    ) {
        for menu in menu_tree {
            let menu_permission = MenuPermission {
                menu_id: menu.menu_id.clone(),
                name: menu.name.clone(),
                path: menu.path.clone(),
                icon: menu.icon.clone(),
                menu_type: menu.menu_type.clone(),
                parent_id: menu.parent_id.clone(),
                sort_order: Some(menu.sort_order),
                required_permissions: vec![], // TODO: 从数据库或配置中获取菜单所需权限
                children: if let Some(children) = menu.children {
                    if children.is_empty() {
                        None
                    } else {
                        let mut child_permissions = Vec::new();
                        for child in children {
                            child_permissions.push(MenuPermission {
                                menu_id: child.menu_id.clone(),
                                name: child.name.clone(),
                                path: child.path.clone(),
                                icon: child.icon.clone(),
                                parent_id: child.parent_id.clone(),
                                sort_order: Some(child.sort_order),
                                menu_type: child.menu_type.clone(),
                                required_permissions: vec![], // TODO: 从数据库或配置中获取菜单所需权限
                                children: None, // 目前只支持两级菜单
                            });
                            // 也将子菜单添加到主映射中
                            menus.insert(child.menu_id.clone(), MenuPermission {
                                menu_id: child.menu_id.clone(),
                                name: child.name.clone(),
                                path: child.path.clone(),
                                icon: child.icon.clone(),
                                parent_id: child.parent_id.clone(),
                                sort_order: Some(child.sort_order),
                                menu_type: child.menu_type.clone(),
                                required_permissions: vec![], // TODO: 从数据库或配置中获取菜单所需权限
                                children: None,
                            });
                        }
                        Some(child_permissions)
                    }
                } else {
                    None
                },
            };

            menus.insert(menu.menu_id.clone(), menu_permission);
        }
    }

    /// 将已排序的菜单树转换为权限列表（保持排序）
    fn convert_sorted_menu_tree_to_permissions(
        &self,
        menu_tree: Vec<DetailedMenuResponse>
    ) -> Vec<MenuPermission> {
        let mut permissions = Vec::new();

        for menu in menu_tree {
            let menu_permission = MenuPermission {
                menu_id: menu.menu_id.clone(),
                name: menu.name.clone(),
                path: menu.path.clone(),
                icon: menu.icon.clone(),
                parent_id: menu.parent_id.clone(),
                menu_type: menu.menu_type.clone(),
                sort_order: Some(menu.sort_order),
                children: if let Some(children) = menu.children {
                    if children.is_empty() {
                        None
                    } else {
                        let mut child_permissions = Vec::new();
                        for child in children {
                            child_permissions.push(MenuPermission {
                                menu_id: child.menu_id.clone(),
                                name: child.name.clone(),
                                path: child.path.clone(),
                                icon: child.icon.clone(),
                                parent_id: child.parent_id.clone(),
                                sort_order: Some(child.sort_order),
                                menu_type: child.menu_type.clone(),
                                required_permissions: vec![], // TODO: 从数据库或配置中获取菜单所需权限
                                children: None, // 目前只支持两级菜单
                            });
                        }
                        Some(child_permissions)
                    }
                } else {
                    None
                },
            };

            permissions.push(menu_permission);
        }

        permissions
    }

    /// 构建用户身份标识
    fn build_user_identity(user_id: &Uuid, role_code: &str, target_type: &str, target_id: Option<&Uuid>) -> String {
        match target_id {
            Some(id) => format!("{}:{}:{}:{}", user_id, role_code, target_type, id),
            None => format!("{}:{}:{}", user_id, role_code, target_type),
        }
    }
    
    /// 过滤用户可访问的菜单
    fn filter_menus_by_permissions<'a>(
        &'a self,
        menus: Vec<MenuPermission>,
        user_identity: &'a str,
        tenant_id: &'a str,
    ) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Vec<MenuPermission>>> + Send + 'a>> {
        Box::pin(async move {
            let enforcer = self.get_enforcer();
            let mut filtered_menus = Vec::new();
            
            for menu in menus {
                // 检查菜单所需权限
                let mut has_all_permissions = true;
                for required_perm in &menu.required_permissions {
                    let perm_parts: Vec<&str> = required_perm.split(':').collect();
                    if perm_parts.len() >= 2 {
                        let resource = perm_parts[0];
                        let action = perm_parts[1];
                        let object = if perm_parts.len() > 2 {
                            format!("{}:*", resource)
                        } else {
                            required_perm.clone()
                        };
                        
                        if !enforcer.enforce((user_identity, tenant_id, &object, action))? {
                            has_all_permissions = false;
                            break;
                        }
                    }
                }
                
                if has_all_permissions {
                    let mut filtered_menu = menu.clone();
                    // 递归过滤子菜单
                    if let Some(children) = menu.children {
                        let filtered_children = self.filter_menus_by_permissions(
                            children, user_identity, tenant_id
                        ).await?;
                        filtered_menu.children = if filtered_children.is_empty() {
                            None
                        } else {
                            Some(filtered_children)
                        };
                    }
                    filtered_menus.push(filtered_menu);
                }
            }
            
            Ok(filtered_menus)
        })
    }
    
    /// 从casbin_policies表中获取菜单权限信息
    async fn get_menu_permissions_from_policies(&self, tenant_id: &str) -> Result<Vec<MenuPermission>> {
        // 获取基础菜单信息（不包含权限字段）
        let menu_rows = sqlx::query!(
            r#"
            SELECT menu_id, name, path, icon, parent_id, sort_order, menu_type, is_active
            FROM public.menu_permissions
            WHERE is_active = true
            ORDER BY
                CASE WHEN parent_id IS NULL THEN 0 ELSE 1 END,
                sort_order ASC,
                name ASC
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| anyhow!("Failed to fetch menu basic info: {}", e))?;

        let mut menus = Vec::new();

        for row in menu_rows {
            // 从casbin_policies表中获取菜单的权限要求
            let required_permissions = self.get_menu_required_permissions(&row.menu_id, tenant_id).await?;

            // 从casbin_policies表中获取菜单的数据范围
            let data_scopes = self.get_menu_data_scopes(&row.menu_id, tenant_id).await?;

            menus.push(MenuPermission {
                menu_id: row.menu_id,
                name: row.name,
                path: row.path,
                icon: row.icon,
                parent_id: row.parent_id,
                sort_order: row.sort_order,
                menu_type: row.menu_type.unwrap_or_default(),
                required_permissions,
                children: None, // 将在后续构建树结构时填充
            });
        }

        // 构建菜单树结构
        Ok(self.build_menu_tree(menus))
    }

    /// 从casbin_policies表中获取菜单的权限要求
    pub async fn get_menu_required_permissions(&self, menu_id: &str) -> Result<Vec<String>> {
        let policy_rows = sqlx::query!(
            r#"
            SELECT v5
            FROM public.casbin_policies
            WHERE ptype = 'p'
            AND v0 = 'menu'
            AND v2 = $1
            AND v3 = 'check'
            LIMIT 1
            "#,
            format!("menu:{}:requirements", menu_id)
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| anyhow!("Failed to fetch menu required permissions: {}", e))?;

        if let Some(row) = policy_rows.first() {
            if let Some(v5) = &row.v5 {
                if let Ok(policy_data) = serde_json::from_str::<serde_json::Value>(v5) {
                    if let Some(permissions) = policy_data.get("required_permissions") {
                        if let Some(perms_array) = permissions.as_array() {
                            return Ok(perms_array.iter()
                                .filter_map(|v| v.as_str().map(|s| s.to_string()))
                                .collect());
                        }
                    }
                }
            }
        }

        Ok(Vec::new())
    }

    /// 从casbin_policies表中获取菜单的数据范围
    async fn get_menu_data_scopes(&self, menu_id: &str, _tenant_id: &str) -> Result<Option<Vec<String>>> {
        let policy_rows = sqlx::query!(
            r#"
            SELECT v5
            FROM public.casbin_policies
            WHERE ptype = 'p'
            AND v0 = 'menu'
            AND v2 = $1
            AND v3 = 'scope'
            LIMIT 1
            "#,
            format!("menu:{}:data_scope", menu_id)
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| anyhow!("Failed to fetch menu data scopes: {}", e))?;

        if let Some(row) = policy_rows.first() {
            if let Some(v5) = &row.v5 {
                if let Ok(policy_data) = serde_json::from_str::<serde_json::Value>(v5) {
                    if let Some(scopes) = policy_data.get("data_scopes") {
                        if let Some(scopes_array) = scopes.as_array() {
                            let scopes_vec: Vec<String> = scopes_array.iter()
                                .filter_map(|v| v.as_str().map(|s| s.to_string()))
                                .collect();
                            return Ok(if scopes_vec.is_empty() { None } else { Some(scopes_vec) });
                        }
                    }
                }
            }
        }

        Ok(None)
    }

    /// 构建菜单树结构
    fn build_menu_tree(&self, mut menus: Vec<MenuPermission>) -> Vec<MenuPermission> {
        let mut menu_map: std::collections::HashMap<String, MenuPermission> = std::collections::HashMap::new();
        let mut root_menus = Vec::new();

        // 将所有菜单放入map中
        for menu in menus.drain(..) {
            menu_map.insert(menu.menu_id.clone(), menu);
        }

        // 构建树结构
        let menu_ids: Vec<String> = menu_map.keys().cloned().collect();
        for menu_id in menu_ids {
            if let Some(menu) = menu_map.remove(&menu_id) {
                if let Some(parent_id) = &menu.parent_id {
                    // 有父菜单，添加到父菜单的children中
                    if let Some(parent) = menu_map.get_mut(parent_id) {
                        if parent.children.is_none() {
                            parent.children = Some(Vec::new());
                        }
                        parent.children.as_mut().unwrap().push(menu);
                    } else {
                        // 父菜单不存在，作为根菜单处理
                        root_menus.push(menu);
                    }
                } else {
                    // 根菜单
                    root_menus.push(menu);
                }
            }
        }

        // 将剩余的菜单（父菜单）添加到根菜单列表
        for (_, menu) in menu_map {
            root_menus.push(menu);
        }

        // 对菜单进行排序
        root_menus.sort_by(|a, b| {
            a.sort_order.unwrap_or(0).cmp(&b.sort_order.unwrap_or(0))
        });

        // 递归排序子菜单
        for menu in &mut root_menus {
            self.sort_menu_children(menu);
        }

        root_menus
    }

    /// 递归排序菜单子项
    fn sort_menu_children(&self, menu: &mut MenuPermission) {
        if let Some(children) = &mut menu.children {
            children.sort_by(|a, b| {
                a.sort_order.unwrap_or(0).cmp(&b.sort_order.unwrap_or(0))
            });

            for child in children {
                self.sort_menu_children(child);
            }
        }
    }

    /// 使用Casbin策略过滤用户可访问的菜单
    async fn filter_menus_by_casbin_policies(
        &self,
        menus: Vec<MenuPermission>,
        user_identity: &str,
        tenant_id: &str,
    ) -> Result<Vec<MenuPermission>> {
        let enforcer = self.get_enforcer();
        let mut filtered_menus = Vec::new();

        for menu in menus {
            // 检查用户是否有访问该菜单的权限
            let menu_access_allowed = enforcer.enforce((
                user_identity,
                tenant_id,
                &format!("menu:{}", menu.menu_id),
                "access"
            ))?;

            if menu_access_allowed {
                let mut filtered_menu = menu.clone();

                // 递归过滤子菜单
                if let Some(children) = menu.children {
                    let filtered_children = Box::pin(self.filter_menus_by_casbin_policies(
                        children, user_identity, tenant_id
                    )).await?;
                    filtered_menu.children = if filtered_children.is_empty() {
                        None
                    } else {
                        Some(filtered_children)
                    };
                }

                filtered_menus.push(filtered_menu);
            }
        }

        Ok(filtered_menus)
    }

    /// 检查用户是否有访问特定菜单的权限
    pub async fn check_menu_access(
        &self,
        user_identity: &str,
        tenant_id: &str,
        menu_id: &str,
    ) -> Result<bool> {
        let enforcer = self.get_enforcer();

        // 检查基础菜单访问权限
        let menu_access = enforcer.enforce((
            user_identity,
            tenant_id,
            &format!("menu:{}", menu_id),
            "access"
        ))?;

        if !menu_access {
            return Ok(false);
        }

        // 检查菜单的权限要求
        let required_permissions = self.get_menu_required_permissions(menu_id, tenant_id).await?;

        for required_perm in required_permissions {
            let perm_parts: Vec<&str> = required_perm.split(':').collect();
            if perm_parts.len() >= 2 {
                let resource = perm_parts[0];
                let action = perm_parts[1];
                let object = if perm_parts.len() > 2 {
                    format!("{}:{}", resource, perm_parts[2])
                } else {
                    format!("{}:*", resource)
                };

                if !enforcer.enforce((user_identity, tenant_id, &object, action))? {
                    return Ok(false);
                }
            }
        }

        Ok(true)
    }

    /// 获取用户对特定菜单的数据访问范围
    pub async fn get_menu_data_access_scopes(
        &self,
        user_identity: &str,
        tenant_id: &str,
        menu_id: &str,
    ) -> Result<Vec<DataScope>> {
        // 首先检查用户是否有访问该菜单的权限
        if !self.check_menu_access(user_identity, tenant_id, menu_id).await? {
            return Ok(Vec::new());
        }

        // 获取菜单定义的数据范围
        let menu_data_scopes = self.get_menu_data_scopes(menu_id, tenant_id).await?;

        if let Some(scopes) = menu_data_scopes {
            let mut data_scopes = Vec::new();

            for scope in scopes {
                let scope_parts: Vec<&str> = scope.split(':').collect();
                if scope_parts.len() >= 2 {
                    let resource = scope_parts[0];
                    let scope_type = scope_parts[1];
                    let scope_value = if scope_parts.len() > 2 {
                        scope_parts[2].to_string()
                    } else {
                        "*".to_string()
                    };

                    // 检查用户是否有该数据范围的权限
                    let enforcer = self.get_enforcer();
                    if enforcer.enforce((
                        user_identity,
                        tenant_id,
                        &format!("{}:{}", resource, scope_type),
                        "read"
                    ))? {
                        data_scopes.push(DataScope {
                            resource: resource.to_string(),
                            scope_type: scope_type.to_string(),
                            scope_value,
                            actions: vec!["read".to_string()], // 基础读权限
                        });
                    }
                }
            }

            Ok(data_scopes)
        } else {
            Ok(Vec::new())
        }
    }

    /// 清除租户的 enforcer 缓存
    pub async fn clear_tenant_cache(&self, tenant_id: &str) {
        self.enforcers.remove(tenant_id);
        info!("Cleared enforcer cache for tenant: {}", tenant_id);
    }
    
    /// 重新加载租户的权限策略
    pub async fn reload_tenant_policies(&self, tenant_id: &str) -> Result<()> {
        self.clear_tenant_cache(tenant_id).await;
        self.get_tenant_enforcer(tenant_id).await?;
        info!("Reloaded policies for tenant: {}", tenant_id);
        Ok(())
    }
    
    /// 获取租户的 enforcer（公开方法，用于策略管理）
    pub async fn get_tenant_enforcer_public(&self, tenant_id: &str) -> Result<Arc<Enforcer>> {
        self.get_tenant_enforcer(tenant_id).await
    }
}

#[async_trait]
impl CasbinPermissionService for MultiTenantCasbinService {
    async fn enforce(&self, req: &PermissionRequest) -> Result<bool> {
        let enforcer = self.get_enforcer();
        
        let result = enforcer.enforce((
            &req.subject,
            &req.domain, 
            &req.object,
            &req.action,
        ))?;
        
        debug!(
            "Permission check: {} -> {} @ {} | {} -> {}",
            req.subject, req.object, req.domain, req.action, result
        );
        
        Ok(result)
    }
    
    async fn batch_enforce(&self, requests: &[PermissionRequest]) -> Result<Vec<bool>> {
        if requests.is_empty() {
            return Ok(vec![]);
        }
        
        // 按租户分组批量检查
        let mut results = Vec::with_capacity(requests.len());
        let mut tenant_groups: HashMap<String, Vec<(usize, &PermissionRequest)>> = HashMap::new();
        
        // 按租户分组
        for (idx, req) in requests.iter().enumerate() {
            tenant_groups
                .entry(req.domain.clone())
                .or_insert_with(Vec::new)
                .push((idx, req));
        }
        
        // 初始化结果向量
        results.resize(requests.len(), false);
        
        // 按租户批量检查
        for (tenant_id, tenant_requests) in tenant_groups {
            let enforcer = self.get_enforcer();
            
            for (idx, req) in tenant_requests {
                let result = enforcer.enforce((
                    &req.subject,
                    &req.domain,
                    &req.object, 
                    &req.action,
                ))?;
                results[idx] = result;
            }
        }
        
        Ok(results)
    }
    
    async fn add_policy(&self, policy: &PermissionPolicy) -> Result<bool> {
        let enforcer = self.get_enforcer();
        
        // 由于现在使用Arc<Enforcer>，暂时不支持直接修改
        // 需要重新设计架构来支持可变操作
        error!("add_policy: Operation not supported with current Arc<Enforcer> design");
        Ok(false)
    }
    
    async fn remove_policy(&self, policy: &PermissionPolicy) -> Result<bool> {
        let enforcer = self.get_enforcer();
        
        // 由于现在使用Arc<Enforcer>，暂时不支持直接修改
        error!("remove_policy: Operation not supported with current Arc<Enforcer> design");
        Ok(false)
    }
    
    async fn add_role(&self, relation: &RoleRelation) -> Result<bool> {
        // 由于现在使用Arc<Enforcer>，暂时不支持直接修改
        error!("add_role: Operation not supported with current Arc<Enforcer> design");
        Ok(false)
    }
    
    async fn remove_role(&self, relation: &RoleRelation) -> Result<bool> {
        // 由于现在使用Arc<Enforcer>，暂时不支持直接修改
        error!("remove_role: Operation not supported with current Arc<Enforcer> design");
        Ok(false)
    }
    
    async fn get_roles_for_user(&self, user: &str, domain: &str) -> Result<Vec<String>> {
        let enforcer = self.get_enforcer();
        let roles = enforcer.get_roles_for_user(user, Some(domain));
        Ok(roles)
    }
    
    async fn get_user_menus(&self, user_identity: &str, tenant_id: &str) -> Result<Vec<MenuPermission>> {
        // 1. 获取用户角色列表
        let user_roles = self.get_roles_for_user(user_identity, tenant_id).await?;
        
        if user_roles.is_empty() {
            debug!("No roles found for user: {} in tenant: {}", user_identity, tenant_id);
            return Ok(Vec::new());
        }
        
        // 2. 从casbin_policies表中查询角色对应的菜单权限
        let mut menu_ids = Vec::new();
        
        // 构建角色查询参数
        for role in &user_roles {
            let role_policies = sqlx::query!(
                r#"
                SELECT DISTINCT v2
                FROM public.casbin_policies
                WHERE ptype = 'p'
                AND v0 = $1
                AND (v1 = $2 OR v1 = 'tenant_*')
                AND v2 LIKE 'menu:%'
                AND v3 = 'access'
                AND v4 = 'allow'
                ORDER BY v2
                "#,
                role,
                tenant_id
            )
            .fetch_all(&self.pool)
            .await
            .map_err(|e| anyhow!("Failed to fetch role menu permissions: {}", e))?;
            
            // 从 v2 字段提取 menu_id (格式: menu:menu_id)
            for policy in role_policies {
                if let Some(v2) = policy.v2 {
                    if let Some(menu_id) = v2.strip_prefix("menu:") {
                        if !menu_ids.contains(&menu_id.to_string()) {
                            menu_ids.push(menu_id.to_string());
                        }
                    }
                }
            }
        }
        
        if menu_ids.is_empty() {
            debug!("No menu permissions found for roles: {:?} in tenant: {}", user_roles, tenant_id);
            return Ok(Vec::new());
        }
        
        // 3. 根据menu_ids从menu_permissions表中获取菜单详细信息
        let menu_rows = sqlx::query!(
            r#"
            SELECT menu_id, name, path, icon, parent_id, sort_order, menu_type, is_active
            FROM public.menu_permissions
            WHERE menu_id = ANY($1)
            AND is_active = true
            ORDER BY
                CASE WHEN parent_id IS NULL THEN 0 ELSE 1 END,
                sort_order ASC,
                name ASC
            "#,
            &menu_ids
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| anyhow!("Failed to fetch menu details: {}", e))?;
        
        // 4. 构造MenuPermission对象
        let mut menus = Vec::new();
        for row in menu_rows {
            menus.push(MenuPermission {
                menu_id: row.menu_id,
                name: row.name,
                path: row.path,
                icon: row.icon,
                parent_id: row.parent_id,
                sort_order: row.sort_order,
                menu_type: row.menu_type.unwrap_or_default(),
                required_permissions: vec![], // TODO: 如需要权限检查，可在此处获取
                children: None, // 将在后续构建树结构时填充
            });
        }
        
        // 5. 构建菜单树结构并返回
        Ok(self.build_menu_tree(menus))
    }

    async fn get_all_menus(&self) -> Result<Vec<MenuPermission>> {
        // 从casbin_policies表中获取所有菜单权限信息（包括非激活的）
        self.get_all_menu_permissions_from_policies().await
    }

    /// 从casbin_policies表中获取所有菜单权限信息（包括非激活的）
    async fn get_all_menu_permissions_from_policies(&self) -> Result<Vec<MenuPermission>> {
        // 获取所有菜单的基础信息（包括非激活的）
        let menu_rows = sqlx::query!(
            r#"
            SELECT menu_id, name, path, icon, parent_id, sort_order, menu_type, is_active
            FROM public.menu_permissions
            ORDER BY
                CASE WHEN parent_id IS NULL THEN 0 ELSE 1 END,
                sort_order ASC,
                name ASC
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| anyhow!("Failed to fetch all menu basic info: {}", e))?;

        let mut menus = Vec::new();

        for row in menu_rows {
            // 从casbin_policies表中获取菜单的权限要求（使用template租户）
            let required_permissions = self.get_menu_required_permissions(&row.menu_id).await?;
            menus.push(MenuPermission {
                menu_id: row.menu_id,
                name: row.name,
                path: row.path,
                icon: row.icon,
                parent_id: row.parent_id,
                sort_order: row.sort_order,
                menu_type: row.menu_type.unwrap_or_default(),
                required_permissions,
                children: None, // 将在后续构建树结构时填充
            });
        }

        // 构建菜单树结构
        Ok(self.build_menu_tree(menus))
    }
    
    async fn get_user_data_scopes(&self, user_identity: &str, tenant_id: &str, resource: &str) -> Result<Vec<DataScope>> {
        let enforcer = self.get_enforcer();
        let mut data_scopes: Vec<DataScope> = Vec::new();
        
        // 获取用户的所有权限策略
        let policies = enforcer.get_filtered_policy(0, vec![user_identity.to_string()]);
        
        for policy in policies {
            if policy.len() >= 4 {
                let obj = &policy[2];
                let act = &policy[3];
                
                // 解析对象，格式：resource:scope:target_id 或 resource:scope
                let obj_parts: Vec<&str> = obj.split(':').collect();
                if obj_parts.len() >= 2 && obj_parts[0] == resource {
                    let scope_type = obj_parts[1].to_string();
                    let scope_value = if obj_parts.len() > 2 {
                        obj_parts[2].to_string()
                    } else {
                        "*".to_string()
                    };
                    
                    // 查找或创建数据范围
                    if let Some(existing_scope) = data_scopes.iter_mut().find(|s| {
                        s.scope_type == scope_type && s.scope_value == scope_value
                    }) {
                        if !existing_scope.actions.contains(&act.to_string()) {
                            existing_scope.actions.push(act.to_string());
                        }
                    } else {
                        data_scopes.push(DataScope {
                            resource: resource.to_string(),
                            scope_type,
                            scope_value,
                            actions: vec![act.to_string()],
                        });
                    }
                }
            }
        }
        
        Ok(data_scopes)
    }
    
    async fn sync_permissions(&self, tenant_id: &str) -> Result<()> {
        // 重新创建 enforcer 并重新加载策略
        let mut new_enforcer = self.create_tenant_enforcer(tenant_id).await?;
        
        // 重新加载策略
        new_enforcer.load_policy().await?;
        
        // 更新缓存
        let new_enforcer_arc = Arc::new(new_enforcer);
        self.enforcers.insert(tenant_id.to_string(), new_enforcer_arc);
        
        info!("Synced permissions for tenant: {}", tenant_id);
        Ok(())
    }
    
    async fn clear_tenant_policies(&self, tenant_id: &str) -> Result<()> {
        // 重新创建 enforcer
        let mut new_enforcer = self.create_tenant_enforcer(tenant_id).await?;
        
        // 清除所有策略
        let _= new_enforcer.clear_policy().await;
        
        // 保存到数据库
        new_enforcer.save_policy().await?;
        
        // 更新缓存
        let new_enforcer_arc = Arc::new(new_enforcer);
        self.enforcers.insert(tenant_id.to_string(), new_enforcer_arc);
        
        info!("Cleared all policies for tenant: {}", tenant_id);
        Ok(())
    }

    /// 从casbin_policies表中获取菜单权限信息（trait实现）
    async fn get_menu_permissions_from_policies(&self, tenant_id: &str) -> Result<Vec<MenuPermission>> {
        // 直接调用已有的实现
        MultiTenantCasbinService::get_menu_permissions_from_policies(self, tenant_id).await
    }



    /// 检查用户是否有访问特定菜单的权限（trait实现）
    async fn check_menu_access(&self, user_identity: &str, tenant_id: &str, menu_id: &str) -> Result<bool> {
        // 调用实际的实现方法
        MultiTenantCasbinService::check_menu_access(self, user_identity, tenant_id, menu_id).await
    }

    /// 获取用户对特定菜单的数据访问范围（trait实现）
    async fn get_menu_data_access_scopes(&self, user_identity: &str, tenant_id: &str, menu_id: &str) -> Result<Vec<DataScope>> {
        // 调用现有的数据权限获取逻辑
        self.get_user_data_scopes(user_identity, tenant_id, &format!("menu:{}", menu_id)).await
    }

    /// 获取指定对象的所有策略
    async fn get_policies_for_object(&self, object: &str, tenant_id: &str) -> Result<Vec<Vec<String>>> {
        let enforcer = self.get_enforcer();

        // 获取所有策略
        let all_policies = enforcer.get_policy();

        // 过滤出与指定对象相关的策略
        let filtered_policies: Vec<Vec<String>> = all_policies
            .into_iter()
            .filter(|policy| {
                // 策略格式: [subject, domain, object, action, effect]
                policy.len() >= 3 && policy[1] == tenant_id && policy[2] == object
            })
            .collect();

        Ok(filtered_policies)
    }

    /// 获取指定主体的所有策略
    async fn get_policies_for_subject(&self, subject: &str, tenant_id: &str) -> Result<Vec<Vec<String>>> {
        let enforcer = self.get_enforcer();

        // 获取所有策略
        let all_policies = enforcer.get_policy();

        // 过滤出与指定主体相关的策略
        let filtered_policies: Vec<Vec<String>> = all_policies
            .into_iter()
            .filter(|policy| {
                // 策略格式: [subject, domain, object, action, effect]
                policy.len() >= 2 && policy[0] == subject && policy[1] == tenant_id
            })
            .collect();

        Ok(filtered_policies)
    }


}