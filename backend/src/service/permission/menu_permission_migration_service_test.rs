#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::PgPool;
    use std::collections::HashMap;
    use uuid::Uuid;

    /// 创建测试数据库连接池
    async fn create_test_pool() -> PgPool {
        // 这里应该连接到测试数据库
        // 实际实现中需要配置测试数据库连接
        todo!("Configure test database connection")
    }

    /// 设置测试数据
    async fn setup_test_data(pool: &PgPool) -> Result<(), sqlx::Error> {
        // 清理测试数据
        sqlx::query("DELETE FROM public.casbin_policies WHERE tenant_id = 'test'")
            .execute(pool)
            .await?;
        
        sqlx::query("DELETE FROM public.menu_permissions WHERE menu_id LIKE 'test_%'")
            .execute(pool)
            .await?;

        // 插入测试菜单数据
        sqlx::query!(
            r#"
            INSERT INTO public.menu_permissions (
                menu_id, name, path, icon, parent_id, sort_order, 
                required_permissions, data_scopes, is_active
            ) VALUES 
            ($1, $2, $3, $4, $5, $6, $7, $8, $9),
            ($10, $11, $12, $13, $14, $15, $16, $17, $18)
            "#,
            "test_menu_1", "测试菜单1", "/test1", "test-icon", None::<String>, 100,
            vec!["test:read", "test:write"], Some(vec!["class:*", "school:own"]), true,
            "test_menu_2", "测试菜单2", "/test2", "test-icon2", Some("test_menu_1"), 101,
            vec!["test2:read"], None::<Vec<String>>, true
        )
        .execute(pool)
        .await?;

        Ok(())
    }

    /// 清理测试数据
    async fn cleanup_test_data(pool: &PgPool) -> Result<(), sqlx::Error> {
        sqlx::query("DELETE FROM public.casbin_policies WHERE tenant_id = 'test'")
            .execute(pool)
            .await?;
        
        sqlx::query("DELETE FROM public.menu_permissions WHERE menu_id LIKE 'test_%'")
            .execute(pool)
            .await?;

        Ok(())
    }

    #[tokio::test]
    async fn test_menu_permission_migration_service_creation() {
        let pool = create_test_pool().await;
        let service = MenuPermissionMigrationService::new(pool);
        
        // 测试服务创建成功
        assert!(true); // 如果能创建服务实例，测试通过
    }

    #[tokio::test]
    async fn test_fetch_all_menu_permissions() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        let service = MenuPermissionMigrationService::new(pool.clone());
        let result = service.fetch_all_menu_permissions().await;
        
        assert!(result.is_ok());
        let menus = result.unwrap();
        assert_eq!(menus.len(), 2);
        
        // 验证菜单数据
        let menu1 = menus.iter().find(|m| m.menu_id == "test_menu_1").unwrap();
        assert_eq!(menu1.name, "测试菜单1");
        assert_eq!(menu1.required_permissions, vec!["test:read", "test:write"]);
        assert_eq!(menu1.data_scopes, Some(vec!["class:*".to_string(), "school:own".to_string()]));
        
        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_migrate_single_menu() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        let service = MenuPermissionMigrationService::new(pool.clone());
        
        let menu_data = MenuPermissionData {
            menu_id: "test_menu_1".to_string(),
            name: "测试菜单1".to_string(),
            path: "/test1".to_string(),
            required_permissions: vec!["test:read".to_string(), "test:write".to_string()],
            data_scopes: Some(vec!["class:*".to_string()]),
            is_active: true,
        };
        
        let result = service.migrate_single_menu(&menu_data).await;
        assert!(result.is_ok());
        
        let policy_count = result.unwrap();
        assert_eq!(policy_count, 3); // 基础访问策略 + 权限要求策略 + 数据范围策略
        
        // 验证策略是否正确创建
        let access_policy_count: i64 = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM public.casbin_policies WHERE v2 = $1 AND v3 = 'access'",
            "menu:test_menu_1"
        )
        .fetch_one(&pool)
        .await
        .unwrap()
        .unwrap_or(0);
        assert_eq!(access_policy_count, 1);
        
        let requirement_policy_count: i64 = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM public.casbin_policies WHERE v2 = $1 AND v3 = 'check'",
            "menu:test_menu_1:requirements"
        )
        .fetch_one(&pool)
        .await
        .unwrap()
        .unwrap_or(0);
        assert_eq!(requirement_policy_count, 1);
        
        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_create_menu_access_policy() {
        let pool = create_test_pool().await;
        let service = MenuPermissionMigrationService::new(pool);
        
        let menu_data = MenuPermissionData {
            menu_id: "test_menu".to_string(),
            name: "测试菜单".to_string(),
            path: "/test".to_string(),
            required_permissions: vec![],
            data_scopes: None,
            is_active: true,
        };
        
        let policy = service.create_menu_access_policy(&menu_data);
        
        assert_eq!(policy.ptype, "p");
        assert_eq!(policy.v0, "menu");
        assert_eq!(policy.v1, "tenant_*");
        assert_eq!(policy.v2, "menu:test_menu");
        assert_eq!(policy.v3, "access");
        assert_eq!(policy.v4, "allow");
        assert_eq!(policy.tenant_id, "template");
        assert!(policy.v5.is_some());
        
        // 验证v5字段包含正确的JSON数据
        let v5_data: serde_json::Value = serde_json::from_str(&policy.v5.unwrap()).unwrap();
        assert_eq!(v5_data["menu_name"], "测试菜单");
        assert_eq!(v5_data["menu_path"], "/test");
    }

    #[tokio::test]
    async fn test_create_permission_requirement_policy() {
        let pool = create_test_pool().await;
        let service = MenuPermissionMigrationService::new(pool);
        
        let menu_data = MenuPermissionData {
            menu_id: "test_menu".to_string(),
            name: "测试菜单".to_string(),
            path: "/test".to_string(),
            required_permissions: vec!["test:read".to_string(), "test:write".to_string()],
            data_scopes: None,
            is_active: true,
        };
        
        let policy = service.create_permission_requirement_policy(&menu_data);
        
        assert_eq!(policy.ptype, "p");
        assert_eq!(policy.v0, "menu");
        assert_eq!(policy.v1, "tenant_*");
        assert_eq!(policy.v2, "menu:test_menu:requirements");
        assert_eq!(policy.v3, "check");
        assert_eq!(policy.v4, "allow");
        assert_eq!(policy.tenant_id, "template");
        
        // 验证v5字段包含正确的权限要求
        let v5_data: serde_json::Value = serde_json::from_str(&policy.v5.unwrap()).unwrap();
        let required_perms = v5_data["required_permissions"].as_array().unwrap();
        assert_eq!(required_perms.len(), 2);
        assert!(required_perms.contains(&serde_json::Value::String("test:read".to_string())));
        assert!(required_perms.contains(&serde_json::Value::String("test:write".to_string())));
    }

    #[tokio::test]
    async fn test_create_data_scope_policy() {
        let pool = create_test_pool().await;
        let service = MenuPermissionMigrationService::new(pool);
        
        let menu_data = MenuPermissionData {
            menu_id: "test_menu".to_string(),
            name: "测试菜单".to_string(),
            path: "/test".to_string(),
            required_permissions: vec![],
            data_scopes: Some(vec!["class:*".to_string(), "school:own".to_string()]),
            is_active: true,
        };
        
        let data_scopes = vec!["class:*".to_string(), "school:own".to_string()];
        let policy = service.create_data_scope_policy(&menu_data, &data_scopes);
        
        assert_eq!(policy.ptype, "p");
        assert_eq!(policy.v0, "menu");
        assert_eq!(policy.v1, "tenant_*");
        assert_eq!(policy.v2, "menu:test_menu:data_scope");
        assert_eq!(policy.v3, "scope");
        assert_eq!(policy.v4, "allow");
        assert_eq!(policy.tenant_id, "template");
        
        // 验证v5字段包含正确的数据范围
        let v5_data: serde_json::Value = serde_json::from_str(&policy.v5.unwrap()).unwrap();
        let scopes = v5_data["data_scopes"].as_array().unwrap();
        assert_eq!(scopes.len(), 2);
        assert!(scopes.contains(&serde_json::Value::String("class:*".to_string())));
        assert!(scopes.contains(&serde_json::Value::String("school:own".to_string())));
    }

    #[tokio::test]
    async fn test_migration_result_structure() {
        let result = MigrationResult {
            total_menus: 10,
            migrated_menus: 8,
            created_policies: 24,
            failed_menus: vec!["menu1".to_string(), "menu2".to_string()],
            warnings: vec!["Warning 1".to_string()],
        };
        
        assert_eq!(result.total_menus, 10);
        assert_eq!(result.migrated_menus, 8);
        assert_eq!(result.created_policies, 24);
        assert_eq!(result.failed_menus.len(), 2);
        assert_eq!(result.warnings.len(), 1);
    }

    #[tokio::test]
    async fn test_check_migration_status() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        let service = MenuPermissionMigrationService::new(pool.clone());
        let result = service.check_migration_status().await;
        
        assert!(result.is_ok());
        let status = result.unwrap();
        
        // 验证状态包含预期的键
        assert!(status.contains_key("total_menus"));
        assert!(status.contains_key("migrated_policies"));
        assert!(status.contains_key("menu_access_policies"));
        
        cleanup_test_data(&pool).await.unwrap();
    }

    #[tokio::test]
    async fn test_rollback_migration() {
        let pool = create_test_pool().await;
        setup_test_data(&pool).await.unwrap();
        
        let service = MenuPermissionMigrationService::new(pool.clone());
        
        // 先执行迁移
        let _migration_result = service.migrate_all_menu_permissions().await.unwrap();
        
        // 然后回滚
        let rollback_result = service.rollback_migration().await;
        assert!(rollback_result.is_ok());
        
        let deleted_count = rollback_result.unwrap();
        assert!(deleted_count > 0);
        
        // 验证策略已被删除
        let remaining_policies: i64 = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM public.casbin_policies WHERE v5::text LIKE '%migration_source%'"
        )
        .fetch_one(&pool)
        .await
        .unwrap()
        .unwrap_or(0);
        assert_eq!(remaining_policies, 0);
        
        cleanup_test_data(&pool).await.unwrap();
    }
}
