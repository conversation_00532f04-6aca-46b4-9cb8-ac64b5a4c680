use anyhow::Result;
use std::collections::HashMap;
use tracing::{debug, info, warn};

use super::{CasbinPermissionService, DataScope};

/// 统一菜单权限服务
pub struct UnifiedMenuPermissionService<'a> {
    casbin_service: &'a dyn CasbinPermissionService,
}

impl<'a> UnifiedMenuPermissionService<'a> {
    pub fn new(casbin_service: &'a dyn CasbinPermissionService) -> Self {
        Self { casbin_service }
    }
    
    /// 检查菜单访问权限（基于Casbin策略）
    pub async fn can_access_menu(
        &self,
        user_identity: &str,
        tenant_id: &str,
        menu_id: &str,
    ) -> Result<bool> {
        debug!("检查菜单访问权限: user_identity={}, menu_id={}", user_identity, menu_id);
        
        // 通过Casbin检查菜单访问权限
        let has_access = self.casbin_service
            .enforce(user_identity, "menu", "access", menu_id, Some(tenant_id))
            .await?;
            
        debug!("菜单权限检查结果: {} -> {} = {}", user_identity, menu_id, has_access);
        Ok(has_access)
    }
    
    /// 批量检查菜单访问权限
    pub async fn check_menu_access_batch(
        &self,
        user_identity: &str,
        tenant_id: &str,
        menu_ids: &[String],
    ) -> Result<HashMap<String, bool>> {
        info!("批量检查菜单权限: user_identity={}, menu_count={}", user_identity, menu_ids.len());
        
        let mut results = HashMap::new();
        
        for menu_id in menu_ids {
            let has_access = self.can_access_menu(user_identity, tenant_id, menu_id).await?;
            results.insert(menu_id.clone(), has_access);
        }
        
        Ok(results)
    }
    
    /// 获取用户可访问的菜单列表
    pub async fn get_accessible_menus(
        &self,
        user_identity: &str,
        tenant_id: &str,
        all_menu_ids: &[String],
    ) -> Result<Vec<String>> {
        info!("获取用户可访问菜单: user_identity={}, total_menus={}", user_identity, all_menu_ids.len());
        
        let mut accessible_menus = Vec::new();
        
        for menu_id in all_menu_ids {
            if self.can_access_menu(user_identity, tenant_id, menu_id).await? {
                accessible_menus.push(menu_id.clone());
            }
        }
        
        info!("用户可访问菜单数量: {}", accessible_menus.len());
        Ok(accessible_menus)
    }
    
    /// 获取菜单的数据权限范围
    pub async fn get_menu_data_scopes(
        &self,
        user_identity: &str,
        tenant_id: &str,
        resource: &str,
    ) -> Result<Vec<DataScope>> {
        debug!("获取菜单数据权限范围: user_identity={}, resource={}", user_identity, resource);
        
        // 复用现有的数据权限获取逻辑
        self.casbin_service
            .get_user_data_scopes(user_identity, tenant_id, resource)
            .await
    }
    
    /// 检查菜单访问权限和数据权限
    pub async fn check_menu_with_data_access(
        &self,
        user_identity: &str,
        tenant_id: &str,
        menu_id: &str,
        resource: &str,
    ) -> Result<MenuAccessResult> {
        info!("检查菜单和数据权限: menu_id={}, resource={}", menu_id, resource);
        
        // 1. 检查菜单访问权限
        let can_access_menu = self.can_access_menu(user_identity, tenant_id, menu_id).await?;
        
        if !can_access_menu {
            return Ok(MenuAccessResult {
                can_access_menu: false,
                data_scopes: Vec::new(),
                reason: Some("无菜单访问权限".to_string()),
            });
        }
        
        // 2. 获取数据权限范围
        let data_scopes = self.get_menu_data_scopes(user_identity, tenant_id, resource).await?;
        
        Ok(MenuAccessResult {
            can_access_menu: true,
            data_scopes,
            reason: None,
        })
    }
}

/// 菜单访问检查结果
#[derive(Debug, Clone)]
pub struct MenuAccessResult {
    pub can_access_menu: bool,
    pub data_scopes: Vec<DataScope>,
    pub reason: Option<String>,
}

impl MenuAccessResult {
    pub fn has_data_access(&self) -> bool {
        self.can_access_menu && !self.data_scopes.is_empty()
    }
    
    pub fn get_scope_values(&self, scope_type: &str) -> Vec<String> {
        self.data_scopes
            .iter()
            .filter(|scope| scope.scope_type == scope_type)
            .map(|scope| scope.scope_value.clone())
            .collect()
    }
}

/// 菜单数据范围
#[derive(Debug, Clone)]
pub struct MenuDataScope {
    pub menu_id: String,
    pub resource: String,
    pub data_scopes: Vec<DataScope>,
}

impl MenuDataScope {
    pub fn new(menu_id: String, resource: String, data_scopes: Vec<DataScope>) -> Self {
        Self {
            menu_id,
            resource,
            data_scopes,
        }
    }
    
    pub fn has_scope(&self, scope_type: &str, scope_value: &str) -> bool {
        self.data_scopes.iter().any(|scope| {
            scope.scope_type == scope_type && 
            (scope.scope_value == scope_value || scope.scope_value == "*")
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    
    // Mock实现用于测试
    struct MockCasbinService {
        allowed_menus: HashMap<String, Vec<String>>,
    }
    
    impl MockCasbinService {
        fn new() -> Self {
            let mut allowed_menus = HashMap::new();
            allowed_menus.insert("role:班主任".to_string(), vec!["student_management".to_string()]);
            allowed_menus.insert("role:教导主任".to_string(), vec!["student_management".to_string(), "teacher_management".to_string()]);
            
            Self { allowed_menus }
        }
    }
    
    #[async_trait]
    impl CasbinPermissionService for MockCasbinService {
        async fn enforce(
            &self,
            user: &str,
            resource: &str,
            action: &str,
            object: &str,
            tenant_id: Option<&str>,
        ) -> Result<bool> {
            if resource == "menu" && action == "access" {
                let allowed = self.allowed_menus
                    .get(user)
                    .map(|menus| menus.contains(&object.to_string()))
                    .unwrap_or(false);
                return Ok(allowed);
            }
            Ok(false)
        }
        
        async fn get_user_data_scopes(&self, _user: &str, _tenant_id: &str, _resource: &str) -> Result<Vec<DataScope>> {
            Ok(vec![])
        }
        
        async fn add_policy_for_tenant(&self, _tenant_id: &str, _policy: Vec<String>) -> Result<bool> {
            Ok(true)
        }
    }
    
    #[tokio::test]
    async fn test_can_access_menu() {
        let casbin_service = MockCasbinService::new();
        let menu_service = UnifiedMenuPermissionService::new(&casbin_service);
        
        // 班主任可以访问学生管理
        let result = menu_service
            .can_access_menu("role:班主任", "tenant_001", "student_management")
            .await
            .unwrap();
        assert!(result);
        
        // 班主任不能访问教师管理
        let result = menu_service
            .can_access_menu("role:班主任", "tenant_001", "teacher_management")
            .await
            .unwrap();
        assert!(!result);
    }
    
    #[tokio::test]
    async fn test_get_accessible_menus() {
        let casbin_service = MockCasbinService::new();
        let menu_service = UnifiedMenuPermissionService::new(&casbin_service);
        
        let all_menus = vec![
            "student_management".to_string(),
            "teacher_management".to_string(),
            "exam_management".to_string(),
        ];
        
        let accessible = menu_service
            .get_accessible_menus("role:班主任", "tenant_001", &all_menus)
            .await
            .unwrap();
            
        assert_eq!(accessible, vec!["student_management"]);
    }
}