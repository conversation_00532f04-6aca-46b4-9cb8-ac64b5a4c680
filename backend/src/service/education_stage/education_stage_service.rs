use crate::model::base::PageResult;
use crate::model::education_stage::{
    CreateEducationStageRequest, EducationStage, EducationStageQueryParams,
    EducationStageStatistics, EducationStageSummary, EducationStageUsageStats, EducationStageVO,
    UpdateEducationStageRequest,
};
use crate::model::grade::grade::GradeLevel;
use crate::model::{PageParams, SubjectQueryParams, SubjectVO};
use crate::utils::error::AppError;
use serde::Deserialize;
use sqlx::{PgPool, Postgres, QueryBuilder, Row};
use uuid::Uuid;
use anyhow::bail;
use anyhow::Result;

#[derive(Clone)]
pub struct EducationStageService {
    db_pool: PgPool,
}

impl EducationStageService {
    pub fn new(db_pool: PgPool) -> Self {
        Self { db_pool }
    }

    /// 获取学段列表（支持分页和查询）
    pub async fn get_education_stages(
        &self,
        params: EducationStageQueryParams,
    ) -> Result<PageResult<EducationStageVO>, AppError> {
        // 内部函数 构造公共的查询内容，传入的是build引用
        fn build_where_clause(
            builder: &mut QueryBuilder<Postgres>,
            params: &EducationStageQueryParams,
        ) {
            let mut has_where = false;
            
            if let Some(is_active) = params.is_active {
                builder.push(" WHERE es.is_active = ").push_bind(is_active);
                has_where = true;
            }

            // 添加搜索条件
            if let Some(search) = &params.search {
                if !search.trim().is_empty() {
                    let pattern = format!("%{}%", search.trim());
                    if has_where {
                        builder.push(" AND ");
                    } else {
                        builder.push(" WHERE ");
                        has_where = true;
                    }
                    builder
                        .push("(es.name ILIKE ")
                        .push_bind(pattern.clone())
                        .push(" OR es.code ILIKE ")
                        .push_bind(pattern.clone())
                        .push(" OR es.short_name ILIKE ")
                        .push_bind(pattern)
                        .push(")");
                }
            }

            // 添加是否标准学段条件
            if let Some(is_standard) = params.is_standard {
                if has_where {
                    builder.push(" AND ");
                } else {
                    builder.push(" WHERE ");
                    has_where = true;
                }
                builder.push("es.is_standard = ").push_bind(is_standard);
            }
        }

        // 构建 COUNT 查询
        let mut count_builder = QueryBuilder::new(
            "SELECT COUNT(*) as total FROM public.education_stages es",
        );
        build_where_clause(&mut count_builder, &params);

        // 构建主查询
        let mut query_builder = QueryBuilder::new(
            "SELECT 
                es.id, es.code, es.name, es.short_name, es.description, 
                es.order_level, es.duration_years, es.age_range, 
                es.is_standard, es.is_active, es.created_at, es.updated_at
            FROM public.education_stages es",
        );
        build_where_clause(&mut query_builder, &params);

        // 添加排序和分页
        let pagination = PageParams {
            page: params.page,
            page_size: params.page_size,
        };
        let page = pagination.get_page();
        let page_size = pagination.get_page_size();
        let offset = pagination.get_offset();
        
        let order_direction = params.order_direction.as_deref().unwrap_or("asc");
        query_builder
            .push(" ORDER BY es.order_level ")
            .push(order_direction)
            .push(" LIMIT ")
            .push_bind(page_size as i64)
            .push(" OFFSET ")
            .push_bind(offset as i64);

        // 执行查询
        let total: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.db_pool)
            .await?;

        let education_stages = query_builder
            .build_query_as::<EducationStageVO>()
            .fetch_all(&self.db_pool)
            .await?;

        Ok(PageResult {
            data: education_stages,
            total,
            page: page as i64,
            page_size: page_size as i64,
            total_pages: (total + page_size as i64 - 1) / page_size as i64,
        })
    }

    /// 获取学段简要信息列表（用于下拉选择）
    pub async fn get_education_stage_summaries(
        &self,
        is_active: Option<bool>,
    ) -> Result<Vec<EducationStageSummary>> {
        let query = match is_active {
            Some(active) => {
                sqlx::query_as::<_, EducationStageSummary>(
                    "SELECT id, code, name, short_name, is_active FROM public.education_stages WHERE is_active = $1 ORDER BY order_level"
                )
                .bind(active)
            },
            None => {
                sqlx::query_as::<_, EducationStageSummary>(
                    "SELECT id, code, name, short_name, is_active FROM public.education_stages ORDER BY order_level"
                )
            }
        };

        let stages = query.fetch_all(&self.db_pool).await?;
        Ok(stages)
    }

    /// 根据ID获取学段详情
    pub async fn get_education_stage_by_id(
        &self,
        id: Uuid,
    ) -> Result<Option<EducationStageVO>> {
        let esvo = sqlx::query_as::<_,EducationStageVO>(r#"
            SELECT 
                es.id,
                es.code,
                es.name,
                es.short_name,
                es.description,
                es.order_level,
                es.duration_years,
                es.age_range,
                es.is_standard,
                es.is_active,
                es.created_at,
                es.updated_at
            FROM public.education_stages es
            WHERE es.id = $1
        "#)
            .bind(id)
            .fetch_optional(&self.db_pool)
            .await?;
        Ok(esvo)
    }

    /// 根据代码获取学段
    pub async fn get_education_stage_by_code(
        &self,
        code: &str,
    ) -> Result<Option<EducationStage>, AppError> {
        let stage = sqlx::query_as::<_, EducationStage>(
            "SELECT * FROM public.education_stages WHERE code = $1",
        )
        .bind(code)
        .fetch_optional(&self.db_pool)
        .await?;

        Ok(stage)
    }

    /// 创建学段
    pub async fn create_education_stage(
        &self,
        payload: CreateEducationStageRequest,
    ) -> Result<EducationStage> {
        // 检查代码是否已存在
        if let Some(_) = self.get_education_stage_by_code(&payload.code).await? {
            bail!("Education stage code already exists")
        }

        let stage = sqlx::query_as::<_, EducationStage>(
            r#"
            INSERT INTO public.education_stages (code, name, short_name, description, order_level, duration_years, age_range, is_standard)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
            "#,
        )
        .bind(&payload.code)
        .bind(&payload.name)
        .bind(&payload.short_name)
        .bind(&payload.description)
        .bind(payload.order_level)
        .bind(payload.duration_years)
        .bind(&payload.age_range)
        .bind(payload.is_standard.unwrap_or(false))
        .fetch_one(&self.db_pool)
        .await?;

        Ok(stage)
    }

    /// 更新学段
    pub async fn update_education_stage(
        &self,
        id: Uuid,
        payload: UpdateEducationStageRequest,
    ) -> Result<EducationStage> {
        let stage = sqlx::query_as::<_, EducationStage>(
            r#"
            UPDATE public.education_stages 
            SET 
                name = COALESCE($2, name),
                short_name = COALESCE($3, short_name),
                description = COALESCE($4, description),
                order_level = COALESCE($5, order_level),
                duration_years = COALESCE($6, duration_years),
                age_range = COALESCE($7, age_range),
                is_standard = COALESCE($8, is_standard),
                is_active = COALESCE($9, is_active),
                updated_at = NOW()
            WHERE id = $1
            RETURNING *
            "#,
        )
        .bind(id)
        .bind(&payload.name)
        .bind(&payload.short_name)
        .bind(&payload.description)
        .bind(payload.order_level)
        .bind(payload.duration_years)
        .bind(&payload.age_range)
        .bind(payload.is_standard)
        .bind(payload.is_active)
        .fetch_one(&self.db_pool)
        .await?;

        Ok(stage)
    }

    /// 删除学段（软删除，设置为不活跃）
    pub async fn delete_education_stage(&self, id: Uuid) -> Result<()> {
        // 检查是否有关联数据（年级、题目或试卷）
        let usage_check = sqlx::query(
            r#"
            SELECT 
                (SELECT COUNT(*) FROM public.grade_levels WHERE education_stage_code = (SELECT code FROM public.education_stages WHERE id = $1)) as grade_count,
                COALESCE((
                    SELECT COUNT(DISTINCT qb.id)
                    FROM public.education_stages es
                    LEFT JOIN public.grade_levels gl ON es.code = gl.education_stage_code
                    LEFT JOIN public.question_bank qb ON gl.code::integer = qb.grade_level
                    WHERE es.id = $1
                ), 0) as question_count,
                COALESCE((
                    SELECT COUNT(DISTINCT ep.id)
                    FROM public.education_stages es
                    LEFT JOIN public.grade_levels gl ON es.code = gl.education_stage_code
                    LEFT JOIN public.exam_papers ep ON gl.code::integer = ep.grade_level
                    WHERE es.id = $1
                ), 0) as paper_count
            "#
        )
        .bind(id)
        .fetch_one(&self.db_pool)
        .await?;

        let grade_count: i64 = usage_check.get("grade_count");
        let question_count: i64 = usage_check.get("question_count");
        let paper_count: i64 = usage_check.get("paper_count");

        if grade_count > 0 || question_count > 0 || paper_count > 0 {
            bail!(format!(
                "仍有 {} 年级、 {} 试题和 {} 试卷试卷关联在该学段",
                grade_count, question_count, paper_count
            ))
        }

        sqlx::query(
            "UPDATE public.education_stages SET is_active = false, updated_at = NOW() WHERE id = $1",
        )
        .bind(id)
        .execute(&self.db_pool)
        .await?;

        Ok(())
    }

    /// 获取学段统计信息
    pub async fn get_education_stage_statistics(
        &self,
    ) -> Result<EducationStageStatistics> {
        // 基本统计
        let basic_stats = sqlx::query(
            r#"
            SELECT 
                COUNT(*) as total_stages,
                COUNT(CASE WHEN is_active = true THEN 1 END) as active_stages,
                COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_stages,
                COUNT(CASE WHEN is_standard = true THEN 1 END) as standard_stages,
                COUNT(CASE WHEN is_standard = false THEN 1 END) as custom_stages
            FROM public.education_stages
            "#,
        )
        .fetch_one(&self.db_pool)
        .await?;

        // 使用统计
        let usage_stats = sqlx::query(
            r#"
            SELECT 
                es.id as stage_id,
                es.name as stage_name,
                COALESCE(g.grade_count, 0) as grade_count,
                COALESCE(q.question_count, 0) as question_count,
                COALESCE(p.paper_count, 0) as paper_count
            FROM public.education_stages es
            LEFT JOIN (
                SELECT education_stage_code, COUNT(*) as grade_count
                FROM public.grade_levels
                WHERE education_stage_code IS NOT NULL
                GROUP BY education_stage_code
            ) g ON es.code = g.education_stage_code
            LEFT JOIN (
                SELECT 
                    es2.code, 
                    COUNT(DISTINCT qb.id) as question_count
                FROM public.education_stages es2
                LEFT JOIN public.grade_levels gl ON es2.code = gl.education_stage_code
                LEFT JOIN public.question_bank qb ON gl.code::integer = qb.grade_level
                WHERE qb.status != 'archived'
                GROUP BY es2.code
            ) q ON es.code = q.code
            LEFT JOIN (
                SELECT 
                    es2.code, 
                    COUNT(DISTINCT ep.id) as paper_count
                FROM public.education_stages es2
                LEFT JOIN public.grade_levels gl ON es2.code = gl.education_stage_code
                LEFT JOIN public.exam_papers ep ON gl.code::integer = ep.grade_level
                WHERE ep.status != 'archived'
                GROUP BY es2.code
            ) p ON es.code = p.code
            WHERE es.is_active = true
            ORDER BY es.order_level
            "#,
        )
        .fetch_all(&self.db_pool)
        .await?;

        let usage_stats: Vec<EducationStageUsageStats> = usage_stats
            .into_iter()
            .map(|row| EducationStageUsageStats {
                stage_id: row.get("stage_id"),
                stage_name: row.get("stage_name"),
                grade_count: row.get("grade_count"),
                question_count: row.get("question_count"),
                paper_count: row.get("paper_count"),
            })
            .collect();

        Ok(EducationStageStatistics {
            total_stages: basic_stats.get("total_stages"),
            active_stages: basic_stats.get("active_stages"),
            inactive_stages: basic_stats.get("inactive_stages"),
            standard_stages: basic_stats.get("standard_stages"),
            custom_stages: basic_stats.get("custom_stages"),
            usage_stats,
        })
    }

    /// 批量更新学段排序
    pub async fn update_education_stage_orders(
        &self,
        orders: Vec<(Uuid, i32)>,
    ) -> Result<()> {
        let mut tx = self.db_pool.begin().await?;

        for (stage_id, order_level) in orders {
            sqlx::query(
                "UPDATE public.education_stages SET order_level = $2, updated_at = NOW() WHERE id = $1",
            )
            .bind(stage_id)
            .bind(order_level)
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(())
    }

    /// 检查学段代码是否可用
    pub async fn is_code_available(
        &self,
        code: &str,
        exclude_id: Option<Uuid>,
    ) -> Result<bool> {
        let query = match exclude_id {
            Some(id) => sqlx::query(
                "SELECT COUNT(*) as count FROM public.education_stages WHERE code = $1 AND id != $2",
            )
            .bind(code)
            .bind(id),
            None => sqlx::query("SELECT COUNT(*) as count FROM public.education_stages WHERE code = $1")
                .bind(code),
        };

        let row = query.fetch_one(&self.db_pool).await?;
        let count: i64 = row.get("count");
        Ok(count == 0)
    }

    /// 根据学段编码获取关联年级列表
    pub async fn get_grades_by_education_stage_code(
        &self,
        stage_code: &str,
    ) -> Result<Vec<GradeLevel>, AppError> {
        let grades = sqlx::query_as::<_, GradeLevel>(
            "SELECT * FROM public.grade_levels WHERE education_stage_code = $1 ORDER BY order_level"
        )
        .bind(stage_code)
        .fetch_all(&self.db_pool)
        .await?;

        Ok(grades)
    }
}

/// 学段查询参数（用于分页接口）
#[derive(Debug, Deserialize)]
pub struct PageEducationStageParams {
    pub page: i32,
    pub page_size: i32,
    pub name_or_code_ilike: Option<String>,
    pub is_active: Option<bool>,
    pub is_standard: Option<bool>,
    pub order_by: Option<String>, // 排序字段：name, code, order_level, created_at
    pub order_direction: Option<String>, // 排序方向：asc, desc
}
