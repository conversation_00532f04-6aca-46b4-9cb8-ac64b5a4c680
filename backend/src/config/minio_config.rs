use serde::{Deserialize, Serialize};
use std::env;
use anyhow::Result;

/// MinIO 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MinioConfig {
    /// MinIO 服务端点 (不包含协议)
    pub endpoint: String,
    
    /// 访问密钥
    pub access_key: String,
    
    /// 秘密密钥
    pub secret_key: String,
    
    /// 是否使用 HTTPS
    pub use_ssl: bool,
    
    /// 默认存储桶名称
    pub default_bucket: String,
    
    /// 区域 (可选)
    pub region: Option<String>,
    
    /// 最大文件大小 (字节)
    pub max_file_size: u64,
    
    /// 允许的文件类型
    pub allowed_mime_types: Vec<String>,
}

impl Default for MinioConfig {
    fn default() -> Self {
        Self {
            endpoint: "localhost:9000".to_string(),
            access_key: "minioadmin".to_string(),
            secret_key: "minioadmin".to_string(),
            use_ssl: false,
            default_bucket: "deep-mate".to_string(),
            region: None,
            max_file_size: 100 * 1024 * 1024, // 100MB
            allowed_mime_types: vec![
                "image/jpeg".to_string(),
                "image/png".to_string(),
                "image/gif".to_string(),
                "image/webp".to_string(),
                "application/pdf".to_string(),
                "application/msword".to_string(),
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document".to_string(),
                "application/vnd.ms-excel".to_string(),
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".to_string(),
                "text/plain".to_string(),
            ],
        }
    }
}

impl MinioConfig {
    /// 从环境变量加载配置
    pub fn from_env() -> Result<Self> {
        let mut config = Self::default();
        
        // 从环境变量覆盖默认配置
        if let Ok(endpoint) = env::var("MINIO_ENDPOINT") {
            config.endpoint = endpoint;
        }
        
        if let Ok(access_key) = env::var("MINIO_ACCESS_KEY") {
            config.access_key = access_key;
        }
        
        if let Ok(secret_key) = env::var("MINIO_SECRET_KEY") {
            config.secret_key = secret_key;
        }
        
        if let Ok(use_ssl) = env::var("MINIO_USE_SSL") {
            config.use_ssl = use_ssl.parse().unwrap_or(false);
        }
        
        if let Ok(bucket) = env::var("MINIO_DEFAULT_BUCKET") {
            config.default_bucket = bucket;
        }
        
        if let Ok(region) = env::var("MINIO_REGION") {
            config.region = Some(region);
        }
        
        if let Ok(max_size) = env::var("MINIO_MAX_FILE_SIZE") {
            config.max_file_size = max_size.parse().unwrap_or(100 * 1024 * 1024);
        }
        
        if let Ok(mime_types) = env::var("MINIO_ALLOWED_MIME_TYPES") {
            config.allowed_mime_types = mime_types
                .split(',')
                .map(|s| s.trim().to_string())
                .collect();
        }
        
        Ok(config)
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        if self.endpoint.is_empty() {
            return Err(anyhow::anyhow!("MinIO endpoint cannot be empty"));
        }
        
        if self.access_key.is_empty() {
            return Err(anyhow::anyhow!("MinIO access key cannot be empty"));
        }
        
        if self.secret_key.is_empty() {
            return Err(anyhow::anyhow!("MinIO secret key cannot be empty"));
        }
        
        if self.default_bucket.is_empty() {
            return Err(anyhow::anyhow!("Default bucket name cannot be empty"));
        }
        
        if self.max_file_size == 0 {
            return Err(anyhow::anyhow!("Max file size must be greater than 0"));
        }
        
        Ok(())
    }
    
    /// 检查文件类型是否允许
    pub fn is_mime_type_allowed(&self, mime_type: &str) -> bool {
        self.allowed_mime_types.contains(&mime_type.to_string())
    }
    
    /// 检查文件大小是否允许
    pub fn is_file_size_allowed(&self, size: u64) -> bool {
        size <= self.max_file_size
    }
    
    /// 获取完整的端点 URL
    pub fn get_endpoint_url(&self) -> String {
        let protocol = if self.use_ssl { "https" } else { "http" };
        format!("{}://{}", protocol, self.endpoint)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;
    
    #[test]
    fn test_default_config() {
        let config = MinioConfig::default();
        assert_eq!(config.endpoint, "localhost:9000");
        assert_eq!(config.access_key, "minioadmin");
        assert_eq!(config.secret_key, "minioadmin");
        assert!(!config.use_ssl);
        assert_eq!(config.default_bucket, "deep-mate");
        assert!(config.max_file_size > 0);
        assert!(!config.allowed_mime_types.is_empty());
    }
    
    #[test]
    fn test_config_validation() {
        let config = MinioConfig::default();
        assert!(config.validate().is_ok());
        
        let mut invalid_config = config.clone();
        invalid_config.endpoint = "".to_string();
        assert!(invalid_config.validate().is_err());
    }
    
    #[test]
    fn test_mime_type_checking() {
        let config = MinioConfig::default();
        assert!(config.is_mime_type_allowed("image/jpeg"));
        assert!(config.is_mime_type_allowed("application/pdf"));
        assert!(!config.is_mime_type_allowed("application/unknown"));
    }
    
    #[test]
    fn test_file_size_checking() {
        let config = MinioConfig::default();
        assert!(config.is_file_size_allowed(1024)); // 1KB
        assert!(config.is_file_size_allowed(50 * 1024 * 1024)); // 50MB
        assert!(!config.is_file_size_allowed(200 * 1024 * 1024)); // 200MB
    }
    
    #[test]
    fn test_endpoint_url() {
        let mut config = MinioConfig::default();
        assert_eq!(config.get_endpoint_url(), "http://localhost:9000");
        
        config.use_ssl = true;
        assert_eq!(config.get_endpoint_url(), "https://localhost:9000");
    }
    
    #[test]
    fn test_config_from_env() {
        // 设置环境变量
        env::set_var("MINIO_ENDPOINT", "test.example.com:9000");
        env::set_var("MINIO_ACCESS_KEY", "testkey");
        env::set_var("MINIO_SECRET_KEY", "testsecret");
        env::set_var("MINIO_USE_SSL", "true");
        env::set_var("MINIO_DEFAULT_BUCKET", "test-bucket");
        
        let config = MinioConfig::from_env().unwrap();
        assert_eq!(config.endpoint, "test.example.com:9000");
        assert_eq!(config.access_key, "testkey");
        assert_eq!(config.secret_key, "testsecret");
        assert!(config.use_ssl);
        assert_eq!(config.default_bucket, "test-bucket");
        
        // 清理环境变量
        env::remove_var("MINIO_ENDPOINT");
        env::remove_var("MINIO_ACCESS_KEY");
        env::remove_var("MINIO_SECRET_KEY");
        env::remove_var("MINIO_USE_SSL");
        env::remove_var("MINIO_DEFAULT_BUCKET");
    }
}