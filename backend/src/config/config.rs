use serde::{Deserialize, Serialize};
use crate::config::grading_config::GradingConfig;
use crate::config::{MinioConfig, SchemaConfig};

/// 统一配置结构体（支持序列化/反序列化）
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(default)] // 允许字段有默认值
pub struct GlobalConfig {
    minio_config: MinioConfig,
    grading_config:GradingConfig,
    schema_config:SchemaConfig,
}
impl Default for GlobalConfig {
    fn default() -> Self {
        Self{
            minio_config: MinioConfig::default(),
            grading_config: GradingConfig::default(),
            schema_config: SchemaConfig::default(),
        }
    }
}

 impl GlobalConfig {
     pub fn new(minio_config: MinioConfig,grading_config: GradingConfig,schema_config: SchemaConfig) -> Self {
          Self {
             minio_config,
             grading_config,
             schema_config,
         }
     }

     // ---- 字段访问器 ----
     pub fn minio_config(&self) -> &MinioConfig {
         &self.minio_config
     }

     pub fn grading_config(&self) -> &GradingConfig {
         &self.grading_config
     }

     pub fn schema_config(&self) -> &SchemaConfig {
         &self.schema_config
     }


 }