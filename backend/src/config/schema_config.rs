use serde::{Deserialize, Serialize};
use std::env;

/// Schema 管理配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SchemaConfig {
    /// 是否在启动时自动创建缺失的 schema
    pub auto_create_missing_schemas: bool,
    
    /// 租户模板文件路径
    pub tenant_template_path: String,
    
    /// Schema 预热策略
    pub warmup_strategy: WarmupStrategy,
    
    /// 是否启用 schema 缓存
    pub enable_schema_cache: bool,
    
    /// Schema 检查间隔（秒）
    pub schema_check_interval: u64,
    
    /// 最大并发 schema 创建数
    pub max_concurrent_schema_creation: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WarmupStrategy {
    /// 不进行预热
    None,
    /// 预热所有已注册的租户 schema
    All,
    /// 仅预热活跃的租户 schema
    ActiveOnly,
    /// 懒加载：仅在需要时创建
    Lazy,
}

impl Default for SchemaConfig {
    fn default() -> Self {
        Self {
            auto_create_missing_schemas: true,
            tenant_template_path: "tenants/template/init_tenant_schema.migrations_temp".to_string(),
            warmup_strategy: WarmupStrategy::All,
            enable_schema_cache: true,
            schema_check_interval: 300, // 5 分钟
            max_concurrent_schema_creation: 5,
        }
    }
}

impl SchemaConfig {
    /// 从环境变量加载配置
    pub fn from_env() -> Self {
        let mut config = Self::default();
        
        // 从环境变量覆盖默认配置
        if let Ok(auto_create) = env::var("SCHEMA_AUTO_CREATE") {
            config.auto_create_missing_schemas = auto_create.parse().unwrap_or(true);
        }
        
        if let Ok(template_path) = env::var("TENANT_TEMPLATE_PATH") {
            config.tenant_template_path = template_path;
        }
        
        if let Ok(warmup_strategy) = env::var("SCHEMA_WARMUP_STRATEGY") {
            config.warmup_strategy = match warmup_strategy.to_lowercase().as_str() {
                "none" => WarmupStrategy::None,
                "all" => WarmupStrategy::All,
                "active" => WarmupStrategy::ActiveOnly,
                "lazy" => WarmupStrategy::Lazy,
                _ => WarmupStrategy::All,
            };
        }
        
        if let Ok(enable_cache) = env::var("SCHEMA_ENABLE_CACHE") {
            config.enable_schema_cache = enable_cache.parse().unwrap_or(true);
        }
        
        if let Ok(check_interval) = env::var("SCHEMA_CHECK_INTERVAL") {
            config.schema_check_interval = check_interval.parse().unwrap_or(300);
        }
        
        if let Ok(max_concurrent) = env::var("SCHEMA_MAX_CONCURRENT_CREATION") {
            config.max_concurrent_schema_creation = max_concurrent.parse().unwrap_or(5);
        }
        
        config
    }
    
    /// 获取开发环境配置
    pub fn development() -> Self {
        Self {
            auto_create_missing_schemas: true,
            tenant_template_path: "tenants/template/init_tenant_schema.migrations_temp".to_string(),
            warmup_strategy: WarmupStrategy::Lazy, // 开发环境使用懒加载
            enable_schema_cache: true,
            schema_check_interval: 60, // 1 分钟检查一次
            max_concurrent_schema_creation: 3,
        }
    }
    
    /// 获取生产环境配置
    pub fn production() -> Self {
        Self {
            auto_create_missing_schemas: false, // 生产环境不自动创建
            tenant_template_path: "/app/templates/init_tenant_schema.migrations_temp".to_string(),
            warmup_strategy: WarmupStrategy::All, // 预热所有 schema
            enable_schema_cache: true,
            schema_check_interval: 600, // 10 分钟检查一次
            max_concurrent_schema_creation: 10,
        }
    }
    
    /// 获取测试环境配置
    pub fn test() -> Self {
        Self {
            auto_create_missing_schemas: true,
            tenant_template_path: "tests/fixtures/test_tenant_schema.migrations_temp".to_string(),
            warmup_strategy: WarmupStrategy::None, // 测试环境不预热
            enable_schema_cache: false, // 测试环境不缓存
            schema_check_interval: 30,
            max_concurrent_schema_creation: 1,
        }
    }
}

/// 环境类型
#[derive(Debug, Clone, PartialEq)]
pub enum Environment {
    Development,
    Production,
    Test,
}

impl Environment {
    /// 从环境变量获取当前环境
    pub fn from_env() -> Self {
        match env::var("RUST_ENV")
            .or_else(|_| env::var("ENVIRONMENT"))
            .unwrap_or_else(|_| "development".to_string())
            .to_lowercase()
            .as_str()
        {
            "production" | "prod" => Environment::Production,
            "test" | "testing" => Environment::Test,
            _ => Environment::Development,
        }
    }
}

/// 根据环境获取 Schema 配置
pub fn get_schema_config() -> SchemaConfig {
    let env = Environment::from_env();
    
    match env {
        Environment::Development => {
            let mut config = SchemaConfig::development();
            // 允许环境变量覆盖
            if env::var("SCHEMA_AUTO_CREATE").is_ok() {
                config = SchemaConfig::from_env();
            }
            config
        }
        Environment::Production => SchemaConfig::production(),
        Environment::Test => SchemaConfig::test(),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;
    
    #[test]
    fn test_default_config() {
        let config = SchemaConfig::default();
        assert!(config.auto_create_missing_schemas);
        assert_eq!(config.tenant_template_path, "tenants/template/init_tenant_schema.migrations_temp");
        assert!(matches!(config.warmup_strategy, WarmupStrategy::All));
    }
    
    #[test]
    fn test_environment_detection() {
        // 测试默认环境
        let env = Environment::from_env();
        assert_eq!(env, Environment::Development);
        
        // 测试设置环境变量
        env::set_var("RUST_ENV", "production");
        let env = Environment::from_env();
        assert_eq!(env, Environment::Production);
        
        env::set_var("RUST_ENV", "test");
        let env = Environment::from_env();
        assert_eq!(env, Environment::Test);
        
        // 清理环境变量
        env::remove_var("RUST_ENV");
    }
    
    #[test]
    fn test_config_from_env() {
        // 设置环境变量
        env::set_var("SCHEMA_AUTO_CREATE", "false");
        env::set_var("TENANT_TEMPLATE_PATH", "/custom/path/template.migrations_temp");
        env::set_var("SCHEMA_WARMUP_STRATEGY", "lazy");
        
        let config = SchemaConfig::from_env();
        assert!(!config.auto_create_missing_schemas);
        assert_eq!(config.tenant_template_path, "/custom/path/template.migrations_temp");
        assert!(matches!(config.warmup_strategy, WarmupStrategy::Lazy));
        
        // 清理环境变量
        env::remove_var("SCHEMA_AUTO_CREATE");
        env::remove_var("TENANT_TEMPLATE_PATH");
        env::remove_var("SCHEMA_WARMUP_STRATEGY");
    }
}
