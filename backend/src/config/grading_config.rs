use serde::{Deserialize, Serialize};
use std::env;
use anyhow::Result;

/// 阅卷服务参数配置
#[derive(Debug, C<PERSON>, Serialize, Deserialize)]
pub struct GradingConfig {
    /// Grading 服务端点 (不包含协议)
    pub endpoint: String,

    /// 是否使用 HTTPS
    pub use_ssl: bool,
}

impl Default for GradingConfig {
    fn default() -> Self {
        Self {
            endpoint: "localhost:3000".to_string(),
            use_ssl: false,
        }
    }
}

impl GradingConfig {
    /// 从环境变量加载配置
    pub fn from_env()->Result<Self>{
        let mut config = Self::default();

        // 从环境变量覆盖默认配置
        if let Ok(endpoint) = env::var("GRADING_SERVICE_ENDPOINT") {
            config.endpoint = endpoint;
        }

        Ok(config)
    }

    /// 验证配置
    pub fn validate(&self)->Result<()>{
        if self.endpoint.is_empty() {
            return Err(anyhow::anyhow!("Grading endpoint cannot be empty"));
        }

        Ok(())
    }

    /// 获取完整的端点 URL
    pub fn get_endpoint_url(&self)->String{
        let protocol = if self.use_ssl{ "https" } else { "http" };
        format!("{}://{}",protocol,self.endpoint)
    }
}

#[cfg(test)]
mod tests{
    use std::env;
    use crate::config::grading_config::GradingConfig;

    #[test]
    fn test_default_config() {
        let config = GradingConfig::default();
        assert_eq!(config.endpoint, "localhost:3000");
        assert!(!config.use_ssl);
    }

    #[test]
    fn test_config_validation() {
        let config = GradingConfig::default();
        assert!(config.validate().is_ok());

        let mut invalid_config = config.clone();
        invalid_config.endpoint = "".to_string();
        assert!(invalid_config.validate().is_err());
    }

    #[test]
    fn test_endpoint_url() {
        let mut config = GradingConfig::default();
        assert_eq!(config.get_endpoint_url(), "http://localhost:3000");

        config.use_ssl = true;
        assert_eq!(config.get_endpoint_url(), "https://localhost:3000");
    }
    #[test]
    fn test_config_from_env() {
        // 设置环境变量
        env::set_var("GRADING_SERVICE_ENDPOINT", "localhost:3000");
        env::set_var("GRADING_SERVICE_USE_SSL", "true");

        let config = GradingConfig::from_env().unwrap();
        assert_eq!(config.endpoint, "localhost:4000");
        assert!(config.use_ssl);

        // 清理环境变量
        env::remove_var("GRADING_SERVICE_ENDPOINT");
        env::remove_var("GRADING_SERVICE_USE_SSL");
    }

}
