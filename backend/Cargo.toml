[package]
name = "backend"
version = "6.0.0"
edition = "2021"

[lib]
name = "backend"
path = "src/lib.rs"

[dependencies]
tokio = { version = "1", features = ["full"] }
axum = { version = "0.8", features = ["ws", "multipart", "macros"] }
tower-http = { version = "0.6", features = ["cors"] }
sqlx = { version = "0.8", features = ["postgres", "runtime-tokio-rustls", "uuid", "chrono", "json", "bigdecimal", "ipnetwork"] }
dotenvy = "0.15"
uuid = { version = "1.17", features = ["v4","serde"] }
anyhow = "1"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
argon2 = "0.5"
rand_core = "0.9"
jsonwebtoken = "9"
chrono = { version = "0.4", features = ["serde"] }            # ✅ 开启 serde 特性
time = "0.3"
regex = "1"
once_cell = "1.21"
thiserror = "2.0"
tracing = "0.1"
tracing-subscriber = "0.3"
async-trait = "0.1"
rand = "0.9"
tower = "0.5.2"
ipnetwork = "0.20"
# MinIO and file handling
minio = "0.3"
bytes = "1.10"
mime = "0.3"
mime_guess = "2.0"
urlencoding = "2.1"
zip = "4.3.0"
tempfile = "3.10"
walkdir = "2.5"
reqwest = { version = "0.11", features = ["json", "stream"] }
tokio-stream = "0.1.17"  # 启用 JSON 和流式传输
# Casbin RBAC system
casbin = { version = "2.10", default-features = false, features = ["runtime-tokio", "cached","logging", "incremental"] }
dashmap = "5.5"  # For thread-safe caching in multi-tenant enforcer
clap = { version = "4.0", optional = true }

[dev-dependencies]
dotenvy = "0.15"
tower = "0.5.2"
reqwest = { version = "0.12", features = ["json", "cookies"] }
tokio-test = "0.4"

[features]
default = ["cli"]
cli = ["clap"]
