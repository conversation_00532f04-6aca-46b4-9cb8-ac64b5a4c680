warning: unused imports: `Environment`, `SchemaConfig`, and `WarmupStrategy`
 --> src/config/mod.rs:4:25
  |
4 | pub use schema_config::{SchemaConfig, WarmupStrategy, Environment, get_schema_config};
  |                         ^^^^^^^^^^^^  ^^^^^^^^^^^^^^  ^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `str::FromStr`
 --> src/controller/administrative_classes/administrative_classes_controller.rs:3:5
  |
3 |     str::FromStr,
  |     ^^^^^^^^^^^^

warning: unused import: `HeaderValue`
 --> src/controller/administrative_classes/administrative_classes_controller.rs:8:23
  |
8 |     http::{HeaderMap, HeaderValue},
  |                       ^^^^^^^^^^^

warning: unused import: `axum::response::IntoResponse`
  --> src/controller/auth/auth_controller.rs:11:5
   |
11 | use axum::response::IntoResponse;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::middleware::auth_middleware::AuthExtractor`
  --> src/controller/auth/auth_controller.rs:13:5
   |
13 | use crate::middleware::auth_middleware::AuthExtractor;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `ApiResponse` and `responses`
  --> src/controller/auth/auth_controller.rs:14:34
   |
14 | use crate::utils::api_response::{responses, ApiResponse};
   |                                  ^^^^^^^^^  ^^^^^^^^^^^

warning: unused import: `crate::model::base::PageResult`
 --> src/controller/education_stage/education_stage_controller.rs:1:5
  |
1 | use crate::model::base::PageResult;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::model::education_stage::EducationStage`
 --> src/controller/education_stage/education_stage_controller.rs:2:5
  |
2 | use crate::model::education_stage::EducationStage;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `EducationStageStatistics`, `EducationStageSummary`, and `EducationStageVO`
 --> src/controller/education_stage/education_stage_controller.rs:4:61
  |
4 |     CreateEducationStageRequest, EducationStageQueryParams, EducationStageStatistics,
  |                                                             ^^^^^^^^^^^^^^^^^^^^^^^^
5 |     EducationStageSummary, EducationStageVO, UpdateEducationStageRequest,
  |     ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^

warning: unused import: `education_stage_controller::*`
 --> src/controller/education_stage/mod.rs:3:9
  |
3 | pub use education_stage_controller::*;
  |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `CheckCodeResponse`, `GradeLevelStatistics`, `GradeLevelSummary`, `GradeLevelVO`, and `GradeLevel`
 --> src/controller/grade/grade_controller.rs:2:5
  |
2 |     GradeLevel, GradeLevelVO, GradeLevelSummary, CreateGradeLevel, UpdateGradeLevel, 
  |     ^^^^^^^^^^  ^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^
3 |     GradeLevelQueryParams, GradeLevelStatistics, CheckCodeResponse
  |                            ^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^

warning: unused import: `crate::model::base::PageResult`
 --> src/controller/grade/grade_controller.rs:5:5
  |
5 | use crate::model::base::PageResult;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `put`
  --> src/controller/grade/grade_controller.rs:11:26
   |
11 |     routing::{get, post, put, patch},
   |                          ^^^

warning: unused import: `Extension`
 --> src/controller/permission/menu_controller.rs:8:5
  |
8 |     Extension,
  |     ^^^^^^^^^

warning: unused import: `uuid::Uuid`
  --> src/controller/permission/menu_controller.rs:11:5
   |
11 | use uuid::Uuid;
   |     ^^^^^^^^^^

warning: unused import: `warn`
  --> src/controller/permission/menu_controller.rs:12:21
   |
12 | use tracing::{info, warn, error, debug};
   |                     ^^^^

warning: unused import: `AuthContext`
  --> src/controller/permission/menu_controller.rs:19:42
   |
19 | use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
   |                                          ^^^^^^^^^^^

warning: unused imports: `UserContextExtractor` and `UserContext`
  --> src/controller/permission/menu_controller.rs:20:48
   |
20 | use crate::middleware::permission_middleware::{UserContext, UserContextExtractor};
   |                                                ^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
  --> src/controller/permission/menu_controller.rs:22:5
   |
22 | use crate::utils::error::AppError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `AuthContext` and `ErrorResponse`
  --> src/controller/permission/casbin_policy_controller.rs:11:50
   |
11 |     middleware::auth_middleware::{AuthExtractor, AuthContext},
   |                                                  ^^^^^^^^^^^
...
18 |     utils::api_response::{ApiResponse, ErrorResponse, PaginatedApiResponse},
   |                                        ^^^^^^^^^^^^^

warning: unused imports: `delete` and `post`
   --> src/controller/permission/casbin_policy_controller.rs:578:30
    |
578 |     use axum::routing::{get, post, delete};
    |                              ^^^^  ^^^^^^

warning: unused import: `menu_controller::MenuPermissionController`
 --> src/controller/permission/mod.rs:4:9
  |
4 | pub use menu_controller::MenuPermissionController;
  |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `casbin_policy_controller::CasbinPolicyController`
 --> src/controller/permission/mod.rs:5:9
  |
5 | pub use casbin_policy_controller::CasbinPolicyController;
  |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `subject_controller::*`
 --> src/controller/subject/mod.rs:2:9
  |
2 | pub use subject_controller::*;
  |         ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::middleware::tenant_middleware::TenantExtractor`
 --> src/controller/teacher/teacher_controller.rs:2:5
  |
2 | use crate::middleware::tenant_middleware::TenantExtractor;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `CreateTeacher`, `TeacherQueryParams`, and `UpdateTeacher`
 --> src/controller/teacher/teacher_controller.rs:3:29
  |
3 | use crate::model::teacher::{CreateTeacher, TeacherQueryParams, UpdateTeacher};
  |                             ^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^

warning: unused import: `crate::service::teacher::teacher_service::TeacherService`
 --> src/controller/teacher/teacher_controller.rs:5:5
  |
5 | use crate::service::teacher::teacher_service::TeacherService;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
 --> src/controller/teacher/teacher_controller.rs:7:5
  |
7 | use crate::utils::error::AppError;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `Query`, `get`, and `patch`
  --> src/controller/teacher/teacher_controller.rs:11:21
   |
11 |     extract::{Path, Query, State},
   |                     ^^^^^
12 |     response::Json,
13 |     routing::{get, patch},
   |               ^^^  ^^^^^

warning: unused import: `uuid::Uuid`
  --> src/controller/teacher/teacher_controller.rs:17:5
   |
17 | use uuid::Uuid;
   |     ^^^^^^^^^^

warning: unused import: `crate::service::auth::auth_service::AuthService`
  --> src/controller/user/identity_controller.rs:15:5
   |
15 | use crate::service::auth::auth_service::AuthService;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `HashSet`
 --> src/controller/subject_groups/subject_groups_controller.rs:1:33
  |
1 | use std::collections::{HashMap, HashSet};
  |                                 ^^^^^^^

warning: unused import: `get`
 --> src/controller/subject_groups/subject_groups_controller.rs:5:15
  |
5 |     routing::{get, post},
  |               ^^^

warning: unused import: `uuid`
 --> src/controller/subject_groups/subject_groups_controller.rs:8:12
  |
8 | use uuid::{uuid, Uuid};
  |            ^^^^

warning: unused import: `str::FromStr`
 --> src/controller/teaching_classes/teaching_classes_controller.rs:3:5
  |
3 |     str::FromStr,
  |     ^^^^^^^^^^^^

warning: unused import: `crate::model::PageParams`
 --> src/model/education_stage/education_stage.rs:5:5
  |
5 | use crate::model::PageParams;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::model::PageParams`
 --> src/model/grade/grade.rs:5:5
  |
5 | use crate::model::PageParams;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `question::*`
 --> src/model/question/mod.rs:3:9
  |
3 | pub use question::*;
  |         ^^^^^^^^^^^

warning: unused import: `crate::model::PageParams`
 --> src/model/subject/subject.rs:5:5
  |
5 | use crate::model::PageParams;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `NaiveDateTime`
 --> src/model/tenant/tenant.rs:1:24
  |
1 | use chrono::{DateTime, NaiveDateTime, Utc};
  |                        ^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/model/user/auth.rs:5:5
  |
5 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::model::UserIdentitySelectVO`
 --> src/model/user/auth.rs:6:5
  |
6 | use crate::model::UserIdentitySelectVO;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `PageResult`
  --> src/model/mod.rs:21:28
   |
21 | pub use base::{PageParams, PageResult};
   |                            ^^^^^^^^^^

warning: unused import: `grade::*`
  --> src/model/mod.rs:39:9
   |
39 | pub use grade::*;
   |         ^^^^^^^^

warning: unused import: `education_stage::*`
  --> src/model/mod.rs:42:9
   |
42 | pub use education_stage::*;
   |         ^^^^^^^^^^^^^^^^^^

warning: unused imports: `Pool` and `Postgres`
 --> src/service/homework/homework_service.rs:5:20
  |
5 | use sqlx::{PgPool, Pool, Postgres, Result};
  |                    ^^^^  ^^^^^^^^

warning: unused import: `anyhow::Result`
 --> src/service/tenant/tenant_data_service.rs:1:5
  |
1 | use anyhow::Result;
  |     ^^^^^^^^^^^^^^

warning: unused import: `PgPool`
 --> src/service/tenant/tenant_data_service.rs:2:12
  |
2 | use sqlx::{PgPool, Row};
  |            ^^^^^^

warning: unused import: `std::sync::Arc`
 --> src/service/tenant/tenant_data_service.rs:6:5
  |
6 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused imports: `HashMap` and `HashSet`
 --> src/service/tenant/member.rs:1:24
  |
1 | use std::collections::{HashMap, HashSet};
  |                        ^^^^^^^  ^^^^^^^

warning: unused import: `uuid::Uuid`
 --> src/service/tenant/member.rs:7:5
  |
7 | use uuid::Uuid;
  |     ^^^^^^^^^^

warning: unused import: `anyhow::Result`
  --> src/service/tenant/mod.rs:10:9
   |
10 |     use anyhow::Result;
   |         ^^^^^^^^^^^^^^

warning: unused import: `anyhow::Result`
  --> src/service/tenant/mod.rs:23:9
   |
23 |     use anyhow::Result;
   |         ^^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
 --> src/service/user/identity_service.rs:3:5
  |
3 | use crate::utils::error::AppError;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `warn`
 --> src/service/user/parent_service.rs:4:21
  |
4 | use tracing::{info, warn};
  |                     ^^^^

warning: unused import: `crate::model::auth::User`
 --> src/service/user/user_service.rs:1:5
  |
1 | use crate::model::auth::User;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `sqlx::postgres::PgRow`
 --> src/service/user/user_service.rs:7:5
  |
7 | use sqlx::postgres::PgRow;
  |     ^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `tower::builder`
 --> src/service/administrative_classes/administrative_classes_service.rs:4:5
  |
4 | use tower::builder;
  |     ^^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
 --> src/service/auth/auth_service.rs:5:5
  |
5 | use crate::utils::error::AppError;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `AnyhowResultExt`, `AppResult`, `SqlxResultExt`, and `errors`
 --> src/service/auth/auth_service.rs:6:35
  |
6 | use crate::utils::error_handler::{SqlxResultExt, AnyhowResultExt, errors, AppResult};
  |                                   ^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^  ^^^^^^  ^^^^^^^^^

warning: unused import: `DateTime`
 --> src/service/auth/auth_service.rs:7:14
  |
7 | use chrono::{DateTime, Duration, Utc};
  |              ^^^^^^^^

warning: unused import: `error`
  --> src/service/auth/auth_service.rs:13:15
   |
13 | use tracing::{error, info, warn};
   |               ^^^^^

warning: unused imports: `SubjectQueryParams` and `SubjectVO`
 --> src/service/education_stage/education_stage_service.rs:8:32
  |
8 | use crate::model::{PageParams, SubjectQueryParams, SubjectVO};
  |                                ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `CheckCodeResponse`
 --> src/service/grade/grade_service.rs:5:72
  |
5 |     GradeLevelQueryParams, GradeLevelStatistics, GradeLevelUsageStats, CheckCodeResponse
  |                                                                        ^^^^^^^^^^^^^^^^^

warning: unused imports: `SubjectQueryParams` and `SubjectVO`
  --> src/service/grade/grade_service.rs:11:32
   |
11 | use crate::model::{PageParams, SubjectQueryParams, SubjectVO};
   |                                ^^^^^^^^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `anyhow`
 --> src/service/permission/casbin_service.rs:5:22
  |
5 | use anyhow::{Result, anyhow};
  |                      ^^^^^^

warning: unused import: `anyhow`
 --> src/service/permission/role_sync_service.rs:2:22
  |
2 | use anyhow::{Result, anyhow};
  |                      ^^^^^^

warning: unused import: `warn`
 --> src/service/permission/role_sync_service.rs:5:21
  |
5 | use tracing::{info, warn, error, debug};
  |                     ^^^^

warning: unused import: `crate::model::role::permission::Permission`
  --> src/service/permission/role_sync_service.rs:15:5
   |
15 | use crate::model::role::permission::Permission;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
  --> src/service/permission/role_sync_service.rs:16:5
   |
16 | use crate::utils::error::AppError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `DataScope`
  --> src/service/permission/mod.rs:12:5
   |
12 |     DataScope,
   |     ^^^^^^^^^

warning: unused imports: `RolePermissionSyncService`, `SyncConfig`, and `SyncResult`
  --> src/service/permission/mod.rs:16:5
   |
16 |     RolePermissionSyncService,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^
17 |     SyncConfig,
   |     ^^^^^^^^^^
18 |     SyncResult,
   |     ^^^^^^^^^^

warning: unused imports: `CasbinRule` and `PostgresAdapter`
  --> src/service/permission/mod.rs:22:5
   |
22 |     PostgresAdapter,
   |     ^^^^^^^^^^^^^^^
23 |     CasbinRule,
   |     ^^^^^^^^^^

warning: unused import: `SystemRoles`
 --> src/service/role/role_service.rs:5:17
  |
5 |     Permission, SystemRoles
  |                 ^^^^^^^^^^^

warning: unused import: `warn`
  --> src/service/role/role_service.rs:12:28
   |
12 | use tracing::{info, error, warn};
   |                            ^^^^

warning: unused import: `role_service::*`
 --> src/service/role/mod.rs:4:9
  |
4 | pub use role_service::*;
  |         ^^^^^^^^^^^^^^^

warning: unused import: `tracing::debug`
  --> src/service/storage/minio_storage.rs:14:5
   |
14 | use tracing::debug;
   |     ^^^^^^^^^^^^^^

warning: unused import: `tracing::log::info`
  --> src/service/storage/minio_storage.rs:15:5
   |
15 | use tracing::log::info;
   |     ^^^^^^^^^^^^^^^^^^

warning: unused imports: `FileInfo`, `StorageResult`, and `UploadOptions as StorageUploadOptions`
 --> src/service/storage/mod.rs:4:43
  |
4 | pub use storage_service::{StorageService, StorageResult, FileInfo, UploadOptions as StorageUploadOptions};
  |                                           ^^^^^^^^^^^^^  ^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `serde::de::value`
  --> src/service/student/student_service.rs:10:5
   |
10 | use serde::de::value;
   |     ^^^^^^^^^^^^^^^^

warning: unused imports: `HashMap` and `HashSet`
 --> src/service/subject_groups/subject_groups_service.rs:1:24
  |
1 | use std::collections::{HashMap, HashSet};
  |                        ^^^^^^^  ^^^^^^^

warning: unused imports: `AdministrativeClassesStatistics`, `AdministrativeClasses`, and `CreateAdministrativeClassesParams`
 --> src/service/subject_groups/subject_groups_service.rs:8:9
  |
8 |         AdministrativeClasses, AdministrativeClassesStatistics, CreateAdministrativeClassesParams,
  |         ^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `Json` and `http::StatusCode`
 --> src/utils/error_handler.rs:6:5
  |
6 |     http::StatusCode,
  |     ^^^^^^^^^^^^^^^^
7 |     response::{IntoResponse, Response},
8 |     Json,
  |     ^^^^

warning: unused import: `serde_json::json`
  --> src/utils/error_handler.rs:10:5
   |
10 | use serde_json::json;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `Claims`
 --> src/middleware/auth_middleware.rs:4:52
  |
4 | pub(crate) use crate::utils::jwt::{validate_token, Claims};
  |                                                    ^^^^^^

warning: unused import: `HeaderMap`
 --> src/middleware/tenant_middleware.rs:5:12
  |
5 |     http::{HeaderMap, StatusCode},
  |            ^^^^^^^^^

warning: unused import: `Path`
 --> src/middleware/permission_middleware.rs:4:31
  |
4 |     extract::{Request, State, Path},
  |                               ^^^^

warning: unused import: `AuthExtractor`
  --> src/middleware/permission_middleware.rs:19:55
   |
19 | use crate::middleware::auth_middleware::{AuthContext, AuthExtractor};
   |                                                       ^^^^^^^^^^^^^

warning: unused import: `crate::utils::error::AppError`
  --> src/middleware/permission_middleware.rs:20:5
   |
20 | use crate::utils::error::AppError;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: use of deprecated struct `model::base::ApiResponse`: Please use utils::api_response::ApiResponse instead
  --> src/model/base.rs:49:12
   |
49 | pub struct ApiResponse<T> {
   |            ^^^^^^^^^^^
   |
   = note: `#[warn(deprecated)]` on by default

warning: use of deprecated struct `model::base::ApiResponse`: Please use utils::api_response::ApiResponse instead
  --> src/model/base.rs:49:12
   |
49 | pub struct ApiResponse<T> {
   |            ^^^^^^^^^^^

warning: unused variable: `admin_context`
   --> src/controller/administrative_classes/administrative_classes_controller.rs:191:19
    |
191 |     AuthExtractor(admin_context): AuthExtractor,
    |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `token`
   --> src/controller/auth/auth_controller.rs:174:9
    |
174 |     let token = extract_bearer_token(&headers).ok_or_else(|| {
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_token`

warning: unused variable: `menu_permission`
   --> src/controller/permission/menu_controller.rs:410:14
    |
410 |         Json(menu_permission): Json<MenuPermission>,
    |              ^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_menu_permission`

warning: value assigned to `param_index` is never read
   --> src/controller/permission/casbin_policy_controller.rs:161:13
    |
161 |             param_index += 1;
    |             ^^^^^^^^^^^
    |
    = help: maybe it is overwritten before being read?
    = note: `#[warn(unused_assignments)]` on by default

warning: unused variable: `admin_context`
   --> src/controller/teacher/teacher_controller.rs:145:19
    |
145 |     AuthExtractor(admin_context): AuthExtractor,
    |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `error_msg`
  --> src/controller/tenant/tenant_controller.rs:79:31
   |
79 |             let (status_code, error_msg, user_msg) = if e.to_string().contains("not found") {
   |                               ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_error_msg`

warning: unused variable: `error_msg`
   --> src/controller/tenant/tenant_controller.rs:111:31
    |
111 |             let (status_code, error_msg, user_msg) = if e.to_string().contains("not found") {
    |                               ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_error_msg`

warning: unused variable: `error_msg`
   --> src/controller/tenant/tenant_controller.rs:142:31
    |
142 |             let (status_code, error_msg, user_msg) = if e.to_string().contains("not found") {
    |                               ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_error_msg`

warning: unused variable: `admin_context`
  --> src/controller/user/user_controller.rs:74:19
   |
74 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
  --> src/controller/user/user_controller.rs:91:19
   |
91 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
  --> src/controller/subject_groups/subject_groups_controller.rs:31:19
   |
31 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
  --> src/controller/subject_groups/subject_groups_controller.rs:47:19
   |
47 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
  --> src/controller/subject_groups/subject_groups_controller.rs:63:19
   |
63 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
  --> src/controller/subject_groups/subject_groups_controller.rs:74:19
   |
74 |     AuthExtractor(admin_context): AuthExtractor,
   |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: variable `metadata` is assigned to, but never used
  --> src/controller/teaching_aids/teaching_aids_controller.rs:83:13
   |
83 |     let mut metadata: Option<ImportTeachingAidRequest> = None;
   |             ^^^^^^^^
   |
   = note: consider using `_metadata` instead

warning: value assigned to `metadata` is never read
  --> src/controller/teaching_aids/teaching_aids_controller.rs:91:13
   |
91 |             metadata = Some(serde_json::from_slice(&data).unwrap());
   |             ^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

warning: unused variable: `page_params`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:362:9
    |
362 |     let page_params = PageParams {
    |         ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_page_params`

warning: unused variable: `service`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:490:9
    |
490 |     let service = TeachingAidsService::new(state.db.clone());
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_service`

warning: unused variable: `state`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:534:11
    |
534 |     State(state): State<Arc<TeachingAidsRouteState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `service`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:607:9
    |
607 |     let service = TeachingAidsService::new(state.db.clone());
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_service`

warning: unused variable: `state`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:642:11
    |
642 |     State(state): State<Arc<TeachingAidsRouteState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `state`
   --> src/controller/teaching_aids/teaching_aids_controller.rs:660:11
    |
660 |     State(state): State<Arc<TeachingAidsRouteState>>,
    |           ^^^^^ help: if this is intentional, prefix it with an underscore: `_state`

warning: unused variable: `admin_context`
   --> src/controller/teaching_classes/teaching_classes_controller.rs:181:19
    |
181 |     AuthExtractor(admin_context): AuthExtractor,
    |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `admin_context`
   --> src/controller/teaching_classes/teaching_classes_controller.rs:195:19
    |
195 |     AuthExtractor(admin_context): AuthExtractor,
    |                   ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_admin_context`

warning: unused variable: `tenant_id`
  --> src/service/homework/homework_service.rs:25:9
   |
25 |         tenant_id: String,
   |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `user_id`
   --> src/service/user/identity_service.rs:203:9
    |
203 |         user_id: Uuid,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `user_id`
   --> src/service/user/identity_service.rs:314:9
    |
314 |         user_id: Uuid,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `session_id`
   --> src/service/user/identity_service.rs:315:9
    |
315 |         session_id: Uuid,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_session_id`

warning: unused variable: `request`
   --> src/service/user/identity_service.rs:316:9
    |
316 |         request: SwitchIdentityRequest,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_request`

warning: unused variable: `ip_address`
   --> src/service/user/identity_service.rs:317:9
    |
317 |         ip_address: Option<std::net::IpAddr>,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_ip_address`

warning: unused variable: `user_agent`
   --> src/service/user/identity_service.rs:318:9
    |
318 |         user_agent: Option<String>,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_agent`

warning: unused variable: `identity_id`
   --> src/service/user/identity_service.rs:400:41
    |
400 |     pub async fn verify_identity(&self, identity_id: Uuid, verified_by: Uuid) -> AuthResult<()> {
    |                                         ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_identity_id`

warning: unused variable: `verified_by`
   --> src/service/user/identity_service.rs:400:60
    |
400 |     pub async fn verify_identity(&self, identity_id: Uuid, verified_by: Uuid) -> AuthResult<()> {
    |                                                            ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_verified_by`

warning: unused variable: `assigned_by`
   --> src/service/user/user_service.rs:177:9
    |
177 |         assigned_by: Uuid,
    |         ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_assigned_by`

warning: value assigned to `password_hash` is never read
   --> src/service/user/user_service.rs:476:17
    |
476 |         let mut password_hash = String::new();
    |                 ^^^^^^^^^^^^^
    |
    = help: maybe it is overwritten before being read?

warning: value assigned to `salt` is never read
   --> src/service/user/user_service.rs:477:17
    |
477 |         let mut salt = String::new();
    |                 ^^^^
    |
    = help: maybe it is overwritten before being read?

warning: unused variable: `user_id`
   --> src/service/administrative_classes/administrative_classes_service.rs:172:9
    |
172 |         user_id: &Uuid,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `tenant_id`
  --> src/service/classes/classes_service.rs:39:9
   |
39 |         tenant_id: String,
   |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `tenant_id`
  --> src/service/classes/classes_service.rs:55:9
   |
55 |         tenant_id: String,
   |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `params`
  --> src/service/classes/classes_service.rs:56:9
   |
56 |         params: PageAdministrativeClassesParams,
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_params`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:210:9
    |
210 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `params`
   --> src/service/classes/classes_service.rs:211:9
    |
211 |         params: PageTeachingClassesParams,
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_params`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:366:9
    |
366 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `name`
   --> src/service/classes/classes_service.rs:370:13
    |
370 |             name,
    |             ^^^^ help: try ignoring the field: `name: _`

warning: unused variable: `code`
   --> src/service/classes/classes_service.rs:371:13
    |
371 |             code,
    |             ^^^^ help: try ignoring the field: `code: _`

warning: unused variable: `grade_level_code`
   --> src/service/classes/classes_service.rs:372:13
    |
372 |             grade_level_code,
    |             ^^^^^^^^^^^^^^^^ help: try ignoring the field: `grade_level_code: _`

warning: unused variable: `subject_code`
   --> src/service/classes/classes_service.rs:373:13
    |
373 |             subject_code,
    |             ^^^^^^^^^^^^ help: try ignoring the field: `subject_code: _`

warning: unused variable: `class_type`
   --> src/service/classes/classes_service.rs:374:13
    |
374 |             class_type,
    |             ^^^^^^^^^^ help: try ignoring the field: `class_type: _`

warning: unused variable: `school_year`
   --> src/service/classes/classes_service.rs:375:13
    |
375 |             school_year,
    |             ^^^^^^^^^^^ help: try ignoring the field: `school_year: _`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:474:9
    |
474 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `id`
   --> src/service/classes/classes_service.rs:478:13
    |
478 |             id,
    |             ^^ help: try ignoring the field: `id: _`

warning: unused variable: `name`
   --> src/service/classes/classes_service.rs:479:13
    |
479 |             name,
    |             ^^^^ help: try ignoring the field: `name: _`

warning: unused variable: `code`
   --> src/service/classes/classes_service.rs:480:13
    |
480 |             code,
    |             ^^^^ help: try ignoring the field: `code: _`

warning: unused variable: `grade_level_code`
   --> src/service/classes/classes_service.rs:481:13
    |
481 |             grade_level_code,
    |             ^^^^^^^^^^^^^^^^ help: try ignoring the field: `grade_level_code: _`

warning: unused variable: `subject_code`
   --> src/service/classes/classes_service.rs:482:13
    |
482 |             subject_code,
    |             ^^^^^^^^^^^^ help: try ignoring the field: `subject_code: _`

warning: unused variable: `class_type`
   --> src/service/classes/classes_service.rs:483:13
    |
483 |             class_type,
    |             ^^^^^^^^^^ help: try ignoring the field: `class_type: _`

warning: unused variable: `school_year`
   --> src/service/classes/classes_service.rs:484:13
    |
484 |             school_year,
    |             ^^^^^^^^^^^ help: try ignoring the field: `school_year: _`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:583:9
    |
583 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `id`
   --> src/service/classes/classes_service.rs:586:30
    |
586 |         let IdStringParams { id } = params;
    |                              ^^ help: try ignoring the field: `id: _`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:596:9
    |
596 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `id`
   --> src/service/classes/classes_service.rs:599:40
    |
599 |         let PageClassesStudentParams { id, pagination } = params;
    |                                        ^^ help: try ignoring the field: `id: _`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:712:9
    |
712 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `params`
   --> src/service/classes/classes_service.rs:713:9
    |
713 |         params: AddTeacherParams,
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_params`

warning: unused variable: `tenant_id`
   --> src/service/classes/classes_service.rs:724:9
    |
724 |         tenant_id: String,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_tenant_id`

warning: unused variable: `params`
   --> src/service/classes/classes_service.rs:725:9
    |
725 |         params: RemoveTeacherParams,
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_params`

warning: unused variable: `config`
   --> src/service/permission/role_sync_service.rs:504:79
    |
504 |     async fn create_tenant_role_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
    |                                                                               ^^^^^^ help: if this is intentional, prefix it with an underscore: `_config`

warning: unused variable: `chapters`
   --> src/service/teaching_aids/teaching_aids_service.rs:158:9
    |
158 |         chapters: Vec<TeachingAidChapter2>,
    |         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_chapters`

warning: unused variable: `textbook_id`
   --> src/service/teaching_aids/teaching_aids_service.rs:213:9
    |
213 |         textbook_id: Uuid,
    |         ^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_textbook_id`

warning: unused variable: `auth_context`
   --> src/middleware/auth_middleware.rs:402:9
    |
402 |     let auth_context = request
    |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_auth_context`

warning: unused variable: `auth_context`
   --> src/middleware/auth_middleware.rs:419:9
    |
419 |     let auth_context = request
    |         ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_auth_context`

warning: unused variable: `config`
   --> src/middleware/permission_middleware.rs:285:5
    |
285 |     config: &PermissionConfig,
    |     ^^^^^^ help: if this is intentional, prefix it with an underscore: `_config`

warning: variable does not need to be mutable
   --> src/middleware/permission_middleware.rs:314:11
    |
314 |     move |mut request: Request, next: Next| {
    |           ----^^^^^^^
    |           |
    |           help: remove this `mut`
    |
    = note: `#[warn(unused_mut)]` on by default

warning: variable does not need to be mutable
   --> src/middleware/permission_middleware.rs:344:5
    |
344 |     mut request: Request,
    |     ----^^^^^^^
    |     |
    |     help: remove this `mut`

warning: unused import: `Row`
 --> src/model/role/role.rs:2:21
  |
2 | use sqlx::{FromRow, Row};
  |                     ^^^

warning: unused import: `Row`
 --> src/service/tenant/tenant_data_service.rs:2:20
  |
2 | use sqlx::{PgPool, Row};
  |                    ^^^

warning: unused import: `Row`
 --> src/utils/db.rs:2:20
  |
2 | use sqlx::{PgPool, Row, Transaction, Postgres};
  |                    ^^^

warning: unused variable: `headers`
   --> src/controller/user/identity_controller.rs:175:36
    |
175 | fn extract_session_id_from_headers(headers: &HeaderMap) -> Result<Uuid, (StatusCode, Json<ErrorResponse>)> {
    |                                    ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_headers`

warning: value assigned to `has_where` is never read
  --> src/service/education_stage/education_stage_service.rs:70:21
   |
70 |                     has_where = true;
   |                     ^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

warning: value assigned to `has_where` is never read
   --> src/service/grade/grade_service.rs:128:25
    |
128 |                         has_where = true;
    |                         ^^^^^^^^^
    |
    = help: maybe it is overwritten before being read?

warning: unused variable: `enforcer`
   --> src/service/permission/casbin_service.rs:585:13
    |
585 |         let enforcer = self.get_tenant_enforcer(&policy.domain).await?;
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_enforcer`

warning: unused variable: `enforcer`
   --> src/service/permission/casbin_service.rs:613:13
    |
613 |         let enforcer = self.get_tenant_enforcer(&policy.domain).await?;
    |             ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_enforcer`

warning: unused variable: `resource`
   --> src/utils/error_handler.rs:133:30
    |
133 |     fn ok_or_not_found(self, resource: &str) -> AppResult<T>
    |                              ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_resource`

warning: unused variable: `secret`
  --> src/utils/jwt.rs:40:9
   |
40 |     let secret = env::var("JWT_SECRET").map_err(|_| "JWT_SECRET not set".to_string());
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_secret`

warning: unused variable: `secret`
  --> src/utils/jwt.rs:56:9
   |
56 |     let secret = env::var("JWT_SECRET").map_err(|_| "JWT_SECRET not set".to_string());
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_secret`

warning: type `SmsStats` is more private than the item `SmsService::get_delivery_stats`
   --> src/service/sms/sms_service.rs:196:5
    |
196 |     pub async fn get_delivery_stats(&self) -> HashMap<String, SmsStats> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method `SmsService::get_delivery_stats` is reachable at visibility `pub`
    |
note: but type `SmsStats` is only usable at visibility `pub(self)`
   --> src/service/sms/sms_service.rs:38:1
    |
38  | struct SmsStats {
    | ^^^^^^^^^^^^^^^
    = note: `#[warn(private_interfaces)]` on by default

warning: methods `is_mime_type_allowed` and `is_file_size_allowed` are never used
   --> src/config/minio_config.rs:129:12
    |
59  | impl MinioConfig {
    | ---------------- methods in this implementation
...
129 |     pub fn is_mime_type_allowed(&self, mime_type: &str) -> bool {
    |            ^^^^^^^^^^^^^^^^^^^^
...
134 |     pub fn is_file_size_allowed(&self, size: u64) -> bool {
    |            ^^^^^^^^^^^^^^^^^^^^
    |
    = note: `#[warn(dead_code)]` on by default

warning: struct `CheckCodeResponse` is never constructed
   --> src/controller/education_stage/education_stage_controller.rs:269:8
    |
269 | struct CheckCodeResponse {
    |        ^^^^^^^^^^^^^^^^^

warning: function `create_exam_handler` is never used
  --> src/controller/exam/exam_controller.rs:12:14
   |
12 | pub async fn create_exam_handler(
   |              ^^^^^^^^^^^^^^^^^^^

warning: function `get_exam_handler` is never used
  --> src/controller/exam/exam_controller.rs:29:14
   |
29 | pub async fn get_exam_handler(
   |              ^^^^^^^^^^^^^^^^

warning: function `list_exams_handler` is never used
  --> src/controller/exam/exam_controller.rs:46:14
   |
46 | pub async fn list_exams_handler(
   |              ^^^^^^^^^^^^^^^^^^

warning: function `update_exam_handler` is never used
  --> src/controller/exam/exam_controller.rs:62:14
   |
62 | pub async fn update_exam_handler(
   |              ^^^^^^^^^^^^^^^^^^^

warning: function `delete_exam_handler` is never used
  --> src/controller/exam/exam_controller.rs:80:14
   |
80 | pub async fn delete_exam_handler(
   |              ^^^^^^^^^^^^^^^^^^^

warning: function `get_exam_statistics_handler` is never used
  --> src/controller/exam/exam_controller.rs:96:14
   |
96 | pub async fn get_exam_statistics_handler(
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: field `include_disabled` is never read
  --> src/controller/permission/menu_controller.rs:29:9
   |
26 | pub struct MenuQueryParams {
   |            --------------- field in this struct
...
29 |     pub include_disabled: Option<bool>,
   |         ^^^^^^^^^^^^^^^^
   |
   = note: `MenuQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
  --> src/controller/permission/casbin_policy_controller.rs:60:9
   |
59 | pub struct CreatePolicyRequest {
   |            ------------------- fields in this struct
60 |     pub tenant_id: String,
   |         ^^^^^^^^^
61 |     pub policy_type: String,  // "permission" or "role"
   |         ^^^^^^^^^^^
62 |     pub subject: String,
   |         ^^^^^^^
63 |     pub object: Option<String>,
   |         ^^^^^^
64 |     pub action: Option<String>,
   |         ^^^^^^
65 |     pub effect: Option<String>,
   |         ^^^^^^
66 |     pub role: Option<String>,  // for role policies
   |         ^^^^
   |
   = note: `CreatePolicyRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
  --> src/controller/permission/casbin_policy_controller.rs:72:9
   |
71 | pub struct DeletePolicyRequest {
   |            ------------------- fields in this struct
72 |     pub tenant_id: String,
   |         ^^^^^^^^^
73 |     pub policy_type: String,
   |         ^^^^^^^^^^^
74 |     pub subject: String,
   |         ^^^^^^^
75 |     pub object: Option<String>,
   |         ^^^^^^
76 |     pub action: Option<String>,
   |         ^^^^^^
77 |     pub effect: Option<String>,
   |         ^^^^^^
78 |     pub role: Option<String>,
   |         ^^^^
   |
   = note: `DeletePolicyRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `tenant_id`, `subject`, `object`, and `action` are never read
  --> src/controller/permission/casbin_policy_controller.rs:84:9
   |
83 | pub struct TestPermissionRequest {
   |            --------------------- fields in this struct
84 |     pub tenant_id: String,
   |         ^^^^^^^^^
85 |     pub subject: String,
   |         ^^^^^^^
86 |     pub object: String,
   |         ^^^^^^
87 |     pub action: String,
   |         ^^^^^^
   |
   = note: `TestPermissionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `tenant_id` and `policies` are never read
   --> src/controller/permission/casbin_policy_controller.rs:105:9
    |
104 | pub struct BatchPolicyRequest {
    |            ------------------ fields in this struct
105 |     pub tenant_id: String,
    |         ^^^^^^^^^
106 |     pub policies: Vec<CreatePolicyRequest>,
    |         ^^^^^^^^
    |
    = note: `BatchPolicyRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple associated functions are never used
   --> src/controller/permission/casbin_policy_controller.rs:251:18
    |
118 | impl CasbinPolicyController {
    | --------------------------- associated functions in this implementation
...
251 |     pub async fn create_policy(
    |                  ^^^^^^^^^^^^^
...
301 |     pub async fn delete_policy(
    |                  ^^^^^^^^^^^^^
...
351 |     pub async fn test_permission(
    |                  ^^^^^^^^^^^^^^^
...
448 |     pub async fn export_policies(
    |                  ^^^^^^^^^^^^^^^
...
495 |     pub async fn batch_create_policies(
    |                  ^^^^^^^^^^^^^^^^^^^^^
...
536 |     pub async fn clear_tenant_policies(
    |                  ^^^^^^^^^^^^^^^^^^^^^
...
556 |     pub async fn sync_tenant_policies(
    |                  ^^^^^^^^^^^^^^^^^^^^

warning: struct `CheckCodeResponse` is never constructed
   --> src/controller/subject/subject_controller.rs:244:8
    |
244 | struct CheckCodeResponse {
    |        ^^^^^^^^^^^^^^^^^

warning: struct `UserIdentitiesResponse` is never constructed
   --> src/controller/user/identity_controller.rs:271:8
    |
271 | struct UserIdentitiesResponse {
    |        ^^^^^^^^^^^^^^^^^^^^^^

warning: struct `UserIdentitiesData` is never constructed
   --> src/controller/user/identity_controller.rs:277:8
    |
277 | struct UserIdentitiesData {
    |        ^^^^^^^^^^^^^^^^^^

warning: function `create_chapter_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:485:14
    |
485 | pub async fn create_chapter_handler(
    |              ^^^^^^^^^^^^^^^^^^^^^^

warning: function `get_chapter_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:575:14
    |
575 | pub async fn get_chapter_handler(
    |              ^^^^^^^^^^^^^^^^^^^

warning: function `delete_chapter_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:592:14
    |
592 | pub async fn delete_chapter_handler(
    |              ^^^^^^^^^^^^^^^^^^^^^^

warning: function `create_authorization_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:603:14
    |
603 | pub async fn create_authorization_handler(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `get_authorizations_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:621:14
    |
621 | pub async fn get_authorizations_handler(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `delete_authorization_handler` is never used
   --> src/controller/teaching_aids/teaching_aids_controller.rs:630:14
    |
630 | pub async fn delete_authorization_handler(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: fields `student_id`, `subject`, `level`, `level_description`, and `assessment_date` are never read
   --> src/model/analysis/analysis.rs:121:9
    |
120 | pub struct CreateStudentProfileLevelRequest {
    |            -------------------------------- fields in this struct
121 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
122 |     pub subject: String,
    |         ^^^^^^^
123 |     pub level: String,
    |         ^^^^^
124 |     pub level_description: Option<String>,
    |         ^^^^^^^^^^^^^^^^^
125 |     pub assessment_date: DateTime<Utc>,
    |         ^^^^^^^^^^^^^^^
    |
    = note: `CreateStudentProfileLevelRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `level`, `level_description`, and `assessment_date` are never read
   --> src/model/analysis/analysis.rs:130:9
    |
129 | pub struct UpdateStudentProfileLevelRequest {
    |            -------------------------------- fields in this struct
130 |     pub level: Option<String>,
    |         ^^^^^
131 |     pub level_description: Option<String>,
    |         ^^^^^^^^^^^^^^^^^
132 |     pub assessment_date: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^^^^^^
    |
    = note: `UpdateStudentProfileLevelRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `student_id`, `tag_name`, `tag_value`, and `tag_category` are never read
   --> src/model/analysis/analysis.rs:137:9
    |
136 | pub struct CreateStudentProfileTagRequest {
    |            ------------------------------ fields in this struct
137 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
138 |     pub tag_name: String,
    |         ^^^^^^^^
139 |     pub tag_value: Option<String>,
    |         ^^^^^^^^^
140 |     pub tag_category: String,
    |         ^^^^^^^^^^^^
    |
    = note: `CreateStudentProfileTagRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `tag_value` and `tag_category` are never read
   --> src/model/analysis/analysis.rs:145:9
    |
144 | pub struct UpdateStudentProfileTagRequest {
    |            ------------------------------ fields in this struct
145 |     pub tag_value: Option<String>,
    |         ^^^^^^^^^
146 |     pub tag_category: Option<String>,
    |         ^^^^^^^^^^^^
    |
    = note: `UpdateStudentProfileTagRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id`, `student_ids`, `subjects`, and `force_regenerate` are never read
   --> src/model/analysis/analysis.rs:151:9
    |
150 | pub struct GenerateLearningRecordRequest {
    |            ----------------------------- fields in this struct
151 |     pub exam_id: Uuid,
    |         ^^^^^^^
152 |     pub student_ids: Option<Vec<Uuid>>,
    |         ^^^^^^^^^^^
153 |     pub subjects: Option<Vec<String>>,
    |         ^^^^^^^^
154 |     pub force_regenerate: Option<bool>,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `GenerateLearningRecordRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/analysis/analysis.rs:159:9
    |
158 | pub struct AnalysisQueryParams {
    |            ------------------- fields in this struct
159 |     pub exam_id: Option<Uuid>,
    |         ^^^^^^^
160 |     pub student_id: Option<Uuid>,
    |         ^^^^^^^^^^
161 |     pub subject: Option<String>,
    |         ^^^^^^^
162 |     pub class_id: Option<Uuid>,
    |         ^^^^^^^^
163 |     pub grade_level: Option<String>,
    |         ^^^^^^^^^^^
164 |     pub start_date: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^
165 |     pub end_date: Option<DateTime<Utc>>,
    |         ^^^^^^^^
166 |     pub page: Option<i32>,
    |         ^^^^
167 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
    |
    = note: `AnalysisQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `base_exam_id`, `compare_exam_ids`, `analysis_type`, and `target_ids` are never read
   --> src/model/analysis/analysis.rs:172:9
    |
171 | pub struct ComparisonAnalysisRequest {
    |            ------------------------- fields in this struct
172 |     pub base_exam_id: Uuid,
    |         ^^^^^^^^^^^^
173 |     pub compare_exam_ids: Vec<Uuid>,
    |         ^^^^^^^^^^^^^^^^
174 |     pub analysis_type: String, // 'student', 'class', 'grade', 'subject'
    |         ^^^^^^^^^^^^^
175 |     pub target_ids: Vec<Uuid>,
    |         ^^^^^^^^^^
    |
    = note: `ComparisonAnalysisRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: associated functions `error` and `success` are never used
  --> src/model/base.rs:58:12
   |
55 | impl<T> ApiResponse<T> {
   | ---------------------- associated functions in this implementation
...
58 |     pub fn error(message: Option<String>) -> ApiResponse<Option<String>> {
   |            ^^^^^
...
67 |     pub fn success(data: Option<T>, _message: Option<T>) -> ApiResponse<Option<T>> {
   |            ^^^^^^^

warning: struct `SystemEducationStages` is never constructed
   --> src/model/education_stage/education_stage.rs:145:12
    |
145 | pub struct SystemEducationStages;
    |            ^^^^^^^^^^^^^^^^^^^^^

warning: multiple associated constants are never used
   --> src/model/education_stage/education_stage.rs:148:15
    |
147 | impl SystemEducationStages {
    | -------------------------- associated constants in this implementation
148 |     pub const PRIMARY: &'static str = "PRIMARY";
    |               ^^^^^^^
149 |     pub const MIDDLE: &'static str = "MIDDLE";
    |               ^^^^^^
150 |     pub const HIGH: &'static str = "HIGH";
    |               ^^^^
151 |     pub const VOCATIONAL: &'static str = "VOCATIONAL";
    |               ^^^^^^^^^^
152 |     pub const INTERNATIONAL_IB: &'static str = "INTERNATIONAL_IB";
    |               ^^^^^^^^^^^^^^^^
153 |     pub const INTERNATIONAL_AP: &'static str = "INTERNATIONAL_AP";
    |               ^^^^^^^^^^^^^^^^
154 |     pub const INTERNATIONAL_ALEVEL: &'static str = "INTERNATIONAL_ALEVEL";
    |               ^^^^^^^^^^^^^^^^^^^^

warning: multiple fields are never read
   --> src/model/exam/exam.rs:91:9
    |
90  | pub struct CreateExamRequest {
    |            ----------------- fields in this struct
91  |     pub name: String,
    |         ^^^^
92  |     pub exam_type: String,
    |         ^^^^^^^^^
93  |     pub grade_level: String,
    |         ^^^^^^^^^^^
94  |     pub exam_nature: String,
    |         ^^^^^^^^^^^
95  |     pub description: Option<String>,
    |         ^^^^^^^^^^^
96  |     pub start_time: DateTime<Utc>,
    |         ^^^^^^^^^^
97  |     pub end_time: DateTime<Utc>,
    |         ^^^^^^^^
98  |     pub expected_collection_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^^^^^^^^^^^^^^^
99  |     pub scan_start_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^^^^^^
100 |     pub grading_mode: String,
    |         ^^^^^^^^^^^^
101 |     pub quality_control: String,
    |         ^^^^^^^^^^^^^^^
102 |     pub ai_confidence_threshold: Option<f32>,
    |         ^^^^^^^^^^^^^^^^^^^^^^^
103 |     pub manual_review_ratio: Option<f32>,
    |         ^^^^^^^^^^^^^^^^^^^
104 |     pub subjects: Vec<ExamSubjectRequest>,
    |         ^^^^^^^^
105 |     pub classes: Vec<ExamClassRequest>,
    |         ^^^^^^^
106 |     pub selected_students: Option<Vec<Uuid>>,
    |         ^^^^^^^^^^^^^^^^^
    |
    = note: `CreateExamRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `subject_id`, `paper_template_id`, `total_score`, and `pass_score` are never read
   --> src/model/exam/exam.rs:111:9
    |
110 | pub struct ExamSubjectRequest {
    |            ------------------ fields in this struct
111 |     pub subject_id: Uuid,
    |         ^^^^^^^^^^
112 |     pub paper_template_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^^^^
113 |     pub total_score: f32,
    |         ^^^^^^^^^^^
114 |     pub pass_score: Option<f32>,
    |         ^^^^^^^^^^
    |
    = note: `ExamSubjectRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `class_id` and `class_type` are never read
   --> src/model/exam/exam.rs:119:9
    |
118 | pub struct ExamClassRequest {
    |            ---------------- fields in this struct
119 |     pub class_id: Uuid,
    |         ^^^^^^^^
120 |     pub class_type: String,
    |         ^^^^^^^^^^
    |
    = note: `ExamClassRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/exam/exam.rs:125:9
    |
124 | pub struct UpdateExamRequest {
    |            ----------------- fields in this struct
125 |     pub name: Option<String>,
    |         ^^^^
126 |     pub description: Option<String>,
    |         ^^^^^^^^^^^
127 |     pub start_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^
128 |     pub end_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^
129 |     pub expected_collection_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^^^^^^^^^^^^^^^
130 |     pub scan_start_time: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^^^^^^
131 |     pub grading_mode: Option<String>,
    |         ^^^^^^^^^^^^
132 |     pub quality_control: Option<String>,
    |         ^^^^^^^^^^^^^^^
133 |     pub ai_confidence_threshold: Option<f32>,
    |         ^^^^^^^^^^^^^^^^^^^^^^^
134 |     pub manual_review_ratio: Option<f32>,
    |         ^^^^^^^^^^^^^^^^^^^
135 |     pub status: Option<String>,
    |         ^^^^^^
    |
    = note: `UpdateExamRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `participant_tenant_ids`, `exam_id`, and `invitation_message` are never read
   --> src/model/exam/exam.rs:140:9
    |
139 | pub struct JointExamInvitationRequest {
    |            -------------------------- fields in this struct
140 |     pub participant_tenant_ids: Vec<Uuid>,
    |         ^^^^^^^^^^^^^^^^^^^^^^
141 |     pub exam_id: Uuid,
    |         ^^^^^^^
142 |     pub invitation_message: Option<String>,
    |         ^^^^^^^^^^^^^^^^^^
    |
    = note: `JointExamInvitationRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `invitation_id`, `response`, and `response_message` are never read
   --> src/model/exam/exam.rs:147:9
    |
146 | pub struct JointExamResponseRequest {
    |            ------------------------ fields in this struct
147 |     pub invitation_id: Uuid,
    |         ^^^^^^^^^^^^^
148 |     pub response: String, // 'accept' or 'reject'
    |         ^^^^^^^^
149 |     pub response_message: Option<String>,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `JointExamResponseRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/exam/exam.rs:189:9
    |
188 | pub struct ExamQueryParams {
    |            --------------- fields in this struct
189 |     pub page: Option<i32>,
    |         ^^^^
190 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
191 |     pub name: Option<String>,
    |         ^^^^
192 |     pub exam_type: Option<String>,
    |         ^^^^^^^^^
193 |     pub grade_level: Option<String>,
    |         ^^^^^^^^^^^
194 |     pub status: Option<String>,
    |         ^^^^^^
195 |     pub start_date: Option<DateTime<Utc>>,
    |         ^^^^^^^^^^
196 |     pub end_date: Option<DateTime<Utc>>,
    |         ^^^^^^^^
    |
    = note: `ExamQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id`, `student_id`, `paper_sequence`, `scan_method`, and `scan_device` are never read
   --> src/model/grading/grading.rs:164:9
    |
163 | pub struct UploadPaperRequest {
    |            ------------------ fields in this struct
164 |     pub exam_id: Uuid,
    |         ^^^^^^^
165 |     pub student_id: Option<Uuid>,
    |         ^^^^^^^^^^
166 |     pub paper_sequence: i32,
    |         ^^^^^^^^^^^^^^
167 |     pub scan_method: String,
    |         ^^^^^^^^^^^
168 |     pub scan_device: Option<String>,
    |         ^^^^^^^^^^^
    |
    = note: `UploadPaperRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/grading/grading.rs:173:9
    |
172 | pub struct CreateAnswerCardBlockRequest {
    |            ---------------------------- fields in this struct
173 |     pub exam_id: Uuid,
    |         ^^^^^^^
174 |     pub block_name: String,
    |         ^^^^^^^^^^
175 |     pub block_type: String,
    |         ^^^^^^^^^^
176 |     pub position_info: serde_json::Value,
    |         ^^^^^^^^^^^^^
177 |     pub max_score: f32,
    |         ^^^^^^^^^
178 |     pub questions: Vec<CardBlockQuestionRequest>,
    |         ^^^^^^^^^
179 |     pub question_links: Vec<CardBlockQuestionRequest>,
    |         ^^^^^^^^^^^^^^
    |
    = note: `CreateAnswerCardBlockRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `question_id`, `link_type`, `weight_ratio`, `score_mapping`, and `is_primary` are never read
   --> src/model/grading/grading.rs:184:9
    |
183 | pub struct CardBlockQuestionRequest {
    |            ------------------------ fields in this struct
184 |     pub question_id: Uuid,
    |         ^^^^^^^^^^^
185 |     pub link_type: String,
    |         ^^^^^^^^^
186 |     pub weight_ratio: f32,
    |         ^^^^^^^^^^^^
187 |     pub score_mapping: serde_json::Value,
    |         ^^^^^^^^^^^^^
188 |     pub is_primary: bool,
    |         ^^^^^^^^^^
    |
    = note: `CardBlockQuestionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `card_block_id`, `student_id`, `raw_score`, `grading_method`, `confidence_score`, and `grading_notes` are never read
   --> src/model/grading/grading.rs:193:9
    |
192 | pub struct GradeCardBlockRequest {
    |            --------------------- fields in this struct
193 |     pub card_block_id: Uuid,
    |         ^^^^^^^^^^^^^
194 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
195 |     pub raw_score: f32,
    |         ^^^^^^^^^
196 |     pub grading_method: String,
    |         ^^^^^^^^^^^^^^
197 |     pub confidence_score: Option<f32>,
    |         ^^^^^^^^^^^^^^^^
198 |     pub grading_notes: Option<String>,
    |         ^^^^^^^^^^^^^
    |
    = note: `GradeCardBlockRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id`, `question_id`, `grader_user_id`, `control_level`, `control_action`, and `control_reason` are never read
   --> src/model/grading/grading.rs:203:9
    |
202 | pub struct ControlGradingRequest {
    |            --------------------- fields in this struct
203 |     pub exam_id: Uuid,
    |         ^^^^^^^
204 |     pub question_id: Option<Uuid>,
    |         ^^^^^^^^^^^
205 |     pub grader_user_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^
206 |     pub control_level: String,
    |         ^^^^^^^^^^^^^
207 |     pub control_action: String,
    |         ^^^^^^^^^^^^^^
208 |     pub control_reason: Option<String>,
    |         ^^^^^^^^^^^^^^
    |
    = note: `ControlGradingRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exception_id`, `confirmed_student_id`, `resolution_method`, and `processing_notes` are never read
   --> src/model/grading/grading.rs:213:9
    |
212 | pub struct ResolveStudentIdExceptionRequest {
    |            -------------------------------- fields in this struct
213 |     pub exception_id: Uuid,
    |         ^^^^^^^^^^^^
214 |     pub confirmed_student_id: Uuid,
    |         ^^^^^^^^^^^^^^^^^^^^
215 |     pub resolution_method: String,
    |         ^^^^^^^^^^^^^^^^^
216 |     pub processing_notes: Option<String>,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `ResolveStudentIdExceptionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exception_id`, `resolution_status`, and `resolution_notes` are never read
   --> src/model/grading/grading.rs:221:9
    |
220 | pub struct ResolvePaperScanExceptionRequest {
    |            -------------------------------- fields in this struct
221 |     pub exception_id: Uuid,
    |         ^^^^^^^^^^^^
222 |     pub resolution_status: String,
    |         ^^^^^^^^^^^^^^^^^
223 |     pub resolution_notes: Option<String>,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `ResolvePaperScanExceptionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/grading/grading.rs:228:9
    |
227 | pub struct GradingQueryParams {
    |            ------------------ fields in this struct
228 |     pub exam_id: Option<Uuid>,
    |         ^^^^^^^
229 |     pub student_id: Option<Uuid>,
    |         ^^^^^^^^^^
230 |     pub grader_id: Option<Uuid>,
    |         ^^^^^^^^^
231 |     pub question_id: Option<Uuid>,
    |         ^^^^^^^^^^^
232 |     pub grading_method: Option<String>,
    |         ^^^^^^^^^^^^^^
233 |     pub quality_level: Option<String>,
    |         ^^^^^^^^^^^^^
234 |     pub page: Option<i32>,
    |         ^^^^
235 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
    |
    = note: `GradingQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/grading/grading.rs:240:9
    |
239 | pub struct ScanQueryParams {
    |            --------------- fields in this struct
240 |     pub exam_id: Option<Uuid>,
    |         ^^^^^^^
241 |     pub student_id: Option<Uuid>,
    |         ^^^^^^^^^^
242 |     pub scan_quality_min: Option<i32>,
    |         ^^^^^^^^^^^^^^^^
243 |     pub scan_quality_max: Option<i32>,
    |         ^^^^^^^^^^^^^^^^
244 |     pub is_abnormal: Option<bool>,
    |         ^^^^^^^^^^^
245 |     pub needs_review: Option<bool>,
    |         ^^^^^^^^^^^^
246 |     pub page: Option<i32>,
    |         ^^^^
247 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
    |
    = note: `ScanQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id` and `scans` are never read
   --> src/model/grading/grading.rs:364:9
    |
363 | pub struct PaperScanBatchRequest {
    |            --------------------- fields in this struct
364 |     pub exam_id: Uuid,
    |         ^^^^^^^
365 |     pub scans: Vec<CreatePaperScanRequest>,
    |         ^^^^^
    |
    = note: `PaperScanBatchRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/grading/grading.rs:370:9
    |
369 | pub struct CreatePaperScanRequest {
    |            ---------------------- fields in this struct
370 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
371 |     pub paper_sequence: i32,
    |         ^^^^^^^^^^^^^^
372 |     pub front_page_url: Option<String>,
    |         ^^^^^^^^^^^^^^
373 |     pub back_page_url: Option<String>,
    |         ^^^^^^^^^^^^^
374 |     pub scan_quality: i32,
    |         ^^^^^^^^^^^^
375 |     pub scan_method: String,
    |         ^^^^^^^^^^^
376 |     pub scan_device: Option<String>,
    |         ^^^^^^^^^^^
377 |     pub file_size: i64,
    |         ^^^^^^^^^
378 |     pub exam_id: Uuid
    |         ^^^^^^^
    |
    = note: `CreatePaperScanRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/grading/grading.rs:383:9
    |
382 | pub struct PaperScanQueryParams {
    |            -------------------- fields in this struct
383 |     pub exam_id: Option<Uuid>,
    |         ^^^^^^^
384 |     pub student_id: Option<Uuid>,
    |         ^^^^^^^^^^
385 |     pub min_quality: Option<i32>,
    |         ^^^^^^^^^^^
386 |     pub needs_review: Option<bool>,
    |         ^^^^^^^^^^^^
387 |     pub is_abnormal: Option<bool>,
    |         ^^^^^^^^^^^
388 |     pub page: Option<i32>,
    |         ^^^^
389 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
    |
    = note: `PaperScanQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id` and `blocks` are never read
   --> src/model/grading/grading.rs:394:9
    |
393 | pub struct CreateAnswerCardBlocksRequest {
    |            ----------------------------- fields in this struct
394 |     pub exam_id: Uuid,
    |         ^^^^^^^
395 |     pub blocks: Vec<CreateAnswerCardBlockRequest>,
    |         ^^^^^^
    |
    = note: `CreateAnswerCardBlocksRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id`, `grader_id`, `question_ids`, `assignment_type`, and `assignments` are never read
   --> src/model/grading/grading.rs:402:9
    |
401 | pub struct GradingAssignmentRequest {
    |            ------------------------ fields in this struct
402 |     pub exam_id: Uuid,
    |         ^^^^^^^
403 |     pub grader_id: Uuid,
    |         ^^^^^^^^^
404 |     pub question_ids: Vec<Uuid>,
    |         ^^^^^^^^^^^^
405 |     pub assignment_type: String,
    |         ^^^^^^^^^^^^^^^
406 |     pub assignments: Vec<GradingAssignmentItem>,
    |         ^^^^^^^^^^^
    |
    = note: `GradingAssignmentRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `question_id` and `student_ids` are never read
   --> src/model/grading/grading.rs:411:9
    |
410 | pub struct GradingAssignmentItem {
    |            --------------------- fields in this struct
411 |     pub question_id: Uuid,
    |         ^^^^^^^^^^^
412 |     pub student_ids: Vec<Uuid>,
    |         ^^^^^^^^^^^
    |
    = note: `GradingAssignmentItem` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/grading/grading.rs:417:9
    |
416 | pub struct GradingAssignmentQueryParams {
    |            ---------------------------- fields in this struct
417 |     pub exam_id: Option<Uuid>,
    |         ^^^^^^^
418 |     pub grader_id: Option<Uuid>,
    |         ^^^^^^^^^
419 |     pub grader_user_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^
420 |     pub priority_level: Option<String>,
    |         ^^^^^^^^^^^^^^
421 |     pub status: Option<String>,
    |         ^^^^^^
422 |     pub page: Option<i32>,
    |         ^^^^
423 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
    |
    = note: `GradingAssignmentQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `assignment_id`, `student_id`, `question_id`, `score`, `comments`, and `grading_method` are never read
   --> src/model/grading/grading.rs:428:9
    |
427 | pub struct SubmitGradingRecordRequest {
    |            -------------------------- fields in this struct
428 |     pub assignment_id: Uuid,
    |         ^^^^^^^^^^^^^
429 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
430 |     pub question_id: Uuid,
    |         ^^^^^^^^^^^
431 |     pub score: f32,
    |         ^^^^^
432 |     pub comments: Option<String>,
    |         ^^^^^^^^
433 |     pub grading_method: String,
    |         ^^^^^^^^^^^^^^
    |
    = note: `SubmitGradingRecordRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `card_block_id`, `student_id`, `score`, `comments`, and `grading_method` are never read
   --> src/model/grading/grading.rs:438:9
    |
437 | pub struct SubmitCardBlockGradingRequest {
    |            ----------------------------- fields in this struct
438 |     pub card_block_id: Uuid,
    |         ^^^^^^^^^^^^^
439 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
440 |     pub score: f32,
    |         ^^^^^
441 |     pub comments: Option<String>,
    |         ^^^^^^^^
442 |     pub grading_method: String,
    |         ^^^^^^^^^^^^^^
    |
    = note: `SubmitCardBlockGradingRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id`, `question_ids`, `ai_model`, `confidence_threshold`, and `ai_gradings` are never read
   --> src/model/grading/grading.rs:447:9
    |
446 | pub struct ProcessAIGradingRequest {
    |            ----------------------- fields in this struct
447 |     pub exam_id: Uuid,
    |         ^^^^^^^
448 |     pub question_ids: Vec<Uuid>,
    |         ^^^^^^^^^^^^
449 |     pub ai_model: String,
    |         ^^^^^^^^
450 |     pub confidence_threshold: f32,
    |         ^^^^^^^^^^^^^^^^^^^^
451 |     pub ai_gradings: Vec<AIGradingRequest>,
    |         ^^^^^^^^^^^
    |
    = note: `ProcessAIGradingRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `question_id`, `student_id`, `ai_score`, `confidence`, `ai_model`, and `needs_review` are never read
   --> src/model/grading/grading.rs:456:9
    |
455 | pub struct AIGradingRequest {
    |            ---------------- fields in this struct
456 |     pub question_id: Uuid,
    |         ^^^^^^^^^^^
457 |     pub student_id: Uuid,
    |         ^^^^^^^^^^
458 |     pub ai_score: f32,
    |         ^^^^^^^^
459 |     pub confidence: f32,
    |         ^^^^^^^^^^
460 |     pub ai_model: String,
    |         ^^^^^^^^
461 |     pub needs_review: bool,
    |         ^^^^^^^^^^^^
    |
    = note: `AIGradingRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id`, `action`, and `grading_mode` are never read
   --> src/model/grading/grading.rs:466:9
    |
465 | pub struct GradingControlRequest {
    |            --------------------- fields in this struct
466 |     pub exam_id: Uuid,
    |         ^^^^^^^
467 |     pub action: String, // 'start', 'pause', 'resume', 'stop'
    |         ^^^^^^
468 |     pub grading_mode: Option<String>,
    |         ^^^^^^^^^^^^
    |
    = note: `GradingControlRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `scan_id`, `exception_type`, `resolution_type`, `resolution_data`, and `confirmed_student_id` are never read
   --> src/model/grading/grading.rs:473:9
    |
472 | pub struct HandleScanExceptionRequest {
    |            -------------------------- fields in this struct
473 |     pub scan_id: Uuid,
    |         ^^^^^^^
474 |     pub exception_type: String,
    |         ^^^^^^^^^^^^^^
475 |     pub resolution_type: String,
    |         ^^^^^^^^^^^^^^^
476 |     pub resolution_data: serde_json::Value,
    |         ^^^^^^^^^^^^^^^
477 |     pub confirmed_student_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^^^^^^^
    |
    = note: `HandleScanExceptionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `exam_id`, `grader_id`, `grader_user_id`, `question_id`, `date_from`, and `date_to` are never read
   --> src/model/grading/grading.rs:482:9
    |
481 | pub struct GradingStatisticsQueryParams {
    |            ---------------------------- fields in this struct
482 |     pub exam_id: Option<Uuid>,
    |         ^^^^^^^
483 |     pub grader_id: Option<Uuid>,
    |         ^^^^^^^^^
484 |     pub grader_user_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^
485 |     pub question_id: Option<Uuid>,
    |         ^^^^^^^^^^^
486 |     pub date_from: Option<DateTime<Utc>>,
    |         ^^^^^^^^^
487 |     pub date_to: Option<DateTime<Utc>>,
    |         ^^^^^^^
    |
    = note: `GradingStatisticsQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/question/question.rs:104:9
    |
103 | pub struct CreateQuestionRequest {
    |            --------------------- fields in this struct
104 |     pub source_type: String,
    |         ^^^^^^^^^^^
105 |     pub source_id: Option<Uuid>,
    |         ^^^^^^^^^
106 |     pub question_type: String,
    |         ^^^^^^^^^^^^^
107 |     pub question_content: String,
    |         ^^^^^^^^^^^^^^^^
108 |     pub options: Option<serde_json::Value>,
    |         ^^^^^^^
109 |     pub answer_content: Option<String>,
    |         ^^^^^^^^^^^^^^
110 |     pub difficulty_level: i32,
    |         ^^^^^^^^^^^^^^^^
111 |     pub knowledge_points: serde_json::Value,
    |         ^^^^^^^^^^^^^^^^
112 |     pub subject: String,
    |         ^^^^^^^
113 |     pub grade_level: i32,
    |         ^^^^^^^^^^^
    |
    = note: `CreateQuestionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `question_content`, `answer_content`, `difficulty_level`, `knowledge_points`, and `status` are never read
   --> src/model/question/question.rs:118:9
    |
117 | pub struct UpdateQuestionRequest {
    |            --------------------- fields in this struct
118 |     pub question_content: Option<String>,
    |         ^^^^^^^^^^^^^^^^
119 |     pub answer_content: Option<String>,
    |         ^^^^^^^^^^^^^^
120 |     pub difficulty_level: Option<i32>,
    |         ^^^^^^^^^^^^^^^^
121 |     pub knowledge_points: Option<serde_json::Value>,
    |         ^^^^^^^^^^^^^^^^
122 |     pub status: Option<String>,
    |         ^^^^^^
    |
    = note: `UpdateQuestionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/question/question.rs:127:9
    |
126 | pub struct CreatePaperRequest {
    |            ------------------ fields in this struct
127 |     pub title: String,
    |         ^^^^^
128 |     pub subject: String,
    |         ^^^^^^^
129 |     pub grade_level: i32,
    |         ^^^^^^^^^^^
130 |     pub total_score: f32,
    |         ^^^^^^^^^^^
131 |     pub description: Option<String>,
    |         ^^^^^^^^^^^
132 |     pub structure: serde_json::Value,
    |         ^^^^^^^^^
133 |     pub questions: Vec<PaperQuestionRequest>,
    |         ^^^^^^^^^
    |
    = note: `CreatePaperRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `question_id`, `question_number`, `score`, `section_name`, and `display_order` are never read
   --> src/model/question/question.rs:138:9
    |
137 | pub struct PaperQuestionRequest {
    |            -------------------- fields in this struct
138 |     pub question_id: Uuid,
    |         ^^^^^^^^^^^
139 |     pub question_number: String,
    |         ^^^^^^^^^^^^^^^
140 |     pub score: f32,
    |         ^^^^^
141 |     pub section_name: String,
    |         ^^^^^^^^^^^^
142 |     pub display_order: i32,
    |         ^^^^^^^^^^^^^
    |
    = note: `PaperQuestionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `title`, `description`, `structure`, and `status` are never read
   --> src/model/question/question.rs:147:9
    |
146 | pub struct UpdatePaperRequest {
    |            ------------------ fields in this struct
147 |     pub title: Option<String>,
    |         ^^^^^
148 |     pub description: Option<String>,
    |         ^^^^^^^^^^^
149 |     pub structure: Option<serde_json::Value>,
    |         ^^^^^^^^^
150 |     pub status: Option<String>,
    |         ^^^^^^
    |
    = note: `UpdatePaperRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/question/question.rs:155:9
    |
154 | pub struct CreateTextbookRequest {
    |            --------------------- fields in this struct
155 |     pub title: String,
    |         ^^^^^
156 |     pub subject_id: Uuid,
    |         ^^^^^^^^^^
157 |     pub grade_level_id: Uuid,
    |         ^^^^^^^^^^^^^^
158 |     pub publisher: Option<String>,
    |         ^^^^^^^^^
159 |     pub publication_year: Option<i32>,
    |         ^^^^^^^^^^^^^^^^
160 |     pub isbn: Option<String>,
    |         ^^^^
161 |     pub version: Option<String>,
    |         ^^^^^^^
162 |     pub chapters: Vec<TextbookChapterRequest>,
    |         ^^^^^^^^
    |
    = note: `CreateTextbookRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `chapter_number`, `title`, `content`, `knowledge_points`, and `exercises` are never read
   --> src/model/question/question.rs:167:9
    |
166 | pub struct TextbookChapterRequest {
    |            ---------------------- fields in this struct
167 |     pub chapter_number: i32,
    |         ^^^^^^^^^^^^^^
168 |     pub title: String,
    |         ^^^^^
169 |     pub content: Option<String>,
    |         ^^^^^^^
170 |     pub knowledge_points: serde_json::Value,
    |         ^^^^^^^^^^^^^^^^
171 |     pub exercises: Vec<TextbookExerciseRequest>,
    |         ^^^^^^^^^
    |
    = note: `TextbookChapterRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `question_content`, `answer_content`, `difficulty_level`, and `knowledge_points` are never read
   --> src/model/question/question.rs:176:9
    |
175 | pub struct TextbookExerciseRequest {
    |            ----------------------- fields in this struct
176 |     pub question_content: String,
    |         ^^^^^^^^^^^^^^^^
177 |     pub answer_content: Option<String>,
    |         ^^^^^^^^^^^^^^
178 |     pub difficulty_level: i32,
    |         ^^^^^^^^^^^^^^^^
179 |     pub knowledge_points: serde_json::Value,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `TextbookExerciseRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `textbook_id` and `tenant_ids` are never read
   --> src/model/question/question.rs:184:9
    |
183 | pub struct GrantTextbookAccessRequest {
    |            -------------------------- fields in this struct
184 |     pub textbook_id: Uuid,
    |         ^^^^^^^^^^^
185 |     pub tenant_ids: Vec<Uuid>,
    |         ^^^^^^^^^^
    |
    = note: `GrantTextbookAccessRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/question/question.rs:190:9
    |
189 | pub struct QuestionQueryParams {
    |            ------------------- fields in this struct
190 |     pub page: Option<i32>,
    |         ^^^^
191 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
192 |     pub subject: Option<String>,
    |         ^^^^^^^
193 |     pub grade_level: Option<i32>,
    |         ^^^^^^^^^^^
194 |     pub difficulty_level: Option<i32>,
    |         ^^^^^^^^^^^^^^^^
195 |     pub question_type: Option<String>,
    |         ^^^^^^^^^^^^^
196 |     pub status: Option<String>,
    |         ^^^^^^
197 |     pub keyword: Option<String>,
    |         ^^^^^^^
198 |     pub source_type: Option<String>,
    |         ^^^^^^^^^^^
199 |     pub search: Option<String>,
    |         ^^^^^^
    |
    = note: `QuestionQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `page`, `page_size`, `subject`, `grade_level`, `status`, and `keyword` are never read
   --> src/model/question/question.rs:204:9
    |
203 | pub struct PaperQueryParams {
    |            ---------------- fields in this struct
204 |     pub page: Option<i32>,
    |         ^^^^
205 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
206 |     pub subject: Option<String>,
    |         ^^^^^^^
207 |     pub grade_level: Option<i32>,
    |         ^^^^^^^^^^^
208 |     pub status: Option<String>,
    |         ^^^^^^
209 |     pub keyword: Option<String>,
    |         ^^^^^^^
    |
    = note: `PaperQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/question/question.rs:214:9
    |
213 | pub struct TextbookQueryParams {
    |            ------------------- fields in this struct
214 |     pub page: Option<i32>,
    |         ^^^^
215 |     pub page_size: Option<i32>,
    |         ^^^^^^^^^
216 |     pub subject_id: Option<Uuid>,
    |         ^^^^^^^^^^
217 |     pub grade_level_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^
218 |     pub publisher: Option<String>,
    |         ^^^^^^^^^
219 |     pub status: Option<String>,
    |         ^^^^^^
220 |     pub keyword: Option<String>,
    |         ^^^^^^^
    |
    = note: `TextbookQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: methods `is_system_role` and `full_display_name` are never used
   --> src/model/role/role.rs:149:12
    |
147 | impl Role {
    | --------- methods in this implementation
148 |     /// 检查是否是系统预设角色
149 |     pub fn is_system_role(&self) -> bool {
    |            ^^^^^^^^^^^^^^
...
154 |     pub fn full_display_name(&self) -> String {
    |            ^^^^^^^^^^^^^^^^^

warning: struct `SystemRoles` is never constructed
   --> src/model/role/role.rs:184:12
    |
184 | pub struct SystemRoles;
    |            ^^^^^^^^^^^

warning: multiple associated constants are never used
   --> src/model/role/role.rs:187:15
    |
186 | impl SystemRoles {
    | ---------------- associated constants in this implementation
187 |     pub const SUPER_ADMIN: &'static str = "super_admin";
    |               ^^^^^^^^^^^
188 |     pub const SYSTEM_AGENT: &'static str = "system_agent";
    |               ^^^^^^^^^^^^
189 |     pub const OPERATOR: &'static str = "operator";
    |               ^^^^^^^^
190 |     pub const TENANT_ADMIN: &'static str = "tenant_admin";
    |               ^^^^^^^^^^^^
191 |     pub const PRINCIPAL: &'static str = "principal";
    |               ^^^^^^^^^
192 |     pub const ACADEMIC_DIRECTOR: &'static str = "academic_director";
    |               ^^^^^^^^^^^^^^^^^
193 |     pub const SUBJECT_LEADER: &'static str = "subject_leader";
    |               ^^^^^^^^^^^^^^
194 |     pub const EXAM_MANAGER: &'static str = "exam_manager";
    |               ^^^^^^^^^^^^
195 |     pub const GRADING_MANAGER: &'static str = "grading_manager";
    |               ^^^^^^^^^^^^^^^
196 |     pub const SCAN_OPERATOR: &'static str = "scan_operator";
    |               ^^^^^^^^^^^^^
197 |     pub const GRADER: &'static str = "grader";
    |               ^^^^^^
198 |     pub const GRADE_LEADER: &'static str = "grade_leader";
    |               ^^^^^^^^^^^^
199 |     pub const CLASS_TEACHER: &'static str = "class_teacher";
    |               ^^^^^^^^^^^^^
200 |     pub const TEACHER: &'static str = "teacher";
    |               ^^^^^^^
201 |     pub const STUDENT: &'static str = "student";
    |               ^^^^^^^
202 |     pub const PARENT: &'static str = "parent";
    |               ^^^^^^

warning: fields `id`, `role_id`, `permission_id`, and `created_at` are never read
  --> src/model/role/permission.rs:22:9
   |
21 | pub struct RolePermission {
   |            -------------- fields in this struct
22 |     pub id: Uuid,
   |         ^^
23 |     pub role_id: Uuid,
   |         ^^^^^^^
24 |     pub permission_id: Uuid,
   |         ^^^^^^^^^^^^^
25 |     pub created_at: DateTime<Utc>,
   |         ^^^^^^^^^^
   |
   = note: `RolePermission` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: fields `page`, `page_size`, `search`, `resource`, and `action` are never read
  --> src/model/role/permission.rs:31:9
   |
30 | pub struct PermissionQueryParams {
   |            --------------------- fields in this struct
31 |     pub page: Option<i32>,
   |         ^^^^
32 |     pub page_size: Option<i32>,
   |         ^^^^^^^^^
33 |     pub search: Option<String>,
   |         ^^^^^^
34 |     pub resource: Option<String>,
   |         ^^^^^^^^
35 |     pub action: Option<String>,
   |         ^^^^^^
   |
   = note: `PermissionQueryParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `resource` and `action` are never read
  --> src/model/role/permission.rs:48:9
   |
47 | pub struct CheckPermissionRequest {
   |            ---------------------- fields in this struct
48 |     pub resource: String,
   |         ^^^^^^^^
49 |     pub action: String,
   |         ^^^^^^
   |
   = note: `CheckPermissionRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: struct `SystemSubjects` is never constructed
   --> src/model/subject/subject.rs:116:12
    |
116 | pub struct SystemSubjects;
    |            ^^^^^^^^^^^^^^

warning: multiple associated constants are never used
   --> src/model/subject/subject.rs:119:15
    |
118 | impl SystemSubjects {
    | ------------------- associated constants in this implementation
119 |     pub const MATH: &'static str = "MATH";
    |               ^^^^
120 |     pub const CHINESE: &'static str = "CHINESE";
    |               ^^^^^^^
121 |     pub const ENGLISH: &'static str = "ENGLISH";
    |               ^^^^^^^
122 |     pub const PHYSICS: &'static str = "PHYSICS";
    |               ^^^^^^^
123 |     pub const CHEMISTRY: &'static str = "CHEMISTRY";
    |               ^^^^^^^^^
124 |     pub const BIOLOGY: &'static str = "BIOLOGY";
    |               ^^^^^^^
125 |     pub const HISTORY: &'static str = "HISTORY";
    |               ^^^^^^^
126 |     pub const GEOGRAPHY: &'static str = "GEOGRAPHY";
    |               ^^^^^^^^^
127 |     pub const POLITICS: &'static str = "POLITICS";
    |               ^^^^^^^^

warning: field `author` is never read
   --> src/model/teaching_aids/textbooks.rs:145:9
    |
142 | pub struct UpdateTeachingAidRequest {
    |            ------------------------ field in this struct
...
145 |     pub author: Option<String>,
    |         ^^^^^^
    |
    = note: `UpdateTeachingAidRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/model/teaching_aids/textbooks.rs:155:9
    |
154 | pub struct ImportTeachingAidRequest {
    |            ------------------------ fields in this struct
155 |     pub title: String,
    |         ^^^^^
156 |     pub description: Option<String>,
    |         ^^^^^^^^^^^
157 |     pub author: Option<String>,
    |         ^^^^^^
158 |     pub publisher: Option<String>,
    |         ^^^^^^^^^
159 |     pub subject_id: Option<Uuid>,
    |         ^^^^^^^^^^
160 |     pub grade_level_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^
161 |     pub version: Option<String>,
    |         ^^^^^^^
    |
    = note: `ImportTeachingAidRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `subject_id`, `grade_level_id`, `status`, and `search` are never read
   --> src/model/teaching_aids/textbooks.rs:168:9
    |
165 | pub struct TeachingAidQuery {
    |            ---------------- fields in this struct
...
168 |     pub subject_id: Option<Uuid>,
    |         ^^^^^^^^^^
169 |     pub grade_level_id: Option<Uuid>,
    |         ^^^^^^^^^^^^^^
170 |     pub status: Option<String>,
    |         ^^^^^^
171 |     pub search: Option<String>,
    |         ^^^^^^
    |
    = note: `TeachingAidQuery` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `chapter_number`, `title`, `content`, and `knowledge_points` are never read
   --> src/model/teaching_aids/textbooks.rs:176:9
    |
175 | pub struct CreateChapterRequest {
    |            -------------------- fields in this struct
176 |     pub chapter_number: i32,
    |         ^^^^^^^^^^^^^^
177 |     pub title: String,
    |         ^^^^^
178 |     pub content: Option<String>,
    |         ^^^^^^^
179 |     pub knowledge_points: Option<serde_json::Value>,
    |         ^^^^^^^^^^^^^^^^
    |
    = note: `CreateChapterRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `textbook_id` and `tenant_id` are never read
   --> src/model/teaching_aids/textbooks.rs:184:9
    |
183 | pub struct CreateAuthorizationRequest {
    |            -------------------------- fields in this struct
184 |     pub textbook_id: Uuid,
    |         ^^^^^^^^^^^
185 |     pub tenant_id: Uuid,
    |         ^^^^^^^^^
    |
    = note: `CreateAuthorizationRequest` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: method `calculate_effective_risk_level` is never used
   --> src/model/tenant/permission.rs:251:12
    |
250 | impl PermissionTemplate {
    | ----------------------- method in this implementation
251 |     pub fn calculate_effective_risk_level(&self, context: &PermissionContext) -> i32 {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: methods `is_expired` and `can_auto_approve` are never used
   --> src/model/tenant/permission.rs:272:12
    |
271 | impl SpecialPermissionRequest {
    | ----------------------------- methods in this implementation
272 |     pub fn is_expired(&self) -> bool {
    |            ^^^^^^^^^^
...
280 |     pub fn can_auto_approve(&self, workflow: &ApprovalWorkflow) -> bool {
    |            ^^^^^^^^^^^^^^^^

warning: associated function `new` is never used
   --> src/model/tenant/permission.rs:298:12
    |
297 | impl PermissionAuditLog {
    | ----------------------- associated function in this implementation
298 |     pub fn new(
    |            ^^^

warning: fields `id`, `username`, and `password` are never read
 --> src/model/user/user.rs:7:8
  |
6 | pub struct UserRecord {
  |            ---------- fields in this struct
7 |    pub id: Uuid,
  |        ^^
8 |    pub username: String,
  |        ^^^^^^^^
9 |    pub password: String,
  |        ^^^^^^^^

warning: struct `ExamService` is never constructed
 --> src/service/exam/exam_service.rs:8:12
  |
8 | pub struct ExamService {
  |            ^^^^^^^^^^^

warning: multiple associated items are never used
   --> src/service/exam/exam_service.rs:13:12
    |
12  | impl ExamService {
    | ---------------- associated items in this implementation
13  |     pub fn new(pool: PgPool) -> Self {
    |            ^^^
...
17  |     fn schema_name(tenant_id: Uuid) -> String {
    |        ^^^^^^^^^^^
...
21  |     pub async fn create_exam(
    |                  ^^^^^^^^^^^
...
187 |     pub async fn get_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<Option<ExamResponse>> {
    |                  ^^^^^^^^
...
256 |     pub async fn update_exam(
    |                  ^^^^^^^^^^^
...
316 |     pub async fn delete_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
    |                  ^^^^^^^^^^^
...
327 |     pub async fn list_exams(
    |                  ^^^^^^^^^^
...
412 |     pub async fn create_joint_exam_invitation(
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
446 |     pub async fn respond_to_joint_exam_invitation(
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
477 |     pub async fn get_exam_statistics(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<ExamStatisticsResponse> {
    |                  ^^^^^^^^^^^^^^^^^^^
...
569 |     pub async fn get_exam_statistics_overview(&self, tenant_id: Uuid) -> Result<ExamStatistics> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
629 |     pub async fn publish_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
    |                  ^^^^^^^^^^^^
...
647 |     pub async fn start_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
    |                  ^^^^^^^^^^
...
665 |     pub async fn complete_exam(&self, tenant_id: Uuid, exam_id: Uuid) -> Result<bool> {
    |                  ^^^^^^^^^^^^^

warning: struct `StartupSchemaService` is never constructed
 --> src/service/tenant/startup_schema_service.rs:8:12
  |
8 | pub struct StartupSchemaService {
  |            ^^^^^^^^^^^^^^^^^^^^

warning: multiple associated items are never used
   --> src/service/tenant/startup_schema_service.rs:14:12
    |
13  | impl StartupSchemaService {
    | ------------------------- associated items in this implementation
14  |     pub fn new(pool: PgPool, template_path: Option<String>) -> Self {
    |            ^^^
...
23  |     pub async fn ensure_startup_schemas(&self) -> Result<()> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^
...
71  |     async fn create_default_tenant_for_development(&self) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
115 |     async fn create_schema_from_template(&self, schema_name: &str) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
133 |     async fn execute_template_sql(
    |              ^^^^^^^^^^^^^^^^^^^^
...
158 |     async fn get_all_tenants(&self) -> Result<Vec<TenantInfo>> {
    |              ^^^^^^^^^^^^^^^
...
174 |     async fn get_existing_schemas(&self) -> Result<HashSet<String>> {
    |              ^^^^^^^^^^^^^^^^^^^^
...
194 |     async fn schema_exists(&self, schema_name: &str) -> Result<bool> {
    |              ^^^^^^^^^^^^^
...
206 |     fn validate_schema_name(&self, schema_name: &str) -> Result<()> {
    |        ^^^^^^^^^^^^^^^^^^^^
...
220 |     pub async fn create_compile_time_schemas(&self) -> Result<()> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
240 |     pub async fn cleanup_development_data(&self) -> Result<()> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^

warning: struct `TenantInfo` is never constructed
   --> src/service/tenant/startup_schema_service.rs:267:8
    |
267 | struct TenantInfo {
    |        ^^^^^^^^^^

warning: struct `StartupSchemaInitializer` is never constructed
   --> src/service/tenant/startup_schema_service.rs:273:12
    |
273 | pub struct StartupSchemaInitializer;
    |            ^^^^^^^^^^^^^^^^^^^^^^^^

warning: associated function `initialize_for_compilation` is never used
   --> src/service/tenant/startup_schema_service.rs:277:18
    |
275 | impl StartupSchemaInitializer {
    | ----------------------------- associated function in this implementation
276 |     /// 在程序启动时调用，确保编译时需要的 schema 存在
277 |     pub async fn initialize_for_compilation(pool: &PgPool) -> Result<()> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `search_teacher` is never used
 --> src/service/tenant/member.rs:9:14
  |
9 | pub async fn search_teacher(
  |              ^^^^^^^^^^^^^^

warning: struct `CompileSafeQueryService` is never constructed
  --> src/service/tenant/mod.rs:12:16
   |
12 |     pub struct CompileSafeQueryService;
   |                ^^^^^^^^^^^^^^^^^^^^^^^

warning: struct `StudentRow` is never constructed
  --> src/service/tenant/mod.rs:13:16
   |
13 |     pub struct StudentRow;
   |                ^^^^^^^^^^

warning: associated function `new` is never used
  --> src/service/tenant/mod.rs:16:16
   |
15 |     impl CompileSafeQueryService {
   |     ---------------------------- associated function in this implementation
16 |         pub fn new() -> Self {
   |                ^^^

warning: struct `DynamicQueryService` is never constructed
  --> src/service/tenant/mod.rs:25:16
   |
25 |     pub struct DynamicQueryService;
   |                ^^^^^^^^^^^^^^^^^^^

warning: associated function `new` is never used
  --> src/service/tenant/mod.rs:28:16
   |
27 |     impl DynamicQueryService {
   |     ------------------------ associated function in this implementation
28 |         pub fn new() -> Self {
   |                ^^^

warning: fields `id`, `username`, and `created_at` are never read
  --> src/service/user/user_service.rs:80:13
   |
79 |         struct UserRecord {
   |                ---------- fields in this struct
80 |             id: Uuid,
   |             ^^
81 |             username: String,
   |             ^^^^^^^^
82 |             created_at: DateTime<Utc>,
   |             ^^^^^^^^^^

warning: method `check_username_exists` is never used
   --> src/service/auth/auth_service.rs:491:14
    |
23  | impl AuthService {
    | ---------------- method in this implementation
...
491 |     async fn check_username_exists(&self, username: &str) -> AuthResult<bool> {
    |              ^^^^^^^^^^^^^^^^^^^^^

warning: field `pool` is never read
  --> src/service/classes/classes_service.rs:22:5
   |
21 | pub struct ClassesService {
   |            -------------- field in this struct
22 |     pool: PgPool,
   |     ^^^^
   |
   = note: `ClassesService` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis

warning: multiple fields are never read
   --> src/service/education_stage/education_stage_service.rs:457:9
    |
456 | pub struct PageEducationStageParams {
    |            ------------------------ fields in this struct
457 |     pub page: i32,
    |         ^^^^
458 |     pub page_size: i32,
    |         ^^^^^^^^^
459 |     pub name_or_code_ilike: Option<String>,
    |         ^^^^^^^^^^^^^^^^^^
460 |     pub is_active: Option<bool>,
    |         ^^^^^^^^^
461 |     pub is_standard: Option<bool>,
    |         ^^^^^^^^^^^
462 |     pub order_by: Option<String>, // 排序字段：name, code, order_level, created_at
    |         ^^^^^^^^
463 |     pub order_direction: Option<String>, // 排序方向：asc, desc
    |         ^^^^^^^^^^^^^^^
    |
    = note: `PageEducationStageParams` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: multiple methods are never used
   --> src/service/permission/casbin_service.rs:71:14
    |
66  | pub trait CasbinPermissionService: Send + Sync {
    |           ----------------------- methods in this trait
...
71  |     async fn batch_enforce(&self, requests: &[PermissionRequest]) -> Result<Vec<bool>>;
    |              ^^^^^^^^^^^^^
...
74  |     async fn add_policy(&self, policy: &PermissionPolicy) -> Result<bool>;
    |              ^^^^^^^^^^
...
77  |     async fn remove_policy(&self, policy: &PermissionPolicy) -> Result<bool>;
    |              ^^^^^^^^^^^^^
...
80  |     async fn add_role(&self, relation: &RoleRelation) -> Result<bool>;
    |              ^^^^^^^^
...
83  |     async fn remove_role(&self, relation: &RoleRelation) -> Result<bool>;
    |              ^^^^^^^^^^^
...
86  |     async fn get_roles_for_user(&self, user: &str, domain: &str) -> Result<Vec<String>>;
    |              ^^^^^^^^^^^^^^^^^^
...
98  |     async fn sync_permissions(&self, tenant_id: &str) -> Result<()>;
    |              ^^^^^^^^^^^^^^^^
...
101 |     async fn clear_tenant_policies(&self, tenant_id: &str) -> Result<()>;
    |              ^^^^^^^^^^^^^^^^^^^^^

warning: associated function `build_user_identity` is never used
   --> src/service/permission/casbin_service.rs:446:8
    |
116 | impl MultiTenantCasbinService {
    | ----------------------------- associated function in this implementation
...
446 |     fn build_user_identity(user_id: &Uuid, role_code: &str, target_type: &str, target_id: Option<&Uuid>) -> String {
    |        ^^^^^^^^^^^^^^^^^^^

warning: struct `RolePermissionSyncService` is never constructed
  --> src/service/permission/role_sync_service.rs:54:12
   |
54 | pub struct RolePermissionSyncService {
   |            ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: multiple associated items are never used
   --> src/service/permission/role_sync_service.rs:61:12
    |
59  | impl RolePermissionSyncService {
    | ------------------------------ associated items in this implementation
60  |     /// 创建新的同步服务实例
61  |     pub fn new(pool: PgPool, casbin_service: Arc<MultiTenantCasbinService>) -> Self {
    |            ^^^
...
69  |     pub async fn sync_all_tenants(&self, config: &SyncConfig) -> Result<Vec<SyncResult>> {
    |                  ^^^^^^^^^^^^^^^^
...
104 |     pub async fn sync_tenant_permissions(&self, tenant_id: &str, config: &SyncConfig) -> Result<SyncResult> {
    |                  ^^^^^^^^^^^^^^^^^^^^^^^
...
162 |     async fn sync_system_roles(
    |              ^^^^^^^^^^^^^^^^^
...
201 |     async fn sync_tenant_roles(
    |              ^^^^^^^^^^^^^^^^^
...
235 |     async fn sync_user_identities(
    |              ^^^^^^^^^^^^^^^^^^^^
...
321 |     async fn sync_role_permissions(
    |              ^^^^^^^^^^^^^^^^^^^^^
...
380 |     async fn create_role_inheritance_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
420 |     async fn add_inheritance_policy(&self, child_role: &str, parent_role: &str, tenant_id: &str, config: &SyncConfig) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^
...
434 |     async fn create_basic_role_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
504 |     async fn create_tenant_role_policies(&self, role: &Role, tenant_id: &str, config: &SyncConfig) -> Result<()> {
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
512 |     async fn create_user_data_policies(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^
...
611 |     fn build_user_identity(&self, user_id: &Uuid, role_code: &str, target_type: &str, target_id: Option<&Uuid>) -> String {
    |        ^^^^^^^^^^^^^^^^^^^
...
619 |     async fn get_all_tenants(&self) -> Result<Vec<TenantInfo>> {
    |              ^^^^^^^^^^^^^^^

warning: struct `TenantInfo` is never constructed
   --> src/service/permission/role_sync_service.rs:639:8
    |
639 | struct TenantInfo {
    |        ^^^^^^^^^^

warning: field `id` is never read
  --> src/service/permission/postgres_adapter.rs:26:9
   |
25 | pub struct CasbinRule {
   |            ---------- field in this struct
26 |     pub id: Option<i32>,
   |         ^^
   |
   = note: `CasbinRule` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: fields `template_id` and `variables` are never read
  --> src/service/sms/sms_service.rs:25:9
   |
24 | pub struct SmsTemplate {
   |            ----------- fields in this struct
25 |     pub template_id: String,
   |         ^^^^^^^^^^^
26 |     pub content: String,
27 |     pub variables: Vec<String>,
   |         ^^^^^^^^^
   |
   = note: `SmsTemplate` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: associated functions `new`, `with_data`, and `without_data` are never used
   --> src/utils/api_response.rs:106:12
    |
104 | impl<T> SuccessResponse<T> {
    | -------------------------- associated functions in this implementation
105 |     /// 创建成功响应
106 |     pub fn new(data: Option<T>, message: String) -> Self {
    |            ^^^
...
115 |     pub fn with_data(data: T, message: Option<String>) -> Self {
    |            ^^^^^^^^^
...
124 |     pub fn without_data(message: Option<String>) -> Self {
    |            ^^^^^^^^^^^^

warning: associated functions `new` and `simple` are never used
   --> src/utils/api_response.rs:143:12
    |
141 | impl ErrorResponse {
    | ------------------ associated functions in this implementation
142 |     /// 创建错误响应
143 |     pub fn new(message: String, error_code: Option<String>) -> Self {
    |            ^^^
...
152 |     pub fn simple(message: String) -> Self {
    |            ^^^^^^

warning: type alias `ApiResult` is never used
   --> src/utils/api_response.rs:341:10
    |
341 | pub type ApiResult<T> = Result<ApiResponse<T>, AppError>;
    |          ^^^^^^^^^

warning: type alias `PaginatedApiResult` is never used
   --> src/utils/api_response.rs:342:10
    |
342 | pub type PaginatedApiResult<T> = Result<PaginatedApiResponse<T>, AppError>;
    |          ^^^^^^^^^^^^^^^^^^

warning: static `SCHEMA_NAME_REGEX` is never used
 --> src/utils/db.rs:8:8
  |
8 | static SCHEMA_NAME_REGEX: Lazy<Regex> = Lazy::new(|| Regex::new(r"^[a-z0-9_]+$").unwrap());
  |        ^^^^^^^^^^^^^^^^^

warning: struct `DynamicSchemaQuery` is never constructed
  --> src/utils/db.rs:11:12
   |
11 | pub struct DynamicSchemaQuery {
   |            ^^^^^^^^^^^^^^^^^^

warning: associated items `new`, `query_in_schema`, and `with_schema_transaction` are never used
  --> src/utils/db.rs:18:12
   |
16 | impl DynamicSchemaQuery {
   | ----------------------- associated items in this implementation
17 |     /// 创建新的动态 schema 查询构建器
18 |     pub fn new(pool: PgPool, schema_name: String) -> Result<Self> {
   |            ^^^
...
28 |     pub async fn query_in_schema<T>(&self, sql: &str) -> Result<Vec<T>>
   |                  ^^^^^^^^^^^^^^^
...
40 |     pub async fn with_schema_transaction<F, R, Fut>(&self, f: F) -> Result<R>
   |                  ^^^^^^^^^^^^^^^^^^^^^^^

warning: function `get_tenant_schema` is never used
  --> src/utils/db.rs:63:14
   |
63 | pub async fn get_tenant_schema(pool: &PgPool, tenant_id: Uuid) -> Result<String> {
   |              ^^^^^^^^^^^^^^^^^

warning: function `build_table_name` is never used
  --> src/utils/db.rs:86:8
   |
86 | pub fn build_table_name(schema_name: &str, table_name: &str) -> Result<String> {
   |        ^^^^^^^^^^^^^^^^

warning: struct `ErrorHandler` is never constructed
  --> src/utils/error_handler.rs:17:12
   |
17 | pub struct ErrorHandler;
   |            ^^^^^^^^^^^^

warning: associated functions `handle_json_rejection`, `log_and_respond`, and `log_and_respond_api` are never used
  --> src/utils/error_handler.rs:21:12
   |
19 | impl ErrorHandler {
   | ----------------- associated functions in this implementation
20 |     /// 处理 JSON 解析错误
21 |     pub fn handle_json_rejection(rejection: JsonRejection) -> AppError {
   |            ^^^^^^^^^^^^^^^^^^^^^
...
47 |     pub fn log_and_respond(error: &AppError, context: &str) -> Response {
   |            ^^^^^^^^^^^^^^^
...
66 |     pub fn log_and_respond_api<T>(error: &AppError, context: &str) -> ApiResponse<T> {
   |            ^^^^^^^^^^^^^^^^^^^

warning: methods `into_app_error_with_context`, `into_db_error`, and `into_validation_error` are never used
  --> src/utils/error_handler.rs:86:8
   |
84 | pub trait AnyhowResultExt<T> {
   |           --------------- methods in this trait
85 |     /// 将 anyhow::Result 转换为 AppResult，并添加上下文
86 |     fn into_app_error_with_context(self, context: &str) -> AppResult<T>;
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
89 |     fn into_db_error(self, operation: &str) -> AppResult<T>;
   |        ^^^^^^^^^^^^^
...
92 |     fn into_validation_error(self, field: &str) -> AppResult<T>;
   |        ^^^^^^^^^^^^^^^^^^^^^

warning: methods `with_db_context` and `ok_or_not_found` are never used
   --> src/utils/error_handler.rs:117:8
    |
115 | pub trait SqlxResultExt<T> {
    |           ------------- methods in this trait
116 |     /// 将 sqlx::Result 转换为 AppResult，并添加操作上下文
117 |     fn with_db_context(self, operation: &str) -> AppResult<T>;
    |        ^^^^^^^^^^^^^^^
...
120 |     fn ok_or_not_found(self, resource: &str) -> AppResult<T>
    |        ^^^^^^^^^^^^^^^

warning: function `validation_error` is never used
   --> src/utils/error_handler.rs:172:12
    |
172 |     pub fn validation_error(message: impl Into<String>) -> AppError {
    |            ^^^^^^^^^^^^^^^^

warning: function `not_found` is never used
   --> src/utils/error_handler.rs:176:12
    |
176 |     pub fn not_found(resource: impl Into<String>) -> AppError {
    |            ^^^^^^^^^

warning: function `forbidden` is never used
   --> src/utils/error_handler.rs:180:12
    |
180 |     pub fn forbidden(message: impl Into<String>) -> AppError {
    |            ^^^^^^^^^

warning: function `conflict` is never used
   --> src/utils/error_handler.rs:184:12
    |
184 |     pub fn conflict(message: impl Into<String>) -> AppError {
    |            ^^^^^^^^

warning: function `bad_request` is never used
   --> src/utils/error_handler.rs:188:12
    |
188 |     pub fn bad_request(message: impl Into<String>) -> AppError {
    |            ^^^^^^^^^^^

warning: function `internal_error` is never used
   --> src/utils/error_handler.rs:192:12
    |
192 |     pub fn internal_error(message: impl Into<String>) -> AppError {
    |            ^^^^^^^^^^^^^^

warning: function `generate_base_token_with_username` is never used
  --> src/utils/jwt.rs:36:8
   |
36 | pub fn generate_base_token_with_username(
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `generate_tenant_token` is never used
  --> src/utils/jwt.rs:69:8
   |
69 | pub fn generate_tenant_token(user_id: Uuid, tenant_context: TenantContext) -> AuthResult<String> {
   |        ^^^^^^^^^^^^^^^^^^^^^

warning: fields `phone_number`, `created_at`, `phone_verified`, and `is_active` are never read
  --> src/middleware/auth_middleware.rs:40:9
   |
35 | pub struct AuthContext {
   |            ----------- fields in this struct
...
40 |     pub phone_number: String,
   |         ^^^^^^^^^^^^
41 |     pub created_at: Option<DateTime<Utc>>,
   |         ^^^^^^^^^^
42 |     pub phone_verified: Option<bool>,
   |         ^^^^^^^^^^^^^^
43 |     pub is_active: Option<bool>,
   |         ^^^^^^^^^
   |
   = note: `AuthContext` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: methods `is_teacher_in_tenant`, `is_student_in_tenant`, and `get_roles_for_tenant` are never used
   --> src/middleware/auth_middleware.rs:82:12
    |
46  | impl AuthContext {
    | ---------------- methods in this implementation
...
82  |     pub fn is_teacher_in_tenant(&self, tenant_id: Option<Uuid>) -> bool {
    |            ^^^^^^^^^^^^^^^^^^^^
...
90  |     pub fn is_student_in_tenant(&self, tenant_id: Option<Uuid>) -> bool {
    |            ^^^^^^^^^^^^^^^^^^^^
...
126 |     pub fn get_roles_for_tenant(&self, tenant_id: Uuid) -> Vec<&UserRole> {
    |            ^^^^^^^^^^^^^^^^^^^^

warning: function `role_auth_middleware` is never used
   --> src/middleware/auth_middleware.rs:136:14
    |
136 | pub async fn role_auth_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^

warning: function `get_auth_context` is never used
   --> src/middleware/auth_middleware.rs:316:8
    |
316 | pub fn get_auth_context(request: &Request) -> Result<&AuthContext, AppError> {
    |        ^^^^^^^^^^^^^^^^

warning: function `check_role_middleware` is never used
   --> src/middleware/auth_middleware.rs:359:14
    |
359 | pub async fn check_role_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^

warning: function `require_admin_middleware` is never used
   --> src/middleware/auth_middleware.rs:379:14
    |
379 | pub async fn require_admin_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `require_teacher_middleware` is never used
   --> src/middleware/auth_middleware.rs:398:14
    |
398 | pub async fn require_teacher_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `require_student_middleware` is never used
   --> src/middleware/auth_middleware.rs:415:14
    |
415 | pub async fn require_student_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `require_any_role_middleware` is never used
   --> src/middleware/auth_middleware.rs:433:14
    |
433 | pub async fn require_any_role_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `require_tenant_role_middleware` is never used
   --> src/middleware/auth_middleware.rs:455:14
    |
455 | pub async fn require_tenant_role_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: field `tenant_name` is never read
  --> src/middleware/tenant_middleware.rs:17:9
   |
14 | pub struct TenantContext {
   |            ------------- field in this struct
...
17 |     pub tenant_name: String,
   |         ^^^^^^^^^^^
   |
   = note: `TenantContext` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: function `tenant_identification_middleware` is never used
  --> src/middleware/tenant_middleware.rs:21:14
   |
21 | pub async fn tenant_identification_middleware(
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_tenant_context` is never used
  --> src/middleware/tenant_middleware.rs:41:10
   |
41 | async fn extract_tenant_context(
   |          ^^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_subdomain` is never used
   --> src/middleware/tenant_middleware.rs:106:4
    |
106 | fn extract_subdomain(host: &str) -> Option<String> {
    |    ^^^^^^^^^^^^^^^^^

warning: fields `skip_system_admin`, `log_permission_checks`, `enable_caching`, and `cache_ttl_seconds` are never read
  --> src/middleware/permission_middleware.rs:25:9
   |
24 | pub struct PermissionConfig {
   |            ---------------- fields in this struct
25 |     pub skip_system_admin: bool,      // 是否跳过系统管理员权限检查
   |         ^^^^^^^^^^^^^^^^^
26 |     pub log_permission_checks: bool,  // 是否记录权限检查日志
   |         ^^^^^^^^^^^^^^^^^^^^^
27 |     pub enable_caching: bool,         // 是否启用权限检查缓存
   |         ^^^^^^^^^^^^^^
28 |     pub cache_ttl_seconds: u64,       // 缓存TTL
   |         ^^^^^^^^^^^^^^^^^
   |
   = note: `PermissionConfig` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: fields `user_id`, `tenant_id`, `user_identity`, `roles`, and `is_system_admin` are never read
  --> src/middleware/permission_middleware.rs:54:9
   |
53 | pub struct UserContext {
   |            ----------- fields in this struct
54 |     pub user_id: Uuid,
   |         ^^^^^^^
55 |     pub tenant_id: String,
   |         ^^^^^^^^^
56 |     pub user_identity: String,  // user_id:role:target_type:target_id
   |         ^^^^^^^^^^^^^
57 |     pub roles: Vec<String>,
   |         ^^^^^
58 |     pub is_system_admin: bool,
   |         ^^^^^^^^^^^^^^^
   |
   = note: `UserContext` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: associated items `from_auth_context` and `identity_string` are never used
  --> src/middleware/permission_middleware.rs:63:12
   |
61 | impl UserContext {
   | ---------------- associated items in this implementation
62 |     /// 从认证上下文构建用户上下文
63 |     pub fn from_auth_context(auth_context: &AuthContext, tenant_id: &str) -> Result<Self> {
   |            ^^^^^^^^^^^^^^^^^
...
96 |     pub fn identity_string(&self) -> String {
   |            ^^^^^^^^^^^^^^^

warning: function `permission_middleware` is never used
   --> src/middleware/permission_middleware.rs:103:14
    |
103 | pub async fn permission_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_resource_and_action` is never used
   --> src/middleware/permission_middleware.rs:185:4
    |
185 | fn extract_resource_and_action(request: &Request) -> Result<(String, String), StatusCode> {
    |    ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_resource_from_path` is never used
   --> src/middleware/permission_middleware.rs:197:4
    |
197 | fn extract_resource_from_path(path: &str) -> String {
    |    ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `http_method_to_action` is never used
   --> src/middleware/permission_middleware.rs:231:4
    |
231 | fn http_method_to_action(method: &Method) -> String {
    |    ^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_tenant_id` is never used
   --> src/middleware/permission_middleware.rs:242:4
    |
242 | fn extract_tenant_id(request: &Request) -> Result<String, StatusCode> {
    |    ^^^^^^^^^^^^^^^^^

warning: function `extract_tenant_from_path` is never used
   --> src/middleware/permission_middleware.rs:271:4
    |
271 | fn extract_tenant_from_path(path: &str) -> Option<String> {
    |    ^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `check_permission_with_caching` is never used
   --> src/middleware/permission_middleware.rs:282:10
    |
282 | async fn check_permission_with_caching(
    |          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `require_permission` is never used
   --> src/middleware/permission_middleware.rs:313:8
    |
313 | pub fn require_permission(resource: String, action: String) -> impl Fn(Request, Next) -> std::pin::Pin<Box<dyn std::future::Future<Output...
    |        ^^^^^^^^^^^^^^^^^^

warning: function `menu_permission_middleware` is never used
   --> src/middleware/permission_middleware.rs:341:14
    |
341 | pub async fn menu_permission_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_menu_id_from_path` is never used
   --> src/middleware/permission_middleware.rs:384:4
    |
384 | fn extract_menu_id_from_path(path: &str) -> Option<String> {
    |    ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `data_permission_middleware` is never used
   --> src/middleware/permission_middleware.rs:399:14
    |
399 | pub async fn data_permission_middleware(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: field `0` is never read
   --> src/middleware/permission_middleware.rs:430:33
    |
430 | pub struct UserContextExtractor(pub UserContext);
    |            -------------------- ^^^^^^^^^^^^^^^
    |            |
    |            field in this struct
    |
    = help: consider removing this field

warning: field `0` is never read
   --> src/middleware/permission_middleware.rs:452:38
    |
452 | pub struct PermissionResultExtractor(pub PermissionCheckResult);
    |            ------------------------- ^^^^^^^^^^^^^^^^^^^^^^^^^
    |            |
    |            field in this struct
    |
    = help: consider removing this field

warning: unused implementer of `std::future::Future` that must be used
   --> src/service/permission/casbin_service.rs:514:9
    |
514 |         self.clear_tenant_cache(tenant_id);
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = note: futures do nothing unless you `.await` or poll them
    = note: `#[warn(unused_must_use)]` on by default

warning: unused `std::result::Result` that must be used
   --> src/service/permission/casbin_service.rs:757:9
    |
757 |         new_enforcer.clear_policy().await;
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |
    = note: this `Result` may be an `Err` variant, which should be handled
help: use `let _ = ...` to ignore the resulting value
    |
757 |         let _ = new_enforcer.clear_policy().await;
    |         +++++++

warning: `backend` (bin "backend") generated 350 warnings (5 duplicates) (run `cargo fix --bin "backend"` to apply 91 suggestions)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 0.71s
     Running `target/debug/backend`
[2m2025-08-01T07:37:53.823105Z[0m [32m INFO[0m 🚀 Starting Deep-Mate Backend Server
[2m2025-08-01T07:37:53.829561Z[0m [32m INFO[0m 📊 Connecting to database...
[2m2025-08-01T07:38:00.322133Z[0m [33m WARN[0m acquired connection, but time to acquire exceeded slow threshold [3maquired_after_secs[0m[2m=[0m6.480321534 [3mslow_acquire_threshold_secs[0m[2m=[0m2.0
[2m2025-08-01T07:38:00.322603Z[0m [32m INFO[0m ✅ Database connection established
[2m2025-08-01T07:38:00.322675Z[0m [32m INFO[0m 🔄 Running database migrations...
[2m2025-08-01T07:38:00.329934Z[0m [32m INFO[0m relation "_sqlx_migrations" already exists, skipping
[2m2025-08-01T07:38:00.343315Z[0m [32m INFO[0m ✅ Database migrations completed
[2m2025-08-01T07:38:00.349614Z[0m [32m INFO[0m 🔐 Initializing authentication services...
[2m2025-08-01T07:38:00.370293Z[0m [32m INFO[0m ✅ Authentication services initialized
[2m2025-08-01T07:38:00.401795Z[0m [32m INFO[0m 📋 Schema config loaded: warmup_strategy=Lazy, auto_create=true
[2m2025-08-01T07:38:00.401832Z[0m [32m INFO[0m 📁 Initializing storage service...
[2m2025-08-01T07:38:00.572149Z[0m [32m INFO[0m ✅ Storage service initialized
[2m2025-08-01T07:38:00.572827Z[0m [32m INFO[0m 🔐 Initializing Casbin permission service...
[2m2025-08-01T07:38:00.575043Z[0m [32m INFO[0m ✅ Casbin permission service initialized
[2m2025-08-01T07:38:00.595572Z[0m [32m INFO[0m 🌐 Server starting on http://0.0.0.0:3000
[2m2025-08-01T07:38:00.596143Z[0m [32m INFO[0m ✅ Deep-Mate Backend Server is running on http://0.0.0.0:3000
[2m2025-08-01T07:38:00.596179Z[0m [32m INFO[0m 📚 API Documentation available at http://0.0.0.0:3000/docs
[2m2025-08-01T07:38:54.209217Z[0m [32m INFO[0m Login attempt with phone: *** from IP: None, User-Agent: Some("curl/8.7.1")
[2m2025-08-01T07:38:54.212595Z[0m [31mERROR[0m Login failed: InvalidPhoneNumber
[2m2025-08-01T07:39:13.909596Z[0m [32m INFO[0m Login attempt with phone: 138****0000 from IP: None, User-Agent: Some("curl/8.7.1")
[2m2025-08-01T07:39:13.913126Z[0m [31mERROR[0m Login failed: UserNotFound
[2m2025-08-01T07:40:07.550125Z[0m [31mERROR[0m Failed to fetch policies: error returned from database: bind message supplies 0 parameters, but prepared statement "sqlx_s_4" requires 1
