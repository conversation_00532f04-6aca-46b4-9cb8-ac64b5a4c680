-- 数据范围权限配置
-- 用于定义基于角色的数据访问范围权限

-- 班主任权限：可以访问自己负责班级的学生数据
-- 格式：p, role:class_teacher, tenant_id, student:class:*, read, allow
-- 说明：class_teacher角色可以读取student资源中class范围的所有数据

-- 年级主任权限：可以访问整个年级的学生数据
-- 格式：p, role:grade_director, tenant_id, student:grade:*, read, allow
-- 说明：grade_director角色可以读取student资源中grade范围的所有数据

-- 学科组长权限：可以访问学科组内的学生数据
-- 格式：p, role:subject_leader, tenant_id, student:subject_group:*, read, allow
-- 说明：subject_leader角色可以读取student资源中subject_group范围的所有数据

-- 校长权限：可以访问整个学校的数据
-- 格式：p, role:principal, tenant_id, student:school:*, manage, allow
-- 说明：principal角色可以管理student资源中school范围的所有数据

-- 系统管理员权限：可以访问所有数据
-- 格式：p, role:system_admin, tenant_id, *:*:*, manage, allow
-- 说明：system_admin角色可以管理所有资源的所有范围数据

-- 示例权限策略插入语句（需要根据实际租户ID调整）
/*
-- 为班主任角色添加学生数据的班级范围读取权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
VALUES ('p', 'role:class_teacher', '{tenant_id}', 'student:class:*', 'read', 'allow', '', '{tenant_id}', NOW(), NOW());

-- 为班主任角色添加行政班数据的读取权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
VALUES ('p', 'role:class_teacher', '{tenant_id}', 'administrative_class:class:*', 'read', 'allow', '', '{tenant_id}', NOW(), NOW());

-- 为年级主任角色添加学生数据的年级范围读取权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
VALUES ('p', 'role:grade_director', '{tenant_id}', 'student:grade:*', 'read', 'allow', '', '{tenant_id}', NOW(), NOW());

-- 为学科组长角色添加学生数据的学科组范围读取权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
VALUES ('p', 'role:subject_leader', '{tenant_id}', 'student:subject_group:*', 'read', 'allow', '', '{tenant_id}', NOW(), NOW());

-- 为校长角色添加学生数据的学校范围管理权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
VALUES ('p', 'role:principal', '{tenant_id}', 'student:school:*', 'manage', 'allow', '', '{tenant_id}', NOW(), NOW());

-- 为系统管理员角色添加所有数据的管理权限
INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
VALUES ('p', 'role:system_admin', '{tenant_id}', '*:*:*', 'manage', 'allow', '', '{tenant_id}', NOW(), NOW());
*/

-- 数据范围权限说明文档
/*
数据范围权限格式说明：
1. 权限对象格式：{resource}:{scope_type}:{scope_value}
   - resource: 资源类型（student, administrative_class, grade, exam等）
   - scope_type: 范围类型（class, grade, subject_group, school等）
   - scope_value: 范围值（具体的ID或*表示所有）

2. 常用范围类型：
   - class: 班级范围（通过administrative_class_id关联）
   - grade: 年级范围（通过grade_level_code关联）
   - subject_group: 学科组范围（通过学科组ID关联）
   - school: 学校范围（租户级别）

3. 权限动作：
   - read: 读取权限
   - write: 写入权限
   - create: 创建权限
   - delete: 删除权限
   - manage: 管理权限（包含所有操作）

4. 权限效果：
   - allow: 允许
   - deny: 拒绝

5. 权限继承：
   - 高级别权限自动包含低级别权限
   - school > grade > class
   - manage > create/write/delete > read

6. 特殊权限：
   - *:*:* 表示所有资源的所有范围
   - student:*:* 表示学生资源的所有范围
   - student:class:* 表示学生资源的所有班级范围

使用示例：
1. 班主任查看自己班级的学生：
   - 权限：student:class:*
   - 实际过滤：WHERE administrative_class_id IN (班主任负责的班级ID列表)

2. 年级主任查看年级内所有学生：
   - 权限：student:grade:*
   - 实际过滤：WHERE grade_level_code IN (年级主任负责的年级编码列表)

3. 学科组长查看学科组内学生：
   - 权限：student:subject_group:*
   - 实际过滤：通过教学班关联查询学科组内的学生

4. 校长查看全校学生：
   - 权限：student:school:*
   - 实际过滤：无额外过滤（租户隔离已处理）
*/

-- 权限同步脚本模板
/*
-- 同步班主任的具体班级权限
-- 需要根据实际的教师-班级关系动态生成
DO $$
DECLARE
    teacher_record RECORD;
    tenant_id_var TEXT;
BEGIN
    -- 获取租户ID
    SELECT id::text INTO tenant_id_var FROM public.tenants WHERE schema_name = '{schema_name}';
    
    -- 为每个班主任添加具体的班级权限
    FOR teacher_record IN 
        SELECT t.user_id, ac.id as class_id
        FROM {schema_name}.teachers t
        JOIN {schema_name}.administrative_classes ac ON t.id = ac.teacher_id
        WHERE t.is_active = true AND ac.is_active = true
    LOOP
        -- 添加具体班级的学生读取权限
        INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
        VALUES ('p', 
                format('user:%s', teacher_record.user_id), 
                tenant_id_var, 
                format('student:class:%s', teacher_record.class_id), 
                'read', 
                'allow', 
                '', 
                tenant_id_var, 
                NOW(), 
                NOW())
        ON CONFLICT DO NOTHING;
        
        -- 添加具体班级的行政班读取权限
        INSERT INTO public.casbin_policies (ptype, v0, v1, v2, v3, v4, v5, tenant_id, created_at, updated_at)
        VALUES ('p', 
                format('user:%s', teacher_record.user_id), 
                tenant_id_var, 
                format('administrative_class:class:%s', teacher_record.class_id), 
                'read', 
                'allow', 
                '', 
                tenant_id_var, 
                NOW(), 
                NOW())
        ON CONFLICT DO NOTHING;
    END LOOP;
END $$;
*/
