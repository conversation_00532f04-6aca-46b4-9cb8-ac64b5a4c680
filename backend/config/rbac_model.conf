# Deep-Mate 多租户 RBAC 权限模型
# 基于 Casbin RBAC 模型，支持多租户、多身份、细粒度数据权限控制

[request_definition]
# 请求定义: subject(主体), domain(域/租户), object(对象), action(动作)
r = sub, dom, obj, act

[policy_definition]
# 策略定义: subject(主体), domain(域/租户), object(对象), action(动作), effect(效果)
p = sub, dom, obj, act, eft

[role_definition]
# 角色继承关系定义
# g: 用户-角色关系 (user, role, domain)
# g2: 角色继承关系 (role1, role2, domain) - 角色级别继承
g = _, _, _
g2 = _, _, _

[policy_effect]
# 策略效果: 允许策略存在且无拒绝策略时允许访问
e = some(where (p.eft == allow)) && !some(where (p.eft == deny))

[matchers]
# 匹配器: 检查用户在指定域内是否有权限访问指定资源
m = g(r.sub, p.sub, r.dom) && r.dom == p.dom && keyMatch2(r.obj, p.obj) && regexMatch(r.act, p.act)

# 模型说明:
# ============================================================================
# 
# 1. 主体标识 (sub):
#    格式: user_id:identity_type:target_id
#    示例: 
#    - user_123:teacher:class_001  (用户123作为教师管理班级001)
#    - user_456:student:self       (用户456作为学生访问自己的数据)
#    - user_789:principal:school   (用户789作为校长管理整个学校)
#
# 2. 域标识 (dom):
#    格式: tenant_id
#    示例: tenant_001, tenant_002
#    说明: 实现租户间完全隔离，不同租户的权限策略互不影响
#
# 3. 对象标识 (obj):
#    格式: resource:scope 或 resource:scope:target_id
#    示例:
#    - student:*                   (所有学生数据)
#    - student:class:class_001     (班级001的学生数据) 
#    - exam:*                      (所有考试数据)
#    - exam:subject:math           (数学学科的考试数据)
#    - grade:self                  (自己的成绩数据)
#    - menu:student_management     (学生管理菜单)
#
# 4. 动作标识 (act):
#    - read                        (读取权限)
#    - write                       (写入权限)  
#    - create                      (创建权限)
#    - delete                      (删除权限)
#    - manage                      (管理权限，包含所有操作)
#    - view                        (查看权限，仅限前端展示)
#
# 5. 效果标识 (eft):
#    - allow                       (允许)
#    - deny                        (拒绝，用于特殊限制场景)
#
# 使用示例:
# ============================================================================
#
# 用户角色关系:
# g, user_123:teacher:class_001, teacher, tenant_001
# g, user_456:student:self, student, tenant_001  
# g, user_789:principal:school, principal, tenant_001
#
# 角色继承关系:
# g2, teacher, staff, tenant_001
# g2, principal, admin, tenant_001
# g2, admin, staff, tenant_001
#
# 权限策略:
# p, teacher, tenant_001, student:class:*, read, allow
# p, teacher, tenant_001, exam:class:*, write, allow
# p, principal, tenant_001, *, manage, allow
# p, student, tenant_001, grade:self, read, allow
# p, student, tenant_001, menu:personal_center, view, allow
#
# 特殊拒绝策略(可选):
# p, teacher, tenant_001, system:config, *, deny
#
# 权限检查示例:
# ============================================================================
# 
# 检查教师是否可以查看班级学生信息:
# enforce("user_123:teacher:class_001", "tenant_001", "student:class:class_001", "read")
# -> true
#
# 检查学生是否可以查看自己成绩:  
# enforce("user_456:student:self", "tenant_001", "grade:self", "read")
# -> true
#
# 检查普通教师是否可以管理系统配置:
# enforce("user_123:teacher:class_001", "tenant_001", "system:config", "manage")  
# -> false (被拒绝策略阻止)