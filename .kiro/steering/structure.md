# Project Structure

## Root Directory
```
deep-mate/
├── backend/           # Rust backend application
├── frontend/          # React frontend application  
├── docs/             # Project documentation
├── .kiro/            # Kiro AI assistant configuration
└── validation_report.md
```

## Backend Structure (`backend/`)
```
backend/
├── src/
│   ├── controller/   # HTTP request handlers
│   │   ├── public/   # Public API endpoints (auth, registration)
│   │   └── tenant/   # Tenant-specific endpoints
│   ├── service/      # Business logic layer
│   │   ├── public/   # Public services (user management)
│   │   └── tenant/   # Tenant services (member, tenant management)
│   ├── model/        # Data models and database entities
│   ├── utils/        # Utility functions (JWT, password hashing)
│   ├── main.rs       # Application entry point
│   └── web_server.rs # Server configuration and routing
├── migrations/       # Database migration files
├── tenants/         # Tenant-specific configurations
│   └── template/    # Template for new tenant schemas
├── Cargo.toml       # Rust dependencies and metadata
└── .env             # Environment configuration
```

## Frontend Structure (`frontend/`)
```
frontend/
├── src/
│   ├── components/   # Reusable UI components
│   │   └── ui/       # shadcn/ui component library
│   ├── pages/        # Page components organized by feature
│   │   ├── ExamManagement/
│   │   ├── GradingCenter/
│   │   ├── Statistics/
│   │   ├── TeachingAids/
│   │   └── Tenant/   # Tenant management pages
│   ├── contexts/     # React contexts (AuthContext)
│   ├── hooks/        # Custom React hooks
│   ├── layouts/      # Layout components
│   ├── lib/          # Utility libraries
│   ├── router/       # Routing configuration
│   ├── types/        # TypeScript type definitions
│   ├── App.tsx       # Main application component
│   └── main.tsx      # Application entry point
├── public/           # Static assets
├── package.json      # Dependencies and scripts
├── vite.config.ts    # Vite build configuration
├── tailwind.config.cjs # Tailwind CSS configuration
└── components.json   # shadcn/ui configuration
```

## Documentation Structure (`docs/`)
```
docs/
├── PRD_modules/      # Product Requirements Document modules
│   ├── 1_project_overview.md
│   ├── 2_user_roles_and_permissions.md
│   ├── 3_core_architecture.md
│   ├── 4_0_paper_question_management.md
│   ├── 4_1_exam_management.md
│   ├── 4_2_grading_center.md
│   ├── 4_3_academic_analysis.md
│   ├── 4_4_teaching_aids_management.md
│   ├── 5_non_functional_requirements.md
│   └── 6_technical_architecture.md
└── tasks/            # Development task breakdown
    ├── 01-setup-project-and-auth.md
    ├── 02-tenant-and-organization-management.md
    └── ... (numbered task files)
```

## Key Conventions
- **Multi-tenant Architecture**: Public schema for shared data, tenant-specific schemas for isolated data
- **Feature-based Organization**: Both frontend pages and backend controllers organized by business features
- **Separation of Concerns**: Clear separation between controllers (HTTP), services (business logic), and models (data)
- **TypeScript Types**: Centralized type definitions in `frontend/src/types/`
- **Environment Configuration**: `.env` files for environment-specific settings
- **Migration-first Database**: All schema changes through SQLx migrations
- **Component Library**: Consistent UI through shadcn/ui components in `components/ui/`