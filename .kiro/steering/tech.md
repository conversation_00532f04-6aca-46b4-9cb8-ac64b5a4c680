# Technology Stack

## Backend
- **Language**: Rust
- **Web Framework**: Axum 0.8 with WebSocket and multipart support
- **Database**: PostgreSQL 18 with SQLx for async database operations
- **Authentication**: JWT tokens with Argon2 password hashing
- **Migration**: SQLx migrations for database schema management
- **Async Runtime**: Tokio with full feature set

## Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **UI Components**: shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS with custom animations
- **Routing**: React Router DOM v7
- **State Management**: Zustand
- **Forms**: React Hook Form with Zod validation
- **HTTP Client**: Axios
- **Icons**: Lucide React

## Development Tools
- **Linting**: ESLint with TypeScript support
- **Package Manager**: npm (frontend), Cargo (backend)
- **Environment**: dotenv for configuration

## Common Commands

### Backend (Rust)
```bash
# Development
cd backend
cargo run

# Build
cargo build --release

# Database migrations
sqlx migrate run

# Testing
cargo test
```

### Frontend (React)
```bash
# Development
cd frontend
npm run dev

# Build
npm run build

# Linting
npm run lint

# Type checking
npm run typecheck

# Preview build
npm run preview
```

## Architecture Patterns
- **Multi-tenant**: Schema-per-tenant isolation with shared public schema
- **RESTful APIs**: Standard HTTP methods with JSON responses
- **JWT Authentication**: Token-based auth with refresh mechanism
- **Database Migrations**: Version-controlled schema changes
- **Component-based UI**: Reusable React components with TypeScript