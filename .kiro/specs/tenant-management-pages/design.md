# Design Document

## Overview

The tenant management pages feature will provide a comprehensive web interface for managing tenants within the Deep-Mate multi-tenant exam management platform. The design leverages the existing multi-tenant architecture with schema-per-tenant isolation and integrates with the current React/TypeScript frontend and Rust/Axum backend stack.

The feature consists of two main interfaces:
1. **Platform Administrator Interface** - For managing all tenants across the platform
2. **Tenant Administrator Interface** - For managing individual tenant settings and users

## Architecture

### Frontend Architecture

The tenant management pages will extend the existing React 18 + TypeScript stack with shadcn/ui components, building upon the current `SchoolTenantList.tsx` and `SchoolManage.tsx` components.

```
frontend/src/pages/Tenant/
├── SchoolTenantList.tsx         # Existing: List all school tenants (enhanced)
├── SchoolManage.tsx             # Existing: Tenant management interface (enhanced)
├── TenantDashboard.tsx          # New: Tenant admin dashboard
├── TenantSettings.tsx           # New: Tenant configuration
├── UserManagement.tsx           # New: Manage tenant users
├── UsageStatistics.tsx          # New: Tenant usage metrics
└── components/
    ├── TenantCard.tsx           # Reusable tenant card component
    ├── TenantForm.tsx           # Enhanced tenant creation/edit form
    ├── UserInviteModal.tsx      # User invitation modal
    └── StatisticsChart.tsx      # Usage statistics charts
```

### Backend Architecture

The backend will extend the existing Rust/Axum architecture with new controllers and services for tenant management.

```
backend/src/
├── controller/
│   ├── public/
│   │   └── tenant_management.rs  # Platform admin endpoints
│   └── tenant/
│       └── tenant_admin.rs       # Tenant admin endpoints
├── service/
│   ├── public/
│   │   └── tenant_service.rs     # Tenant CRUD operations
│   └── tenant/
│       └── tenant_admin_service.rs # Tenant administration
└── model/
    ├── tenant_model.rs           # Tenant data models
    └── tenant_stats_model.rs     # Statistics models
```

### Database Integration

The design leverages the existing multi-tenant database architecture:

- **Public Schema**: Stores tenant metadata, user-tenant relationships, and global configurations
- **Tenant Schemas**: Individual tenant data remains isolated
- **New Tables**: Additional tables for tenant configuration and usage tracking

## Components and Interfaces

### Platform Administrator Interface

#### TenantList Component
- **Purpose**: Display paginated list of all tenants with search and filtering
- **Features**:
  - Search by tenant name, code, or domain
  - Filter by status (active, inactive, suspended)
  - Sort by creation date, user count, or usage metrics
  - Bulk operations (activate/deactivate multiple tenants)

#### TenantDetail Component
- **Purpose**: View and edit detailed tenant information
- **Features**:
  - Tenant basic information (name, code, domain, contact details)
  - Usage statistics (users, exams, storage)
  - Configuration settings (features enabled, limits)
  - User management within tenant
  - Audit log of tenant changes

#### CreateTenant Component
- **Purpose**: Create new tenant with schema initialization
- **Features**:
  - Multi-step wizard for tenant creation
  - Validation of tenant code uniqueness
  - Initial administrator account setup
  - Schema creation and default data seeding
  - Email invitation to tenant administrator

### Tenant Administrator Interface

#### TenantDashboard Component
- **Purpose**: Overview dashboard for tenant administrators
- **Features**:
  - Usage metrics and statistics
  - Recent activity feed
  - Quick actions (add users, create exams)
  - System announcements and updates

#### TenantSettings Component
- **Purpose**: Configure tenant-specific settings
- **Features**:
  - Organization information (name, logo, branding)
  - Exam configuration defaults
  - User role definitions and permissions
  - Integration settings (email, SMS)
  - Data retention policies

#### UserManagement Component
- **Purpose**: Manage users within the tenant
- **Features**:
  - User list with roles and status
  - Invite new users via email
  - Edit user roles and permissions
  - Deactivate/reactivate users
  - Bulk user operations

### Shared Components

#### TenantCard Component
- **Purpose**: Reusable card component for displaying tenant information
- **Props**: tenant data, actions, display mode
- **Features**: Responsive design, status indicators, action buttons

#### TenantForm Component
- **Purpose**: Reusable form for tenant creation and editing
- **Features**: Form validation with Zod, error handling, loading states

## Data Models

### Frontend Types

```typescript
// Tenant management types
interface Tenant {
  id: number;
  code: string;
  name: string;
  schema_name: string;
  domain?: string;
  status: 'active' | 'inactive' | 'suspended';
  created_at: string;
  updated_at: string;
  settings: TenantSettings;
  statistics: TenantStatistics;
}

interface TenantSettings {
  branding: {
    logo_url?: string;
    primary_color?: string;
    organization_name: string;
  };
  features: {
    ai_grading_enabled: boolean;
    joint_exams_enabled: boolean;
    analytics_enabled: boolean;
  };
  limits: {
    max_users: number;
    max_storage_gb: number;
    max_exams_per_month: number;
  };
}

interface TenantStatistics {
  total_users: number;
  active_users: number;
  total_exams: number;
  storage_used_gb: number;
  last_activity: string;
}

interface TenantUser {
  id: number;
  user_id: number;
  name: string;
  email: string;
  phone_number: string;
  roles: string[];
  status: 'active' | 'inactive';
  last_login: string;
  created_at: string;
}
```

### Backend Models

```rust
// Tenant management models
#[derive(Serialize, Deserialize, sqlx::FromRow)]
pub struct Tenant {
    pub id: i64,
    pub code: String,
    pub name: String,
    pub schema_name: String,
    pub domain: Option<String>,
    pub status: TenantStatus,
    pub settings: serde_json::Value,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Serialize, Deserialize)]
pub enum TenantStatus {
    Active,
    Inactive,
    Suspended,
}

#[derive(Serialize, Deserialize)]
pub struct TenantCreateRequest {
    pub code: String,
    pub name: String,
    pub domain: Option<String>,
    pub admin_email: String,
    pub admin_name: String,
    pub admin_phone: String,
}

#[derive(Serialize, Deserialize)]
pub struct TenantStatistics {
    pub total_users: i64,
    pub active_users: i64,
    pub total_exams: i64,
    pub storage_used_bytes: i64,
    pub last_activity: Option<chrono::DateTime<chrono::Utc>>,
}
```

## Error Handling

### Frontend Error Handling
- **Network Errors**: Display user-friendly messages with retry options
- **Validation Errors**: Real-time form validation with clear error messages
- **Permission Errors**: Redirect to appropriate pages with explanatory messages
- **Loading States**: Skeleton loaders and progress indicators

### Backend Error Handling
- **Database Errors**: Proper error logging and generic user messages
- **Schema Creation Errors**: Rollback mechanisms for failed tenant creation
- **Permission Errors**: Consistent HTTP status codes and error responses
- **Validation Errors**: Detailed field-level error messages

```rust
#[derive(Serialize)]
pub enum TenantError {
    NotFound,
    CodeAlreadyExists,
    SchemaCreationFailed,
    InsufficientPermissions,
    ValidationError(Vec<String>),
}
```

## Testing Strategy

### Frontend Testing
- **Unit Tests**: Component testing with React Testing Library
- **Integration Tests**: User flow testing with Playwright
- **Visual Tests**: Component snapshot testing
- **Accessibility Tests**: ARIA compliance and keyboard navigation

### Backend Testing
- **Unit Tests**: Service layer testing with mock databases
- **Integration Tests**: API endpoint testing with test database
- **Database Tests**: Schema creation and migration testing
- **Performance Tests**: Load testing for tenant operations

### End-to-End Testing
- **Tenant Creation Flow**: Complete tenant setup process
- **User Management Flow**: Adding and managing tenant users
- **Permission Testing**: Role-based access control validation
- **Multi-tenant Isolation**: Data isolation verification

## Security Considerations

### Authentication & Authorization
- **JWT Token Validation**: Verify user permissions for tenant operations
- **Role-Based Access**: Platform admin vs tenant admin permissions
- **Multi-tenant Isolation**: Ensure users can only access their tenant data
- **Audit Logging**: Track all tenant management operations

### Data Protection
- **Input Validation**: Sanitize all user inputs
- **SQL Injection Prevention**: Use parameterized queries
- **XSS Protection**: Escape user-generated content
- **CSRF Protection**: Implement CSRF tokens for state-changing operations

### Schema Security
- **Schema Isolation**: Prevent cross-tenant data access
- **Schema Creation**: Secure tenant schema initialization
- **Database Permissions**: Minimal required database privileges
- **Backup Security**: Encrypted tenant data backups

## Performance Optimization

### Frontend Performance
- **Code Splitting**: Lazy load tenant management pages
- **Caching**: Cache tenant data and statistics
- **Pagination**: Efficient loading of large tenant lists
- **Debounced Search**: Optimize search input handling

### Backend Performance
- **Database Indexing**: Optimize queries for tenant operations
- **Connection Pooling**: Efficient database connection management
- **Caching Layer**: Redis caching for frequently accessed data
- **Async Operations**: Non-blocking tenant schema creation

### Monitoring
- **Performance Metrics**: Track page load times and API response times
- **Error Tracking**: Monitor and alert on tenant management errors
- **Usage Analytics**: Track feature usage and user behavior
- **Database Monitoring**: Monitor query performance and connection usage