# MultiSelect 组件使用指南

基于 shadcn/ui 设计系统和 cmdk 库实现的功能完整的多选组件，参考了 [shadcn-ui-expansions](https://github.com/hsuanyi-chou/shadcn-ui-expansions) 并遵循项目代码规范。

## 核心特性

- ✅ **多选功能** - 支持选择多个选项并以标签形式显示
- ✅ **搜索过滤** - 内置搜索功能，支持同步和异步搜索
- ✅ **分组显示** - 支持选项分组展示
- ✅ **创建选项** - 允许用户创建不存在的选项
- ✅ **固定选项** - 支持不可删除的固定选项
- ✅ **最大选择限制** - 可限制最大选择数量
- ✅ **键盘导航** - 完整的键盘操作支持
- ✅ **加载状态** - 异步搜索时的加载提示
- ✅ **空状态** - 自定义空状态提示
- ✅ **响应式设计** - 自适应不同屏幕尺寸
- ✅ **完整 TypeScript 支持** - 类型安全的 API

## 基本用法

```tsx
import { MultiSelect } from "@/components/ui/multi-select"

const options = [
  { value: "react", label: "React" },
  { value: "vue", label: "Vue.js" },
  { value: "angular", label: "Angular" },
  { value: "svelte", label: "Svelte" }
]

export function BasicExample() {
  const [selected, setSelected] = React.useState([])

  return (
    <MultiSelect
      options={options}
      value={selected}
      onChange={setSelected}
      placeholder="选择前端框架..."
    />
  )
}
```

## 高级功能

### 分组选项

```tsx
const frameworkOptions = [
  { value: "react", label: "React", category: "Library" },
  { value: "vue", label: "Vue.js", category: "Framework" },
  { value: "angular", label: "Angular", category: "Framework" },
  { value: "svelte", label: "Svelte", category: "Compiler" }
]

<MultiSelect
  options={frameworkOptions}
  value={selected}
  onChange={setSelected}
  groupBy="category"
  placeholder="按类别分组选择..."
/>
```

### 异步搜索

```tsx
const asyncSearch = async (query: string) => {
  const response = await fetch(`/api/search?q=${query}`)
  const data = await response.json()
  return data.results.map(item => ({
    value: item.id,
    label: item.name
  }))
}

<MultiSelect
  value={selected}
  onChange={setSelected}
  onSearch={asyncSearch}
  loadingIndicator={<div>搜索中...</div>}
  emptyIndicator={<div>未找到结果</div>}
  placeholder="异步搜索..."
/>
```

### 创建新选项

```tsx
<MultiSelect
  options={options}
  value={selected}
  onChange={setSelected}
  creatable
  placeholder="可创建新选项..."
/>
```

### 固定选项和限制

```tsx
const optionsWithFixed = [
  { value: "admin", label: "管理员", fixed: true },
  { value: "user", label: "普通用户" },
  { value: "guest", label: "访客" }
]

<MultiSelect
  options={optionsWithFixed}
  value={selected}
  onChange={setSelected}
  maxSelected={3}
  onMaxSelected={(limit) => toast.error(`最多只能选择 ${limit} 项`)}
  placeholder="固定管理员角色..."
/>
```

## API 参考

### Option 类型

```tsx
interface Option {
  value: string        // 选项值
  label: string        // 显示文本
  disable?: boolean    // 是否禁用
  fixed?: boolean      // 是否固定（不可删除）
  [key: string]: string | boolean | undefined  // 支持额外属性用于分组等
}
```

### MultiSelectRef

```tsx
interface MultiSelectRef {
  selectedValue: Option[]     // 当前选中值
  input: HTMLInputElement     // 输入框元素引用
  focus: () => void          // 聚焦方法
  reset: () => void          // 重置方法
}
```

## 注意事项

1. **性能优化**：对于大量选项，建议使用异步搜索而非一次性加载所有选项
2. **无障碍访问**：组件内置了完整的键盘导航和 ARIA 属性支持
3. **样式定制**：可通过 `className` 和 `badgeClassName` 进行深度样式定制
4. **移动端适配**：组件已针对触摸设备优化，支持触摸交互

## 版本兼容性

- React 18+
- TypeScript 4.7+
- 基于 cmdk 和 @radix-ui/react-icons
- 兼容 shadcn/ui 设计系统