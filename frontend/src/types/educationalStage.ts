/**
 * 学段管理相关类型定义
 */

export interface EducationalStage {
  id: string;
  code: string;
  name: string;
  short_name: string;
  description: string;
  order_level: number;
  duration_years: number;
  age_range: string;
  is_standard: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EducationStageSummary {
  id: string;
  code: string;
  name: string;
  is_active: boolean;
  short_name: string,
}

export interface EducationalStageFormData extends CreateEducationalStageRequest {
  id?: string;
}

export interface CreateEducationalStageRequest {
  code: string;
  name: string;
  short_name: string;
  description?: string;
  order_level: number;
  duration_years: number;
  age_range: string;
  is_standard: boolean;
}

export interface UpdateEducationalStageRequest extends Partial<CreateEducationalStageRequest> {
  is_active?: boolean;
}

export interface EducationalStageQueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  is_active?: boolean;
  order_by?: string;
  order_direction?: 'asc' | 'desc';
}

// 系统预定义的学段代码
export const SYSTEM_EDUCATIONAL_STAGES = {
  PRIMARY: 'PRIMARY',
  JUNIOR: 'JUNIOR',
  SENIOR: 'SENIOR',
} as const;

export const EDUCATIONAL_STAGE_NAME_MAP = {
  [SYSTEM_EDUCATIONAL_STAGES.PRIMARY]: '小学',
  [SYSTEM_EDUCATIONAL_STAGES.JUNIOR]: '初中',
  [SYSTEM_EDUCATIONAL_STAGES.SENIOR]: '高中',
} as const;

// 排序选项
export const EDUCATIONAL_STAGE_SORT_OPTIONS = [
  { value: 'order_level', label: '排序级别' },
  { value: 'name', label: '学段名称' },
  { value: 'code', label: '学段代码' },
  { value: 'created_at', label: '创建时间' },
] as const;

// 状态选项
export const EDUCATIONAL_STAGE_STATUS_OPTIONS = [
  { value: true, label: '启用' },
  { value: false, label: '禁用' },
] as const;

// 默认查询参数
export const DEFAULT_EDUCATIONAL_STAGE_QUERY: Required<EducationalStageQueryParams> = {
  page: 1,
  page_size: 10,
  search: '',
  is_active: true,
  order_by: 'order_level',
  order_direction: 'asc',
};
