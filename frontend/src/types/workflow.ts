export type WorkflowCategory = 'ocr' | 'correction';
export const workflowCategoryOptions: { label: string; value: WorkflowCategory }[] = [
    { label: 'OCR识别', value: 'ocr' },
    { label: '批改流程', value: 'correction' },
];

export type WorkflowSettingQueryParams = {
    page?: number;
    page_size?: number;
    schema_name?:string;
    subject_code?:string;
    question_type_code?:string;
    grade_level_code?:string;
    workflow_type?:WorkflowCategory;
}

// Default values
export const DEFAULT_WORKFLOW_SETTING_QUERY: WorkflowSettingQueryParams = {
    page: 1,
    page_size: 10,
};


export type WorkflowSummaryQueryParams = {
    page?: number;
    page_size?: number;
    schema_name?:string;
    subject_code?:string;
    question_type_code?:string;
    grade_level_code?:string;
    workflow_type?:WorkflowCategory;
}

// Default values
export const DEFAULT_WORKFLOW_SETTING_SUMMARY_QUERY: WorkflowSummaryQueryParams = {
};

export interface WorkflowSetting {
    workflow_name: string;
    workflow_id: string; // UUID 类型通常在前端表示为 string
    workflow_type: WorkflowCategory;
    description: string;

    subject_codes: string[];
    grade_level_codes: string[];
    question_type_codes: string[];
    schema_names: string[];

    created_at: string; // ISO 格式时间戳
    updated_at: string;
}

export interface WorkflowSummary {
    workflow_name: string;
    workflow_id: string;
    workflow_type: WorkflowCategory;
    description: string;
}

export interface CreateWorkflowSettingRequest{
    workflow_id: string; // UUID 类型通常在前端表示为 string
    subject_codes: string[];
    grade_level_codes: string[];
    question_type_codes: string[];
    schema_names: string[];
}