export interface AdministrativeClassesStatistics {
  total_classes: Number,
  total_teacher: Number,
  total_students: Number,
}

export interface AdministrativeClasses {
  id: String,
  class_name: String,
  code?: String,
  academic_year?: String,
  grade_level_code?: String,
  teacher_id: String,
  created_at?: String,
  updated_at?: String,
  is_active: Boolean,
}

export interface CreateAdministrativeClassesParams {
  class_name: String,
  code?: String,
  academic_year?: String,
  grade_level_code?: String,
  teacher_id?: String,
}
export interface AdministrativeClassesDetail {
  id: String,
  class_name: String,
  code?: String,
  academic_year?: String,
  grade_level_code?: String,
  teacher_id: String,
  created_at?: String,
  updated_at?: String,
  is_active: Boolean,
  //额外信息
  teacher_name?: String,
  grade_level_name?: String,
  total_student: Number,
}

export interface FindAllStudentInClassParams {
  class_id: String,
}

export interface UpdateAdministrativeClassesParams {
  id: String,
  class_name: String,
  code?: String,
  academic_year?: String,
  grade_level_code?: String,
  teacher_id?: String,
  is_active: Boolean,
}

export interface MoveStudentToAdministrativeClassesParams {
  class_id: String,
  student_id: String,
}

export interface RemoveStudentFromAdministrativeClassesParams {
  class_id: String,
  student_id: String,
}

export interface DeleteAdministrativeClassesParams {
  class_id: String,
}
