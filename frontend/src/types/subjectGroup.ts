// Subject group management types for the Deep-Mate frontend application

import {SubjectGroupsDetail} from "@/types/subjectGroups.ts";

/**
 * 学科组对象，与数据库中对应
 */
export interface SubjectGroup {
  id: string;
  tenant_id: string;
  name: string;
  subject: string; // 学科代码 (MATH, CHINESE, etc.)
  subject_name: string; // 学科名称 (数学, 语文, etc.)
  description?: string;
  leader_id?: string;
  leader_name?: string;
  teacher_count: number;
  class_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SubjectGroupSummary {
  id: string;
  name: string;
  subject: string;
  subject_name: string;
  leader_name?: string;
  is_active: boolean;
}

export interface SubjectGroupStatistics {
  total_groups: number;
  active_groups: number;
  inactive_groups: number;
  subject_distribution: SubjectDistribution[];
}

export interface SubjectDistribution {
  subject: string;
  subject_name: string;
  group_count: number;
  teacher_count: number;
  class_count: number;
}

export interface CreateSubjectGroupRequest {
  group_name: string;
  subject_code: string;
  description?: string;
  leader_user_id?: string;
}

export interface UpdateSubjectGroupRequest {
  name?: string;
  subject?: string;
  description?: string;
  leader_id?: string;
  is_active?: boolean;
}

export interface SubjectGroupQueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  subject?: string;
  leader_id?: string;
  is_active?: boolean;
  order_by?: 'name' | 'subject' | 'teacher_count' | 'class_count' | 'created_at';
  order_direction?: 'asc' | 'desc';
}

// Form validation schemas
export interface SubjectGroupFormData extends CreateSubjectGroupRequest {
  id?: string;
}

export interface SubjectGroupFormErrors {
  name?: string;
  subject?: string;
  description?: string;
  leader_user_id?: string;
}

// Component prop types
export interface SubjectGroupTableProps {
  subjectGroups: SubjectGroupsDetail[];
  loading?: boolean;
  onEdit: (subjectGroup: SubjectGroupsDetail) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

export interface SubjectGroupFormProps {
  subjectGroup?: SubjectGroup;
  open: boolean;
  onClose: () => void;
  onSubmit: (data: SubjectGroupFormData) => Promise<void>;
  loading?: boolean;
}

export interface SubjectGroupDetailProps {
  subjectGroup?: SubjectGroup;
  open: boolean;
  onClose: () => void;
  loading?: boolean;
}

export interface SubjectGroupSelectProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  subject?: string; // 过滤特定学科的学科组
  showInactive?: boolean;
  className?: string;
}

// Filter and sort options
export const SUBJECT_GROUP_SORT_OPTIONS = [
  { value: 'name', label: '组名' },
  { value: 'subject', label: '学科' },
  { value: 'teacher_count', label: '教师数量' },
  { value: 'class_count', label: '班级数量' },
  { value: 'created_at', label: '创建时间' },
] as const;

export const SUBJECT_GROUP_STATUS_OPTIONS = [
  { value: true, label: '启用' },
  { value: false, label: '禁用' },
] as const;

// Default values
export const DEFAULT_SUBJECT_GROUP_QUERY: Required<SubjectGroupQueryParams> = {
  page: 1,
  page_size: 10,
  search: '',
  subject: '',
  leader_id: '',
  is_active: true,
  order_by: 'name',
  order_direction: 'asc',
};

// Subject group management permissions
export const SUBJECT_GROUP_PERMISSIONS = {
  VIEW: 'subject_group:view',
  CREATE: 'subject_group:create',
  UPDATE: 'subject_group:update',
  DELETE: 'subject_group:delete',
  MANAGE_MEMBERS: 'subject_group:manage_members',
} as const;