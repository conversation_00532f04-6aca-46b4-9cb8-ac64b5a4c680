export interface TeachingClassesStatistics {
  total_classes: Number,
  total_teacher: Number,
  total_students: Number,
}

export interface TeachingClasses {
  id: String,
  class_name: String,
  code?: String,
  academic_year?: String,
  subject_group_id?: String,
  teacher_id?: String,
  created_at?: String,
  updated_at?: String,
  is_active: Boolean,
}

export interface CreateTeachingClassesParams {
  class_name: String,
  code?: String,
  academic_year?: String,
  subject_group_id?: String,
  teacher_id?: String,
}
export interface TeachingClassesDetail {
  id: String,
  class_name: String,
  code?: String,
  academic_year?: String,
  subject_group_id?: String,
  teacher_id?: String,
  created_at?: String,
  updated_at?: String,
  is_active: Boolean,
  //额外信息
  teacher_name?: String,
  subject_groups_name?: String,
  total_student?: Number,
}
export interface UpdateTeachingClassesParams {
  id: String,
  class_name: String,
  code?: String,
  academic_year?: String,
  subject_group_id?: String,
  teacher_id?: String,
  is_active: Boolean,
}

export interface DeleteTeachingClassesParams {
  class_id: String,
}

export interface FindAllStudentInClassParams {
  class_id: String,
}

export interface MoveStudentToTeachingClassesParams {
  class_id: String,
  student_id: String,
}

export interface RemoveStudentFromTeachingClassesParams {
  class_id: String,
  student_id: String,
}