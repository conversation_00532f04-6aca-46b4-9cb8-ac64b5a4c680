/**
 * 菜单管理相关类型定义
 * 基于后端API接口和前端组件需求
 */

// 菜单使用统计时间线项
export interface MenuUsageTimelineItem {
  date: string;
  access_count: number;
  unique_users: number;
  denied_count: number;
}

// 菜单使用统计
export interface MenuUsageStats {
  total_access_count: number;
  unique_user_count: number;
  denied_access_count: number;
  last_accessed_at?: string;
  avg_daily_access: number;
}

// 详细菜单使用统计（包含时间线）
export interface DetailedMenuUsageStats {
  total_access_count: number;
  unique_user_count: number;
  denied_access_count: number;
  last_accessed_at?: string;
  avg_daily_access: number;
  timeline: MenuUsageTimelineItem[];
}

// 菜单清理结果
export interface MenuCleanupResult {
  cleaned_count: number;
  invalid_references: string[];
  message: string;
}

// 菜单验证问题项
export interface MenuValidationIssue {
  type: string;
  menu_id: string;
  description: string;
  severity: string;
}

// 菜单验证结果
export interface MenuValidationResult {
  is_valid: boolean;
  issues: MenuValidationIssue[];
  suggestions: string[];
}

// 菜单项接口
export interface MenuItem {
  id?: string;
  menu_id: string;
  name: string;
  path: string;
  icon?: string;
  parent_id?: string;
  menu_type: string;
  description?: string;
  component_path?: string;
  external_link?: string;
  required_roles?: string[];
  access_level: number;
  sort_order: number;
  is_active: boolean;
  cache_enabled: boolean;
  metadata?: any;
  version: number;
  last_modified_by?: string;
  last_modified_at?: string;
  created_at?: string;
  updated_at?: string;
  children?: MenuItem[];
  children_count: number;
  depth_level: number;
  usage_stats?: MenuUsageStats;
}

// 权限模板接口
export interface PermissionTemplate {
  id: string;
  template_name: string;
  template_type: string;
  template_category?: string;
  permissions: string[];
  data_scopes?: string[];
  permission_mode: string;
  description?: string;
  usage_count: number;
  is_system_template: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 菜单查询参数
export interface MenuQueryParams {
  menu_type?: string;
  parent_id?: string;
  is_active?: boolean;
  include_children?: boolean;
  include_metadata?: boolean;
  search?: string;
  page?: number;
  page_size?: number;
}

// 菜单创建请求
export interface MenuCreateRequest {
  menu_id: string;
  name: string;
  path: string;
  icon?: string;
  parent_id?: string;
  menu_type?: string;
  description?: string;
  component_path?: string;
  external_link?: string;
  required_roles?: string[];
  access_level?: number;
  sort_order?: number;
  is_active?: boolean;
  metadata?: any;
}

// 菜单更新请求
export interface MenuUpdateRequest {
  name?: string;
  path?: string;
  icon?: string;
  parent_id?: string;
  menu_type?: string;
  description?: string;
  component_path?: string;
  external_link?: string;
  required_roles?: string[];
  access_level?: number;
  sort_order?: number;
  is_active?: boolean;
  metadata?: any;
  reason?: string;
}

// 批量菜单操作请求
export interface BatchMenuOperationRequest {
  operation: string; // "create", "update", "delete", "reorder", "status_change"
  menu_ids: string[];
  operation_data?: any;
  reason?: string;
}

// 菜单重排序请求
export interface MenuReorderRequest {
  menu_orders: MenuOrderItem[];
  reason?: string;
}

export interface MenuOrderItem {
  menu_id: string;
  new_parent_id?: string;
  new_sort_order: number;
}

// 批量操作响应
export interface BatchOperationResponse {
  operation: string;
  total_requested: number;
  successful_count: number;
  failed_count: number;
  results: BatchOperationResult[];
  execution_time_ms: number;
}

export interface BatchOperationResult {
  menu_id: string;
  success: boolean;
  message?: string;
  error_code?: string;
}

// 权限模板查询参数
export interface PermissionTemplateQueryParams {
  template_type?: string;
  template_category?: string;
  is_active?: boolean;
  is_system_template?: boolean;
  search?: string;
  page?: number;
  page_size?: number;
}

// 组件Props类型定义
export interface MenuTreeNodeProps {
  menu: MenuItem;
  level: number;
  isExpanded: boolean;
  isSelected: boolean;
  onToggle: () => void;
  onSelect: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

export interface MenuConfigPanelProps {
  menu: MenuItem | null;
  templates: PermissionTemplate[];
  isEditing: boolean;
  isCreating: boolean;
  onSave: (data: Partial<MenuItem>) => void;
  onCancel: () => void;
  onEdit: () => void;
}

export interface FormComponentProps {
  data: Partial<MenuItem>;
  onChange: (data: Partial<MenuItem>) => void;
  disabled: boolean;
}

export interface PermissionFormProps extends FormComponentProps {
  templates: PermissionTemplate[];
}

export interface UsageStatsProps {
  stats: MenuUsageStats;
}

// 菜单树面板Props
export interface MenuTreePanelProps {
  menus: MenuItem[];
  loading: boolean;
  searchTerm: string;
  filterType: string;
  selectedMenu: MenuItem | null;
  expandedNodes: Set<string>;
  onSearchChange: (term: string) => void;
  onFilterChange: (type: string) => void;
  onMenuSelect: (menu: MenuItem | null) => void;
  onMenuEdit: (menu: MenuItem) => void;
  onMenuDelete: (menuId: string) => void;
  onCreateMenu: () => void;
  onToggleNode: (menuId: string) => void;
}

// 枚举类型
export type MenuType = 'functional' | 'admin' | 'personal' | 'system';
export type PermissionMode = 'any' | 'all' | 'custom';

// 选项配置
export const MENU_TYPE_OPTIONS: { value: MenuType; label: string }[] = [
  { value: 'functional', label: '功能菜单' },
  { value: 'admin', label: '管理菜单' },
  { value: 'personal', label: '个人菜单' },
  { value: 'system', label: '系统菜单' },
];

export const PERMISSION_MODE_OPTIONS: { value: PermissionMode; label: string }[] = [
  { value: 'any', label: '任一满足 (ANY)' },
  { value: 'all', label: '全部满足 (ALL)' },
  { value: 'custom', label: '自定义逻辑' },
];

// 菜单状态配置
export const MENU_STATUS_CONFIG = {
  active: { label: '启用', color: 'green' },
  inactive: { label: '禁用', color: 'red' },
} as const;

// 菜单类型配置
export const MENU_TYPE_CONFIG = {
  functional: { label: '功能菜单', color: 'blue', icon: '🔧' },
  admin: { label: '管理菜单', color: 'purple', icon: '⚙️' },
  personal: { label: '个人菜单', color: 'orange', icon: '👤' },
  system: { label: '系统菜单', color: 'gray', icon: '🖥️' },
} as const;
