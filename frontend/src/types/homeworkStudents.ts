import { StudentBaseInfo } from "./student";

export interface HomeworkStudents {
    id: String,
    homework_id: String,
    student_id: String,
    status: String,
    created_at: String,
    updated_at: String,
}

export enum HomeworkStudentsStatusEnum {
  ///未提交
  Unsubmitted = "Unsubmitted",
  ///异常
  Error = "Error",
  ///学生分数已经出来了，后续不再变
  Done = "Done",
}

export interface BatchBindStudentsToHomeworkParams {
  homework_id: String;
  student_id_list: String[];
}


export interface FindAllByHomeworkIdParams {
  homework_id: String;
}

export interface HomeworkStudentsWithStudentBaseInfo {
  id: String,
  homework_id: String,
  student_id: String,
  status: String,
  created_at: String,
  updated_at: String,
  student_base_info?: StudentBaseInfo,
}

export interface BatchUnbindStudentsFromHomeworkParams {
  homework_id: String;
  student_id_list: String[];
}