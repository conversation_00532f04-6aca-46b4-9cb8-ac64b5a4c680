import React from 'react';

// Common types for the Deep-Mate frontend application

export interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
}

export interface AuthContext {
  user: User | null;
  token: string | null;
  tenantId: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
}

export interface LoginCredentials {
  username: string;
  password: string;
  tenant_code?: string;
}

// 统一的API响应接口，与后端保持一致
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  error_code?: string;
  timestamp: number;
}

// 分页响应接口
export interface PaginatedApiResponse<T> {
  success: boolean;
  data: T[];
  message: string;
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  error_code?: string;
  timestamp: number;
}

// 向后兼容的旧接口（已弃用）
/** @deprecated Use ApiResponse instead */
export interface LegacyApiResponse<T> {
  code: number;
  msg: string;
  data: T | null;
}

/** @deprecated Use PaginatedApiResponse instead */
export interface PaginationResponse<T> {
  list: T[];
  totals: number;
  currentPage: number;
  pageSize: number;
}

export interface QueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Form validation types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'select' | 'textarea' | 'number' | 'date' | 'datetime-local';
  required?: boolean;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

export interface FormError {
  field: string;
  message: string;
}

// Navigation types
export interface NavigationItem {
  id: string;
  title: string;
  icon?: React.ComponentType<{ className?: string }>;
  href?: string;
  children?: NavigationItem[];
  permission?: string;
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Theme types
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  fontFamily: string;
  borderRadius: string;
}

// Table types
export interface TableColumn<T = any> {
  key: keyof T;
  title: string;
  width?: string;
  align?: 'left' | 'center' | 'right';
  sorter?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
}

export interface TableProps<T = any> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    onChange: (page: number, pageSize: number) => void;
  };
  rowKey?: keyof T | ((record: T) => string);
  onRow?: (record: T, index: number) => React.HTMLAttributes<HTMLTableRowElement>;
}

// Modal types
export interface ModalProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  width?: string;
  footer?: React.ReactNode;
}

// Chart types
export interface ChartDataPoint {
  x: string | number;
  y: number;
  category?: string;
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  data: ChartDataPoint[];
  xAxis?: {
    title: string;
    type: 'category' | 'time' | 'value';
  };
  yAxis?: {
    title: string;
    min?: number;
    max?: number;
  };
  colors?: string[];
  height?: number;
  width?: number;
}

// File upload types
export interface FileUploadProps {
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in MB
  onUpload: (files: File[]) => Promise<void>;
  children?: React.ReactNode;
}

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadTime: string;
}

// Date picker types
export interface DateRange {
  start: Date;
  end: Date;
}

// Search types
export interface SearchProps {
  placeholder?: string;
  onSearch: (value: string) => void;
  onFilter?: (filters: Record<string, any>) => void;
  filters?: FormField[];
}

// Status types
export type Status = 'draft' | 'published' | 'in_progress' | 'completed' | 'cancelled' | 'archived';

export interface StatusConfig {
  [key: string]: {
    label: string;
    color: string;
    icon?: React.ComponentType<{ className?: string }>;
  };
}

// Permission types
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
}

// Tenant types
export interface TenantInfo {
  id: string;
  name: string;
  code: string;
  logo?: string;
  description?: string;
  settings: Record<string, any>;
  status: 'active' | 'inactive' | 'suspended';
  created_at: string;
  updated_at: string;
}

// Analytics types
export interface AnalyticsData {
  metric: string;
  value: number;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  period: string;
}

export interface DashboardWidget {
  id: string;
  title: string;
  type: 'metric' | 'chart' | 'table' | 'list';
  data: any;
  config: Record<string, any>;
  size: 'sm' | 'md' | 'lg' | 'xl';
  position: { x: number; y: number };
}

// Export all types
export * from '../services/examApi';
export * from '../services/teachingAidsApi';
export * from '../services/gradingApi';
export * from '../services/analysisApi';
export * from './Tenant';
export * from './role';
export * from './subject';
export * from './subjectGroup';
export * from './grade';
export * from './question';
export * from './workflow';
export * from './menu';

/**
 * 对于参数仅有一个id字符串字段的参数的情况下的通用参数
 */
export interface IdStringParams {
  id: String
}

/**
 * 分页参数,与后台对应
 */
export interface PageParams {
  page?: number,
  page_size?: number,
}