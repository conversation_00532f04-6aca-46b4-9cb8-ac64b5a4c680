/**
 * 角色管理相关类型定义
 * 基于 PRD 文档 2_user_roles_and_permissions.md
 */

// 角色分类枚举
export enum RoleCategory {
  SYSTEM = 'system',        // 系统级用户
  TENANT = 'tenant',        // 租户级用户
  SCHOOL = 'school',        // 学校管理用户
  BUSINESS = 'business',    // 业务操作用户
  CLASS_GRADE = 'class_grade', // 班级/年级用户
  END_USER = 'end_user'     // 终端用户
}

// 角色级别枚举
export enum RoleLevel {
  SUPER_ADMIN = 1,      // 系统超级管理员
  TENANT_ADMIN = 2,     // 租户管理员
  PRINCIPAL = 3,        // 校长
  ACADEMIC_DIRECTOR = 4, // 教导主任
  SUBJECT_LEADER = 5,   // 学科组长
  GRADE_LEADER = 6,     // 年级长
  CLASS_TEACHER = 7,    // 班主任
  TEACHER = 8,          // 任课老师
  STUDENT = 9,          // 学生
  PARENT = 10           // 家长
}

// 权限类型
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;      // 资源类型
  action: string;        // 操作类型
  scope: string;         // 权限范围
}

// 角色定义
export interface Role {
  id: string;
  name: string;
  code: string;          // 角色编码
  description: string;
  category: RoleCategory;
  level: RoleLevel;
  permissions: Permission[];
  is_system: boolean;    // 是否系统预设角色
  is_active: boolean;
  tenant_id?: string;    // 租户ID（系统级角色为null）
  created_by: string;
  created_at: string;
  updated_at: string;
}

// 用户身份（用户在租户内的角色身份）
export interface UserIdentity {
  id: string;
  user_id: string;
  tenant_id: string;
  role_id: string;
  role: Role;
  identity_type: string;  // 身份类型
  display_name: string;   // 显示名称
  is_primary: boolean;    // 是否主要身份
  is_verified: boolean;   // 是否已验证
  verification_method?: string;
  verified_at?: string;
  target_type?: string;   // 目标类型（学科组、年级、班级等）
  target_id?: string;     // 目标ID
  subject?: string;       // 学科
  created_at: string;
  updated_at: string;
}

// 角色权限矩阵
export interface RolePermissionMatrix {
  role_code: string;
  data_scope: string[];     // 数据权限范围
  function_permissions: string[]; // 功能权限
  cross_tenant: boolean;    // 跨租户权限
  inheritance: boolean;     // 是否支持权限继承
}

// 创建角色请求
export interface CreateRoleRequest {
  name: string;
  code: string;
  description: string;
  category: RoleCategory;
  level: RoleLevel;
  permissions: string[];   // 权限ID列表
  is_active: boolean;
  tenant_id?: string;
}

// 更新角色请求
export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: string[];  // 权限ID列表
  is_active?: boolean;
}

// 分配角色请求
export interface AssignRoleRequest {
  user_id: string;
  tenant_id: string;
  role_id: string;
  target_type?: string;
  target_id?: string;
  subject?: string;
  display_name?: string;
}

// 角色查询参数
export interface RoleQueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  category?: RoleCategory;
  level?: RoleLevel;
  is_active?: boolean;
  tenant_id?: string;
}

// 权限查询参数
export interface PermissionQueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  resource?: string;
  action?: string;
}

// 角色统计信息
export interface RoleStatistics {
  total_roles: number;
  active_roles: number;
  system_roles: number;
  tenant_roles: number;
  user_count_by_role: Record<string, number>;
}

// 预定义系统角色
export const SYSTEM_ROLES = {
  SUPER_ADMIN: {
    code: 'super_admin',
    name: '系统超级管理员',
    category: RoleCategory.SYSTEM,
    level: RoleLevel.SUPER_ADMIN
  },
  SYSTEM_AGENT: {
    code: 'system_agent',
    name: '系统代理商',
    category: RoleCategory.SYSTEM,
    level: RoleLevel.SUPER_ADMIN
  },
  OPERATOR: {
    code: 'operator',
    name: '运维人员',
    category: RoleCategory.SYSTEM,
    level: RoleLevel.SUPER_ADMIN
  },
  TENANT_ADMIN: {
    code: 'tenant_admin',
    name: '租户管理员',
    category: RoleCategory.TENANT,
    level: RoleLevel.TENANT_ADMIN
  },
  PRINCIPAL: {
    code: 'principal',
    name: '校长',
    category: RoleCategory.SCHOOL,
    level: RoleLevel.PRINCIPAL
  },
  ACADEMIC_DIRECTOR: {
    code: 'academic_director',
    name: '教导主任',
    category: RoleCategory.SCHOOL,
    level: RoleLevel.ACADEMIC_DIRECTOR
  },
  SUBJECT_LEADER: {
    code: 'subject_leader',
    name: '学科组长',
    category: RoleCategory.BUSINESS,
    level: RoleLevel.SUBJECT_LEADER
  },
  EXAM_MANAGER: {
    code: 'exam_manager',
    name: '考试管理员',
    category: RoleCategory.BUSINESS,
    level: RoleLevel.SUBJECT_LEADER
  },
  GRADING_MANAGER: {
    code: 'grading_manager',
    name: '阅卷管理员',
    category: RoleCategory.BUSINESS,
    level: RoleLevel.SUBJECT_LEADER
  },
  SCAN_OPERATOR: {
    code: 'scan_operator',
    name: '试卷扫描员',
    category: RoleCategory.BUSINESS,
    level: RoleLevel.TEACHER
  },
  GRADER: {
    code: 'grader',
    name: '阅卷员',
    category: RoleCategory.BUSINESS,
    level: RoleLevel.TEACHER
  },
  GRADE_LEADER: {
    code: 'grade_leader',
    name: '年级长',
    category: RoleCategory.CLASS_GRADE,
    level: RoleLevel.GRADE_LEADER
  },
  CLASS_TEACHER: {
    code: 'class_teacher',
    name: '班主任',
    category: RoleCategory.CLASS_GRADE,
    level: RoleLevel.CLASS_TEACHER
  },
  TEACHER: {
    code: 'teacher',
    name: '任课老师',
    category: RoleCategory.CLASS_GRADE,
    level: RoleLevel.TEACHER
  },
  STUDENT: {
    code: 'student',
    name: '学生',
    category: RoleCategory.END_USER,
    level: RoleLevel.STUDENT
  },
  PARENT: {
    code: 'parent',
    name: '家长',
    category: RoleCategory.END_USER,
    level: RoleLevel.PARENT
  }
} as const;

// 角色分类标签
export const ROLE_CATEGORY_LABELS = {
  [RoleCategory.SYSTEM]: '系统级用户',
  [RoleCategory.TENANT]: '租户级用户',
  [RoleCategory.SCHOOL]: '学校管理用户',
  [RoleCategory.BUSINESS]: '业务操作用户',
  [RoleCategory.CLASS_GRADE]: '班级/年级用户',
  [RoleCategory.END_USER]: '终端用户'
} as const;

// 角色级别标签
export const ROLE_LEVEL_LABELS = {
  [RoleLevel.SUPER_ADMIN]: '系统管理员',
  [RoleLevel.TENANT_ADMIN]: '租户管理员',
  [RoleLevel.PRINCIPAL]: '校长',
  [RoleLevel.ACADEMIC_DIRECTOR]: '教导主任',
  [RoleLevel.SUBJECT_LEADER]: '学科组长',
  [RoleLevel.GRADE_LEADER]: '年级长',
  [RoleLevel.CLASS_TEACHER]: '班主任',
  [RoleLevel.TEACHER]: '任课老师',
  [RoleLevel.STUDENT]: '学生',
  [RoleLevel.PARENT]: '家长'
} as const;