// Subject management types for the Deep-Mate frontend application

/**
 * 学科对象，与数据库中对应
 */
export interface Subject {
  paper_count: string;
  question_count: string;
  id: string;
  code: string;
  name: string;
  description?: string;
  order_level: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SubjectSummary {
  id: string;
  code: string;
  name: string;
  is_active: boolean;
}

export interface SubjectStatistics {
  total_subjects: number;
  active_subjects: number;
  inactive_subjects: number;
  usage_stats: SubjectUsageStats[];
}

export interface SubjectUsageStats {
  subject_id: string;
  subject_name: string;
  question_count: number;
  paper_count: number;
  exam_count?: number;
}

export interface CreateSubjectRequest {
  code: string;
  name: string;
  description?: string;
  order_level: number;
}

export interface UpdateSubjectRequest {
  name?: string;
  description?: string;
  order_level?: number;
  is_active?: boolean;
}

export interface SubjectQueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  is_active?: boolean;
  order_by?: 'name' | 'code' | 'order_level' | 'created_at';
  order_direction?: 'asc' | 'desc';
}

export interface SubjectOrderUpdate {
  subject_id: string;
  order_level: number;
}

export interface CheckCodeResponse {
  is_available: boolean;
}

// Form validation schemas
export interface SubjectFormData extends CreateSubjectRequest {
  id?: string;
}

export interface SubjectFormErrors {
  code?: string;
  name?: string;
  description?: string;
  order_level?: string;
}

// Component prop types
export interface SubjectTableProps {
  subjects: Subject[];
  loading?: boolean;
  onEdit: (subject: Subject) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

export interface SubjectFormProps {
  subject?: Subject;
  open: boolean;
  onClose: () => void;
  onSubmit: (data: SubjectFormData) => Promise<void>;
  loading?: boolean;
}

export interface SubjectSelectProps {
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  showInactive?: boolean;
  className?: string;
}

// Filter and sort options
export const SUBJECT_SORT_OPTIONS = [
  { value: 'order_level', label: '排序级别' },
  { value: 'name', label: '学科名称' },
  { value: 'code', label: '学科代码' },
  { value: 'created_at', label: '创建时间' },
] as const;

export const SUBJECT_STATUS_OPTIONS = [
  { value: true, label: '启用' },
  { value: false, label: '禁用' },
] as const;

// Default values
export const DEFAULT_SUBJECT_QUERY: Required<SubjectQueryParams> = {
  page: 1,
  page_size: 10,
  search: '',
  is_active: true,
  order_by: 'order_level',
  order_direction: 'asc',
};

// Subject codes constants (matching backend)
export const SYSTEM_SUBJECTS = {
  MATH: 'MATH',
  CHINESE: 'CHINESE',
  ENGLISH: 'ENGLISH',
  PHYSICS: 'PHYSICS',
  CHEMISTRY: 'CHEMISTRY',
  BIOLOGY: 'BIOLOGY',
  HISTORY: 'HISTORY',
  GEOGRAPHY: 'GEOGRAPHY',
  POLITICS: 'POLITICS',
} as const;

export const SUBJECT_NAME_MAP = {
  [SYSTEM_SUBJECTS.MATH]: '数学',
  [SYSTEM_SUBJECTS.CHINESE]: '语文',
  [SYSTEM_SUBJECTS.ENGLISH]: '英语',
  [SYSTEM_SUBJECTS.PHYSICS]: '物理',
  [SYSTEM_SUBJECTS.CHEMISTRY]: '化学',
  [SYSTEM_SUBJECTS.BIOLOGY]: '生物',
  [SYSTEM_SUBJECTS.HISTORY]: '历史',
  [SYSTEM_SUBJECTS.GEOGRAPHY]: '地理',
  [SYSTEM_SUBJECTS.POLITICS]: '政治',
} as const;