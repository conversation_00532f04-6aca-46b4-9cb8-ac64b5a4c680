export type QuestionType = {
    code: string;
    type_name: string;
    description?: string;
    is_active?: boolean;
    created_at?: string;
    updated_at?: string;
};

export type QuestionTypeSummary = {
    code: string;
    type_name: string;
    is_active?: boolean;
};

export type ComposeQuestionType = {
    question_type_name: string;
    question_type_code: string;
    subject_code: string;
    grade_level_code: string;
    subject_name?: string;
    grade_level_name?: string;
    education_stage_name?: string;
    education_stage_code?: string;
}

export type QuestionTypeQueryParams = {
    page?: number;
    page_size?: number;
    search?: string;
    is_active?: boolean|null;
    order_by?: string;  // 排序字段：name, code, order_level, created_at
    order_direction?: string;  // 排序方向：asc, desc
}

export type ComposeQuestionTypeQueryParams = {
    page?: number;
    page_size?: number;
}

// Default values
export const DEFAULT_QUESTION_TYPE_QUERY: Required<QuestionTypeQueryParams> = {
    page: 1,
    page_size: 10,
    search: '',
    is_active: null,
    order_by: 'type_name',
    order_direction: 'asc',
};

// Default values
export const DEFAULT_COMPOSE_QUERY: Required<ComposeQuestionTypeQueryParams> = {
    page: 1,
    page_size: 10,
};


export interface CreateQuestionTypeRequest {
    code: string;
    type_name: string;
    description?: string;
}

export interface UpdateQuestionTypeRequest {
    code: string;
    type_name?: string;
    description?: string;
}

export interface QuestionTypeFormErrors {
    code?: string;
    type_name?: string;
    description?: string;
}

export interface ComposeBindRequest {
    questionTypeCode: string;
    subjectCode: string;
    gradeLevelCode?: string;
}

// Filter and sort options
export const QUESTION_TYPE_SORT_OPTIONS = [
    { value: 'type_name', label: '题型名称' },
    { value: 'code', label: '题型代码' },
    { value: 'created_at', label: '创建时间' },
] as const;

export const QUESTION_TYP_STATUS_OPTIONS = [
    { value: true, label: '启用' },
    { value: false, label: '禁用' },
] as const;
