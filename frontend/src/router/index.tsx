
import RootLayout from "@/layouts/RootLayout";
import AdministrativeClassesDetailPage from "@/pages/Class/AdministrativeClassesDetailPage";
import AdministrativeClassesPage from "@/pages/Class/AdministrativeClassesPage";
import TeachingClassesDetailPage from "@/pages/Class/TeachingClassDetailPage";
import TeachingClassesPage from "@/pages/Class/TeachingClassesPage";
import { EducationalStageManagementPage } from "@/pages/EducationalStageManagement";
import ExamManagementPage from "@/pages/ExamManagement";
import GradeManagementPage from "@/pages/GradeManagement";
import GradingCenterPage from "@/pages/GradingCenter";
import { HomeworkManagementPage } from "@/pages/Homework";
import DetailPage from "@/pages/Homework/DetailPage";
import IdentityBindingPage from "@/pages/Identity/IdentityBindingPage.tsx";
import IdentitySelectPage from "@/pages/Identity/IdentitySelectPage.tsx";
import LoginPage from "@/pages/LoginPage";
import QuestionTypePage from "@/pages/QuestionManagement/QuestionTypePage.tsx";
import { RoleManagementPage } from "@/pages/RoleManagement";
import CasbinPolicyManagementPage from "@/pages/CasbinPolicyManagement/CasbinPolicyManagementPage";
import StatisticsPage from "@/pages/Statistics";
import { StudentManagementPage } from "@/pages/StudentManagement";
import { SubjectGroupsManagementPage } from "@/pages/SubjectGroupsManagement";
import { SubjectManagementPage } from "@/pages/SubjectManagement";
import { TeacherManagementPage } from "@/pages/TeacherManagement";
import TeachingAidsPage from "@/pages/TeachingAids";
import TeachingAidTextbookPreviewPage from "@/pages/TeachingAids/Preview";
import TenantManagementPage from "@/pages/Tenant/TenantManagementPage.tsx";
import UserManagementPage from "@/pages/UserManagement";
import { MenuManagementPage } from "@/pages/MenuManagement";
import WorkflowPage from "@/pages/Workflow";
import { createBrowserRouter } from "react-router-dom";
import ProtectedRoute from "./ProtectedRoute";

export const router = createBrowserRouter([
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: "/identitySelect",
    element: <IdentitySelectPage />,
  },
  {
    path: "/identityBinding",
    element: <IdentityBindingPage />,
  },
  {
    path: "/",
    element: <RootLayout />,
    children: [
      {
        element: <ProtectedRoute />,
        children: [
          { index: true, element: <GradingCenterPage /> },
          { path: "teaching-aids", element: <TeachingAidsPage /> },
          { path: "teaching-aids/textbook/:id", element: <TeachingAidTextbookPreviewPage /> },
          { path: "exam-management", element: <ExamManagementPage /> },
          { path: "homework-management", element: <HomeworkManagementPage /> },
          { path: "grading-center", element: <GradingCenterPage /> },
          { path: "statistics", element: <StatisticsPage /> },
          { path: "tenants", element: <TenantManagementPage /> },
          { path: "users", element: <UserManagementPage /> },
          { path: "homeworksDetail/:homeworkId", element: <DetailPage /> },
          { path: "administrative-classes", element: <AdministrativeClassesPage /> },
          { path: "administrative-classes/:classId", element: <AdministrativeClassesDetailPage /> },
          { path: "teaching-classes", element: <TeachingClassesPage /> },
          { path: "teaching-classes/:classId", element: <TeachingClassesDetailPage /> },
          { path: "roles", element: <RoleManagementPage /> },
          { path: "casbin-policies", element: <CasbinPolicyManagementPage /> },
          { path: "subjects", element: <SubjectManagementPage /> },
          { path: "subject-groups", element: <SubjectGroupsManagementPage /> },
          { path: "grades", element: <GradeManagementPage /> },
          { path: "students", element: <StudentManagementPage /> },
          { path: "teachers", element: <TeacherManagementPage /> },
          { path: "education-stages", element: <EducationalStageManagementPage /> },
          { path: "question-type", element: <QuestionTypePage /> },
          { path: "workflow", element: <WorkflowPage /> },
          { path: "menu-management", element: <MenuManagementPage /> },
        ],
      },
    ],
  },
]);
