// 简化的 MathJax 配置
export const MathJaxConfig = {
    loader: {load: ['input/tex', 'output/chtml']},
    tex: {
        inlineMath: [
            ['$', '$'],
            ['\\(', '\\)'],
        ],
        displayMath: [
            ['$$', '$$'],
            ['\\[', '\\]'],
        ],
        processEscapes: true,
    },
    chtml: {
        scale: 1,
        // fontFamily: 'MathJax_Main, Times New Roman, serif',
        matchFontHeight: true,
    },
};