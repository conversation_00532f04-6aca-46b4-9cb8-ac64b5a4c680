
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Phone, MessageCircle } from 'lucide-react';
import { useState } from 'react';
import {toast} from "sonner"
import logoImage from '@/assets/logo.png';


const loginSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  password: z.string().min(1, '密码不能为空'),
});

export type LoginForm = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const { login } = useAuth();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);

  const form = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });


  const onSubmit = async (data: LoginForm) => {
    if (!data.username || !data.password) {
      console.log('用户名或密码不能为空');
      return;
    }

    try {
      await login(data);
      toast.info('登录成功');
      // todo 1.获取userIdentity list
      //      2.如果只有一个直接进入选择，调用selectIdentity
      //      3.如果有多个，跳转到选择页面，如果没有跳转到绑定
      // navigate('/identitySelect');
      navigate('/');
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast.error('登录失败:' + err.message);
      } else {
        toast.error('登录失败: 未知错误');
      }
    }

  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-slate-100 p-4">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-200/30 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-200/30 rounded-full blur-3xl"></div>
      </div>
      
      <Card className="w-full max-w-md relative bg-white/80 backdrop-blur-sm shadow-xl border-0">
        <CardHeader className="space-y-4 text-center pb-8">
          {/* Logo area */}
          {/*<div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">*/}
          {/*  <span className="text-white font-bold text-xl">TW</span>*/}
          {/*</div>*/}

          <div className="mx-auto w-80 h-20 flex items-center justify-center">
            <img
                src={logoImage}
                alt="语文出版社logo"
                className="w-full h-full object-contain"
            />
          </div>
          
          <div className="space-y-2">
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Teach Wise
              <p className="text-lg text-sidebar-foreground/70">语文出版社数智辅教综合服务平台</p>
            </CardTitle>
            <CardDescription className="text-slate-600">
              欢迎回来，请登录您的账户
            </CardDescription>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-700 font-medium">用户名</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="请输入用户名" 
                        className="h-12 bg-slate-50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-slate-700 font-medium">密码</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type={showPassword ? "text" : "password"}
                          placeholder="请输入密码" 
                          className="h-12 bg-slate-50 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20 pr-12" 
                          {...field} 
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="flex items-center justify-between text-sm">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input type="checkbox" className="rounded border-slate-300 text-blue-600 focus:ring-blue-500" />
                  <span className="text-slate-600">记住我</span>
                </label>
                <a href="#" className="text-blue-600 hover:text-blue-700 transition-colors">
                  忘记密码？
                </a>
              </div>
              
              <Button 
                type="submit" 
                className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium shadow-lg transition-all duration-200 transform hover:scale-[1.02]"
              >
                登录
              </Button>
            </form>
          </Form>
          
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-slate-500">或其它方式登录</span>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" className="h-12 border-slate-200 hover:bg-slate-50">
              <Phone className="w-5 h-5 mr-2" />
              手机号登录
            </Button>
            <Button variant="outline" className="h-12 border-slate-200 hover:bg-slate-50">
              <MessageCircle className="w-5 h-5 mr-2 text-green-600" />
              微信登录
            </Button>
          </div>
          
          <p className="text-center text-sm text-slate-600">
            还没有账户？{' '}
            <a href="#" className="text-blue-600 hover:text-blue-700 font-medium transition-colors">
              立即注册
            </a>
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
