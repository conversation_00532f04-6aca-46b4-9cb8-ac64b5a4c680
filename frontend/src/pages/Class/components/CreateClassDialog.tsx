import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import teachersApi from '@/services/teacherApi';
import { CreateAdministrativeClassesParams } from '@/types/administrativeClasses';
import { GradeLevelSummary } from '@/types/grade';
import { Teacher } from '@/types/teacher';
import React, { useEffect, useState } from 'react';
import { cleanOptionalFields } from './utils';

interface CreateClassDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    gradeLevels: GradeLevelSummary[];
    onCreate: (params: CreateAdministrativeClassesParams) => Promise<void>;
}

const CreateClassDialog: React.FC<CreateClassDialogProps> = ({ open, onOpenChange, gradeLevels, onCreate }) => {
    // 本地表单状态
    const [classForm, setClassForm] = useState<CreateAdministrativeClassesParams>({
        class_name: '',
        code: '',
        academic_year: new Date().getFullYear().toString(),
        grade_level_code: "",
        teacher_id: ''
    });
    const [error, setError] = useState<string | null>(null);

    //teacherseacher
    const [teacherOptions, setTeacherOptions] = useState<Teacher[]>([]);
    const [teacherSearch, setTeacherSearch] = useState('');
    const [loadingTeachers, setLoadingTeachers] = useState(false);

    const handleSubmit = async () => {
        if (!classForm.class_name) {
            setError('请填写所有必填项');
            console.log('请填写所有必填项');
            return;
        }
        const optionalKeys: (keyof CreateAdministrativeClassesParams)[] = [
            'code',
            'academic_year',
            'grade_level_code',
            'teacher_id'
          ];
          // 清洗
          const cleanedForm = cleanOptionalFields(classForm,optionalKeys);
        try {
            await onCreate(cleanedForm);
            setClassForm({ ...classForm, class_name: '', code: '', grade_level_code: '', teacher_id: '', academic_year: new Date().getFullYear().toString() });
            setError(null);
            onOpenChange(false);
        } catch {
            setError('创建班级失败');
        }
    };
    useEffect(() => {
        if (!open) return;
        setLoadingTeachers(true);
            teachersApi.findAll('tenant_zhanghan', { name_like: teacherSearch })
                .then(res => {
                    const { success, data, message } = res
                    if (!success) {
                        return setError(message || '获取班主任失败')
                    }
                    console.log("findAll:", data);
                    setTeacherOptions(Array.isArray(data) ? data : []);
                })
                .finally(() => setLoadingTeachers(false));
        }, [teacherSearch, open]);
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogTrigger asChild>
                <Button>新建班级</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>新建班级</DialogTitle>
                    <DialogDescription>请填写班级的基本信息</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="name">班级名称 *</Label>
                        <Input id="name" value={String(classForm.class_name)} onChange={e => setClassForm({ ...classForm, class_name: e.target.value })} placeholder="请输入班级名称" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="code">班级编号</Label>
                        <Input id="code" value={String(classForm.code)} onChange={e => setClassForm({ ...classForm, code: e.target.value })} placeholder="请输入班级编号" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="grade_level_code">年级</Label>
                        <Select value={String(classForm.grade_level_code)} onValueChange={value => setClassForm({ ...classForm, grade_level_code: value })}>
                            <SelectTrigger>
                                <SelectValue placeholder="选择年级" />
                            </SelectTrigger>
                            <SelectContent>
                                {gradeLevels.map(grade => (
                                    <SelectItem key={grade.id} value={grade.code}>{grade.name}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="teacher_id">班主任</Label>
                        <div className="flex gap-2">
                            {/* 左侧输入框 */}
                            <Input
                                className="flex-1"
                                placeholder="输入姓名进行搜索"
                                value={teacherSearch}
                                onChange={e => setTeacherSearch(e.target.value)}
                            />
                            {/* 右侧下拉选择 */}
                            <Select
                                value={String(classForm.teacher_id)}
                                onValueChange={value => setClassForm({ ...classForm, teacher_id: value })}
                                disabled={loadingTeachers || teacherOptions.length === 0}
                            >
                                <SelectTrigger className="flex-1 min-w-[120px]">
                                    <SelectValue placeholder={loadingTeachers ? "加载中..." : "选择班主任"} />
                                </SelectTrigger>
                                <SelectContent>
                                    {teacherOptions.map(teacher => (
                                        <SelectItem key={teacher.id} value={teacher.id}>{teacher.name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="school_year">学年</Label>
                        <Input id="school_year" type="number" value={String(classForm.academic_year)} onChange={e => setClassForm({ ...classForm, academic_year: e.target.value })} placeholder="请输入学年" />
                    </div>
                </div>
                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => onOpenChange(false)}>取消</Button>
                    <Button onClick={handleSubmit}>创建班级</Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default CreateClassDialog;