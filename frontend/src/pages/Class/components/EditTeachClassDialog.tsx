import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UpdateTeachingClassesParams } from '@/types/teachingClasses';
import { SubjectGroupsDetail } from '@/types/subjectGroups';
import React, { useEffect, useState } from 'react';
import { Teacher } from '@/types/teacher';
import teachersApi from '@/services/teacherApi';

interface EditTeachClassDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    classForm: UpdateTeachingClassesParams;
    onClassFormChange: (form: UpdateTeachingClassesParams) => void;
    subjectGroups: SubjectGroupsDetail[];
    onSave: () => void;
    onCancel: () => void;
}
const EditTeachClassDialog: React.FC<EditTeachClassDialogProps> = ({
    open,
    onOpenChange,
    classForm,
    onClassFormChange,
    subjectGroups,
    onSave,
    onCancel
}) => {
    const [teacherOptions, setTeacherOptions] = useState<Teacher[]>([]);
    const [teacherSearch, setTeacherSearch] = useState('');
    const [loadingTeachers, setLoadingTeachers] = useState(false);
    useEffect(() => {
        if (!open) return;
        if (teacherSearch === '') {
            setTeacherOptions([]);
        }
        setLoadingTeachers(true);
        teachersApi.findAll('tenant_zhanghan', { name_like: teacherSearch })
            .then(res => {
                const { success, data, message } = res
                if (!success) {
                    console.error('报错:' + message)
                    return
                }
                setTeacherOptions(Array.isArray(data) ? data : []);
            })
            .finally(() => setLoadingTeachers(false));
    }, [teacherSearch, open]);
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>编辑班级</DialogTitle>
                    <DialogDescription>修改班级信息</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="edit_name">班级名称</Label>
                        <Input id="edit_name" value={String(classForm.class_name)} onChange={e => onClassFormChange({ ...classForm, class_name: e.target.value })} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="edit_code">班级编号</Label>
                        <Input id="edit_code" value={String(classForm.code)} onChange={e => onClassFormChange({ ...classForm, code: e.target.value })} />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="subject_code">学科组</Label>
                        <Select value={String(classForm.subject_group_id)} onValueChange={value => onClassFormChange({ ...classForm, subject_group_id: value })}>
                            <SelectTrigger>
                                <SelectValue placeholder="选择学科组" />
                            </SelectTrigger>
                            <SelectContent>
                                {subjectGroups.map(subjectGroup => (
                                    <SelectItem key={subjectGroup.id as string} value={subjectGroup.id as string}>{subjectGroup.group_name}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="teacher_id">任课老师</Label>
                        <div className="flex gap-2">
                            {/* 左侧输入框 */}
                            <Input
                                className="flex-1"
                                placeholder="输入姓名进行搜索"
                                value={teacherSearch}
                                onChange={e => setTeacherSearch(e.target.value)}
                            />
                            {/* 右侧下拉选择 */}
                            <Select
                                value={String(classForm.teacher_id)}
                                onValueChange={value => onClassFormChange({ ...classForm, teacher_id: value })}
                                disabled={loadingTeachers || teacherOptions.length === 0}
                            >
                                <SelectTrigger className="flex-1 min-w-[120px]">
                                    <SelectValue placeholder={loadingTeachers ? "加载中..." : "选择任课老师"} />
                                </SelectTrigger>
                                <SelectContent>
                                    {teacherOptions.map(teacher => (
                                        <SelectItem key={teacher.id} value={teacher.id}>{teacher.name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="edit_status">状态</Label>
                        <Select value={String(classForm.is_active)} onValueChange={value => onClassFormChange({ ...classForm, is_active: value === 'true' })}>
                            <SelectTrigger>
                                <SelectValue placeholder="选择状态" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="true">启用</SelectItem>
                                <SelectItem value="false">禁用</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="edit_school_year">学年</Label>
                        <Input id="edit_school_year" type="number" value={String(classForm.academic_year)} onChange={e => onClassFormChange({ ...classForm, academic_year: e.target.value })} />
                    </div>
                </div>
                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={onCancel}>取消</Button>
                    <Button onClick={onSave}>保存</Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default EditTeachClassDialog;
