/**
 * 清理对象中的空字符串字段，将其转为 undefined
 * @param obj 目标对象
 * @param keys 需要检查的键数组
 * @returns 清理后的新对象
 */
export function cleanOptionalFields<T extends object>(obj: T, keys: (keyof T)[]): T {
    const cleaned = { ...obj };
    keys.forEach(key => {
      if (cleaned[key] === '' || cleaned[key] === "null" || cleaned[key] === null) {
        cleaned[key] = undefined as any; // 使用 as any 绕过类型检查
      }
    });
    return cleaned;
  }