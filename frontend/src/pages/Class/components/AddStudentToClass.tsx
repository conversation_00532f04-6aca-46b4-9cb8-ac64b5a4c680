import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { FindAllStudentParams } from '@/types/student';
import { Search, UserPlus } from 'lucide-react';
import React, { useState } from 'react';

interface Student {
  id: string;
  name: string;
  student_number: string;
  gender?: string;
}

interface AddStudentToClassDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddStudents: (studentIds: string[]) => Promise<void>;
  onSearch: (searchParams: FindAllStudentParams) => Promise<Student[]>;
  title?: string;
  description?: string;
}

const AddStudentToClassDialog: React.FC<AddStudentToClassDialogProps> = ({
  open,
  onOpenChange,
  onAddStudents,
  onSearch,
  title = "添加学生",
  description = "搜索并选择要添加的学生"
}) => {
  const [searchName, setSearchName] = useState('');
  const [searchStudentNumber, setSearchStudentNumber] = useState('');
  const [searchResults, setSearchResults] = useState<Student[]>([]);
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isAdding, setIsAdding] = useState(false);

  const handleSearch = async () => {

    if (!searchName.trim() && !searchStudentNumber.trim()) {
      return;
    }
    setIsSearching(true);
    try {
       // 直接构造搜索参数
       const searchParams: FindAllStudentParams = {
        name_like: searchName.trim(),
        student_number: searchStudentNumber.trim(),
        page_params: { page: 1, page_size: 10 }
      };
      const results = await onSearch(searchParams);
      setSearchResults(results);
    } catch (error) {
      console.error('搜索学生失败:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleStudentToggle = (studentId: string) => {
    setSelectedStudents(prev => 
      prev.includes(studentId) 
        ? prev.filter(id => id !== studentId)
        : [...prev, studentId]
    );
  };

  const handleAddStudents = async () => {
    if (selectedStudents.length === 0) return;

    setIsAdding(true);
    try {
      await onAddStudents(selectedStudents);
      // 重置状态
      setSelectedStudents([]);
      setSearchResults([]);
      setSearchName('');
      setSearchStudentNumber('');
      onOpenChange(false);
    } catch (error) {
      console.error('添加学生失败:', error);
    } finally {
      setIsAdding(false);
    }
  };

  const handleCancel = () => {
    setSelectedStudents([]);
    setSearchResults([]);
    setSearchName('');
    setSearchStudentNumber('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* 搜索区域 */}
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="search_name">学生姓名</Label>
              <Input
                id="search_name"
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                placeholder="请输入学生姓名"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <div className="flex-1">
              <Label htmlFor="search_student_number">学号</Label>
              <Input
                id="search_student_number"
                value={searchStudentNumber}
                onChange={(e) => setSearchStudentNumber(e.target.value)}
                placeholder="请输入学号"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <div className="flex items-end">
              <Button 
                onClick={handleSearch} 
                disabled={isSearching || (!searchName.trim() && !searchStudentNumber.trim())}
              >
                <Search className="h-4 w-4 mr-2" />
                {isSearching ? '搜索中...' : '查询'}
              </Button>
            </div>
          </div>

          {/* 搜索结果 */}
          {searchResults.length > 0 && (
            <div className="space-y-2">
              <Label>搜索结果 ({searchResults.length} 人)</Label>
              <ScrollArea className="h-[300px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">选择</TableHead>
                      <TableHead>姓名</TableHead>
                      <TableHead>学号</TableHead>
                      <TableHead>性别</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {searchResults.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedStudents.includes(student.id)}
                            onCheckedChange={() => handleStudentToggle(student.id)}
                          />
                        </TableCell>
                        <TableCell className="font-medium">{student.name}</TableCell>
                        <TableCell>{student.student_number}</TableCell>
                        <TableCell>{student.gender || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </div>
          )}

          {/* 选中学生数量提示 */}
          {selectedStudents.length > 0 && (
            <div className="text-sm text-muted-foreground">
              已选择 {selectedStudents.length} 名学生
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button 
            onClick={handleAddStudents}
            disabled={selectedStudents.length === 0 || isAdding}
          >
            <UserPlus className="h-4 w-4 mr-2" />
            {isAdding ? '添加中...' : `添加 (${selectedStudents.length})`}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddStudentToClassDialog;