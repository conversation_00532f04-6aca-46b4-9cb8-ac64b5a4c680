import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SubjectGroupDetail } from "@/pages/SubjectGroupsManagement/components";
import teachersApi from "@/services/teacherApi";
import { Teacher, TeacherSummary } from "@/types/teacher";
import { CreateTeachingClassesParams } from "@/types/teachingClasses";
import React, { useEffect, useState } from 'react';

interface CreateTeachClassDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    createClassForm: CreateTeachingClassesParams;
    onCreateFormChange: (form: CreateTeachingClassesParams) => void;
    subjectGroups: SubjectGroupDetail[];
    onCreate: () => void;
    onCancel: () => void;
}
const CreateTeachClassDialog: React.FC<CreateTeachClassDialogProps> = ({
    open,
    onOpenChange,
    createClassForm,
    onCreateFormChange,
    subjectGroups,
    onCreate,
    onCancel
}) => {
    
    const [teacherOptions, setTeacherOptions] = useState<Teacher[]>([]);
    const [teacherSearch, setTeacherSearch] = useState('');
    const [loadingTeachers, setLoadingTeachers] = useState(false);
    useEffect(() => {
        if (!open) return;
        if (teacherSearch === '') {
            setTeacherOptions([]);
        }
        setLoadingTeachers(true);
            teachersApi.findAll('tenant_zhanghan', { name_like: teacherSearch })
                .then(res => {
                    const { success, data, message } = res
                    if (!success) {
                        console.error('报错:' + message)
                        return
                    }
                    setTeacherOptions(Array.isArray(data) ? data : []);
                })
                .finally(() => setLoadingTeachers(false));
        }, [teacherSearch, open]);
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogTrigger asChild>
                <Button>新建班级</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>新建班级</DialogTitle>
                    <DialogDescription>请填写班级的基本信息</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="name">班级名称 *</Label>
                        <Input id="name" value={String(createClassForm.class_name)} onChange={e => onCreateFormChange({ ...createClassForm, class_name: e.target.value })} placeholder="请输入班级名称" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="code">班级编号</Label>
                        <Input id="code" value={String(createClassForm.code)} onChange={e => onCreateFormChange({ ...createClassForm, code: e.target.value })} placeholder="请输入班级编号" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="subject_code">学科</Label>
                        <Select value={String(createClassForm.subject_group_id)} onValueChange={value => onCreateFormChange({ ...createClassForm, subject_group_id: value })}>
                            <SelectTrigger>
                                <SelectValue placeholder="选择学科组" />
                            </SelectTrigger>
                            <SelectContent>
                                {subjectGroups.map(subjectGroup => (
                                    <SelectItem key={subjectGroup.id as string} value={subjectGroup.id as string}>{subjectGroup.group_name}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    {/* <div className="space-y-2">
                        <Label htmlFor="teacher_id">任课老师</Label>
                        <Select value={String(createClassForm.teacher_id)} onValueChange={value => onCreateFormChange({ ...createClassForm, teacher_id: value })}>
                            <SelectTrigger>
                                <SelectValue placeholder="选择任课老师" />
                            </SelectTrigger>
                            <SelectContent>
                                {teachers.map(teacher=>(
                                    <SelectItem key={teacher.id as string} value={teacher.id as string}>{teacher.name}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div> */}
                    <div className="space-y-2">
                        <Label htmlFor="teacher_id">任课老师</Label>
                        <div className="flex gap-2">
                            {/* 左侧输入框 */}
                            <Input
                                className="flex-1"
                                placeholder="输入姓名进行搜索"
                                value={teacherSearch}
                                onChange={e => setTeacherSearch(e.target.value)}
                            />
                            {/* 右侧下拉选择 */}
                            <Select
                                value={String(createClassForm.teacher_id)}
                                onValueChange={value => onCreateFormChange({ ...createClassForm, teacher_id: value })}
                                disabled={loadingTeachers || teacherOptions.length === 0}
                            >
                                <SelectTrigger className="flex-1 min-w-[120px]">
                                    <SelectValue placeholder={loadingTeachers ? "加载中..." : "选择任课老师"} />
                                </SelectTrigger>
                                <SelectContent>
                                    {teacherOptions.map(teacher => (
                                        <SelectItem key={teacher.id} value={teacher.id}>{teacher.name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="school_year">学年</Label>
                        <Input id="school_year" type="number" value={String(createClassForm.academic_year)} onChange={e => onCreateFormChange({ ...createClassForm, academic_year: e.target.value })} placeholder="请输入学年" />
                    </div>
                </div>
                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={onCancel}>取消</Button>
                    <Button onClick={onCreate}>创建班级</Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}
export default CreateTeachClassDialog;