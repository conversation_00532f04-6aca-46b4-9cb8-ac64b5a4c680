import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { SubjectGroupsApi } from '@/services/subjectGroupsApi';
import { TeachingClassesApi } from '@/services/teachingClassesApi';
import { CreateTeachingClassesParams, TeachingClassesDetail, UpdateTeachingClassesParams } from '@/types/teachingClasses';
import { Edit, ListCollapse, RefreshCw, Trash2, Upload } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import ClassStatisticsCards from './components/ClassStatisticsCards';
import { Badge } from '@/components/ui/badge';
import { data, useNavigate } from 'react-router-dom';
import { getIdentityInfoFromLocalStorage } from '@/lib/apiUtils';
import EditTeachClassDialog from './components/EditTeachClassDialog';
import { SubjectGroupsDetail } from '@/types/subjectGroups';
import CreateTeachClassDialog from './components/CreateTeachClassDialog';
import { cleanOptionalFields } from './components/utils';



const TeachingClassesPage: React.FC = () => {
    // State management
    const [classes, setClasses] = useState<TeachingClassesDetail[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Statistics
    const [totalClasses, setTotalClasses] = useState(0);
    const [totalTeachers, setTotalTeachers] = useState(0);
    const [totalStudents, setTotalStudents] = useState(0);
    // Dialog states
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [selectedClass, setSelectedClass] = useState<TeachingClassesDetail | null>(null);

    // Form state
    const [subjectGroups, setSubjectGroups] = useState<SubjectGroupsDetail[]>([]);
    const [classForm, setClassForm] = useState<CreateTeachingClassesParams>({
        class_name: '',
        code: '',
        academic_year: new Date().getFullYear().toString(),
        subject_group_id: '',
        teacher_id: '',
    });
    const [editClassForm, setEditClassForm] = useState<UpdateTeachingClassesParams>({
        id: '',
        class_name: '',
        code: '',
        academic_year: new Date().getFullYear().toString(),
        subject_group_id: "",
        teacher_id: '',
        is_active: true
    });
    // Get tenant ID from auth context (mock for now)
    const identityInfo = getIdentityInfoFromLocalStorage();
    const tenantId = identityInfo?.tenant_id || '';
    const tenantName = identityInfo?.tenant_name || '';

    useEffect(() => {
        loadInitialData();
    }, []);
    const navigate = useNavigate();
    const loadInitialData = async () => {
        setLoading(true);
        try {
            //.获取教学班列表
            TeachingClassesApi.getUserClassList(tenantId,tenantName).then(res => {
                const { success, data, message } = res
                if (!success) {
                    return setError(message)
                }
                setClasses(Array.isArray(data) ? data : []);
            })
            //查询所有学科组
            SubjectGroupsApi.findAll('tenant_zhanghan').then(res => {
                const { success, data, message } = res
                if (!success) {
                    console.error('报错:' + message)
                    return
                }
                setSubjectGroups(Array.isArray(data) ? data : []);
            });
            // 2. 获取统计数据
            TeachingClassesApi.getStatistics(tenantId,tenantName).then(stats => {
                const { success, data, message } = stats
                if (!success) {
                    return setError(message)
                }
                setTotalClasses(Number(data?.total_classes) || 0);
                setTotalTeachers(Number(data?.total_teacher) || 0);
                setTotalStudents(Number(data?.total_students) || 0);
            })
            setError(null);
        } catch (err) {
            setError('加载数据失败');
            //setClasses([]);
        } finally {
            setLoading(false);
        }

    };
    // 编辑班级
    const handleUpdateClass = async () => {
        if (!selectedClass) return;
        try {
            const optionalKeys: (keyof UpdateTeachingClassesParams)[] = [
                'class_name',
                'code',
                'academic_year',
                'subject_group_id',
                'teacher_id',
            ];  
            const cleanedForm = cleanOptionalFields(editClassForm, optionalKeys);
            await TeachingClassesApi.updateClasses(tenantId,tenantName, cleanedForm);
            setIsEditDialogOpen(false);
            setSelectedClass(null);
            resetForm();
            loadInitialData();
        } catch (err) {
            setError('编辑班级失败');
        }
    };

    // 删除班级
    const handleDeleteClass = async () => {
        if (!selectedClass) return;
        try {
            await TeachingClassesApi.deleteClass(tenantId,tenantName, { class_id: String(selectedClass.id) });
            setIsDeleteDialogOpen(false);
            setSelectedClass(null);
            loadInitialData();
        } catch (err) {
            setError('删除班级失败');
        }
    };

    // 批量导入班级（需补全接口）
    const handleBatchImport = async () => {
        // TODO: 实现批量导入功能
        alert('批量导入功能待实现');
    };

    const resetForm = () => {
        setClassForm({
            class_name: '',
            code: '',
            academic_year: new Date().getFullYear().toString(),
            subject_group_id: '',
            teacher_id: '',
        });
    };

    const openEditDialog = (cls: TeachingClassesDetail) => {
        setSelectedClass(cls);
        setEditClassForm({
            id: String(cls.id),
            class_name: String(cls.class_name),
            code: String(cls.code),
            academic_year: String(cls.academic_year),
            subject_group_id: String(cls.subject_group_id),
            teacher_id: String(cls.teacher_id),
            is_active: cls.is_active
        });
        setIsEditDialogOpen(true);
    };

    const openDeleteDialog = (cls: TeachingClassesDetail) => {
        setSelectedClass(cls);
        setIsDeleteDialogOpen(true);
    };

    //创建班级
    const handleCreateClass = async () => {
        try {
            if (!classForm.class_name) {
                setError('请填写所有必填项');
                return;
            }
            const optionalKeys: (keyof CreateTeachingClassesParams)[] = [
                'code',
                'academic_year',
                'subject_group_id',
                'teacher_id'
              ];
            // 清洗
            const cleanedForm = cleanOptionalFields(classForm,optionalKeys);
            await TeachingClassesApi.createClasses(tenantId,tenantName, cleanedForm)
            setIsCreateDialogOpen(false);
            resetForm();
            loadInitialData();
        } catch (err) {
            setError('创建班级失败');
        }
    }

    const getClassStatus = (is_active: Boolean) => {
        return is_active ? (
            <Badge variant="default">启用</Badge>
        ) : (
            <Badge variant="secondary">禁用</Badge>
        );
    }


    if (loading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div className="space-y-2">
                        <Skeleton className="h-8 w-32" />
                        <Skeleton className="h-4 w-48" />
                    </div>
                    <Skeleton className="h-10 w-24" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    {[...Array(4)].map((_, i) => (
                        <Card key={i}>
                            <CardHeader>
                                <Skeleton className="h-4 w-24" />
                            </CardHeader>
                            <CardContent>
                                <Skeleton className="h-8 w-16" />
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        );
    }
    const getOperationButtons = (cls: TeachingClassesDetail) => {
        return (
            <div className="flex gap-1">
                <Button variant="ghost" size="sm" onClick={() => navigate(`/teaching-classes/${cls.id}`)}>
                    <ListCollapse className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={() => openEditDialog(cls)}>
                    <Edit className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(cls)}>
                    <Trash2 className="h-4 w-4" />
                </Button>
            </div>
        );
    }
    //------------------------------------------------------------------组件部分
    function RefreshButton() {
        return <Button variant="outline" onClick={() => {
            loadInitialData()
        }}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
        </Button>
    }
    function BatchImportButton() {
        return <Button variant="outline" onClick={handleBatchImport}>
            <Upload className="h-4 w-4 mr-2" />
            批量导入
        </Button>
    }

    return (
        <div className="space-y-6">
            {error && (
                <AlertDialog>
                    <AlertDialogDescription>{error}</AlertDialogDescription>
                </AlertDialog>
            )}
            {/* 顶部按钮栏 */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">班级管理</h1>
                    <p className="text-muted-foreground">创建、管理和监控班级</p>
                </div>
                <div className="flex gap-2">
                    <RefreshButton />
                </div>
            </div>
            {/* 统计卡片 */}
            <ClassStatisticsCards
                totalClasses={totalClasses}
                totalTeachers={totalTeachers}
                totalStudents={totalStudents}
            />
            {/* 班级列表 */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>教学班列表</CardTitle>
                        <CardDescription>当前共有 {classes.length} 个教学班</CardDescription>
                    </div>
                    <div className="flex gap-2">
                        <CreateTeachClassDialog
                            open={isCreateDialogOpen}
                            onOpenChange={setIsCreateDialogOpen}
                            createClassForm={classForm}
                            onCreateFormChange={setClassForm}
                            subjectGroups={subjectGroups}
                            onCreate={handleCreateClass}
                            onCancel={() => setIsCreateDialogOpen(false)}
                        />
                        <BatchImportButton />
                    </div>
                </CardHeader>
                <CardContent>
                    <Table className="w-full table-fixed">
                        <TableHeader>
                            <TableRow>
                                <TableHead>班级名称</TableHead>
                                <TableHead>班级编号</TableHead>
                                <TableHead>任课老师</TableHead>
                                <TableHead>学科组</TableHead>
                                <TableHead>班级人数</TableHead>
                                <TableHead>状态</TableHead>
                                <TableHead>操作</TableHead>
                            </TableRow>
                        </TableHeader>
                    </Table>
                    <ScrollArea className="h-[350px] border-none mt-4">
                        <Table className="w-full table-fixed ">
                            <TableBody>
                                {classes.map(cls => (
                                    <TableRow key={cls.id as string}>
                                        <TableCell className="font-medium">{cls.class_name}</TableCell>
                                        <TableCell>{cls.code}</TableCell>
                                        <TableCell>{cls.teacher_name || '-'}</TableCell>
                                        <TableCell>{cls.subject_groups_name || '-'}</TableCell>
                                        <TableCell>{cls.total_student?.toString() || '-'}</TableCell>
                                        <TableCell>{getClassStatus(cls.is_active)}</TableCell>
                                        <TableCell>{getOperationButtons(cls)}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </ScrollArea>
                </CardContent>
            </Card>
            {/* TODO查看学生列表对话框*/}
            <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>展示学生列表</DialogTitle>
                        <DialogDescription>查看班级学生列表</DialogDescription>
                    </DialogHeader>
                    <div className="my-6 w-full overflow-y-auto">
                        <table className="w-full">
                            <tbody>
                                <tr className="even:bg-muted m-0 border-t p-0">
                                    <td className="border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right">
                                        Empty
                                    </td>
                                    <td className="border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right">
                                        Overflowing
                                    </td>
                                    <td className="border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right">
                                        Overflowing
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </DialogContent>
            </Dialog>
            {/* 编辑班级对话框 */}
            <EditTeachClassDialog
                open={isEditDialogOpen}
                onOpenChange={setIsEditDialogOpen}
                classForm={editClassForm}
                onClassFormChange={setEditClassForm}
                subjectGroups={subjectGroups}
                onSave={handleUpdateClass}
                onCancel={() => setIsEditDialogOpen(false)}
            />
            {/* 删除班级对话框 */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>确认删除</DialogTitle>
                        <DialogDescription>你确定要删除班级 {selectedClass?.class_name} 吗？此操作无法撤销。</DialogDescription>
                    </DialogHeader>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>取消</Button>
                        <Button variant="destructive" onClick={handleDeleteClass}>删除</Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default TeachingClassesPage;