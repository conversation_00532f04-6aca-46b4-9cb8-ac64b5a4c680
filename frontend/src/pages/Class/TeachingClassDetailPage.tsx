import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { getIdentityInfoFromLocalStorage } from '@/lib/apiUtils';
import { TeachingClassesApi } from '@/services/teachingClassesApi';
import { FindAllStudentParams, Student } from '@/types/student';
import { MoveStudentToTeachingClassesParams, TeachingClassesDetail, UpdateTeachingClassesParams } from '@/types/teachingClasses';
import { ArrowLeft, Edit, Layers, RefreshCw, Search, Trash2, UserPlus, Users } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import EditTeachClassDialog from './components/EditTeachClassDialog';
import { SubjectGroupsApi } from '@/services/subjectGroupsApi';
import teachersApi from '@/services/teacherApi';
import { SubjectGroupsDetail } from '@/types/subjectGroups';
import { TeacherSummary } from '@/types/teacher';
import { AdministrativeClassesApi } from '@/services/administrativeClassesApi';
import { cleanOptionalFields } from './components/utils';
import AddStudentToClassDialog from './components/AddStudentToClass';
import studentsApi from '@/services/studentApi';

interface SearchParams {
    name: string;
    status: string;
    type: string;
}
interface StudentFormData {
    name: string;
    student_number: string;
    phone: string;
    parent_phone: string;
    status: string;
    type: string;
}


const TeachingClassesDetailPage: React.FC = () => {
    const { classId } = useParams<{ classId: string }>();
    const navigate = useNavigate();

    // State management
    const [classDetail, setClassDetail] = useState<TeachingClassesDetail | null>(null);
    const [students, setStudents] = useState<Student[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [subjectGroups, setSubjectGroups] = useState<SubjectGroupsDetail[]>([]);
    const [teachers, setTeachers] = useState<TeacherSummary[]>([]);
    // Dialog states
    const [isAddStudentDialogOpen, setIsAddStudentDialogOpen] = useState(false);
    const [isBatchAddDialogOpen, setIsBatchAddDialogOpen] = useState(false);
    const [isEditStudentDialogOpen, setIsEditStudentDialogOpen] = useState(false);
    const [isEditClassDialogOpen, setIsEditClassDialogOpen] = useState(false);
    const [isDeleteStudentDialogOpen, setIsDeleteStudentDialogOpen] = useState(false);
    const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
    const [isDeleteClassDialogOpen, setIsDeleteClassDialogOpen] = useState(false);


    // Get tenant ID from auth context (mock for now)
    const identityInfo = getIdentityInfoFromLocalStorage();
    const tenantId = identityInfo?.tenant_id || '';
    const tenantName = identityInfo?.tenant_name || '';
    // Search states
    const [searchParams, setSearchParams] = useState<SearchParams>({
        name: '',
        status: '',
        type: ''
    });

    const [classForm, setClassForm] = useState<UpdateTeachingClassesParams>({
        id: '',
        class_name: '',
        code: '',
        subject_group_id: '',
        teacher_id: '',
        is_active: true,
        academic_year: new Date().getFullYear().toString()
    });
    const [searchStudentParams, SetSearchStudentParams] = useState<FindAllStudentParams>({
        student_number: '',
        name_like: '',
        id_number: '',
        phone: '',
        page_params: {
            page: 1,
            page_size: 5
        }
    });

    useEffect(() => {
        if (classId) {
            console.log("classId", classId);
            //获取当前班级详细信息
            loadClassDetail();
            //获取班级学生列表
            loadStudents();
            //查询所有学科组
            SubjectGroupsApi.findAll('tenant_zhanghan').then(res => {
                const { success, data, message } = res
                if (!success) {
                    console.error('报错:' + message)
                    return
                }
                console.log("findAll:", data)
                setSubjectGroups(Array.isArray(data) ? data : []);
            });
            //查询任课老师简要信息
            teachersApi.getTeacherSummaries(tenantName, true).then(res => {
                const { success, data, message } = res
                if (!success) {
                    console.error('报错:' + message)
                    return
                }
                console.log("getTeacherSummaries:", data)
                setTeachers(Array.isArray(data) ? data : []);
            });
        }
    }, [classId]);

    const loadClassDetail = async () => {
        try {
            TeachingClassesApi.getUserClassList(tenantId, tenantName).then(res => {
                const { success, data, message } = res;
                if (!success) {
                    return setError(message);
                }
                console.log("getUserClassList:", data);
                const currentClass = data?.find(item => item.id === classId) || null;
                setClassDetail(currentClass);
            })
            setLoading(false);
        } catch (err) {
            setError('加载班级详情失败');
        }
    };
    const loadStudents = async () => {
        try {
            TeachingClassesApi.findAllStudentInClass(tenantId, tenantName, { class_id: classId! }).then(res => {
                const { success, data, message } = res;
                if (!success) {
                    return setError(message);
                }
                console.log("findAllStudentInClass:", data);
                setStudents(Array.isArray(data) ? data : []);
            })
        } catch (err) {
            setError('加载学生列表失败');
        } finally {
            setLoading(false);
        }
    };
    const handleUpdateClass = async () => {
        try {
            const optionalKeys: (keyof UpdateTeachingClassesParams)[] = [
                'class_name',
                'code',
                'academic_year',
                'subject_group_id',
                'teacher_id'
            ];
            // 清洗
            const cleanedForm = cleanOptionalFields(classForm, optionalKeys);
            TeachingClassesApi.updateClasses(tenantId, tenantName, cleanedForm).then(res => {
                const { success, message } = res;
                if (!success) {
                    return setError(message);
                }
                console.log("updateClasses:", res);
                setIsEditClassDialogOpen(false);
                loadClassDetail();
            })
        } catch (err) {
            setError('更新班级信息失败');
        }
    };
    const handleSearchStudents = async (searchParams: FindAllStudentParams) => {
        try {
            const optionalKeys: (keyof FindAllStudentParams)[] = [
                'name_like',
                'student_number',
                'page_params',
                'id_number',
                'phone'
            ];
            const cleanedForm = cleanOptionalFields(searchParams, optionalKeys);
            const res = await studentsApi.pageAllStudent(tenantId, tenantName, cleanedForm);
            return res.data || [];
        } catch (err) {
            setError('查询学生失败');
            return [];
        }
    };
    const handleAddStudent = async (studentIds: string[]) => {
        try {
            for (const studentId of studentIds) {
                if(students.find(student => student.id === studentId)){
                    continue;
                }
                await TeachingClassesApi.moveStudentToTeachingClasses(tenantId, tenantName, {
                    class_id: classId!,
                    student_id: studentId
                });
            }
            loadStudents();
        } catch (err) {
            setError('添加学生失败');
            throw err;
        }
    };
    const handleDeleteClass = async () => {
        try {
            await TeachingClassesApi.deleteClass(tenantId, tenantName, { class_id: classId! });
            navigate('/teaching-classes');
        } catch (err) {
            setError('删除班级失败');
        }
    };
    const handleBatchAddStudents = async () => {
        try {
            console.log("功能待实现");
        } catch (err) {
            setError('批量添加学生失败');
        }
    };

    const handleUpdateStudent = async () => {
        if (!selectedStudent) return;
        try {
            //await MockClassDetailApi.updateStudent(selectedStudent.id, studentForm);
            setIsEditStudentDialogOpen(false);
            setSelectedStudent(null);
            loadStudents();
        } catch (err) {
            setError('更新学生信息失败');
        }
    };

    const handleDeleteStudent = async () => {
        if (!selectedStudent) return;
        try {
            await TeachingClassesApi.removeStudentFromTeachingClasses(tenantId, tenantName, {
                class_id: classId!,
                student_id: selectedStudent.id
            });
            setIsDeleteStudentDialogOpen(false);
            setSelectedStudent(null);
            loadClassDetail();
            loadStudents();
        } catch (err) {
            setError('删除学生失败');
        }
    };


    const openEditStudentDialog = (student: Student) => {
        setSelectedStudent(student);
        // setStudentForm({

        // });
        setIsEditStudentDialogOpen(true);
    };

    const openDeleteStudentDialog = (student: Student) => {
        setSelectedStudent(student);
        setIsDeleteStudentDialogOpen(true);
    };
    const openEditClassDialog = () => {
        if (classDetail) {
            setClassForm({
                id: classDetail.id,
                class_name: classDetail.class_name,
                code: classDetail.code,
                subject_group_id: classDetail.subject_group_id || '',
                teacher_id: classDetail.teacher_id || '',
                is_active: classDetail.is_active,
                academic_year: classDetail.academic_year || new Date().getFullYear().toString()
            });
        }
        setIsEditClassDialogOpen(true);
    };

    const getStatusBadge = (status: string) => {
        switch (status?.toLowerCase()) {
            case 'active':
                return <Badge variant="default">在校</Badge>;
            case 'inactive':
                return <Badge variant="secondary">休学</Badge>;
            case 'graduated':
                return <Badge variant="outline">毕业</Badge>;
            case 'transferred':
                return <Badge variant="destructive">转学</Badge>;
            default:
                return <Badge variant="secondary">未知</Badge>;
        }
    };
    if (loading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-32" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    {[...Array(4)].map((_, i) => (
                        <Card key={i}>
                            <CardHeader>
                                <Skeleton className="h-4 w-24" />
                            </CardHeader>
                            <CardContent>
                                <Skeleton className="h-8 w-16" />
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {error && (
                <AlertDialog>
                    <AlertDialogDescription>{error}</AlertDialogDescription>
                </AlertDialog>
            )}
            {/* 顶部导航栏 */}
            <Button variant="outline" onClick={() => navigate('/teaching-classes')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
            </Button>
            <div className="flex items-center justify-between">

                <div className="flex items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold">班级详细</h1>
                        <p className="text-muted-foreground">管理班级信息和学生</p>
                    </div>
                </div>
                <div className="flex gap-2">
                    <Button variant="outline" onClick={loadStudents}>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        刷新
                    </Button>
                    <Button onClick={openEditClassDialog}>
                        <Edit className="h-4 w-4 mr-2" />
                        编辑班级
                    </Button>
                    <EditTeachClassDialog
                        open={isEditClassDialogOpen}
                        onOpenChange={setIsEditClassDialogOpen}
                        classForm={classForm}
                        onClassFormChange={setClassForm}
                        subjectGroups={subjectGroups}
                        onSave={handleUpdateClass}
                        onCancel={() => setIsEditClassDialogOpen(false)}
                    />
                    <Button variant="destructive" onClick={() => setIsDeleteClassDialogOpen(true)}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        删除班级
                    </Button>
                </div>
            </div>

            {/* 班级信息卡片 */}
            {classDetail && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">班级名称</CardTitle>
                            <Layers className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{classDetail.class_name}</div>
                            <div className="text-sm text-muted-foreground">{classDetail.code}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">班级人数</CardTitle>
                            <Layers className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{classDetail.total_student?.toString()}</div>
                            {/* <div className="text-sm text-muted-foreground">名学生</div> */}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">任课老师</CardTitle>
                            <Layers className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{classDetail.teacher_name}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">学科组</CardTitle>
                            <Layers className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{classDetail.subject_groups_name}</div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* 学生列表 */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>学生列表</CardTitle>
                        <CardDescription>当前共有 {students.length} 名学生</CardDescription>
                    </div>
                    <div className="flex gap-2">
                        <Button onClick={() => setIsAddStudentDialogOpen(true)}>
                            <UserPlus className="h-4 w-4 mr-2" />
                            添加学生
                        </Button>
                        <Button variant="outline" onClick={() => setIsBatchAddDialogOpen(true)}>
                            <Users className="h-4 w-4 mr-2" />
                            批量添加
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    {/* 搜索栏 */}
                    <div className="flex gap-4 mb-4">
                        <div className="flex-1">
                            <Input
                                placeholder="搜索学生姓名"
                                value={searchParams.name}
                                onChange={(e) => setSearchParams({ ...searchParams, name: e.target.value })}
                                className="max-w-sm"
                            />
                        </div>
                        <Select value={searchParams.status} onValueChange={(value) => setSearchParams({ ...searchParams, status: value })}>
                            <SelectTrigger className="w-32">
                                <SelectValue placeholder="状态" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="ALL">全部状态</SelectItem>
                                <SelectItem value="active">在校</SelectItem>
                                <SelectItem value="inactive">休学</SelectItem>
                                <SelectItem value="graduated">毕业</SelectItem>
                                <SelectItem value="transferred">转学</SelectItem>
                            </SelectContent>
                        </Select>
                        <Select value={searchParams.type} onValueChange={(value) => setSearchParams({ ...searchParams, type: value })}>
                            <SelectTrigger className="w-32">
                                <SelectValue placeholder="类型" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="ALL">全部类型</SelectItem>
                                <SelectItem value="REGULAR">普通</SelectItem>
                                <SelectItem value="TRANSFER">转学</SelectItem>
                            </SelectContent>
                        </Select>
                        <Button onClick={loadStudents}>
                            <Search className="h-4 w-4 mr-2" />
                            查询
                        </Button>
                    </div>
                    {/* 学生表格 */}
                    <ScrollArea className="h-[400px]">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>学生姓名</TableHead>
                                    <TableHead>学号</TableHead>
                                    <TableHead>性别</TableHead>
                                    <TableHead>电话</TableHead>
                                    <TableHead>状态</TableHead>
                                    <TableHead>操作</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {students.map((student) => (
                                    <TableRow key={student.id}>
                                        <TableCell className="font-medium">{student.name}</TableCell>
                                        <TableCell>{student.student_number}</TableCell>
                                        <TableCell>{student.gender || '-'}</TableCell>
                                        <TableCell>{student.phone || '-'}</TableCell>
                                        <TableCell>{getStatusBadge(student.status)}</TableCell>
                                        <TableCell>
                                            <div className="flex gap-1">
                                                {/* <Button variant="ghost" size="sm" onClick={() => openEditStudentDialog(student)}>
                                                    <Edit className="h-4 w-4" />
                                                </Button> */}
                                                <Button variant="ghost" size="sm" onClick={() => openDeleteStudentDialog(student)}>
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </ScrollArea>
                </CardContent>
            </Card>
            {/* 添加学生对话框 */}
            <AddStudentToClassDialog
                open={isAddStudentDialogOpen}
                onOpenChange={setIsAddStudentDialogOpen}
                onAddStudents={handleAddStudent}
                onSearch={handleSearchStudents}
                title="添加学生到教学班"
                description="搜索并选择要添加到班级的学生"
            />
            {/* TODO批量添加学生对话框 */}
            <Dialog open={isBatchAddDialogOpen} onOpenChange={setIsBatchAddDialogOpen}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle>功能待实现</DialogTitle>
                        <DialogDescription>功能待实现</DialogDescription>
                    </DialogHeader>
                </DialogContent>
            </Dialog>
            {/* 编辑学生对话框 */}

            {/* 删除学生对话框 */}
            <Dialog open={isDeleteStudentDialogOpen} onOpenChange={setIsDeleteStudentDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>确认删除</DialogTitle>
                        <DialogDescription>你确定要删除学生 {selectedStudent?.name} 吗？此操作无法撤销。</DialogDescription>
                    </DialogHeader>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsDeleteStudentDialogOpen(false)}>取消</Button>
                        <Button variant="destructive" onClick={handleDeleteStudent}>删除</Button>
                    </div>
                </DialogContent>
            </Dialog>

            {/* 删除班级对话框 */}
            <Dialog open={isDeleteClassDialogOpen} onOpenChange={setIsDeleteClassDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>确认删除</DialogTitle>
                        <DialogDescription>你确定要删除班级 {classDetail?.class_name} 吗？此操作无法撤销。</DialogDescription>
                    </DialogHeader>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsDeleteClassDialogOpen(false)}>取消</Button>
                        <Button variant="destructive" onClick={handleDeleteClass}>删除</Button>
                    </div>
                </DialogContent>
            </Dialog>

        </div>
    );
};

export default TeachingClassesDetailPage;
