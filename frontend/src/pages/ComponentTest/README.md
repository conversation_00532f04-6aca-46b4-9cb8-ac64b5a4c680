# MultiSelect 组件测试页面

## 访问方式

启动项目后，在浏览器中访问：
```
http://localhost:5173/component-test
```

## 测试功能

测试页面包含以下功能演示：

### 🎯 基本功能
- **基本多选** - 展示核心的多选功能和搜索
- **分组选项** - 按类别对选项进行分组显示
- **创建选项** - 允许用户创建不存在的选项

### 🚀 高级功能
- **异步搜索** - 模拟远程搜索，带加载状态
- **固定选项** - 演示不可删除的固定选项和选择限制
- **限制选择** - 最大选择数量限制和相关提示

### 🎨 状态演示
- **禁用状态** - 展示组件的禁用状态
- **自定义样式** - 演示自定义样式配置
- **键盘操作** - 完整的键盘导航支持

## 快速开始

1. 启动开发服务器：
   ```bash
   npm run dev
   ```

2. 登录系统后，在地址栏输入 `/component-test` 或直接访问完整URL

3. 在测试页面中体验各种功能：
   - 搜索和筛选选项
   - 创建自定义选项
   - 测试键盘导航
   - 查看异步搜索效果

## 组件引用

在项目中使用 MultiSelect 组件：

```tsx
import { MultiSelect } from "@/components/ui/multi-select"

const options = [
  { value: "react", label: "React" },
  { value: "vue", label: "Vue.js" }
]

function MyComponent() {
  const [selected, setSelected] = useState([])
  
  return (
    <MultiSelect
      options={options}
      value={selected}
      onChange={setSelected}
      placeholder="选择选项..."
    />
  )
}
```

详细的API文档请参考 `src/components/ui/multi-select.md`。