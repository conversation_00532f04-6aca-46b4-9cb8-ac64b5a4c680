import * as React from "react"
import { MultiSelect, type Option } from "@/components/ui/multi-select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"

const ComponentTestPage: React.FC = () => {
  // 基本多选状态
  const [basicSelected, setBasicSelected] = React.useState<Option[]>([])
  
  // 分组选项状态
  const [groupedSelected, setGroupedSelected] = React.useState<Option[]>([])
  
  // 创建选项状态
  const [creatableSelected, setCreatableSelected] = React.useState<Option[]>([])
  
  // 固定选项状态
  const [fixedSelected, setFixedSelected] = React.useState<Option[]>([
    { value: "admin", label: "管理员", fixed: true }
  ])
  
  // 异步搜索状态
  const [asyncSelected, setAsyncSelected] = React.useState<Option[]>([])
  
  // 限制选择状态
  const [limitedSelected, setLimitedSelected] = React.useState<Option[]>([])

  // 基本选项数据
  const basicOptions: Option[] = [
    { value: "react", label: "React" },
    { value: "vue", label: "Vue.js" },
    { value: "angular", label: "Angular" },
    { value: "svelte", label: "Svelte" },
    { value: "nextjs", label: "Next.js" },
    { value: "nuxtjs", label: "Nuxt.js" },
    { value: "gatsby", label: "Gatsby" },
    { value: "vite", label: "Vite" }
  ]

  // 分组选项数据
  const groupedOptions: Option[] = [
    { value: "react", label: "React", category: "Library" },
    { value: "vue", label: "Vue.js", category: "Framework" },
    { value: "angular", label: "Angular", category: "Framework" },
    { value: "svelte", label: "Svelte", category: "Compiler" },
    { value: "jquery", label: "jQuery", category: "Library" },
    { value: "lodash", label: "Lodash", category: "Library" },
    { value: "express", label: "Express", category: "Backend" },
    { value: "koa", label: "Koa", category: "Backend" },
    { value: "fastify", label: "Fastify", category: "Backend" }
  ]

  // 带固定选项的数据
  const fixedOptions: Option[] = [
    { value: "admin", label: "管理员", fixed: true },
    { value: "teacher", label: "教师" },
    { value: "student", label: "学生" },
    { value: "parent", label: "家长" },
    { value: "guest", label: "访客" }
  ]

  // 模拟异步搜索
  const handleAsyncSearch = async (query: string): Promise<Option[]> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockData: Option[] = [
      { value: "javascript", label: "JavaScript" },
      { value: "typescript", label: "TypeScript" },
      { value: "python", label: "Python" },
      { value: "java", label: "Java" },
      { value: "csharp", label: "C#" },
      { value: "golang", label: "Go" },
      { value: "rust", label: "Rust" },
      { value: "kotlin", label: "Kotlin" },
      { value: "swift", label: "Swift" },
      { value: "dart", label: "Dart" }
    ]
    
    const filteredData = mockData.filter(item =>
      item.label.toLowerCase().includes(query.toLowerCase())
    )
    
    return filteredData
  }

  // 处理最大选择回调
  const handleMaxSelected = (limit: number) => {
    toast.error(`最多只能选择 ${limit} 项`)
  }

  // 重置所有选择
  const handleResetAll = () => {
    setBasicSelected([])
    setGroupedSelected([])
    setCreatableSelected([])
    setFixedSelected([{ value: "admin", label: "管理员", fixed: true }])
    setAsyncSelected([])
    setLimitedSelected([])
    toast.success("已重置所有选择")
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">MultiSelect 组件测试</h1>
          <p className="text-muted-foreground mt-2">
            完整测试 MultiSelect 组件的各种功能和配置
          </p>
        </div>
        <Button onClick={handleResetAll} variant="outline">
          重置所有选择
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 基本多选功能 */}
        <Card>
          <CardHeader>
            <CardTitle>基本多选功能</CardTitle>
            <CardDescription>
              演示基本的多选功能，支持搜索和标签显示
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <MultiSelect
              options={basicOptions}
              value={basicSelected}
              onChange={setBasicSelected}
              placeholder="选择前端框架..."
            />
            <div className="text-sm text-muted-foreground">
              已选择: {basicSelected.length} 项
            </div>
            {basicSelected.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {basicSelected.map(option => (
                  <Badge key={option.value} variant="secondary">
                    {option.label}
                  </Badge>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 分组选项 */}
        <Card>
          <CardHeader>
            <CardTitle>分组选项</CardTitle>
            <CardDescription>
              按类别对选项进行分组显示
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <MultiSelect
              options={groupedOptions}
              value={groupedSelected}
              onChange={setGroupedSelected}
              groupBy="category"
              placeholder="按类别选择技术..."
            />
            <div className="text-sm text-muted-foreground">
              已选择: {groupedSelected.length} 项
            </div>
            {groupedSelected.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {groupedSelected.map(option => (
                  <Badge key={option.value} variant="outline">
                    {option.label}
                  </Badge>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 创建新选项 */}
        <Card>
          <CardHeader>
            <CardTitle>创建新选项</CardTitle>
            <CardDescription>
              允许用户创建不存在的选项
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <MultiSelect
              options={basicOptions}
              value={creatableSelected}
              onChange={setCreatableSelected}
              creatable
              placeholder="输入或选择技术栈..."
            />
            <div className="text-sm text-muted-foreground">
              已选择: {creatableSelected.length} 项（可自定义创建）
            </div>
          </CardContent>
        </Card>

        {/* 固定选项和限制 */}
        <Card>
          <CardHeader>
            <CardTitle>固定选项和限制</CardTitle>
            <CardDescription>
              固定选项不可删除，限制最大选择数量为 3
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <MultiSelect
              options={fixedOptions}
              value={fixedSelected}
              onChange={setFixedSelected}
              maxSelected={3}
              onMaxSelected={handleMaxSelected}
              placeholder="选择用户角色..."
            />
            <div className="text-sm text-muted-foreground">
              已选择: {fixedSelected.length} / 3 项（管理员角色固定）
            </div>
          </CardContent>
        </Card>

        {/* 异步搜索 */}
        <Card>
          <CardHeader>
            <CardTitle>异步搜索</CardTitle>
            <CardDescription>
              模拟远程搜索，带加载状态和空状态提示
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <MultiSelect
              value={asyncSelected}
              onChange={setAsyncSelected}
              onSearch={handleAsyncSearch}
              loadingIndicator={
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <span className="ml-2">搜索中...</span>
                </div>
              }
              emptyIndicator={
                <div className="text-center py-4 text-muted-foreground">
                  未找到匹配的编程语言
                </div>
              }
              placeholder="搜索编程语言..."
              triggerSearchOnFocus
            />
            <div className="text-sm text-muted-foreground">
              已选择: {asyncSelected.length} 项（异步搜索）
            </div>
          </CardContent>
        </Card>

        {/* 限制选择数量 */}
        <Card>
          <CardHeader>
            <CardTitle>限制选择数量</CardTitle>
            <CardDescription>
              最多只能选择 2 项，禁用清空按钮
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <MultiSelect
              options={basicOptions}
              value={limitedSelected}
              onChange={setLimitedSelected}
              maxSelected={2}
              onMaxSelected={handleMaxSelected}
              hideClearAllButton
              placeholder="最多选择 2 项..."
            />
            <div className="text-sm text-muted-foreground">
              已选择: {limitedSelected.length} / 2 项
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* 禁用状态和自定义样式 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>禁用状态</CardTitle>
            <CardDescription>
              展示组件的禁用状态
            </CardDescription>
          </CardHeader>
          <CardContent>
            <MultiSelect
              options={basicOptions}
              value={[basicOptions[0], basicOptions[1]]}
              onChange={() => {}}
              disabled
              placeholder="禁用状态..."
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>自定义样式</CardTitle>
            <CardDescription>
              自定义标签和容器样式
            </CardDescription>
          </CardHeader>
          <CardContent>
            <MultiSelect
              options={basicOptions}
              value={basicSelected}
              onChange={setBasicSelected}
              className="border-2 border-primary/20"
              badgeClassName="bg-primary/10 text-primary border-primary/20"
              placeholder="自定义样式..."
            />
          </CardContent>
        </Card>
      </div>

      {/* 使用说明 */}
      <Card>
        <CardHeader>
          <CardTitle>功能说明</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">键盘操作</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Enter - 选择高亮选项</li>
                <li>• Backspace - 删除最后一个选项</li>
                <li>• Escape - 关闭下拉菜单</li>
                <li>• ↑↓ - 上下导航选项</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">高级特性</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• 支持异步搜索和加载状态</li>
                <li>• 选项分组和自定义过滤</li>
                <li>• 创建不存在的选项</li>
                <li>• 固定选项和选择限制</li>
              </ul>
            </div>
          </div>
          <div className="pt-4 border-t">
            <p className="text-sm text-muted-foreground">
              组件基于 shadcn/ui 设计系统和 cmdk 库实现，提供完整的无障碍访问支持和键盘导航。
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ComponentTestPage