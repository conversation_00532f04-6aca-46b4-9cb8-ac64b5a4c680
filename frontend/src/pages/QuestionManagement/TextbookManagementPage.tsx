import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  BookOpen,
  FileText,
  CheckCircle2,
  AlertCircle,
  RefreshCw,
  Eye,
  Import,
  Download,
  Users,
  Share2,
  Lock,
  Unlock
} from 'lucide-react';

import { 
  textbookApi, 
  TextbookResponse, 
  CreateTextbookRequest
} from '@/services/questionApi';

const TextbookManagementPage: React.FC = () => {
  // State management
  const [textbooks, setTextbooks] = useState<TextbookResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination and filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [subjectFilter, setSubjectFilter] = useState('');
  const [gradeFilter, setGradeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAccessDialogOpen, setIsAccessDialogOpen] = useState(false);
  const [selectedTextbook, setSelectedTextbook] = useState<TextbookResponse | null>(null);
  
  // Form state
  const [textbookForm, setTextbookForm] = useState<CreateTextbookRequest>({
    title: '',
    subject_id: '',
    grade_level_id: '',
    publisher: '',
    publication_year: new Date().getFullYear(),
    isbn: '',
    version: '',
    chapters: []
  });

  // Chapter management
  const [chapters, setChapters] = useState<any[]>([]);
  const [currentChapter, setCurrentChapter] = useState({
    chapter_number: 1,
    title: '',
    content: '',
    knowledge_points: {},
    exercises: []
  });

  // Access management
  const [availableTenants, setAvailableTenants] = useState<any[]>([]);
  const [selectedTenants, setSelectedTenants] = useState<string[]>([]);

  // Load initial data
  useEffect(() => {
    loadTextbooks();
    loadAvailableTenants();
  }, []);

  // Load textbooks when filters change
  useEffect(() => {
    loadTextbooks();
  }, [searchTerm, subjectFilter, gradeFilter, statusFilter]);

  const loadTextbooks = async () => {
    try {
      setLoading(true);
      const params = {
        search: searchTerm || undefined,
        subject: subjectFilter || undefined,
        grade_level: gradeFilter || undefined,
        status: statusFilter || undefined
      };
      
      const data = await textbookApi.getTextbooks(params);
      setTextbooks(data);
      setError(null);
    } catch (err) {
      setError('Failed to load textbooks');
      console.error('Error loading textbooks:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableTenants = async () => {
    // Mock data - in real app, this would come from an API
    setAvailableTenants([
      { id: '1', name: '示例学校A', code: 'school-a' },
      { id: '2', name: '示例学校B', code: 'school-b' },
      { id: '3', name: '示例学校C', code: 'school-c' }
    ]);
  };

  const handleCreateTextbook = async () => {
    try {
      if (!textbookForm.title || !textbookForm.subject_id || !textbookForm.grade_level_id) {
        setError('Please fill in all required fields');
        return;
      }

      const textbookData = {
        ...textbookForm,
        chapters: chapters
      };

      await textbookApi.createTextbook(textbookData);
      setIsCreateDialogOpen(false);
      resetForm();
      loadTextbooks();
    } catch (err) {
      setError('Failed to create textbook');
      console.error('Error creating textbook:', err);
    }
  };

  const handleImportExercises = async (textbookId: string) => {
    try {
      const importedCount = await textbookApi.importTextbookExercises(textbookId);
      alert(`成功导入 ${importedCount} 道练习题到题库`);
      loadTextbooks();
    } catch (err) {
      setError('Failed to import exercises');
      console.error('Error importing exercises:', err);
    }
  };

  const handleGrantAccess = async () => {
    if (!selectedTextbook || selectedTenants.length === 0) return;
    
    try {
      const grantedCount = await textbookApi.grantTextbookAccess(selectedTextbook.id, selectedTenants);
      alert(`成功授权 ${grantedCount} 个租户访问该教辅`);
      setIsAccessDialogOpen(false);
      setSelectedTenants([]);
      loadTextbooks();
    } catch (err) {
      setError('Failed to grant access');
      console.error('Error granting access:', err);
    }
  };

  const handleRevokeAccess = async (textbookId: string, tenantId: string) => {
    try {
      await textbookApi.revokeTextbookAccess(textbookId, tenantId);
      alert('成功撤销访问权限');
      loadTextbooks();
    } catch (err) {
      setError('Failed to revoke access');
      console.error('Error revoking access:', err);
    }
  };

  const resetForm = () => {
    setTextbookForm({
      title: '',
      subject_id: '',
      grade_level_id: '',
      publisher: '',
      publication_year: new Date().getFullYear(),
      isbn: '',
      version: '',
      chapters: []
    });
    setChapters([]);
    setCurrentChapter({
      chapter_number: 1,
      title: '',
      content: '',
      knowledge_points: {},
      exercises: []
    });
  };

  const addChapter = () => {
    if (!currentChapter.title) {
      setError('Please enter chapter title');
      return;
    }
    
    setChapters([...chapters, currentChapter]);
    setCurrentChapter({
      chapter_number: chapters.length + 2,
      title: '',
      content: '',
      knowledge_points: {},
      exercises: []
    });
  };

  const removeChapter = (index: number) => {
    setChapters(chapters.filter((_, i) => i !== index));
  };

  const openAccessDialog = (textbook: TextbookResponse) => {
    setSelectedTextbook(textbook);
    setIsAccessDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { variant: 'default' as const, label: '活跃', icon: CheckCircle2 },
      inactive: { variant: 'secondary' as const, label: '未启用', icon: FileText },
      archived: { variant: 'outline' as const, label: '已归档', icon: BookOpen }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">教辅管理</h1>
          <p className="text-muted-foreground">管理教辅资源和访问权限</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadTextbooks}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新建教辅
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>新建教辅</DialogTitle>
                <DialogDescription>
                  请填写教辅的基本信息和章节内容
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">教辅标题 *</Label>
                    <Input
                      id="title"
                      value={textbookForm.title}
                      onChange={(e) => setTextbookForm({...textbookForm, title: e.target.value})}
                      placeholder="请输入教辅标题"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="subject_id">学科 *</Label>
                    <Select value={textbookForm.subject_id} onValueChange={(value) => setTextbookForm({...textbookForm, subject_id: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择学科" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="math">数学</SelectItem>
                        <SelectItem value="chinese">语文</SelectItem>
                        <SelectItem value="english">英语</SelectItem>
                        <SelectItem value="physics">物理</SelectItem>
                        <SelectItem value="chemistry">化学</SelectItem>
                        <SelectItem value="biology">生物</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="grade_level_id">年级 *</Label>
                    <Select value={textbookForm.grade_level_id} onValueChange={(value) => setTextbookForm({...textbookForm, grade_level_id: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择年级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">一年级</SelectItem>
                        <SelectItem value="2">二年级</SelectItem>
                        <SelectItem value="3">三年级</SelectItem>
                        <SelectItem value="4">四年级</SelectItem>
                        <SelectItem value="5">五年级</SelectItem>
                        <SelectItem value="6">六年级</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="publisher">出版社</Label>
                    <Input
                      id="publisher"
                      value={textbookForm.publisher}
                      onChange={(e) => setTextbookForm({...textbookForm, publisher: e.target.value})}
                      placeholder="请输入出版社"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="publication_year">出版年份</Label>
                    <Input
                      id="publication_year"
                      type="number"
                      value={textbookForm.publication_year}
                      onChange={(e) => setTextbookForm({...textbookForm, publication_year: parseInt(e.target.value)})}
                      placeholder="2024"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="isbn">ISBN</Label>
                    <Input
                      id="isbn"
                      value={textbookForm.isbn}
                      onChange={(e) => setTextbookForm({...textbookForm, isbn: e.target.value})}
                      placeholder="978-7-xxx-xxxxx-x"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="version">版本</Label>
                    <Input
                      id="version"
                      value={textbookForm.version}
                      onChange={(e) => setTextbookForm({...textbookForm, version: e.target.value})}
                      placeholder="第1版"
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>章节管理</Label>
                    <Button variant="outline" size="sm" onClick={addChapter}>
                      <Plus className="h-4 w-4 mr-2" />
                      添加章节
                    </Button>
                  </div>
                  
                  {/* Current Chapter Form */}
                  <div className="space-y-3 p-4 border rounded">
                    <div className="flex items-center gap-2">
                      <Label className="text-sm font-medium">第 {currentChapter.chapter_number} 章</Label>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="chapter_title">章节标题</Label>
                      <Input
                        id="chapter_title"
                        value={currentChapter.title}
                        onChange={(e) => setCurrentChapter({...currentChapter, title: e.target.value})}
                        placeholder="请输入章节标题"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="chapter_content">章节内容</Label>
                      <Textarea
                        id="chapter_content"
                        value={currentChapter.content}
                        onChange={(e) => setCurrentChapter({...currentChapter, content: e.target.value})}
                        placeholder="请输入章节内容"
                        rows={3}
                      />
                    </div>
                  </div>

                  {/* Added Chapters List */}
                  {chapters.length > 0 && (
                    <div className="space-y-2">
                      <Label>已添加章节</Label>
                      {chapters.map((chapter, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <div className="font-medium">第 {chapter.chapter_number} 章: {chapter.title}</div>
                            <div className="text-sm text-muted-foreground">
                              {chapter.content ? `${chapter.content.substring(0, 50)}...` : '暂无内容'}
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeChapter(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateTextbook}>
                  创建教辅
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center flex-wrap">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索教辅..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={subjectFilter} onValueChange={setSubjectFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="学科" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部学科</SelectItem>
            <SelectItem value="math">数学</SelectItem>
            <SelectItem value="chinese">语文</SelectItem>
            <SelectItem value="english">英语</SelectItem>
            <SelectItem value="physics">物理</SelectItem>
            <SelectItem value="chemistry">化学</SelectItem>
            <SelectItem value="biology">生物</SelectItem>
          </SelectContent>
        </Select>
        <Select value={gradeFilter} onValueChange={setGradeFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="年级" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部年级</SelectItem>
            <SelectItem value="1">一年级</SelectItem>
            <SelectItem value="2">二年级</SelectItem>
            <SelectItem value="3">三年级</SelectItem>
            <SelectItem value="4">四年级</SelectItem>
            <SelectItem value="5">五年级</SelectItem>
            <SelectItem value="6">六年级</SelectItem>
          </SelectContent>
        </Select>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部状态</SelectItem>
            <SelectItem value="active">活跃</SelectItem>
            <SelectItem value="inactive">未启用</SelectItem>
            <SelectItem value="archived">已归档</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Textbook List */}
      <Card>
        <CardHeader>
          <CardTitle>教辅列表</CardTitle>
          <CardDescription>
            当前共有 {textbooks.length} 本教辅
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>教辅标题</TableHead>
                <TableHead>学科</TableHead>
                <TableHead>年级</TableHead>
                <TableHead>出版社</TableHead>
                <TableHead>章节数</TableHead>
                <TableHead>练习题数</TableHead>
                <TableHead>授权租户</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {textbooks.map((textbook) => (
                <TableRow key={textbook.id}>
                  <TableCell className="font-medium">{textbook.title}</TableCell>
                  <TableCell>{textbook.subject_id}</TableCell>
                  <TableCell>{textbook.grade_level_id}年级</TableCell>
                  <TableCell>{textbook.publisher}</TableCell>
                  <TableCell>{textbook.chapter_count}</TableCell>
                  <TableCell>{textbook.exercise_count}</TableCell>
                  <TableCell>{textbook.access_tenants.length}</TableCell>
                  <TableCell>{getStatusBadge(textbook.status)}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleImportExercises(textbook.id)}
                        title="导入练习题到题库"
                      >
                        <Import className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => openAccessDialog(textbook)}
                        title="管理访问权限"
                      >
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Access Management Dialog */}
      <Dialog open={isAccessDialogOpen} onOpenChange={setIsAccessDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>管理访问权限</DialogTitle>
            <DialogDescription>
              为教辅 "{selectedTextbook?.title}" 管理租户访问权限
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>当前有权访问的租户</Label>
              <div className="max-h-[200px] overflow-y-auto space-y-1">
                {selectedTextbook?.access_tenants.map((tenantId) => {
                  const tenant = availableTenants.find(t => t.id === tenantId);
                  return tenant ? (
                    <div key={tenantId} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <div className="font-medium">{tenant.name}</div>
                        <div className="text-sm text-muted-foreground">{tenant.code}</div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRevokeAccess(selectedTextbook.id, tenantId)}
                      >
                        <Lock className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : null;
                })}
              </div>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <Label>授权新租户</Label>
              <div className="max-h-[200px] overflow-y-auto space-y-1">
                {availableTenants
                  .filter(tenant => !selectedTextbook?.access_tenants.includes(tenant.id))
                  .map((tenant) => (
                    <div
                      key={tenant.id}
                      className={`flex items-center justify-between p-2 border rounded cursor-pointer transition-colors ${
                        selectedTenants.includes(tenant.id)
                          ? 'bg-primary/10 border-primary'
                          : 'hover:bg-muted'
                      }`}
                      onClick={() => {
                        if (selectedTenants.includes(tenant.id)) {
                          setSelectedTenants(selectedTenants.filter(id => id !== tenant.id));
                        } else {
                          setSelectedTenants([...selectedTenants, tenant.id]);
                        }
                      }}
                    >
                      <div>
                        <div className="font-medium">{tenant.name}</div>
                        <div className="text-sm text-muted-foreground">{tenant.code}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        {selectedTenants.includes(tenant.id) ? (
                          <CheckCircle2 className="h-4 w-4 text-primary" />
                        ) : (
                          <Unlock className="h-4 w-4" />
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsAccessDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleGrantAccess} disabled={selectedTenants.length === 0}>
              授权 ({selectedTenants.length})
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TextbookManagementPage;