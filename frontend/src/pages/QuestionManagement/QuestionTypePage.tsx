import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger} from '@/components/ui/tabs';
import {Card, CardContent, CardHeader} from "@/components/ui/card.tsx";
import {useEffect, useState} from "react";

import {questionTypeApi} from "@/services/questionApi.ts";
import {toast} from "sonner";
import QuestionTypeTable from "@/pages/QuestionManagement/components/QuestionTypeTable.tsx";
import QuestionTypeForm from "@/pages/QuestionManagement/components/QuestionTypeForm.tsx";
import QuestionTypeTableHeader from "@/pages/QuestionManagement/components/QuestionTypeTableHeader.tsx";
import ComposeTable from "@/pages/QuestionManagement/components/ComposeTable.tsx";
import ComposeTableHeader from "@/pages/QuestionManagement/components/ComposeTableHeader.tsx";
import subjectApi from "@/services/subjectApi.ts";
import gradeApi from "@/services/gradeApi.ts";
import {
    ComposeQuestionType,
    ComposeQuestionTypeQ<PERSON>y<PERSON>ara<PERSON>,
    CreateQuestionTypeRequest, DEFAULT_COMPOSE_QUERY,
    DEFAULT_QUESTION_TYPE_QUERY,
    QuestionType,
    QuestionTypeQueryParams, QuestionTypeSummary, GradeLevelSummary, SubjectSummary, ComposeBindRequest
} from "@/types";
import ComposeForm from "@/pages/QuestionManagement/components/ComposeForm.tsx";

const QuestionTypePage = () => {
    const [loading, setLoading] = useState(false);

    const [questionTypes, setQuestionTypes] = useState<QuestionType[]>([]);
    const [queryQuestionTypesParams, setQueryQuestionTypesParams] = useState<QuestionTypeQueryParams>(DEFAULT_QUESTION_TYPE_QUERY);
    const [questionTypesPagination, setQueryQuestionTypesPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0,
    });
    const [formOpen, setFormOpen] = useState(false);
    const [editingQuestionType, setEditingQuestionType] = useState<QuestionType | undefined>();
    const [formLoading, setFormLoading] = useState(false);

    const loadQuestionTypes = async (params?: Partial<QuestionTypeQueryParams>) => {
        try {
            setLoading(true)
            const finalParams = {...queryQuestionTypesParams, ...params};
            let response = await questionTypeApi.getQuestionTypes(finalParams)
            if (response.success && response.data) {
                setQuestionTypes(response.data);
                setQueryQuestionTypesPagination({
                    current: response.pagination.page,
                    pageSize: response.pagination.page_size,
                    total: response.pagination.total,
                    totalPages: response.pagination.total_pages,
                });
            }
            setQueryQuestionTypesParams(finalParams);
        } catch (error) {
            console.error('Failed to load question types:', error);
            toast.error('加载题型列表失败');
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (search: string) => {
        loadQuestionTypes({...queryQuestionTypesParams, search, page: 1});
    };

    const handleFilterChange = (key: keyof QuestionTypeQueryParams, value: any) => {
        const newParams = {...queryQuestionTypesParams, [key]: value, page: 1};
        loadQuestionTypes(newParams);
    };

    const handleQuestionTypePageChange = (page: number, pageSize: number) => {
        loadQuestionTypes({...queryQuestionTypesParams, page, page_size: pageSize});
    };

    const handleQuestionTypeToggleStatus = async (code: string, isActive: boolean) => {
        try {
            const response = await questionTypeApi.toggleQuestionTypeStatus(code, isActive);
            if (response.success) {
                toast.success(`题型已${isActive ? '启用' : '禁用'}`);
                await loadQuestionTypes();
            }
        } catch (error: any) {
            console.error('Failed to toggle question type status:', error);
            toast.error(error.response?.data?.message || '状态切换失败');
        }
    };

    const handleCreateQuestionType = () => {
        setEditingQuestionType(undefined);
        setFormOpen(true);
    };

    const handleEditQuestionType = (questionType: QuestionType) => {
        setEditingQuestionType(questionType);
        setFormOpen(true);
    };

    const handleFormSubmit = async (data: CreateQuestionTypeRequest) => {
        try {
            setFormLoading(true);

            const validatedData: CreateQuestionTypeRequest = {
                ...data,
            };

            if (editingQuestionType) {
                // Update question type
                const response = await questionTypeApi.updateQuestionType(validatedData);
                if (response.success) {
                    toast.success('题型更新成功');
                    setFormOpen(false);
                    await loadQuestionTypes();
                }
            } else {
                // Create question type
                const response = await questionTypeApi.createQuestionType(validatedData);
                if (response.success) {
                    toast.success('题型创建成功');
                    setFormOpen(false);
                    await loadQuestionTypes();
                }
            }
        } catch (error: any) {
            console.error('Failed to save question type:', error);
            toast.error(error.response?.data?.message || '保存失败');
        } finally {
            setFormLoading(false);
        }
    };

    const [composeQuestionTypes, setComposeQuestionTypes] = useState<ComposeQuestionType[]>([]);
    const [composePagination, setComposePagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0,
    });
    const [questionTypeSummaries, setQuestionTypeSummaries] = useState<QuestionTypeSummary[]>([])
    const [subjectSummaries, setSubjectSummaries] = useState<SubjectSummary[]>([])
    const [gradeLevelSummaries, setGradeLevelSummaries] = useState<GradeLevelSummary[]>([])

    const [composeFormOpen, setComposeFormOpen] = useState(false);

    const [queryComposeParams, setQueryComposeParams] = useState<ComposeQuestionTypeQueryParams>(DEFAULT_COMPOSE_QUERY);

    const loadComposeBaseInfo = async (params?: Partial<ComposeQuestionTypeQueryParams>) => {
        try {
            setLoading(true)
            const finalParams = {...queryComposeParams, ...params};
            let response = await questionTypeApi.getCompose(finalParams)
            if (response.success && response.data) {
                setComposeQuestionTypes(response.data);
                setComposePagination({
                    current: response.pagination.page,
                    pageSize: response.pagination.page_size,
                    total: response.pagination.total,
                    totalPages: response.pagination.total_pages,
                });
            }
            setQueryComposeParams(finalParams);
        } catch (error) {
            console.error('Failed to load compose:', error);
            toast.error('加载题型绑定数据失败');
        } finally {
            setLoading(false);
        }
    }

    const loadComposeSelection = async () => {
        try {
            setLoading(true)
            const [questionTypesRes, subjectsRes, gradesRes] = await Promise.all([
                questionTypeApi.getQuestionTypeSummaries(),
                subjectApi.getSubjectSummaries(),
                gradeApi.getGradeSummaries()
            ]);

            setQuestionTypeSummaries(questionTypesRes.data ?? [])
            setSubjectSummaries(subjectsRes.data ?? [])
            setGradeLevelSummaries(gradesRes.data ?? [])
        } catch (error) {
            console.error('Failed to load compose:', error);
            toast.error('加载题型绑定数据失败');
        } finally {
            setLoading(false);
        }

    }

    const handleComposePageChange = (page: number, pageSize: number) => {
        loadComposeBaseInfo({...queryComposeParams, page, page_size: pageSize});
    };

    const handleCreateCompose = () => {
        setComposeFormOpen(true);
    }

    const handleComposeFormSubmit = async (data: ComposeBindRequest) => {
        try {
            setFormLoading(true);
            let res = await questionTypeApi.bindCompose(data);
            if (res.success) {
                toast.success(res.message);
                await loadComposeBaseInfo();
            }
            setComposeFormOpen(false);
        } catch (error: any) {
            console.error('Failed to save compose:', error);
            toast.error(error.response?.data?.message || '保存失败');
        } finally {
            setFormLoading(false);
        }
    }

    const handleDeleteCompose = async (compose: ComposeQuestionType) => {
        try {
            let res = await questionTypeApi.unbindCompose({
                questionTypeCode: compose.question_type_code,
                subjectCode: compose.subject_code,
                gradeLevelCode: compose.grade_level_code
            });
            toast.info(res.message)
            await loadComposeBaseInfo();
        } catch (error: any) {
            console.error('Failed to save compose:', error);
            toast.error(error.response?.data?.message || '删除失败');
        }

    };

    const tabsChange = async (value: string) => {
        if (value === 'subjectGradeBind') {
            await loadComposeBaseInfo();
            await loadComposeSelection();
        } else if (value === 'filterPreview') {
            console.log('筛选器预览');
        } else {
            await loadQuestionTypes();
        }
    }

    useEffect(() => {
        loadQuestionTypes();
    }, []);

    return (
        <div className="space-y-6 p-6">
            {/* Page Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">题型管理</h1>
                    <p className="text-gray-600 mt-1">管理系统中的题型信息</p>
                </div>
            </div>

            <Tabs defaultValue="list" className="w-full" onValueChange={tabsChange}>
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="list">题型列表</TabsTrigger>
                    <TabsTrigger value="subjectGradeBind">学科年级绑定</TabsTrigger>
                    <TabsTrigger value="filterPreview">筛选器预览</TabsTrigger>
                </TabsList>

                <TabsContent value="list">
                    <Card>
                        <CardHeader className="pb-2">
                            <QuestionTypeTableHeader
                                onSearch={handleSearch}
                                params={queryQuestionTypesParams}
                                onCreateQuestionType={handleCreateQuestionType}
                                onFilterChange={handleFilterChange}
                            />
                        </CardHeader>
                        <CardContent>
                            <QuestionTypeTable
                                questionTypes={questionTypes}
                                loading={loading}
                                onEdit={handleEditQuestionType}
                                onToggleStatus={handleQuestionTypeToggleStatus}
                                pagination={{
                                    current: questionTypesPagination.current,
                                    pageSize: questionTypesPagination.pageSize,
                                    total: questionTypesPagination.total,
                                    onChange: handleQuestionTypePageChange,
                                }}
                            />
                        </CardContent>
                        <QuestionTypeForm
                            questionType={editingQuestionType}
                            open={formOpen}
                            onClose={() => setFormOpen(false)}
                            onSubmit={handleFormSubmit}
                            loading={formLoading}
                        />
                    </Card>
                </TabsContent>
                <TabsContent value="subjectGradeBind">
                    <Card>
                        <CardHeader className="pb-2">
                            <ComposeTableHeader onCreateCompose={handleCreateCompose}/>
                        </CardHeader>
                        <CardContent>
                            <ComposeTable
                                composeList={composeQuestionTypes}
                                loading={loading}
                                onDelete={handleDeleteCompose}
                                pagination={{
                                    current: composePagination.current,
                                    pageSize: composePagination.pageSize,
                                    total: composePagination.total,
                                    onChange: handleComposePageChange,
                                }}
                            />
                        </CardContent>
                        <ComposeForm
                            open={composeFormOpen}
                            onClose={() => setComposeFormOpen(false)}
                            onSubmit={handleComposeFormSubmit}
                            loading={formLoading}
                            questionTypeSummaries={questionTypeSummaries}
                            subjectSummaries={subjectSummaries}
                            gradeLevelSummaries={gradeLevelSummaries}
                        />
                    </Card>
                </TabsContent>
                <TabsContent value="filterPreview">
                    <div>筛选器预览开发中</div>
                </TabsContent>

            </Tabs>
        </div>
    );
};

export default QuestionTypePage;
