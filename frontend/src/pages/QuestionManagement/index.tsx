import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import QuestionBankPage from './QuestionBankPage';
import PaperTemplatePage from './PaperTemplatePage';
import TextbookManagementPage from './TextbookManagementPage';

const QuestionManagementPage: React.FC = () => {
  return (
    <div className="container mx-auto p-6">
      <Tabs defaultValue="question-bank" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="question-bank">题库管理</TabsTrigger>
          <TabsTrigger value="paper-template">试卷模板</TabsTrigger>
          <TabsTrigger value="textbook">教辅管理</TabsTrigger>
        </TabsList>
        
        <TabsContent value="question-bank" className="mt-6">
          <QuestionBankPage />
        </TabsContent>
        
        <TabsContent value="paper-template" className="mt-6">
          <PaperTemplatePage />
        </TabsContent>
        
        <TabsContent value="textbook" className="mt-6">
          <TextbookManagementPage />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default QuestionManagementPage;