import {CardTitle} from "@/components/ui/card.tsx";
import {Plus, Search} from "lucide-react";
import {Input} from "@/components/ui/input.tsx";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select.tsx";
import React, {FC, useState} from "react";
import {QUESTION_TYP_STATUS_OPTIONS, QUESTION_TYPE_SORT_OPTIONS, QuestionTypeQueryParams} from "@/types/question.ts";
import {Button} from "@/components/ui/button.tsx";


interface QuestionTypeTableHeaderProps {
    onSearch:(search: string)=>void;
    params:QuestionTypeQueryParams;
    onCreateQuestionType:()=>void;
    onFilterChange:(key: keyof QuestionTypeQueryParams, value: any) => void;
}
const QuestionTypeTableHeader:FC<QuestionTypeTableHeaderProps> = ({onSearch,params,onCreateQuestionType,onFilterChange}) => {
    const [searchInput, setSearchInput] = useState('');

    return (
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <CardTitle className="text-lg">题型列表</CardTitle>
            <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                <div className="relative w-full md:w-64">
                    <button
                        type="button"
                        onClick={() => onSearch(searchInput)}
                        className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
                    >
                        <Search className="h-4 w-4" />
                    </button>
                    <Input
                        placeholder="搜索题型名称或代码..."
                        className="w-full pl-8"
                        value={searchInput}
                        onChange={(e) => setSearchInput(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && onSearch(searchInput)}
                    />
                </div>

                <div className="flex gap-2">
                    <Select
                        value={params.is_active === null ? 'all' : String(params.is_active)}
                        onValueChange={(value) =>
                            onFilterChange('is_active', value === 'all' ? null : value === 'true')
                        }
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="全部状态" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">全部状态</SelectItem>
                            {QUESTION_TYP_STATUS_OPTIONS.map((option) => (
                                <SelectItem key={String(option.value)} value={String(option.value)}>
                                    {option.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>

                    <Select
                        value={params.order_by ? `${params.order_by}-${params.order_direction}` : ''}
                        onValueChange={(value) => {
                            const [orderBy, orderDirection] = value.split('-');
                            onFilterChange('order_by', orderBy);
                            onFilterChange('order_direction', orderDirection);
                        }}
                    >
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="排序方式" />
                        </SelectTrigger>
                        <SelectContent>
                            {QUESTION_TYPE_SORT_OPTIONS.map((option) => (
                                <React.Fragment key={option.value}>
                                    <SelectItem value={`${option.value}-asc`}>
                                        {option.label} ↑
                                    </SelectItem>
                                    <SelectItem value={`${option.value}-desc`}>
                                        {option.label} ↓
                                    </SelectItem>
                                </React.Fragment>
                            ))}
                        </SelectContent>
                    </Select>
                </div>

                <div className="flex space-x-2">
                    <Button onClick={onCreateQuestionType}>
                        <Plus className="w-4 h-4 mr-2"/>
                        新增题型
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default QuestionTypeTableHeader;
