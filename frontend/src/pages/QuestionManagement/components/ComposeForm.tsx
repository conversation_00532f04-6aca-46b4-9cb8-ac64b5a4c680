import {ComposeBindRequest, GradeLevelSummary, QuestionTypeSummary, SubjectSummary} from "@/types";
import {FC, useEffect} from "react";
import {Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle} from "@/components/ui/dialog.tsx";
import {Button} from "@/components/ui/button.tsx";
import {Loader2} from "lucide-react";
import {useForm} from "react-hook-form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {Form, FormControl, FormField, FormItem, FormLabel} from "@/components/ui/form.tsx";

interface ComposeFormProps {
    open: boolean;
    onClose: () => void;
    loading?: boolean;
    onSubmit: (data:ComposeBindRequest) => Promise<void>;
    questionTypeSummaries: QuestionTypeSummary[];
    subjectSummaries: SubjectSummary[];
    gradeLevelSummaries: GradeLevelSummary[];
}

const ComposeForm: FC<ComposeFormProps> = ({
                                               open,
                                               onClose,
                                               loading,
                                               onSubmit,
                                               questionTypeSummaries,
                                               subjectSummaries,
                                               gradeLevelSummaries
                                           }) => {
    const form = useForm<ComposeBindRequest>({
        defaultValues: {
            'questionTypeCode':'',
            'subjectCode':'',
            'gradeLevelCode':'',
        },
    });

    useEffect(() => {
        if (open) {
            form.reset({
                'questionTypeCode':'',
                'subjectCode':'',
                'gradeLevelCode':'',
            });
        }
    }, [open, form.reset, questionTypeSummaries, subjectSummaries, gradeLevelSummaries]);

    const handleFormSubmit = async (data:ComposeBindRequest) => {
        await onSubmit(data)
    }

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>
                        新建题型绑定
                    </DialogTitle>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
                        <FormField
                            control={form.control}
                            name="questionTypeCode"
                            render={({ field }) => (
                                <FormItem className='flex items-baseline justify-between'>
                                    <FormLabel>
                                        题型代码 <span className="text-red-500">*</span>
                                    </FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger className="w-[340px]">
                                                <SelectValue placeholder="题型代码" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {questionTypeSummaries.map((questionTypeSummary) => (
                                                <SelectItem key={questionTypeSummary.code} value={questionTypeSummary.code}>
                                                    {questionTypeSummary.type_name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="subjectCode"
                            render={({ field }) => (
                                <FormItem className='flex items-baseline justify-between'>
                                    <FormLabel>
                                        学科代码 <span className="text-red-500">*</span>
                                    </FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger className="w-[340px]">
                                                <SelectValue placeholder="学科代码" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {subjectSummaries.map((subjectSummary) => (
                                                <SelectItem key={subjectSummary.code} value={subjectSummary.code}>
                                                    {subjectSummary.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="gradeLevelCode"
                            render={({ field }) => (
                                <FormItem className='flex items-baseline justify-between'>
                                    <FormLabel>
                                        年级代码 <span className="text-red-500">*</span>
                                    </FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger className="w-[340px]">
                                                <SelectValue placeholder="年级代码" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {gradeLevelSummaries.map((gradeLevelSummary) => (
                                                <SelectItem key={gradeLevelSummary.code} value={gradeLevelSummary.code}>
                                                    {gradeLevelSummary.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </FormItem>
                            )}
                        />
                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                            disabled={loading}
                        >
                            取消
                        </Button>
                        <Button
                            type="submit"
                            disabled={loading}
                        >
                            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin"/>}
                            创建
                        </Button>
                    </DialogFooter>
                </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
};

export default ComposeForm;
