// Component prop types
import {ComposeQuestionType} from "@/types/question.ts";
import {FC} from "react";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table.tsx";
import {Badge} from "@/components/ui/badge.tsx";
import {Button} from "@/components/ui/button.tsx";
import {Trash} from "lucide-react";
import {
    Pagination,
    PaginationContent,
    PaginationItem,
    PaginationLink, PaginationNext,
    PaginationPrevious
} from "@/components/ui/pagination.tsx";
import {TooltipContent,TooltipTrigger,Tooltip} from "@/components/ui/tooltip.tsx";

interface ComposeTableProps {
    composeList: ComposeQuestionType[];
    loading?: boolean;
    onDelete:(compose:ComposeQuestionType)=>void;
    pagination?: {
        current: number;
        pageSize: number;
        total: number;
        onChange: (page: number, pageSize: number) => void;
    };
}

const ComposeTable: FC<ComposeTableProps> = ({composeList, onDelete, pagination, loading}) => {
    if (loading) {
        return (
            <div className="space-y-4">
                <div className="rounded-lg border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>题型</TableHead>
                                <TableHead>学科</TableHead>
                                <TableHead>年级</TableHead>
                                <TableHead>学段</TableHead>
                                <TableHead>操作</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {Array.from({length: 5}).map((_, index) => (
                                <TableRow key={index}>
                                    {Array.from({length: 1}).map((_, cellIndex) => (
                                        <TableCell key={cellIndex}>
                                            <div className="h-4 bg-gray-200 rounded animate-pulse"/>
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </div>
        );
    }

    if (!Array.isArray(composeList) || composeList.length === 0) {
        return (
            <div className="text-center py-8">
                <div className="text-gray-500 text-lg mb-2">暂无数据</div>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="rounded-lg border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[100px]">题型</TableHead>
                            <TableHead className="w-[100px]">学科</TableHead>
                            <TableHead className="w-[100px]">年级</TableHead>
                            <TableHead className="w-[100px]">学段</TableHead>
                            <TableHead className="w-[20px]">操作</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {(composeList as ComposeQuestionType[]).map((compose, index) => (
                            <TableRow key={index} className="hover:bg-gray-50">
                                <TableCell className="font-mono">
                                    <Badge variant="outline" className="text-xs">
                                        {compose.question_type_code} - {compose.question_type_name}
                                    </Badge>
                                </TableCell>

                                <TableCell className="text-gray-600">
                                    <Badge variant="outline" className="text-xs">
                                        {compose.subject_code} - {compose.subject_name}
                                    </Badge>

                                </TableCell>

                                <TableCell className="text-gray-600">
                                    <Badge variant="outline" className="text-xs">
                                        {compose.grade_level_code} - {compose.grade_level_name}
                                    </Badge>

                                </TableCell>

                                <TableCell className="text-gray-600">
                                    <Badge variant="outline" className="text-xs">
                                        {compose.education_stage_code} - {compose.education_stage_name}
                                    </Badge>
                                </TableCell>
                                <TableCell className="text-gray-600">
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button variant="destructive" size="sm" className="h-8 w-8 p-0" onClick={()=>onDelete(compose)}>
                                                <Trash className="h-4 w-4"/>
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>删除此题型绑定</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TableCell>

                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            {/* 分页 */}
            {pagination && pagination.total > 0 && (
                <div className="flex flex-wrap items-center gap-4 mt-2">
                    <div className="text-sm text-gray-500 whitespace-nowrap">
                        显示第 {((pagination.current - 1) * pagination.pageSize) + 1} -{' '}
                        {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共{' '}
                        {pagination.total} 条记录
                    </div>
                    {/* 分页大小选择器和跳页 */}
                    <div className="flex flex-wrap gap-3 items-center">
                        <select
                            className="border rounded px-2 py-1 text-sm min-w-[80px]"
                            value={pagination.pageSize}
                            onChange={e => pagination.onChange(1, Number(e.target.value))}
                        >
                            {[10, 20, 50, 100].map(size => (
                                <option key={size} value={size}>{size} 条/页</option>
                            ))}
                        </select>
                        <span className="text-sm">跳转到</span>
                        <input
                            type="number"
                            min={1}
                            max={Math.ceil(pagination.total / pagination.pageSize)}
                            defaultValue={pagination.current}
                            onBlur={e => {
                                let page = Number(e.target.value);
                                if (page < 1) page = 1;
                                if (page > Math.ceil(pagination.total / pagination.pageSize)) page = Math.ceil(pagination.total / pagination.pageSize);
                                if (page !== pagination.current) {
                                    pagination.onChange(page, pagination.pageSize);
                                }
                            }}
                            className="border rounded px-2 py-1 w-16 text-sm text-center"
                        />
                        <span className="text-sm">页</span>
                    </div>
                    {/* 分页按钮 */}
                    <div className="flex items-center">
                        <Pagination>
                            <PaginationContent>
                                <PaginationItem>
                                    <PaginationPrevious
                                        onClick={() => {
                                            if (pagination.current > 1) {
                                                pagination.onChange(pagination.current - 1, pagination.pageSize);
                                            }
                                        }}
                                        className={pagination.current === 1 ? 'pointer-events-none opacity-50' : ''}
                                    />
                                </PaginationItem>
                                {Array.from({length: Math.ceil(pagination.total / pagination.pageSize)}, (_, i) => i + 1).map((page) => (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            isActive={page === pagination.current}
                                            onClick={() => pagination.onChange(page, pagination.pageSize)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}
                                <PaginationItem>
                                    <PaginationNext
                                        onClick={() => {
                                            if (pagination.current < Math.ceil(pagination.total / pagination.pageSize)) {
                                                pagination.onChange(pagination.current + 1, pagination.pageSize);
                                            }
                                        }}
                                        className={
                                            pagination.current === Math.ceil(pagination.total / pagination.pageSize)
                                                ? 'pointer-events-none opacity-50'
                                                : ''
                                        }
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ComposeTable;
