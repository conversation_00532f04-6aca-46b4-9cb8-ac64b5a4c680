import {FC} from "react";
import {CardTitle} from "@/components/ui/card.tsx";
import {Plus} from "lucide-react";
import {Button} from "@/components/ui/button.tsx";

interface ComposeTableHeaderProps {
    onCreateCompose:()=>void;
}
const ComposeTableHeader:FC<ComposeTableHeaderProps> = ({onCreateCompose}) => {
    return (
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <CardTitle className="text-lg">题型关联列表</CardTitle>
            <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                <div className="flex space-x-2">
                    <Button onClick={onCreateCompose}>
                        <Plus className="w-4 h-4 mr-2"/>
                        新增题型关联
                    </Button>
                </div>
            </div>
        </div>
    );

};

export default ComposeTableHeader;
