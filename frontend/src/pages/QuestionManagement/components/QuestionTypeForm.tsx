import {CreateQuestionTypeRequest, QuestionType, QuestionTypeFormErrors} from "@/types/question.ts";
import {FC, useEffect, useState} from "react";
import {useForm} from "react-hook-form";
import {questionType<PERSON>pi} from "@/services/questionApi.ts";
import {toast} from "sonner";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/components/ui/dialog.tsx";
import {Label} from "@/components/ui/label.tsx";
import {Input} from "@/components/ui/input.tsx";
import {cn} from "@/lib/utils.ts";
import {AlertCircle, CheckCircle, Loader2} from "lucide-react";
import {Textarea} from "@/components/ui/textarea.tsx";
import {AlertDialog, AlertDialogDescription} from "@/components/ui/alert-dialog.tsx";
import {Badge} from "@/components/ui/badge.tsx";
import {Button} from "@/components/ui/button.tsx";

interface QuestionTypeFormProps {
    questionType?: QuestionType;
    open: boolean;
    onClose: () => void;
    onSubmit: (data: CreateQuestionTypeRequest) => Promise<void>;
    loading?: boolean;
}

const QuestionTypeForm:FC<QuestionTypeFormProps> = ({
    questionType,
    open,
    onClose,
    onSubmit,
    loading,
                                                    }) => {
    const [formErrors, setFormErrors] = useState<QuestionTypeFormErrors>({});
    const [codeChecking, setCodeChecking] = useState(false);
    const [codeAvailable, setCodeAvailable] = useState<boolean | null>(null);

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        watch,
    } = useForm<CreateQuestionTypeRequest>({
        defaultValues: {
            code: '',
            type_name: '',
            description: ''
        },
    });

    const watchedCode = watch('code');

    useEffect(() => {
        if (open) {
            if (questionType) {
                reset({
                    code: questionType.code,
                    type_name: questionType.type_name,
                    description: questionType.description || '',
                });
            } else {
                reset({
                    code: '',
                    type_name: '',
                    description: '',
                });
            }
            setFormErrors({});
            setCodeAvailable(null);
        }
    }, [open, questionType, reset]);

    useEffect(() => {
        if (!questionType && watchedCode && watchedCode.length >= 2) {
            const checkCodeDebounced = setTimeout(async () => {
                try {
                    setCodeChecking(true);
                    const response = await questionTypeApi.checkCodeAvailability(watchedCode);
                    console.log(response,response.success,response.data)
                    if (response.success && response.data) {
                        setCodeAvailable(response.data.is_available);
                    }
                    else {
                        setCodeAvailable(false);
                    }
                } catch (error) {
                    console.error('Failed to check code availability:', error);
                } finally {
                    setCodeChecking(false);
                }
            }, 500);

            return () => clearTimeout(checkCodeDebounced);
        } else {
            setCodeAvailable(null);
            setCodeChecking(false);
        }
    }, [watchedCode, questionType]);

    const handleFormSubmit = async (data: CreateQuestionTypeRequest) => {
        if (!questionType && codeAvailable === false) {
            toast.error('题型代码已存在，请使用其他代码');
            return;
        }

        try {
            await onSubmit(data);
        } catch (error: any) {
            // Handle validation errors
            if (error.response?.data?.errors) {
                setFormErrors(error.response.data.errors);
            }
        }
    };

    const isEditMode = !!questionType;


    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>
                        {isEditMode ? '编辑题型' : '新增题型'}
                    </DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="code" className="text-sm font-medium">
                                题型代码 <span className="text-red-500">*</span>
                            </Label>
                            <div className="relative">
                                <Input
                                    id="code"
                                    {...register('code', {
                                        required: '请输入题型代码',
                                        pattern: {
                                            value: /^[A-Z0-9_]+$/,
                                            message: '题型代码只能包含大写字母、数字和下划线'
                                        },
                                        minLength: {
                                            value: 2,
                                            message: '题型代码至少需要2个字符'
                                        },
                                        maxLength: {
                                            value: 20,
                                            message: '题型代码不能超过20个字符'
                                        }
                                    })}
                                    disabled={isEditMode || loading}
                                    className={cn(
                                        errors.code || formErrors.code ? "border-red-500" : "",
                                        !isEditMode && codeAvailable === false ? "border-red-500" : "",
                                        !isEditMode && codeAvailable === true ? "border-green-500" : ""
                                    )}
                                />

                                {/* Code checking indicator */}
                                {!isEditMode && watchedCode && (
                                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                        {codeChecking ? (
                                            <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
                                        ) : codeAvailable === true ? (
                                            <CheckCircle className="w-4 h-4 text-green-500" />
                                        ) : codeAvailable === false ? (
                                            <AlertCircle className="w-4 h-4 text-red-500" />
                                        ) : null}
                                    </div>
                                )}
                            </div>

                            {/* Code validation messages */}
                            {errors.code && (
                                <p className="text-sm text-red-500">{errors.code.message}</p>
                            )}
                            {formErrors.code && (
                                <p className="text-sm text-red-500">{formErrors.code}</p>
                            )}
                            {!isEditMode && codeAvailable === false && (
                                <p className="text-sm text-red-500">该题型代码已存在</p>
                            )}
                            {!isEditMode && codeAvailable === true && (
                                <p className="text-sm text-green-600">题型代码可用</p>
                            )}

                            {!isEditMode && (
                                <p className="text-xs text-gray-500">
                                    题型代码用于系统识别，创建后不可修改，建议使用英文大写字母
                                </p>
                            )}
                        </div>

                        {/* Subject Name */}
                        <div className="space-y-2">
                            <Label htmlFor="name" className="text-sm font-medium">
                                题型名称 <span className="text-red-500">*</span>
                            </Label>
                            <Input
                                id="name"
                                {...register('type_name', {
                                    required: '请输入题型名称',
                                    minLength: {
                                        value: 1,
                                        message: '题型名称不能为空'
                                    },
                                    maxLength: {
                                        value: 100,
                                        message: '题型名称不能超过100个字符'
                                    }
                                })}
                                disabled={loading}
                                className={errors.type_name || formErrors.type_name ? "border-red-500" : ""}
                            />
                            {errors.type_name && (
                                <p className="text-sm text-red-500">{errors.type_name.message}</p>
                            )}
                            {formErrors.type_name && (
                                <p className="text-sm text-red-500">{formErrors.type_name}</p>
                            )}
                        </div>

                        {/* Description */}
                        <div className="space-y-2">
                            <Label htmlFor="description" className="text-sm font-medium">
                                题型描述
                            </Label>
                            <Textarea
                                id="description"
                                {...register('description', {
                                    maxLength: {
                                        value: 500,
                                        message: '题型描述不能超过500个字符'
                                    }
                                })}
                                placeholder="可选，输入题型的详细描述..."
                                disabled={loading}
                                rows={3}
                                className={formErrors.description ? "border-red-500" : ""}
                            />
                            {errors.description && (
                                <p className="text-sm text-red-500">{errors.description.message}</p>
                            )}
                            {formErrors.description && (
                                <p className="text-sm text-red-500">{formErrors.description}</p>
                            )}
                        </div>

                    </div>

                    {/* System Subject Info */}
                    {isEditMode && questionType && (
                        <AlertDialog>
                            <AlertCircle className="h-4 w-4" />
                            <AlertDialogDescription>
                                <div className="flex items-center justify-between">
                                    <span>题型状态：</span>
                                    <Badge variant={questionType.is_active ? "default" : "secondary"}>
                                        {questionType.is_active ? '启用' : '禁用'}
                                    </Badge>
                                </div>
                            </AlertDialogDescription>
                        </AlertDialog>
                    )}

                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                            disabled={loading}
                        >
                            取消
                        </Button>
                        <Button
                            type="submit"
                            disabled={loading || (codeChecking || (!isEditMode && codeAvailable === false))}
                        >
                            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                            {isEditMode ? '更新' : '创建'}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
};

export default QuestionTypeForm;
