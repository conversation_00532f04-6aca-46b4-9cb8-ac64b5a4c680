import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  BookOpen,
  FileText,
  CheckCircle2,
  Clock,
  AlertCircle,
  RefreshCw,
  Eye,
  Import,
  Download,
  Filter,
  Star
} from 'lucide-react';

import { 
  questionBankApi, 
  QuestionResponse, 
  CreateQuestionRequest, 
  UpdateQuestionRequest,
  QuestionStatistics, 
  QuestionQueryParams
} from '@/services/questionApi';

const QuestionBankPage: React.FC = () => {
  // State management
  const [questions, setQuestions] = useState<QuestionResponse[]>([]);
  const [statistics, setStatistics] = useState<QuestionStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [subjectFilter, setSubjectFilter] = useState('');
  const [gradeFilter, setGradeFilter] = useState('');
  const [difficultyFilter, setDifficultyFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<QuestionResponse | null>(null);
  
  // Form state
  const [questionForm, setQuestionForm] = useState<CreateQuestionRequest>({
    source_type: 'manual',
    question_type: 'multiple_choice',
    question_content: '',
    answer_content: '',
    difficulty_level: 1,
    knowledge_points: {},
    subject: '',
    grade_level: 1
  });

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Load questions when filters change
  useEffect(() => {
    loadQuestions();
  }, [currentPage, searchTerm, subjectFilter, gradeFilter, difficultyFilter, typeFilter, statusFilter]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const statsData = await questionBankApi.getStatistics();
      setStatistics(statsData);
      setError(null);
    } catch (err) {
      setError('Failed to load initial data');
      console.error('Error loading initial data:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadQuestions = async () => {
    try {
      const params: QuestionQueryParams = {
        page: currentPage,
        page_size: 10,
        keyword: searchTerm || undefined,
        subject: subjectFilter || undefined,
        grade_level: gradeFilter ? parseInt(gradeFilter) : undefined,
        difficulty_level: difficultyFilter ? parseInt(difficultyFilter) : undefined,
        question_type: typeFilter || undefined,
        status: statusFilter || undefined
      };
      
      const data = await questionBankApi.getQuestions(params);
      setQuestions(data.questions);
      setTotalPages(Math.ceil(data.total / 10));
    } catch (err) {
      setError('Failed to load questions');
      console.error('Error loading questions:', err);
    }
  };

  const handleCreateQuestion = async () => {
    try {
      if (!questionForm.question_content || !questionForm.subject) {
        setError('Please fill in all required fields');
        return;
      }

      await questionBankApi.createQuestion(questionForm);
      setIsCreateDialogOpen(false);
      resetForm();
      loadQuestions();
      loadInitialData(); // Refresh statistics
    } catch (err) {
      setError('Failed to create question');
      console.error('Error creating question:', err);
    }
  };

  const handleUpdateQuestion = async () => {
    if (!selectedQuestion) return;
    
    try {
      const updateData: UpdateQuestionRequest = {
        question_content: questionForm.question_content,
        answer_content: questionForm.answer_content,
        difficulty_level: questionForm.difficulty_level,
        knowledge_points: questionForm.knowledge_points
      };

      await questionBankApi.updateQuestion(selectedQuestion.id, updateData);
      setIsEditDialogOpen(false);
      setSelectedQuestion(null);
      resetForm();
      loadQuestions();
    } catch (err) {
      setError('Failed to update question');
      console.error('Error updating question:', err);
    }
  };

  const handleDeleteQuestion = async () => {
    if (!selectedQuestion) return;
    
    try {
      await questionBankApi.deleteQuestion(selectedQuestion.id);
      setIsDeleteDialogOpen(false);
      setSelectedQuestion(null);
      loadQuestions();
      loadInitialData(); // Refresh statistics
    } catch (err) {
      setError('Failed to delete question');
      console.error('Error deleting question:', err);
    }
  };

  const resetForm = () => {
    setQuestionForm({
      source_type: 'manual',
      question_type: 'multiple_choice',
      question_content: '',
      answer_content: '',
      difficulty_level: 1,
      knowledge_points: {},
      subject: '',
      grade_level: 1
    });
  };

  const openEditDialog = (question: QuestionResponse) => {
    setSelectedQuestion(question);
    setQuestionForm({
      source_type: question.source_type,
      question_type: question.question_type,
      question_content: question.question_content,
      answer_content: question.answer_content || '',
      difficulty_level: question.difficulty_level,
      knowledge_points: question.knowledge_points,
      subject: question.subject,
      grade_level: question.grade_level
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (question: QuestionResponse) => {
    setSelectedQuestion(question);
    setIsDeleteDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: 'secondary' as const, label: '草稿', icon: FileText },
      published: { variant: 'default' as const, label: '已发布', icon: CheckCircle2 },
      reviewing: { variant: 'outline' as const, label: '审核中', icon: Clock }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getDifficultyBadge = (level: number) => {
    const difficultyConfig = {
      1: { variant: 'default' as const, label: '简单', color: 'bg-green-100 text-green-800' },
      2: { variant: 'secondary' as const, label: '中等', color: 'bg-yellow-100 text-yellow-800' },
      3: { variant: 'destructive' as const, label: '困难', color: 'bg-red-100 text-red-800' }
    };
    
    const config = difficultyConfig[level as keyof typeof difficultyConfig] || difficultyConfig[1];
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getQuestionTypeLabel = (type: string) => {
    const typeLabels = {
      multiple_choice: '选择题',
      fill_blank: '填空题',
      short_answer: '简答题',
      essay: '论述题',
      calculation: '计算题',
      true_false: '判断题'
    };
    return typeLabels[type as keyof typeof typeLabels] || type;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">题库管理</h1>
          <p className="text-muted-foreground">创建、管理和组织题库资源</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadQuestions}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button variant="outline">
            <Import className="h-4 w-4 mr-2" />
            导入题目
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新建题目
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>新建题目</DialogTitle>
                <DialogDescription>
                  请填写题目的详细信息
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="subject">学科 *</Label>
                    <Select value={questionForm.subject} onValueChange={(value) => setQuestionForm({...questionForm, subject: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择学科" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="math">数学</SelectItem>
                        <SelectItem value="chinese">语文</SelectItem>
                        <SelectItem value="english">英语</SelectItem>
                        <SelectItem value="physics">物理</SelectItem>
                        <SelectItem value="chemistry">化学</SelectItem>
                        <SelectItem value="biology">生物</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="grade_level">年级 *</Label>
                    <Select value={questionForm.grade_level.toString()} onValueChange={(value) => setQuestionForm({...questionForm, grade_level: parseInt(value)})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择年级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">一年级</SelectItem>
                        <SelectItem value="2">二年级</SelectItem>
                        <SelectItem value="3">三年级</SelectItem>
                        <SelectItem value="4">四年级</SelectItem>
                        <SelectItem value="5">五年级</SelectItem>
                        <SelectItem value="6">六年级</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="question_type">题型 *</Label>
                    <Select value={questionForm.question_type} onValueChange={(value) => setQuestionForm({...questionForm, question_type: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择题型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="multiple_choice">选择题</SelectItem>
                        <SelectItem value="fill_blank">填空题</SelectItem>
                        <SelectItem value="short_answer">简答题</SelectItem>
                        <SelectItem value="essay">论述题</SelectItem>
                        <SelectItem value="calculation">计算题</SelectItem>
                        <SelectItem value="true_false">判断题</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="difficulty_level">难度等级 *</Label>
                    <Select value={questionForm.difficulty_level.toString()} onValueChange={(value) => setQuestionForm({...questionForm, difficulty_level: parseInt(value)})}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择难度" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">简单</SelectItem>
                        <SelectItem value="2">中等</SelectItem>
                        <SelectItem value="3">困难</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="question_content">题目内容 *</Label>
                  <Textarea
                    id="question_content"
                    value={questionForm.question_content}
                    onChange={(e) => setQuestionForm({...questionForm, question_content: e.target.value})}
                    placeholder="请输入题目内容"
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="answer_content">答案内容</Label>
                  <Textarea
                    id="answer_content"
                    value={questionForm.answer_content}
                    onChange={(e) => setQuestionForm({...questionForm, answer_content: e.target.value})}
                    placeholder="请输入答案内容"
                    rows={3}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateQuestion}>
                  创建题目
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总题目数</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.total_questions || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已发布题目</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.published_questions || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">草稿题目</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.draft_questions || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">使用次数</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {questions.reduce((sum, q) => sum + q.usage_count, 0)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center flex-wrap">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索题目..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={subjectFilter} onValueChange={setSubjectFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="学科" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部学科</SelectItem>
            <SelectItem value="math">数学</SelectItem>
            <SelectItem value="chinese">语文</SelectItem>
            <SelectItem value="english">英语</SelectItem>
            <SelectItem value="physics">物理</SelectItem>
            <SelectItem value="chemistry">化学</SelectItem>
            <SelectItem value="biology">生物</SelectItem>
          </SelectContent>
        </Select>
        <Select value={gradeFilter} onValueChange={setGradeFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="年级" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部年级</SelectItem>
            <SelectItem value="1">一年级</SelectItem>
            <SelectItem value="2">二年级</SelectItem>
            <SelectItem value="3">三年级</SelectItem>
            <SelectItem value="4">四年级</SelectItem>
            <SelectItem value="5">五年级</SelectItem>
            <SelectItem value="6">六年级</SelectItem>
          </SelectContent>
        </Select>
        <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="难度" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部难度</SelectItem>
            <SelectItem value="1">简单</SelectItem>
            <SelectItem value="2">中等</SelectItem>
            <SelectItem value="3">困难</SelectItem>
          </SelectContent>
        </Select>
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="题型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部题型</SelectItem>
            <SelectItem value="multiple_choice">选择题</SelectItem>
            <SelectItem value="fill_blank">填空题</SelectItem>
            <SelectItem value="short_answer">简答题</SelectItem>
            <SelectItem value="essay">论述题</SelectItem>
            <SelectItem value="calculation">计算题</SelectItem>
            <SelectItem value="true_false">判断题</SelectItem>
          </SelectContent>
        </Select>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部状态</SelectItem>
            <SelectItem value="draft">草稿</SelectItem>
            <SelectItem value="published">已发布</SelectItem>
            <SelectItem value="reviewing">审核中</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Question List */}
      <Card>
        <CardHeader>
          <CardTitle>题目列表</CardTitle>
          <CardDescription>
            当前共有 {questions.length} 道题目
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>题目内容</TableHead>
                <TableHead>学科</TableHead>
                <TableHead>年级</TableHead>
                <TableHead>题型</TableHead>
                <TableHead>难度</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>使用次数</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {questions.map((question) => (
                <TableRow key={question.id}>
                  <TableCell className="font-medium max-w-[300px]">
                    <div className="truncate" title={question.question_content}>
                      {question.question_content}
                    </div>
                  </TableCell>
                  <TableCell>{question.subject}</TableCell>
                  <TableCell>{question.grade_level}年级</TableCell>
                  <TableCell>{getQuestionTypeLabel(question.question_type)}</TableCell>
                  <TableCell>{getDifficultyBadge(question.difficulty_level)}</TableCell>
                  <TableCell>{getStatusBadge(question.status)}</TableCell>
                  <TableCell>{question.usage_count}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm" onClick={() => openEditDialog(question)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(question)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>编辑题目</DialogTitle>
            <DialogDescription>
              修改题目信息
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit_question_content">题目内容</Label>
              <Textarea
                id="edit_question_content"
                value={questionForm.question_content}
                onChange={(e) => setQuestionForm({...questionForm, question_content: e.target.value})}
                rows={4}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_answer_content">答案内容</Label>
              <Textarea
                id="edit_answer_content"
                value={questionForm.answer_content}
                onChange={(e) => setQuestionForm({...questionForm, answer_content: e.target.value})}
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_difficulty_level">难度等级</Label>
              <Select value={questionForm.difficulty_level.toString()} onValueChange={(value) => setQuestionForm({...questionForm, difficulty_level: parseInt(value)})}>
                <SelectTrigger>
                  <SelectValue placeholder="选择难度" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">简单</SelectItem>
                  <SelectItem value="2">中等</SelectItem>
                  <SelectItem value="3">困难</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleUpdateQuestion}>
              保存
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              你确定要删除这道题目吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteQuestion}>
              删除
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default QuestionBankPage;