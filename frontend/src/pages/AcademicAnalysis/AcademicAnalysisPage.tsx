import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Award,
  AlertTriangle,
  RefreshCw,
  Download,
  FileText,
  Eye,
  Star,
  Target,
  BookOpen,
  Brain,
  Trophy,
  Clock,
  CheckCircle2,
  XCircle
} from 'lucide-react';

import { 
  studentStatisticsApi, 
  questionAnalysisApi, 
  comprehensiveAnalysisApi, 
  gradeLabelApi,
  learningRecordApi,
  StudentStatistics,
  QuestionAnalysis,
  ExamAnalysis,
  StudentGradeLabel,
  LearningRecord,
  WeakKnowledgePoint,
  ClassComparison,
  SubjectAnalysis
} from '@/services/analysisApi';

const AcademicAnalysisPage: React.FC = () => {
  // State management
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Data states
  const [examAnalysis, setExamAnalysis] = useState<ExamAnalysis | null>(null);
  const [studentStatistics, setStudentStatistics] = useState<StudentStatistics[]>([]);
  const [questionAnalysis, setQuestionAnalysis] = useState<QuestionAnalysis[]>([]);
  const [subjectAnalysis, setSubjectAnalysis] = useState<SubjectAnalysis[]>([]);
  const [classComparison, setClassComparison] = useState<ClassComparison[]>([]);
  const [weakKnowledgePoints, setWeakKnowledgePoints] = useState<WeakKnowledgePoint[]>([]);
  const [learningRecords, setLearningRecords] = useState<LearningRecord[]>([]);
  
  // Filter states
  const [selectedExam, setSelectedExam] = useState<string>('');
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedSubject, setSelectedSubject] = useState<string>('');
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  
  // Dialog states
  const [isAnalysisDialogOpen, setIsAnalysisDialogOpen] = useState(false);
  const [isGradeLabelDialogOpen, setIsGradeLabelDialogOpen] = useState(false);
  const [selectedStudentData, setSelectedStudentData] = useState<StudentStatistics | null>(null);
  const [gradeLabelData, setGradeLabelData] = useState<StudentGradeLabel[]>([]);

  // Mock exam ID - in real app, this would come from route params or context
  const examId = '00000000-0000-0000-0000-000000000001';

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [
        examAnalysisData,
        studentStatsData,
        questionAnalysisData,
        subjectAnalysisData,
        classComparisonData,
        weakPointsData
      ] = await Promise.all([
        comprehensiveAnalysisApi.getExamAnalysis(examId),
        studentStatisticsApi.getStudentsStatistics(examId),
        questionAnalysisApi.getExamQuestionsAnalysis(examId),
        comprehensiveAnalysisApi.getSubjectComparison(examId),
        comprehensiveAnalysisApi.getClassComparison(examId),
        comprehensiveAnalysisApi.getWeakKnowledgePoints(examId)
      ]);
      
      setExamAnalysis(examAnalysisData);
      setStudentStatistics(studentStatsData);
      setQuestionAnalysis(questionAnalysisData);
      setSubjectAnalysis(subjectAnalysisData);
      setClassComparison(classComparisonData);
      setWeakKnowledgePoints(weakPointsData);
      setError(null);
    } catch (err) {
      setError('Failed to load analysis data');
      console.error('Error loading analysis data:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadLearningRecords = async (studentId?: string) => {
    try {
      const records = await learningRecordApi.getLearningRecords({
        student_id: studentId,
        exam_id: examId
      });
      setLearningRecords(records);
    } catch (err) {
      setError('Failed to load learning records');
      console.error('Error loading learning records:', err);
    }
  };

  const loadGradeLabels = async (studentId: string) => {
    try {
      const labels = await gradeLabelApi.getStudentGradeLabels(studentId);
      setGradeLabelData(labels);
    } catch (err) {
      setError('Failed to load grade labels');
      console.error('Error loading grade labels:', err);
    }
  };

  const handleGenerateReport = async (reportType: string) => {
    try {
      await comprehensiveAnalysisApi.generateAnalysisReport(examId, reportType);
      alert('报告生成成功！');
    } catch (err) {
      setError('Failed to generate report');
      console.error('Error generating report:', err);
    }
  };

  const handleGenerateLearningRecords = async () => {
    try {
      await learningRecordApi.generateLearningRecords(examId);
      alert('学习记录生成成功！');
      loadLearningRecords();
    } catch (err) {
      setError('Failed to generate learning records');
      console.error('Error generating learning records:', err);
    }
  };

  const openStudentAnalysis = (student: StudentStatistics) => {
    setSelectedStudentData(student);
    setIsAnalysisDialogOpen(true);
  };

  const openGradeLabelDialog = (studentId: string) => {
    loadGradeLabels(studentId);
    setIsGradeLabelDialogOpen(true);
  };

  const getAbilityLevelBadge = (level: string) => {
    const levelConfig = {
      'A+': { variant: 'default' as const, label: 'A+', color: 'bg-green-100 text-green-800' },
      'A': { variant: 'default' as const, label: 'A', color: 'bg-green-100 text-green-800' },
      'B+': { variant: 'secondary' as const, label: 'B+', color: 'bg-blue-100 text-blue-800' },
      'B': { variant: 'secondary' as const, label: 'B', color: 'bg-blue-100 text-blue-800' },
      'C+': { variant: 'outline' as const, label: 'C+', color: 'bg-yellow-100 text-yellow-800' },
      'C': { variant: 'outline' as const, label: 'C', color: 'bg-yellow-100 text-yellow-800' },
      'D+': { variant: 'destructive' as const, label: 'D+', color: 'bg-red-100 text-red-800' },
      'D': { variant: 'destructive' as const, label: 'D', color: 'bg-red-100 text-red-800' }
    };
    
    const config = levelConfig[level as keyof typeof levelConfig] || levelConfig['C'];
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />;
      default:
        return <TrendingUp className="h-4 w-4 text-gray-500" />;
    }
  };

  const getDifficultyColor = (difficulty: number) => {
    if (difficulty >= 0.8) return 'text-green-600';
    if (difficulty >= 0.6) return 'text-yellow-600';
    if (difficulty >= 0.4) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">学情分析</h1>
          <p className="text-muted-foreground">基于大数据的学情诊断和个性化推荐</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadInitialData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button variant="outline" onClick={() => handleGenerateReport('comprehensive')}>
            <Download className="h-4 w-4 mr-2" />
            生成报告
          </Button>
          <Button onClick={handleGenerateLearningRecords}>
            <FileText className="h-4 w-4 mr-2" />
            生成学习记录
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="students">学生分析</TabsTrigger>
          <TabsTrigger value="questions">题目分析</TabsTrigger>
          <TabsTrigger value="subjects">学科分析</TabsTrigger>
          <TabsTrigger value="knowledge">知识点</TabsTrigger>
          <TabsTrigger value="records">学习记录</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Overview Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">参与学生</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{examAnalysis?.participated_students || 0}</div>
                <div className="text-xs text-muted-foreground">
                  缺考: {examAnalysis?.absent_students || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">平均分</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{(examAnalysis?.average_score || 0).toFixed(1)}</div>
                <div className="text-xs text-muted-foreground">
                  满分: 100
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">及格率</CardTitle>
                <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{((examAnalysis?.pass_rate || 0) * 100).toFixed(1)}%</div>
                <div className="text-xs text-muted-foreground">
                  优秀率: {((examAnalysis?.excellence_rate || 0) * 100).toFixed(1)}%
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">薄弱知识点</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{weakKnowledgePoints.length}</div>
                <div className="text-xs text-muted-foreground">
                  需要关注
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Classes Comparison */}
          <Card>
            <CardHeader>
              <CardTitle>班级对比</CardTitle>
              <CardDescription>各班级成绩对比分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {classComparison.map((classData, index) => (
                  <div key={classData.class_id} className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                        <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                      </div>
                      <div>
                        <div className="font-medium">{classData.class_name}</div>
                        <div className="text-sm text-muted-foreground">{classData.student_count}人</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="font-medium">{classData.average_score.toFixed(1)}</div>
                        <div className="text-sm text-muted-foreground">平均分</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{(classData.pass_rate * 100).toFixed(1)}%</div>
                        <div className="text-sm text-muted-foreground">及格率</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{(classData.excellence_rate * 100).toFixed(1)}%</div>
                        <div className="text-sm text-muted-foreground">优秀率</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Subject Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>学科分析</CardTitle>
              <CardDescription>各学科表现分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {subjectAnalysis.map((subject) => (
                  <div key={subject.subject_id} className="p-4 border rounded">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{subject.subject_name}</h3>
                      <Badge variant={subject.difficulty_rating === 'easy' ? 'default' : 
                                   subject.difficulty_rating === 'medium' ? 'secondary' : 'destructive'}>
                        {subject.difficulty_rating === 'easy' ? '简单' : 
                         subject.difficulty_rating === 'medium' ? '中等' : '困难'}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>平均分</span>
                        <span className="font-medium">{subject.average_score.toFixed(1)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>及格率</span>
                        <span className="font-medium">{(subject.pass_rate * 100).toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>优秀率</span>
                        <span className="font-medium">{(subject.excellence_rate * 100).toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="students" className="space-y-6">
          {/* Student Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>学生成绩分析</CardTitle>
              <CardDescription>学生个人成绩统计和排名</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>学生姓名</TableHead>
                    <TableHead>总分</TableHead>
                    <TableHead>得分率</TableHead>
                    <TableHead>班级排名</TableHead>
                    <TableHead>年级排名</TableHead>
                    <TableHead>能力等级</TableHead>
                    <TableHead>趋势</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {studentStatistics.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell className="font-medium">{student.student_id}</TableCell>
                      <TableCell>{student.total_score}/{student.total_possible}</TableCell>
                      <TableCell>{(student.percentage * 100).toFixed(1)}%</TableCell>
                      <TableCell>{student.class_rank}</TableCell>
                      <TableCell>{student.grade_rank}</TableCell>
                      <TableCell>{getAbilityLevelBadge(student.ability_level)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {getTrendIcon(student.trend_analysis.trend_direction)}
                          <span className="text-sm">
                            {(student.trend_analysis.improvement_rate * 100).toFixed(1)}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="sm" onClick={() => openStudentAnalysis(student)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" onClick={() => openGradeLabelDialog(student.student_id)}>
                            <Star className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-6">
          {/* Question Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>题目分析</CardTitle>
              <CardDescription>题目得分率和难度分析</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>题目编号</TableHead>
                    <TableHead>总答题数</TableHead>
                    <TableHead>正确数</TableHead>
                    <TableHead>平均分</TableHead>
                    <TableHead>难度系数</TableHead>
                    <TableHead>区分度</TableHead>
                    <TableHead>知识点</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {questionAnalysis.map((question) => (
                    <TableRow key={question.id}>
                      <TableCell className="font-medium">{question.question_id}</TableCell>
                      <TableCell>{question.total_attempts}</TableCell>
                      <TableCell>{question.correct_count}</TableCell>
                      <TableCell>{question.average_score.toFixed(1)}</TableCell>
                      <TableCell className={getDifficultyColor(question.difficulty_coefficient)}>
                        {question.difficulty_coefficient.toFixed(2)}
                      </TableCell>
                      <TableCell>{question.discrimination_index.toFixed(2)}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {question.knowledge_point_mastery.slice(0, 2).map((kp, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {kp.knowledge_point}
                            </Badge>
                          ))}
                          {question.knowledge_point_mastery.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{question.knowledge_point_mastery.length - 2}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects" className="space-y-6">
          {/* Subject Detailed Analysis */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {subjectAnalysis.map((subject) => (
              <Card key={subject.subject_id}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    {subject.subject_name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-blue-50 rounded">
                        <div className="text-2xl font-bold text-blue-600">{subject.average_score.toFixed(1)}</div>
                        <div className="text-sm text-blue-600">平均分</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded">
                        <div className="text-2xl font-bold text-green-600">{(subject.pass_rate * 100).toFixed(1)}%</div>
                        <div className="text-sm text-green-600">及格率</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>优秀率</span>
                        <span className="font-medium">{(subject.excellence_rate * 100).toFixed(1)}%</span>
                      </div>
                      <Progress value={subject.excellence_rate * 100} className="h-2" />
                    </div>
                    <div className="p-3 bg-gray-50 rounded">
                      <div className="text-sm text-gray-600">相比表现</div>
                      <div className="font-medium">{subject.performance_comparison}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="knowledge" className="space-y-6">
          {/* Knowledge Points Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>薄弱知识点分析</CardTitle>
              <CardDescription>需要重点关注的知识点</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {weakKnowledgePoints.map((kp, index) => (
                  <div key={index} className="p-4 border rounded">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{kp.knowledge_point}</h3>
                      <Badge variant="destructive">
                        掌握率: {(kp.mastery_rate * 100).toFixed(1)}%
                      </Badge>
                    </div>
                    <div className="mb-3">
                      <div className="text-sm text-muted-foreground mb-1">
                        影响学生: {kp.affected_students}人
                      </div>
                      <Progress value={kp.mastery_rate * 100} className="h-2" />
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">改进建议:</div>
                      {kp.suggested_actions.map((action, actionIndex) => (
                        <div key={actionIndex} className="text-sm text-muted-foreground">
                          • {action}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="records" className="space-y-6">
          {/* Learning Records */}
          <Card>
            <CardHeader>
              <CardTitle>学习记录</CardTitle>
              <CardDescription>个性化学习记录和建议</CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => loadLearningRecords()} className="mb-4">
                加载学习记录
              </Button>
              <div className="space-y-4">
                {learningRecords.map((record) => (
                  <div key={record.id} className="p-4 border rounded">
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium">学生ID: {record.student_id}</div>
                      <div className="text-sm text-muted-foreground">
                        版本: {record.version}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <div className="text-sm text-muted-foreground">原始分数</div>
                        <div className="font-medium">{record.original_score}</div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">标准化分数</div>
                        <div className="font-medium">{record.standardized_score}</div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">得分率</div>
                        <div className="font-medium">{(record.score_percentage * 100).toFixed(1)}%</div>
                      </div>
                    </div>
                    <div className="mt-3">
                      <div className="text-sm text-muted-foreground mb-1">能力分析:</div>
                      <div className="space-y-1">
                        <div className="text-sm">掌握点: {record.ability_analysis.mastered_points.join(', ')}</div>
                        <div className="text-sm">薄弱点: {record.ability_analysis.weak_points.join(', ')}</div>
                        <div className="text-sm">能力等级: {getAbilityLevelBadge(record.ability_analysis.ability_level)}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Student Analysis Dialog */}
      <Dialog open={isAnalysisDialogOpen} onOpenChange={setIsAnalysisDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>学生详细分析</DialogTitle>
            <DialogDescription>
              学生ID: {selectedStudentData?.student_id}
            </DialogDescription>
          </DialogHeader>
          {selectedStudentData && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>总分</Label>
                  <div className="font-medium">{selectedStudentData.total_score}/{selectedStudentData.total_possible}</div>
                </div>
                <div>
                  <Label>得分率</Label>
                  <div className="font-medium">{(selectedStudentData.percentage * 100).toFixed(1)}%</div>
                </div>
                <div>
                  <Label>班级排名</Label>
                  <div className="font-medium">{selectedStudentData.class_rank}</div>
                </div>
                <div>
                  <Label>年级排名</Label>
                  <div className="font-medium">{selectedStudentData.grade_rank}</div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <Label>学科成绩</Label>
                <div className="space-y-2 mt-2">
                  {selectedStudentData.subject_scores.map((subject) => (
                    <div key={subject.subject_id} className="flex items-center justify-between p-2 border rounded">
                      <div className="font-medium">{subject.subject_name}</div>
                      <div className="flex items-center gap-2">
                        <span>{subject.score}/{subject.total_possible}</span>
                        <span className="text-sm text-muted-foreground">({(subject.percentage * 100).toFixed(1)}%)</span>
                        <Badge variant="outline">排名: {subject.rank}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <Separator />
              
              <div>
                <Label>趋势分析</Label>
                <div className="space-y-2 mt-2">
                  <div className="flex items-center justify-between">
                    <span>趋势方向</span>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(selectedStudentData.trend_analysis.trend_direction)}
                      <span>{selectedStudentData.trend_analysis.trend_direction}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>改进率</span>
                    <span>{(selectedStudentData.trend_analysis.improvement_rate * 100).toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>一致性</span>
                    <span>{(selectedStudentData.trend_analysis.consistency_score * 100).toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Grade Label Dialog */}
      <Dialog open={isGradeLabelDialogOpen} onOpenChange={setIsGradeLabelDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>学生能力标签</DialogTitle>
            <DialogDescription>
              管理学生的能力等级和标签
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {gradeLabelData.map((label) => (
              <div key={label.id} className="p-3 border rounded">
                <div className="flex items-center justify-between mb-2">
                  <div className="font-medium">{label.subject_id}</div>
                  {getAbilityLevelBadge(label.ability_level)}
                </div>
                <div className="space-y-2">
                  <div>
                    <div className="text-sm text-muted-foreground">学术标签</div>
                    <div className="flex flex-wrap gap-1">
                      {label.academic_tags.map((tag, index) => (
                        <Badge key={index} variant="outline">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">行为标签</div>
                    <div className="flex flex-wrap gap-1">
                      {label.behavior_tags.map((tag, index) => (
                        <Badge key={index} variant="outline">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AcademicAnalysisPage;