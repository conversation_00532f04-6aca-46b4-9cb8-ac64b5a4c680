import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormComponentProps, MENU_TYPE_OPTIONS } from '@/types/menu';

const BasicInfoForm: React.FC<FormComponentProps> = ({ data, onChange, disabled }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="menu_id">菜单ID *</Label>
          <Input
            id="menu_id"
            value={data.menu_id || ''}
            onChange={(e) => onChange({ ...data, menu_id: e.target.value })}
            disabled={disabled}
            placeholder="输入菜单ID"
          />
        </div>
        <div>
          <Label htmlFor="name">菜单名称 *</Label>
          <Input
            id="name"
            value={data.name || ''}
            onChange={(e) => onChange({ ...data, name: e.target.value })}
            disabled={disabled}
            placeholder="输入菜单名称"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="path">路由路径 *</Label>
          <Input
            id="path"
            value={data.path || ''}
            onChange={(e) => onChange({ ...data, path: e.target.value })}
            disabled={disabled}
            placeholder="/path/to/page"
          />
        </div>
        <div>
          <Label htmlFor="icon">图标</Label>
          <Input
            id="icon"
            value={data.icon || ''}
            onChange={(e) => onChange({ ...data, icon: e.target.value })}
            disabled={disabled}
            placeholder="menu, settings, user"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="menu_type">菜单类型</Label>
          <Select
            value={data.menu_type || 'functional'}
            onValueChange={(value) => onChange({ ...data, menu_type: value })}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {MENU_TYPE_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="parent_id">父菜单ID</Label>
          <Input
            id="parent_id"
            value={data.parent_id || ''}
            onChange={(e) => onChange({ ...data, parent_id: e.target.value })}
            disabled={disabled}
            placeholder="父菜单ID（可选）"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="description">描述</Label>
        <Textarea
          id="description"
          value={data.description || ''}
          onChange={(e) => onChange({ ...data, description: e.target.value })}
          disabled={disabled}
          placeholder="菜单功能描述"
          rows={3}
        />
      </div>

      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Switch
            checked={data.is_active ?? true}
            onCheckedChange={(checked) => onChange({ ...data, is_active: checked })}
            disabled={disabled}
          />
          <Label>启用菜单</Label>
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            checked={data.cache_enabled ?? true}
            onCheckedChange={(checked) => onChange({ ...data, cache_enabled: checked })}
            disabled={disabled}
          />
          <Label>启用缓存</Label>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoForm;