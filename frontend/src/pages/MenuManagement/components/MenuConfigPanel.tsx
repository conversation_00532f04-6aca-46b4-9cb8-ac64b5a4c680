import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Edit, Save, X, Menu, Shield, TestTube, Trees } from 'lucide-react';
import { MenuConfigPanelProps, MenuItem } from '@/types/menu';
import BasicInfoForm from './BasicInfoForm';
import PermissionConfigForm from './PermissionConfigForm';
import AdvancedSettingsForm from './AdvancedSettingsForm';
import UsageStatistics from './UsageStatistics';

const MenuConfigPanel: React.FC<MenuConfigPanelProps> = ({
  menu,
  templates,
  isEditing,
  isCreating,
  onSave,
  onCancel,
  onEdit,
}) => {
  const [formData, setFormData] = useState<Partial<MenuItem>>({});

  useEffect(() => {
    if (menu) {
      setFormData(menu);
    } else if (isCreating) {
      setFormData({
        menu_id: '',
        name: '',
        path: '',
        icon: '',
        parent_id: '',
        menu_type: 'functional',
        description: '',
        required_permissions: [],
        data_scopes: [],
        permission_mode: 'any',
        access_level: 0,
        sort_order: 0,
        is_active: true,
        cache_enabled: true,
      });
    }
  }, [menu, isCreating]);

  const handleSave = () => {
    onSave(formData);
  };

  // 空状态组件
  if (!menu && !isCreating) {
    return (
      <div className="flex-1 flex items-center justify-center bg-white">
        <div className="text-center">
          <Menu className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            选择菜单进行配置
          </h3>
          <p className="text-gray-500 mb-4">
            从左侧菜单树中选择一个菜单项来查看和编辑其权限配置
          </p>
          <div className="flex justify-center space-x-4 text-sm text-gray-400">
            <div className="flex items-center">
              <Trees className="w-4 h-4 mr-1" />
              菜单结构管理
            </div>
            <div className="flex items-center">
              <Shield className="w-4 h-4 mr-1" />
              权限配置
            </div>
            <div className="flex items-center">
              <TestTube className="w-4 h-4 mr-1" />
              权限测试
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-white flex flex-col">
      {/* 头部 */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {isCreating ? '创建菜单' : menu?.name || '菜单配置'}
            </h2>
            {menu && (
              <p className="text-sm text-gray-500 mt-1">
                ID: {menu.menu_id} | 路径: {menu.path}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <Button variant="outline" onClick={onCancel}>
                  <X className="w-4 h-4 mr-2" />
                  取消
                </Button>
                <Button onClick={handleSave}>
                  <Save className="w-4 h-4 mr-2" />
                  保存
                </Button>
              </>
            ) : (
              <Button onClick={onEdit}>
                <Edit className="w-4 h-4 mr-2" />
                编辑
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 配置内容 */}
      <div className="flex-1 overflow-auto p-6">
        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="permissions">权限配置</TabsTrigger>
            <TabsTrigger value="advanced">高级设置</TabsTrigger>
            <TabsTrigger value="stats">使用统计</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <BasicInfoForm
              data={formData}
              onChange={setFormData}
              disabled={!isEditing}
            />
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <PermissionConfigForm
              data={formData}
              templates={templates}
              onChange={setFormData}
              disabled={!isEditing}
            />
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <AdvancedSettingsForm
              data={formData}
              onChange={setFormData}
              disabled={!isEditing}
            />
          </TabsContent>

          <TabsContent value="stats" className="space-y-4">
            {menu?.usage_stats && (
              <UsageStatistics stats={menu.usage_stats} />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default MenuConfigPanel;