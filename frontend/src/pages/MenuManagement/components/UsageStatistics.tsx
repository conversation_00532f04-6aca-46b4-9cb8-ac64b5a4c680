import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { UsageStatsProps } from '@/types/menu';

const UsageStatistics: React.FC<UsageStatsProps> = ({ stats }) => {
  if (!stats) {
    return (
      <div className="text-center text-gray-500 py-8">
        暂无使用统计数据
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 gap-4">
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-blue-600">
            {stats.total_access_count}
          </div>
          <div className="text-sm text-gray-600">总访问次数</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-green-600">
            {stats.unique_user_count}
          </div>
          <div className="text-sm text-gray-600">独立用户数</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-red-600">
            {stats.denied_access_count}
          </div>
          <div className="text-sm text-gray-600">拒绝访问次数</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-purple-600">
            {stats.avg_daily_access.toFixed(1)}
          </div>
          <div className="text-sm text-gray-600">日均访问</div>
        </CardContent>
      </Card>
      
      {stats.last_accessed_at && (
        <Card className="col-span-2">
          <CardContent className="p-4">
            <div className="text-sm text-gray-600">最后访问时间</div>
            <div className="text-lg font-medium">
              {new Date(stats.last_accessed_at).toLocaleString()}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UsageStatistics;