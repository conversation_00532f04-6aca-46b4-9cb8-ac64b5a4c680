import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { MenuItem, PermissionTemplate } from '@/types/menu';
import { MenuTreePanel, MenuConfigPanel } from './components';
import { menuApi, permissionTemplateApi } from '@/services/menuApi';

const MenuManagementPage: React.FC = () => {
  // State management
  const [menus, setMenus] = useState<MenuItem[]>([]);
  const [templates, setTemplates] = useState<PermissionTemplate[]>([]);
  const [selectedMenu, setSelectedMenu] = useState<MenuItem | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  // 加载菜单数据
  const loadMenus = useCallback(async () => {
    try {
      setLoading(true);
      const response = await menuApi.getMenuTree({
        include_children: true,
        include_metadata: true
      });
      if (response.success) {
        setMenus(response.data || []);
      } else {
        throw new Error(response.message || 'Failed to load menus');
      }
    } catch (error) {
      console.error('Load menus error:', error);
      toast.error("加载失败：无法加载菜单数据");
    } finally {
      setLoading(false);
    }
  }, []);

  // 加载权限模板
  const loadTemplates = useCallback(async () => {
    try {
      const response = await permissionTemplateApi.getTemplates({
        is_active: true
      });
      if (response.success) {
        setTemplates(response.data || []);
      } else {
        throw new Error(response.message || 'Failed to load templates');
      }
    } catch (error) {
      console.error('Load templates error:', error);
      toast.error("加载失败：无法加载权限模板");
    }
  }, []);

  useEffect(() => {
    loadMenus();
    loadTemplates();
  }, [loadMenus, loadTemplates]);

  // 切换节点展开状态
  const toggleNode = (menuId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(menuId)) {
      newExpanded.delete(menuId);
    } else {
      newExpanded.add(menuId);
    }
    setExpandedNodes(newExpanded);
  };

  // 保存菜单
  const handleSaveMenu = async (menuData: Partial<MenuItem>) => {
    try {
      let response;

      if (isCreating) {
        // 创建菜单
        response = await menuApi.createMenu(menuData as any);
      } else if (selectedMenu?.menu_id) {
        // 更新菜单
        response = await menuApi.updateMenu(selectedMenu.menu_id, menuData as any);
      } else {
        throw new Error('Invalid menu operation');
      }

      if (response.success) {
        toast.success(`菜单${isCreating ? '创建' : '更新'}成功`);
        setIsEditing(false);
        setIsCreating(false);
        setSelectedMenu(null);
        loadMenus();
      } else {
        throw new Error(response.message || 'Save failed');
      }
    } catch (error) {
      console.error('Save menu error:', error);
      toast.error("菜单保存失败，请重试");
    }
  };

  // 删除菜单
  const handleDeleteMenu = async (menuId: string) => {
    if (!confirm('确定删除此菜单吗？此操作不可撤销。')) {
      return;
    }

    try {
      const response = await menuApi.deleteMenu(menuId, false);

      if (response.success) {
        toast.success("菜单已删除");
        loadMenus();
        if (selectedMenu?.menu_id === menuId) {
          setSelectedMenu(null);
        }
      } else {
        throw new Error(response.message || 'Delete failed');
      }
    } catch (error) {
      console.error('Delete menu error:', error);
      toast.error("菜单删除失败，请重试");
    }
  };

  // 创建菜单
  const handleCreateMenu = () => {
    setSelectedMenu(null);
    setIsCreating(true);
    setIsEditing(true);
  };

  // 编辑菜单
  const handleEditMenu = (menu: MenuItem) => {
    setSelectedMenu(menu);
    setIsEditing(true);
  };

  // 取消编辑
  const handleCancel = () => {
    setIsEditing(false);
    setIsCreating(false);
    setSelectedMenu(null);
  };

  return (
    <div className="h-full bg-gray-50">
      {/* 页面标题 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">菜单权限管理</h1>
          <p className="text-gray-600 mt-1">管理系统菜单结构和权限配置，支持层次化菜单管理和灵活的权限控制</p>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex h-[calc(100vh-120px)]">
        {/* 左侧菜单树 */}
        <MenuTreePanel
          menus={menus}
          loading={loading}
          searchTerm={searchTerm}
          filterType={filterType}
          selectedMenu={selectedMenu}
          expandedNodes={expandedNodes}
          onSearchChange={setSearchTerm}
          onFilterChange={setFilterType}
          onMenuSelect={setSelectedMenu}
          onMenuEdit={handleEditMenu}
          onMenuDelete={handleDeleteMenu}
          onCreateMenu={handleCreateMenu}
          onToggleNode={toggleNode}
        />

        {/* 右侧配置面板 */}
        <MenuConfigPanel
          menu={selectedMenu}
          templates={templates}
          isEditing={isEditing}
          isCreating={isCreating}
          onSave={handleSaveMenu}
          onCancel={handleCancel}
          onEdit={() => setIsEditing(true)}
        />
      </div>
    </div>
  );
};

export default MenuManagementPage;