import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, Download } from 'lucide-react';
import { toast } from 'sonner';

import SubjectTable from './components/SubjectTable';
import SubjectForm from './components/SubjectForm';
import { subjectApi } from '@/services/subjectApi';
import { 
  Subject, 
  SubjectQueryParams, 
  SubjectFormData,
  DEFAULT_SUBJECT_QUERY,
  SUBJECT_SORT_OPTIONS,
  SUBJECT_STATUS_OPTIONS
} from '@/types/subject';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

const SubjectManagementPage: React.FC = () => {
  // State management
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(false);
  const [queryParams, setQueryParams] = useState<SubjectQueryParams>(DEFAULT_SUBJECT_QUERY);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });
  
  // Form state
  const [formOpen, setFormOpen] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | undefined>();
  const [formLoading, setFormLoading] = useState(false);
  const [searchInput, setSearchInput] = useState('');

  // Load subjects data
  const loadSubjects = async (params?: Partial<SubjectQueryParams>) => {
    try {
      setLoading(true);
      const finalParams = { ...queryParams, ...params };
      const response = await subjectApi.getSubjects(finalParams);
      
      if (response.success && response.data) {
        setSubjects(response.data);
        setPagination({
          current: response.pagination.page,
          pageSize: response.pagination.page_size,
          total: response.pagination.total,
          totalPages: response.pagination.total_pages,
        });
        setQueryParams(finalParams);
      }
    } catch (error) {
      console.error('Failed to load subjects:', error);
      toast.error('加载学科列表失败');
    } finally {
      setLoading(false);
    }
  };

  // Initialize data
  useEffect(() => {
    loadSubjects().then( );
  }, []);

  // Handle search
  const handleSearch = (search: string) => {
    loadSubjects({ ...queryParams, search, page: 1 });
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof SubjectQueryParams, value: any) => {
    const newParams = { ...queryParams, [key]: value, page: 1 };
    loadSubjects(newParams);
  };

  // Handle pagination
  const handlePageChange = (page: number, pageSize: number) => {
    loadSubjects({ ...queryParams, page, page_size: pageSize });
  };

  // Handle create subject
  const handleCreateSubject = () => {
    setEditingSubject(undefined);
    setFormOpen(true);
  };

  // Handle edit subject
  const handleEditSubject = (subject: Subject) => {
    setEditingSubject(subject);
    setFormOpen(true);
  };

    // Handle form submit
    const handleFormSubmit = async (data: SubjectFormData) => {
      try {
        setFormLoading(true);

        const validatedData: SubjectFormData = {
          ...data,
          order_level: Number(data.order_level), // 强制转换为数字
        };

        if (editingSubject) {
          // Update subject
          const response = await subjectApi.updateSubject(editingSubject.id, validatedData);
          if (response.success) {
            toast.success('学科更新成功');
            setFormOpen(false);
            await loadSubjects();
          }
        } else {
          // Create subject
          const response = await subjectApi.createSubject(validatedData);
          if (response.success) {
            toast.success('学科创建成功');
            setFormOpen(false);
            await loadSubjects();
          }
        }
      } catch (error: any) {
        console.error('Failed to save subject:', error);
        toast.error(error.response?.data?.message || '保存失败');
      } finally {
        setFormLoading(false);
      }
    };

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [subjectToDelete, setSubjectToDelete] = useState<string | null>(null);

  const handleDeleteSubject = (id: string) => {
    setSubjectToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteSubject = async () => {
    if (!subjectToDelete) return;

    try {
      const response = await subjectApi.deleteSubject(subjectToDelete);
      if (response.success) {
        toast.success('学科删除成功');
        await loadSubjects();
      }
    } catch (error: any) {
      console.error('Failed to delete subject:', error);
      toast.error(error.response?.data?.message || '删除失败');
    } finally {
      setDeleteDialogOpen(false);
      setSubjectToDelete(null);
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await subjectApi.toggleSubjectStatus(id, isActive);
      if (response.success) {
        toast.success(`学科已${isActive ? '启用' : '禁用'}`);
        await loadSubjects();
      }
    } catch (error: any) {
      console.error('Failed to toggle subject status:', error);
      toast.error(error.response?.data?.message || '状态切换失败');
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      const blob = await subjectApi.exportSubjects(queryParams);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `subjects-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('导出成功');
    } catch (error) {
      console.error('Failed to export subjects:', error);
      toast.error('导出接口开发中');
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">学科管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的学科信息，包括学科的创建、编辑和状态管理</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            导出
          </Button>
          <Button onClick={handleCreateSubject}>
            <Plus className="w-4 h-4 mr-2" />
            新增学科
          </Button>
        </div>
      </div>

      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">学科列表</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <CardTitle className="text-lg">学科列表</CardTitle>
                
                <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                  <div className="relative w-full md:w-64">
                    <button 
                      type="button" 
                      onClick={() => handleSearch(searchInput)}
                      className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
                    >
                      <Search className="h-4 w-4" />
                    </button>
                    <Input
                      placeholder="搜索学科名称或代码..."
                      className="w-full pl-8"
                      value={searchInput}
                      onChange={(e) => setSearchInput(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch(searchInput)}
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Select
                      value={queryParams.is_active === undefined ? 'all' : String(queryParams.is_active)}
                      onValueChange={(value) => 
                        handleFilterChange('is_active', value === 'all' ? undefined : value === 'true')
                      }
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="全部状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        {SUBJECT_STATUS_OPTIONS.map((option) => (
                          <SelectItem key={String(option.value)} value={String(option.value)}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Select
                      value={queryParams.order_by ? `${queryParams.order_by}-${queryParams.order_direction}` : ''}
                      onValueChange={(value) => {
                        const [orderBy, orderDirection] = value.split('-');
                        handleFilterChange('order_by', orderBy);
                        handleFilterChange('order_direction', orderDirection);
                      }}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="排序方式" />
                      </SelectTrigger>
                      <SelectContent>
                        {SUBJECT_SORT_OPTIONS.map((option) => (
                          <React.Fragment key={option.value}>
                            <SelectItem value={`${option.value}-asc`}>
                              {option.label} ↑
                            </SelectItem>
                            <SelectItem value={`${option.value}-desc`}>
                              {option.label} ↓
                            </SelectItem>
                          </React.Fragment>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <SubjectTable
                subjects={subjects}
                loading={loading}
                onEdit={handleEditSubject}
                onDelete={handleDeleteSubject}
                onToggleStatus={handleToggleStatus}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  onChange: handlePageChange,
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="statistics">
          <div>统计分析接口开发中</div>
        </TabsContent>
      </Tabs>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除该学科吗？此操作不可恢复。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteSubject}>
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Subject Form Modal */}
      <SubjectForm
        subject={editingSubject}
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />
    </div>
  );
};

export default SubjectManagementPage;