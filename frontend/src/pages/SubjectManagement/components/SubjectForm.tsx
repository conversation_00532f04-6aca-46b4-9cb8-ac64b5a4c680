import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

import { subjectApi } from '@/services/subjectApi';
import { SubjectFormProps, SubjectFormData, SubjectFormErrors } from '@/types/subject';

const SubjectForm: React.FC<SubjectFormProps> = ({
  subject,
  open,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [formErrors, setFormErrors] = useState<SubjectFormErrors>({});
  const [codeChecking, setCodeChecking] = useState(false);
  const [codeAvailable, setCodeAvailable] = useState<boolean | null>(null);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<SubjectFormData>({
    defaultValues: {
      code: '',
      name: '',
      description: '',
      order_level: 1,
    },
  });

  const watchedCode = watch('code');

  // Reset form when dialog opens/closes or subject changes
  useEffect(() => {
    if (open) {
      if (subject) {
        reset({
          code: subject.code,
          name: subject.name,
          description: subject.description || '',
          order_level: subject.order_level,
        });
      } else {
        reset({
          code: '',
          name: '',
          description: '',
          order_level: 1,
        });
      }
      setFormErrors({});
      setCodeAvailable(null);
    }
  }, [open, subject, reset]);

  // Check code availability when code changes (for new subjects)
  useEffect(() => {
    if (!subject && watchedCode && watchedCode.length >= 2) {
      const checkCodeDebounced = setTimeout(async () => {
        try {
          setCodeChecking(true);
          const response = await subjectApi.checkCodeAvailability(watchedCode);
          if (response.success && response.data) {
            setCodeAvailable(response.data.is_available);
          }
          else {
            setCodeAvailable(false);
          }
        } catch (error) {
          console.error('Failed to check code availability:', error);
        } finally {
          setCodeChecking(false);
        }
      }, 500);

      return () => clearTimeout(checkCodeDebounced);
    } else {
      setCodeAvailable(null);
      setCodeChecking(false);
    }
  }, [watchedCode, subject]);

  const handleFormSubmit = async (data: SubjectFormData) => {
    // Validate code availability for new subjects
    if (!subject && codeAvailable === false) {
      toast.error('学科代码已存在，请使用其他代码');
      return;
    }

    try {
      await onSubmit(data);
    } catch (error: any) {
      // Handle validation errors
      if (error.response?.data?.errors) {
        setFormErrors(error.response.data.errors);
      }
    }
  };

  const isEditMode = !!subject;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? '编辑学科' : '新增学科'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="space-y-4">
            {/* Subject Code */}
            <div className="space-y-2">
              <Label htmlFor="code" className="text-sm font-medium">
                学科代码 <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <Input
                  id="code"
                  {...register('code', {
                    required: '请输入学科代码',
                    pattern: {
                      value: /^[A-Z0-9_]+$/,
                      message: '学科代码只能包含大写字母、数字和下划线'
                    },
                    minLength: {
                      value: 2,
                      message: '学科代码至少需要2个字符'
                    },
                    maxLength: {
                      value: 20,
                      message: '学科代码不能超过20个字符'
                    }
                  })}
                  placeholder="例如：MATH, CHINESE"
                  disabled={isEditMode || loading}
                  className={cn(
                    errors.code || formErrors.code ? "border-red-500" : "",
                    !isEditMode && codeAvailable === false ? "border-red-500" : "",
                    !isEditMode && codeAvailable === true ? "border-green-500" : ""
                  )}
                />
                
                {/* Code checking indicator */}
                {!isEditMode && watchedCode && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    {codeChecking ? (
                      <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
                    ) : codeAvailable === true ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : codeAvailable === false ? (
                      <AlertCircle className="w-4 h-4 text-red-500" />
                    ) : null}
                  </div>
                )}
              </div>
              
              {/* Code validation messages */}
              {errors.code && (
                <p className="text-sm text-red-500">{errors.code.message}</p>
              )}
              {formErrors.code && (
                <p className="text-sm text-red-500">{formErrors.code}</p>
              )}
              {!isEditMode && codeAvailable === false && (
                <p className="text-sm text-red-500">该学科代码已存在</p>
              )}
              {!isEditMode && codeAvailable === true && (
                <p className="text-sm text-green-600">学科代码可用</p>
              )}
              
              {!isEditMode && (
                <p className="text-xs text-gray-500">
                  学科代码用于系统识别，创建后不可修改，建议使用英文大写字母
                </p>
              )}
            </div>

            {/* Subject Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium">
                学科名称 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                {...register('name', {
                  required: '请输入学科名称',
                  minLength: {
                    value: 1,
                    message: '学科名称不能为空'
                  },
                  maxLength: {
                    value: 100,
                    message: '学科名称不能超过100个字符'
                  }
                })}
                placeholder="例如：数学、语文、英语"
                disabled={loading}
                className={errors.name || formErrors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
              {formErrors.name && (
                <p className="text-sm text-red-500">{formErrors.name}</p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">
                学科描述
              </Label>
              <Textarea
                id="description"
                {...register('description', {
                  maxLength: {
                    value: 500,
                    message: '学科描述不能超过500个字符'
                  }
                })}
                placeholder="可选，输入学科的详细描述..."
                disabled={loading}
                rows={3}
                className={formErrors.description ? "border-red-500" : ""}
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description.message}</p>
              )}
              {formErrors.description && (
                <p className="text-sm text-red-500">{formErrors.description}</p>
              )}
            </div>

            {/* Order Level */}
            <div className="space-y-2">
              <Label htmlFor="order_level" className="text-sm font-medium">
                排序级别 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="order_level"
                min="1"
                type="number"
                {...register('order_level', {
                  required: '请输入排序级别',
                  min: {
                    value: 1,
                    message: '排序级别不能小于0'
                  },
                  max: {
                    value: 999,
                    message: '排序级别不能大于999'
                  }
                })}
                placeholder="数字越小排序越靠前"
                disabled={loading}
                className={errors.order_level || formErrors.order_level ? "border-red-500" : ""}
              />
              {errors.order_level && (
                <p className="text-sm text-red-500">{errors.order_level.message}</p>
              )}
              {formErrors.order_level && (
                <p className="text-sm text-red-500">{formErrors.order_level}</p>
              )}
              <p className="text-xs text-gray-500">
                排序级别决定学科在列表中的显示顺序，数字越小越靠前
              </p>
            </div>
          </div>

          {/* System Subject Info */}
          {isEditMode && subject && (
            <AlertDialog>
              <AlertCircle className="h-4 w-4" />
              <AlertDialogDescription>
                <div className="flex items-center justify-between">
                  <span>学科状态：</span>
                  <Badge variant={subject.is_active ? "default" : "secondary"}>
                    {subject.is_active ? '启用' : '禁用'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <span>关联数据：</span>
                  <span className="text-sm">
                    {subject.question_count} 题目，{subject.paper_count} 试卷
                  </span>
                </div>
              </AlertDialogDescription>
            </AlertDialog>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading || (codeChecking || (!isEditMode && codeAvailable === false))}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditMode ? '更新' : '创建'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default SubjectForm;