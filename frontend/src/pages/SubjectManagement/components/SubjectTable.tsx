import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2,
  Power, 
  PowerOff,
  BarChart3
} from 'lucide-react';
import { Subject, SubjectTableProps } from '@/types/subject';

import { cn } from '@/lib/utils';

const SubjectTable: React.FC<SubjectTableProps> = ({
  subjects,
  loading,
  onEdit,
  onDelete,
  onToggleStatus,
  pagination,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const handleToggleStatus = (subject: Subject) => {
    onToggleStatus(subject.id, !subject.is_active);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>学科代码</TableHead>
                <TableHead>学科名称</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>排序</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>题目数</TableHead>
                <TableHead>试卷数</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead className="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  {Array.from({ length: 9 }).map((_, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <div className="h-4 bg-gray-200 rounded animate-pulse" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (!Array.isArray(subjects) || subjects.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 text-lg mb-2">暂无学科数据</div>
        <div className="text-gray-400 text-sm">点击&#34;新增学科&#34;创建第一个学科</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">学科代码</TableHead>
              <TableHead className="w-[120px]">学科名称</TableHead>
              <TableHead className="min-w-[200px]">描述</TableHead>
              <TableHead className="w-[80px] text-center">排序</TableHead>
              <TableHead className="w-[80px] text-center">状态</TableHead>
              <TableHead className="w-[80px] text-center">题目数</TableHead>
              <TableHead className="w-[80px] text-center">试卷数</TableHead>
              <TableHead className="w-[150px]">创建时间</TableHead>
              <TableHead className="w-[100px] text-center">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {(subjects as Subject[]).map((subject) => (
              <TableRow key={subject.id} className="hover:bg-gray-50">
                <TableCell className="font-mono">
                  <Badge variant="outline" className="text-xs">
                    {subject.code}
                  </Badge>
                </TableCell>
                
                <TableCell className="font-medium">
                  {subject.name}
                </TableCell>
                
                <TableCell className="text-gray-600">
                  {subject.description || '-'}
                </TableCell>
                
                <TableCell className="text-center">
                  <Badge variant="secondary" className="text-xs">
                    {subject.order_level}
                  </Badge>
                </TableCell>
                
                <TableCell className="text-center">
                  <Badge 
                    variant={subject.is_active ? "default" : "secondary"}
                    className={cn(
                      "text-xs",
                      subject.is_active 
                        ? "bg-green-100 text-green-800 hover:bg-green-200" 
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    )}
                  >
                    {subject.is_active ? '启用' : '禁用'}
                  </Badge>
                </TableCell>
                
                <TableCell className="text-center">
                  <div className="flex items-center justify-center">
                    <BarChart3 className="w-3 h-3 mr-1 text-gray-400" />
                    <span className="text-sm">{subject.question_count || 0}</span>
                  </div>
                </TableCell>
                
                <TableCell className="text-center">
                  <div className="flex items-center justify-center">
                    <BarChart3 className="w-3 h-3 mr-1 text-gray-400" />
                    <span className="text-sm">{subject.paper_count || 0}</span>
                  </div>
                </TableCell>
                
                <TableCell className="text-sm text-gray-500">
                  {formatDate(subject.created_at)}
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center justify-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem 
                          onClick={() => onEdit(subject)}
                          className="cursor-pointer"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>
                        
                        <DropdownMenuItem 
                          onClick={() => handleToggleStatus(subject)}
                          className="cursor-pointer"
                        >
                          {subject.is_active ? (
                            <>
                              <PowerOff className="mr-2 h-4 w-4" />
                              禁用
                            </>
                          ) : (
                            <>
                              <Power className="mr-2 h-4 w-4" />
                              启用
                            </>
                          )}
                        </DropdownMenuItem>

                        
                        <DropdownMenuItem 
                          onClick={() => onDelete(subject.id)}
                          className="cursor-pointer text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* 分页 */}
      {pagination && pagination.total > 0 && (
        <div className="flex flex-wrap items-center gap-4 mt-2">
          <div className="text-sm text-gray-500 whitespace-nowrap">
            显示第 {((pagination.current - 1) * pagination.pageSize) + 1} -{' '}
            {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共{' '}
            {pagination.total} 条记录
          </div>
          {/* 分页大小选择器和跳页 */}
          <div className="flex flex-wrap gap-3 items-center">
            <select
              className="border rounded px-2 py-1 text-sm min-w-[80px]"
              value={pagination.pageSize}
              onChange={e => pagination.onChange(1, Number(e.target.value))}
            >
              {[10, 20, 50, 100].map(size => (
                <option key={size} value={size}>{size} 条/页</option>
              ))}
            </select>
            <span className="text-sm">跳转到</span>
            <input
              type="number"
              min={1}
              max={Math.ceil(pagination.total / pagination.pageSize)}
              defaultValue={pagination.current}
              onBlur={e => {
                let page = Number(e.target.value);
                if (page < 1) page = 1;
                if (page > Math.ceil(pagination.total / pagination.pageSize)) page = Math.ceil(pagination.total / pagination.pageSize);
                if (page !== pagination.current) {
                  pagination.onChange(page, pagination.pageSize);
                }
              }}
              className="border rounded px-2 py-1 w-16 text-sm text-center"
            />
            <span className="text-sm">页</span>
          </div>
          {/* 分页按钮 */}
          <div className="flex items-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => {
                      if (pagination.current > 1) {
                        pagination.onChange(pagination.current - 1, pagination.pageSize);
                      }
                    }}
                    className={pagination.current === 1 ? 'pointer-events-none opacity-50' : ''}
                  />
                </PaginationItem>
                {Array.from({ length: Math.ceil(pagination.total / pagination.pageSize) }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      isActive={page === pagination.current}
                      onClick={() => pagination.onChange(page, pagination.pageSize)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                <PaginationItem>
                  <PaginationNext
                    onClick={() => {
                      if (pagination.current < Math.ceil(pagination.total / pagination.pageSize)) {
                        pagination.onChange(pagination.current + 1, pagination.pageSize);
                      }
                    }}
                    className={
                      pagination.current === Math.ceil(pagination.total / pagination.pageSize)
                        ? 'pointer-events-none opacity-50'
                        : ''
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubjectTable;