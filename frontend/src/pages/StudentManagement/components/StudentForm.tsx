import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle, Loader2, Mail, Phone, User } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import {
  GENDER_OPTIONS,
  GUARDIAN_RELATIONS,
  STUDENT_PROFILE_LEVELS,
  STUDENT_STATUS_OPTIONS,
  StudentFormData,
  StudentFormErrors,
  StudentFormProps
} from '@/types/student';

const StudentForm: React.FC<StudentFormProps> = ({
  student,
  open,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [formErrors, setFormErrors] = useState<StudentFormErrors>({});
  const [studentIdChecking, setStudentIdChecking] = useState(false);
  const [studentIdAvailable, setStudentIdAvailable] = useState<boolean | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<StudentFormData>({
    defaultValues: {
      student_number: '',
      name: '',
      gender: 'male',
      birth_date: '',
      id_number: '',
      phone: '',
      email: '',
      address: '',
      guardian_name: '',
      guardian_phone: '',
      guardian_relation: 'father',
      enrollment_date: new Date().toISOString().split('T')[0],
      status: 'active',
      profile_level: undefined,
      notes: '',
    },
  });

  const watchedStudentId = watch('student_id');

  // Reset form when dialog opens/closes or student changes
  useEffect(() => {
    if (open) {
      if (student) {
        reset({
          student_number: student.student_number,
          name: student.name,
          gender: student.gender || 'male',
          birth_date: student.birth_date || '',
          id_number: student.id_number || '',
          phone: student.phone || '',
          email: student.email || '',
          address: student.address || '',
          guardian_name: student.guardian_name || '',
          guardian_phone: student.guardian_phone || '',
          guardian_relation: student.guardian_relation || 'father',
          enrollment_date: student.enrollment_date || new Date().toISOString().split('T')[0],
          status: student.status || 'active',
          profile_level: student.profile_level || undefined,
          notes: student.notes || '',
        });
      } else {
        reset({
          student_number: '',
          name: '',
          gender: 'male',
          birth_date: '',
          id_number: '',
          phone: '',
          email: '',
          address: '',
          guardian_name: '',
          guardian_phone: '',
          guardian_relation: 'father',
          enrollment_date: new Date().toISOString().split('T')[0],
          status: 'active',
          profile_level: undefined,
          notes: '',
        });
      }
      setFormErrors({});
      setStudentIdAvailable(null);
    }
  }, [open, student, reset]);

  // Check student ID availability when student_id changes (for new students)
  useEffect(() => {
    if (!student && watchedStudentId && watchedStudentId.length >= 2) {
      const checkStudentIdDebounced = setTimeout(async () => {
        try {
          setStudentIdChecking(true);
          // This would need to be implemented in the API
          // const response = await studentsApi.checkStudentIdAvailability(watchedStudentId);
          // For now, just simulate availability check
          setStudentIdAvailable(true);
        } catch (error) {
          console.error('Failed to check student ID availability:', error);
        } finally {
          setStudentIdChecking(false);
        }
      }, 500);

      return () => clearTimeout(checkStudentIdDebounced);
    } else {
      setStudentIdAvailable(null);
      setStudentIdChecking(false);
    }
  }, [watchedStudentId, student]);

  const handleFormSubmit = async (data: StudentFormData) => {
    // Validate student ID availability for new students
    if (!student && studentIdAvailable === false) {
      toast.error('学号已存在，请使用其他学号');
      return;
    }

    try {
      await onSubmit(data);
    } catch (error: any) {
      // Handle validation errors
      if (error.response?.data?.errors) {
        setFormErrors(error.response.data.errors);
      }
    }
  };

  const isEditMode = !!student;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <User className="w-5 h-5 mr-2" />
            {isEditMode ? '编辑学生' : '新增学生'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium border-b pb-2">基本信息</h3>

              {/* Student ID */}
              <div className="space-y-2">
                <Label htmlFor="student_id" className="text-sm font-medium">
                  学号 <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="student_id"
                    {...register('student_id', {
                      required: '请输入学号',
                      pattern: {
                        value: /^[A-Z0-9]+$/,
                        message: '学号只能包含大写字母和数字'
                      },
                      minLength: {
                        value: 2,
                        message: '学号至少需要2个字符'
                      },
                      maxLength: {
                        value: 50,
                        message: '学号不能超过50个字符'
                      }
                    })}
                    placeholder="例如：2024001"
                    disabled={isEditMode || loading}
                    className={cn(
                      errors.student_number || formErrors.student_id ? "border-red-500" : "",
                      !isEditMode && studentIdAvailable === false ? "border-red-500" : "",
                      !isEditMode && studentIdAvailable === true ? "border-green-500" : ""
                    )}
                  />

                  {/* Student ID checking indicator */}
                  {!isEditMode && watchedStudentId && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      {studentIdChecking ? (
                        <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
                      ) : studentIdAvailable === true ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : studentIdAvailable === false ? (
                        <AlertCircle className="w-4 h-4 text-red-500" />
                      ) : null}
                    </div>
                  )}
                </div>

                {errors.student_number && (
                  <p className="text-sm text-red-500">{errors.student_number.message}</p>
                )}
                {formErrors.student_id && (
                  <p className="text-sm text-red-500">{formErrors.student_id}</p>
                )}
                {!isEditMode && studentIdAvailable === false && (
                  <p className="text-sm text-red-500">该学号已存在</p>
                )}
                {!isEditMode && studentIdAvailable === true && (
                  <p className="text-sm text-green-600">学号可用</p>
                )}
              </div>

              {/* Student Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  姓名 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  {...register('name', {
                    required: '请输入学生姓名',
                    minLength: {
                      value: 2,
                      message: '姓名至少需要2个字符'
                    },
                    maxLength: {
                      value: 100,
                      message: '姓名不能超过100个字符'
                    }
                  })}
                  placeholder="请输入学生姓名"
                  disabled={loading}
                  className={errors.name || formErrors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
                {formErrors.name && (
                  <p className="text-sm text-red-500">{formErrors.name}</p>
                )}
              </div>

              {/* Gender */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">性别</Label>
                <Select
                  value={watch('gender') || ''}
                  onValueChange={(value) => setValue('gender', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="请选择性别" />
                  </SelectTrigger>
                  <SelectContent>
                    {GENDER_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Birth Date */}
              <div className="space-y-2">
                <Label htmlFor="birth_date" className="text-sm font-medium">
                  出生日期
                </Label>
                <Input
                  id="birth_date"
                  type="date"
                  {...register('birth_date')}
                  disabled={loading}
                />
              </div>

              {/* ID Number */}
              <div className="space-y-2">
                <Label htmlFor="id_number" className="text-sm font-medium">
                  身份证号
                </Label>
                <Input
                  id="id_number"
                  {...register('id_number', {
                    pattern: {
                      value: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
                      message: '请输入有效的身份证号'
                    }
                  })}
                  placeholder="请输入身份证号码"
                  disabled={loading}
                  className={errors.id_number ? "border-red-500" : ""}
                />
                {errors.id_number && (
                  <p className="text-sm text-red-500">{errors.id_number.message}</p>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium border-b pb-2">联系信息</h3>

              {/* Phone */}
              <div className="space-y-2">
                <Label htmlFor="phone" className="text-sm font-medium flex items-center">
                  <Phone className="w-4 h-4 mr-1" />
                  联系电话
                </Label>
                <Input
                  id="phone"
                  {...register('phone', {
                    pattern: {
                      value: /^1[3-9]\d{9}$/,
                      message: '请输入有效的手机号码'
                    }
                  })}
                  placeholder="请输入手机号码"
                  disabled={loading}
                  className={errors.phone || formErrors.phone ? "border-red-500" : ""}
                />
                {errors.phone && (
                  <p className="text-sm text-red-500">{errors.phone.message}</p>
                )}
                {formErrors.phone && (
                  <p className="text-sm text-red-500">{formErrors.phone}</p>
                )}
              </div>

              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium flex items-center">
                  <Mail className="w-4 h-4 mr-1" />
                  邮箱地址
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...register('email', {
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: '请输入有效的邮箱地址'
                    }
                  })}
                  placeholder="请输入邮箱地址"
                  disabled={loading}
                  className={errors.email ? "border-red-500" : ""}
                />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>

              {/* Address */}
              <div className="space-y-2">
                <Label htmlFor="address" className="text-sm font-medium">
                  家庭住址
                </Label>
                <Textarea
                  id="address"
                  {...register('address')}
                  placeholder="请输入家庭住址"
                  disabled={loading}
                  rows={2}
                />
              </div>

              {/* Guardian Information */}
              <div className="space-y-3">
                <h4 className="text-md font-medium text-gray-700">监护人信息</h4>

                <div className="space-y-2">
                  <Label htmlFor="guardian_name" className="text-sm font-medium">
                    监护人姓名
                  </Label>
                  <Input
                    id="guardian_name"
                    {...register('guardian_name')}
                    placeholder="请输入监护人姓名"
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="guardian_phone" className="text-sm font-medium">
                    监护人电话
                  </Label>
                  <Input
                    id="guardian_phone"
                    {...register('guardian_phone', {
                      pattern: {
                        value: /^1[3-9]\d{9}$/,
                        message: '请输入有效的手机号码'
                      }
                    })}
                    placeholder="请输入监护人电话"
                    disabled={loading}
                    className={errors.guardian_phone ? "border-red-500" : ""}
                  />
                  {errors.guardian_phone && (
                    <p className="text-sm text-red-500">{errors.guardian_phone.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">监护人关系</Label>
                  <Select
                    value={watch('guardian_relation') || ''}
                    onValueChange={(value) => setValue('guardian_relation', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="请选择关系" />
                    </SelectTrigger>
                    <SelectContent>
                      {GUARDIAN_RELATIONS.map(relation => (
                        <SelectItem key={relation.value} value={relation.value}>
                          {relation.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>

          {/* Academic Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="enrollment_date" className="text-sm font-medium">
                入学时间
              </Label>
              <Input
                id="enrollment_date"
                type="date"
                {...register('enrollment_date')}
                disabled={loading}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">学生状态</Label>
              <Select
                value={watch('status') || 'active'}
                onValueChange={(value) => setValue('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {STUDENT_STATUS_OPTIONS.map(status => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">能力等级</Label>
              <Select
                value={watch('profile_level') || ''}
                onValueChange={(value) => setValue('profile_level', value === '' ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择等级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">未设置</SelectItem>
                  {STUDENT_PROFILE_LEVELS.map(level => (
                    <SelectItem key={level.value} value={level.value}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes" className="text-sm font-medium">
              备注信息
            </Label>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="可选，输入学生的其他相关信息..."
              disabled={loading}
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading || (studentIdChecking || (!isEditMode && studentIdAvailable === false))}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditMode ? '更新' : '创建'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default StudentForm;