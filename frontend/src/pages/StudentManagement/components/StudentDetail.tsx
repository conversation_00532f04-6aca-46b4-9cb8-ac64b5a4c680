import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { StudentDetailProps } from '@/types/student';
import {
  Award,
  BookOpen,
  Calendar,
  Loader2,
  Mail,
  MapPin,
  Phone,
  Tag,
  User,
  Users
} from 'lucide-react';
import React from 'react';

const StudentDetail: React.FC<StudentDetailProps> = ({
  student,
  open,
  onClose,
  loading = false,
}) => {
  const formatDate = (dateString?: string) => {
    if (!dateString) return '未设置';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800';
      case 'graduated':
        return 'bg-blue-100 text-blue-800';
      case 'transferred':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    const statusMap = {
      'active': '在校',
      'inactive': '休学',
      'graduated': '毕业',
      'transferred': '转学',
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getProfileLevelColor = (level?: string) => {
    if (!level) return 'bg-gray-100 text-gray-600';

    const levelColors = {
      'A+': 'bg-red-100 text-red-800',
      'A': 'bg-red-100 text-red-700',
      'B+': 'bg-orange-100 text-orange-800',
      'B': 'bg-orange-100 text-orange-700',
      'C+': 'bg-yellow-100 text-yellow-800',
      'C': 'bg-yellow-100 text-yellow-700',
      'D+': 'bg-green-100 text-green-800',
      'D': 'bg-green-100 text-green-700',
    };
    return levelColors[level as keyof typeof levelColors] || 'bg-gray-100 text-gray-600';
  };

  const getGenderLabel = (gender?: string) => {
    return gender === 'male' ? '男' : gender === 'female' ? '女' : '未设置';
  };

  const getRelationLabel = (relation?: string) => {
    const relationMap = {
      'father': '父亲',
      'mother': '母亲',
      'grandfather': '祖父',
      'grandmother': '祖母',
      'uncle': '叔叔',
      'aunt': '阿姨',
      'other': '其他',
    };
    return relationMap[relation as keyof typeof relationMap] || relation || '未设置';
  };

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!student) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[800px]">
          <div className="text-center py-8">
            <div className="text-gray-500">学生信息加载失败</div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <User className="w-5 h-5 mr-2" />
            学生详情 - {student.student.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <User className="w-5 h-5 mr-2" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">学号：</span>
                    <Badge variant="outline">{student.student.student_number}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">姓名：</span>
                    <span className="font-medium">{student.student.name}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">性别：</span>
                    <Badge variant="secondary">{getGenderLabel(student.student.gender)}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">出生日期：</span>
                    <span>{formatDate(student.student.birth_date)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">身份证号：</span>
                    <span className="font-mono text-sm">{student.student.id_number || '未设置'}</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">入学时间：</span>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1 text-gray-400" />
                      <span>{formatDate(student.student.enrollment_date)}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">学生状态：</span>
                    <Badge className={cn("text-xs", getStatusColor(student.student.status))}>
                      {getStatusLabel(student.student.status)}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">能力等级：</span>
                    {student.student.profile_level ? (
                      <Badge className={cn("text-xs", getProfileLevelColor(student.student.profile_level))}>
                        {student.student.profile_level}
                      </Badge>
                    ) : (
                      <span className="text-gray-400">未评级</span>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">班级：</span>
                    <span>{student.class_name || '未分配'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">年级：</span>
                    <span>{student.grade_level_name || '未设置'}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <Phone className="w-5 h-5 mr-2" />
                联系信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center">
                      <Phone className="w-4 h-4 mr-1" />
                      联系电话：
                    </span>
                    <span>{student.student.phone || '未设置'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center">
                      <Mail className="w-4 h-4 mr-1" />
                      邮箱地址：
                    </span>
                    <span className="text-sm">{student.student.email || '未设置'}</span>
                  </div>
                  <div className="flex items-start justify-between">
                    <span className="text-gray-600 flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      家庭住址：
                    </span>
                    <span className="text-sm text-right max-w-[250px]">
                      {student.student.address || '未设置'}
                    </span>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-gray-700 border-b pb-1">监护人信息</h4>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">监护人姓名：</span>
                    <span>{student.student.guardian_name || '未设置'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">监护人电话：</span>
                    <span>{student.student.guardian_phone || '未设置'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">监护人关系：</span>
                    <Badge variant="outline">
                      {getRelationLabel(student.student.guardian_relation)}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Teaching Classes */}
          {student.teaching_classes && student.teaching_classes.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <BookOpen className="w-5 h-5 mr-2" />
                  教学班级
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-2">
                  {student.teaching_classes.map((teachingClass) => (
                    <div key={teachingClass.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-2 text-gray-400" />
                        <span>班级ID: {teachingClass.class_id}</span>
                      </div>
                      <Badge variant="outline">{teachingClass.subject}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Profile Levels */}
          {student.profile_levels && student.profile_levels.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <Award className="w-5 h-5 mr-2" />
                  能力等级记录
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {student.profile_levels.map((level) => (
                    <div key={level.id} className="flex items-center justify-between p-3 border rounded">
                      <div className="space-y-1">
                        <div className="flex items-center">
                          <span className="font-medium">{level.subject}</span>
                          <Badge className={cn("ml-2 text-xs", getProfileLevelColor(level.level))}>
                            {level.level}
                          </Badge>
                        </div>
                        {level.level_description && (
                          <p className="text-sm text-gray-600">{level.level_description}</p>
                        )}
                      </div>
                      <div className="text-right text-sm text-gray-500">
                        <div>评估日期: {formatDate(level.assessment_date)}</div>
                        {level.assessed_by && (
                          <div>评估人: {level.assessed_by}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Profile Tags */}
          {student.profile_tags && student.profile_tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <Tag className="w-5 h-5 mr-2" />
                  学生标签
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {student.profile_tags.map((tag) => (
                    <div key={tag.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">{tag.tag_category}</Badge>
                        <span className="font-medium">{tag.tag_name}</span>
                        {tag.tag_value && (
                          <span className="text-gray-600">: {tag.tag_value}</span>
                        )}
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatDate(tag.created_at)}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {student.student.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">备注信息</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 whitespace-pre-wrap">
                  {student.student.notes}
                </p>
              </CardContent>
            </Card>
          )}

          {/* System Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">系统信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">创建时间：</span>
                  <span>{formatDate(student.student.created_at)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">更新时间：</span>
                  <span>{formatDate(student.student.updated_at)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end pt-4">
          <Button onClick={onClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default StudentDetail;