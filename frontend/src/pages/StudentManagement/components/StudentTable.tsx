import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { StudentTableProps } from '@/types/student';
import {
  Edit,
  Eye,
  MoreHorizontal,
  Trash2
} from 'lucide-react';
import React from 'react';

const StudentTable: React.FC<StudentTableProps> = ({
  students,
  loading,
  onEdit,
  onDelete,
  onViewDetail,
  pagination,
}) => {
  // const formatDate = (dateString?: string) => {
  //   if (!dateString) return '-';
  //   return new Date(dateString).toLocaleDateString('zh-CN');
  // };

  // const getStatusColor = (status: string) => {
  //   switch (status) {
  //     case 'active':
  //       return 'bg-green-100 text-green-800 hover:bg-green-200';
  //     case 'inactive':
  //       return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
  //     case 'graduated':
  //       return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
  //     case 'transferred':
  //       return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
  //     default:
  //       return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
  //   }
  // };

  // const getStatusLabel = (status: string) => {
  //   const statusMap = {
  //     'active': '在校',
  //     'inactive': '休学',
  //     'graduated': '毕业',
  //     'transferred': '转学',
  //   };
  //   return statusMap[status as keyof typeof statusMap] || status;
  // };

  // const getProfileLevelColor = (level?: string) => {
  //   if (!level) return 'bg-gray-100 text-gray-600';

  //   const levelColors = {
  //     'A+': 'bg-red-100 text-red-800',
  //     'A': 'bg-red-100 text-red-700',
  //     'B+': 'bg-orange-100 text-orange-800',
  //     'B': 'bg-orange-100 text-orange-700',
  //     'C+': 'bg-yellow-100 text-yellow-800',
  //     'C': 'bg-yellow-100 text-yellow-700',
  //     'D+': 'bg-green-100 text-green-800',
  //     'D': 'bg-green-100 text-green-700',
  //   };
  //   return levelColors[level as keyof typeof levelColors] || 'bg-gray-100 text-gray-600';
  // };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>学号</TableHead>
                <TableHead>姓名</TableHead>
                <TableHead>性别</TableHead>
                <TableHead>联系方式</TableHead>
                <TableHead>班级</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>能力等级</TableHead>
                <TableHead>入学时间</TableHead>
                <TableHead className="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  {Array.from({ length: 9 }).map((_, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <div className="h-4 bg-gray-200 rounded animate-pulse" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (students.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-500 text-lg mb-2">暂无学生数据</div>
        <div className="text-gray-400 text-sm">点击&#34;新增学生&#34;创建第一个学生档案</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[120px]">学号</TableHead>
              <TableHead className="w-[100px]">姓名</TableHead>
              <TableHead className="w-[60px] text-center">性别</TableHead>
              <TableHead className="w-[60px] text-center">生日</TableHead>
              <TableHead className="w-[120px]">手机</TableHead>
              <TableHead className="w-[120px]">邮箱</TableHead>
              <TableHead className="w-[120px]">地址</TableHead>
              <TableHead className="w-[100px] text-center">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {students.map((student) => (
              <TableRow key={student.id} className="hover:bg-gray-50">
                <TableCell className="font-mono">
                  <Badge variant="outline" className="text-xs">
                    {student.student_number}
                  </Badge>
                </TableCell>

                <TableCell className="font-medium">
                  <div className="flex items-center">
                    {student.name}
                  </div>
                </TableCell>

                <TableCell className="text-center">
                  <Badge variant="secondary" className="text-xs">
                    {student.gender === 'male' ? '男' : student.gender === 'female' ? '女' : '-'}
                  </Badge>
                </TableCell>

                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center text-sm text-gray-600">
                      <Badge variant="secondary" className="text-xs">
                        {student.birth_date ? student.birth_date : '-'}
                      </Badge>
                    </div>
                  </div>
                </TableCell>


                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center text-sm text-gray-600">
                      <Badge variant="secondary" className="text-xs">
                        {student.phone ? student.phone : '-'}
                      </Badge>
                      </div>
                  </div>
                </TableCell>

                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center text-sm text-gray-600">
                      <Badge variant="secondary" className="text-xs">
                        {student.email? student.email: '-'}
                      </Badge>
                    </div>
                  </div>
                </TableCell>

                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center text-sm text-gray-600">
                      <Badge variant="secondary" className="text-xs">
                        {student.address? student.address: '-'}
                      </Badge>
                    </div>
                  </div>
                </TableCell>

                {/*<TableCell>*/}
                {/*  <div className="text-sm">*/}
                {/*    {student.administrative_class_id ? (*/}
                {/*      <Badge variant="outline" className="text-xs">*/}
                {/*        待关联班级名称*/}
                {/*      </Badge>*/}
                {/*    ) : (*/}
                {/*      <span className="text-gray-400">未分配</span>*/}
                {/*    )}*/}
                {/*  </div>*/}
                {/*</TableCell>*/}

                {/*<TableCell className="text-center">*/}
                {/*  <Badge*/}
                {/*    variant="secondary"*/}
                {/*    className={cn("text-xs", getStatusColor(student.status))}*/}
                {/*  >*/}
                {/*    {getStatusLabel(student.status)}*/}
                {/*  </Badge>*/}
                {/*</TableCell>*/}

                {/*<TableCell className="text-center">*/}
                {/*  {student.profile_level ? (*/}
                {/*    <Badge*/}
                {/*      variant="secondary"*/}
                {/*      className={cn("text-xs", getProfileLevelColor(student.profile_level))}*/}
                {/*    >*/}
                {/*      {student.profile_level}*/}
                {/*    </Badge>*/}
                {/*  ) : (*/}
                {/*    <span className="text-gray-400 text-xs">未评级</span>*/}
                {/*  )}*/}
                {/*</TableCell>*/}

                {/*<TableCell className="text-sm text-gray-500">*/}
                {/*  <div className="flex items-center">*/}
                {/*    <Calendar className="w-3 h-3 mr-1" />*/}
                {/*    {formatDate(student.enrollment_date)}*/}
                {/*  </div>*/}
                {/*</TableCell>*/}

                <TableCell>
                  <div className="flex items-center justify-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem
                          onClick={() => onViewDetail(student)}
                          className="cursor-pointer"
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>

                        <DropdownMenuItem
                          onClick={() => onEdit(student)}
                          className="cursor-pointer"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>

                        <DropdownMenuSeparator />

                        <DropdownMenuItem
                          onClick={() => onDelete(student.id)}
                          className="cursor-pointer text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && pagination.total > 0 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>
              共 {pagination.total} 条记录
            </span>
            <div className="flex items-center space-x-2">
              <span>每页</span>
              <select
                className="border rounded px-2 py-1 text-sm"
                value={pagination.pageSize}
                onChange={e => pagination.onChange(1, Number(e.target.value))}
              >
                {[10, 20, 50, 100].map(size => (
                  <option key={size} value={size}>{size} 条/页</option>
                ))}
              </select>
              <span>跳转到</span>
              <input
                type="number"
                min={1}
                max={Math.ceil(pagination.total / pagination.pageSize)}
                defaultValue={pagination.current}
                onBlur={e => {
                  let page = Number(e.target.value);
                  if (page < 1) page = 1;
                  if (page > Math.ceil(pagination.total / pagination.pageSize)) {
                    page = Math.ceil(pagination.total / pagination.pageSize);
                  }
                  if (page !== pagination.current) {
                    pagination.onChange(page, pagination.pageSize);
                  }
                }}
                className="border rounded px-2 py-1 w-16 text-sm text-center"
              />
              <span>页</span>
            </div>
          </div>
          
          <div className="flex items-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => {
                      if (pagination.current > 1) {
                        pagination.onChange(pagination.current - 1, pagination.pageSize);
                      }
                    }}
                    className={pagination.current === 1 ? 'pointer-events-none opacity-50' : ''}
                  />
                </PaginationItem>
                {Array.from({ length: Math.ceil(pagination.total / pagination.pageSize) }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      isActive={page === pagination.current}
                      onClick={() => pagination.onChange(page, pagination.pageSize)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                <PaginationItem>
                  <PaginationNext
                    onClick={() => {
                      if (pagination.current < Math.ceil(pagination.total / pagination.pageSize)) {
                        pagination.onChange(pagination.current + 1, pagination.pageSize);
                      }
                    }}
                    className={
                      pagination.current === Math.ceil(pagination.total / pagination.pageSize)
                        ? 'pointer-events-none opacity-50'
                        : ''
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentTable;