import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle, DialogDescription, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { ClassType, Classes } from '@/types/classess';

interface CreateClassDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    subjects: any[];
    onCreate: (params: Classes, subjectCodes: string[]) => Promise<void>;
}

const CreateClassDialog: React.FC<CreateClassDialogProps> = ({ open, onOpenChange, subjects, onCreate }) => {
    // 本地表单状态
    const [classForm, setClassForm] = useState<Classes>({
        id: '',
        name: '',
        code: '',
        grade_level_code: '',
        subject_code: '',
        class_type: ClassType.TeachingClass,
        school_year: new Date().getFullYear(),
    });
    const [subjectCodes, setSubjectCodes] = useState<string[]>([]);
    const [error, setError] = useState<string | null>(null);

    const handleSubmit = async () => {
        if (!classForm.name || !classForm.code || !classForm.grade_level_code) {
            setError('请填写所有必填项');
            return;
        }
        try {
            await onCreate(classForm, subjectCodes);
            setClassForm({ ...classForm, name: '', code: '', grade_level_code: '', subject_code: '', class_type: ClassType.TeachingClass, school_year: new Date().getFullYear() });
            setSubjectCodes([]);
            setError(null);
            onOpenChange(false);
        } catch {
            setError('创建班级失败');
        }
    };

    // ...表单渲染和学科多选逻辑同原来

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogTrigger asChild>
                <Button>新建班级</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>新建班级</DialogTitle>
                    <DialogDescription>请填写班级的基本信息</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="name">班级名称 *</Label>
                        <Input id="name" value={String(classForm.name)} onChange={e => setClassForm({ ...classForm, name: e.target.value })} placeholder="请输入班级名称" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="code">班级编号 *</Label>
                        <Input id="code" value={String(classForm.code)} onChange={e => setClassForm({ ...classForm, code: e.target.value })} placeholder="请输入班级编号" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="grade_level_code">年级 *</Label>
                        <Select value={String(classForm.grade_level_code)} onValueChange={value => setClassForm({ ...classForm, grade_level_code: value })}>
                            <SelectTrigger>
                                <SelectValue placeholder="选择年级" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="CHN">语文</SelectItem>
                                <SelectItem value="MATH">数学</SelectItem>
                                <SelectItem value="ENG">英语</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="class_type">班级类型</Label>
                        <Select value={classForm.class_type} onValueChange={value => setClassForm({ ...classForm, class_type: value as ClassType })}>
                            <SelectTrigger>
                                <SelectValue placeholder="选择班级类型" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value={ClassType.TeachingClass}>教学班</SelectItem>
                                <SelectItem value={ClassType.AdministrativeClass}>行政班</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    {classForm.class_type === ClassType.TeachingClass && (
                        <div className="space-y-2">
                            <Label htmlFor="subject_code">学科</Label>
                            <div className="flex flex-wrap gap-2">
                                {subjects.map(subject => (
                                    <label key={subject.id} className="flex items-center gap-1">
                                        <input
                                            type="checkbox"
                                            checked={subjectCodes.includes(subject.name)}
                                            onChange={e => {
                                                if (e.target.checked) {
                                                    setSubjectCodes([...subjectCodes, subject.name]);
                                                } else {
                                                    setSubjectCodes(subjectCodes.filter(s => s !== subject.name));
                                                }
                                            }}
                                        />
                                        {subject.name}
                                    </label>
                                ))}
                            </div>
                            <div>
                                已选学科：{subjectCodes.join(',')}
                            </div>
                        </div>
                    )}
                    <div className="space-y-2">
                        <Label htmlFor="school_year">学年</Label>
                        <Input id="school_year" type="number" value={String(classForm.school_year)} onChange={e => setClassForm({ ...classForm, school_year: Number(e.target.value) })} placeholder="请输入学年" />
                    </div>
                </div>
                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => onOpenChange(false)}>取消</Button>
                    <Button onClick={handleSubmit}>创建班级</Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default CreateClassDialog;