import React from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Layers, Users, BookOpen } from 'lucide-react';

interface ClassStatisticsCardsProps {
  totalClasses: number;
  totalTeachers: number;
  totalStudents: number;
}

const ClassStatisticsCards: React.FC<ClassStatisticsCardsProps> = ({
  totalClasses,
  totalTeachers,
  totalStudents,
}) => (
  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">总班级数</CardTitle>
        <Layers className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{totalClasses}</div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">总老师数</CardTitle>
        <Users className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{totalTeachers}</div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">总学生数</CardTitle>
        <BookOpen className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{totalStudents}</div>
      </CardContent>
    </Card>
  </div>
);

export default ClassStatisticsCards;