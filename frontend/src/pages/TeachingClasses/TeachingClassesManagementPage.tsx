import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Plus, Edit, Trash2, Users, BookOpen, Layers, RefreshCw, Upload, ListCollapse } from 'lucide-react';
import { ClassesApi, CreateClassParams, UpdateClassParams } from '@/services/classesApi';
import { ClassesDetail, ClassesStatistics, ClassType } from '@/types/classess';

import subjectApi from '@/services/subjectApi';
import { ScrollArea } from '@/components/ui/scroll-area';
import CreateClassDialog from './components/CreateClassDialog';
import ClassStatisticsCards from './components/ClassStatisticsCards';



const TeachingClassesManagementPage: React.FC = () => {
  // State management
  const [classes, setClasses] = useState<ClassesDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Statistics
  const [totalClasses, setTotalClasses] = useState(0);
  const [totalTeachers, setTotalTeachers] = useState(0);
  const [totalStudents, setTotalStudents] = useState(0);
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedClass, setSelectedClass] = useState<ClassesDetail | null>(null);

  // Form state
  const [subjects, setSubjects] = useState<any[]>([]);
  const [gradeLevels, setGradeLevels] = useState<any[]>([]);
  const [classForm, setClassForm] = useState<CreateClassParams>({
    name: '',
    code: '',
    grade_level_code: '',
    subject_code: '', // 仍为 string
    class_type: ClassType.TeachingClass,
    school_year: new Date().getFullYear(),
  });
  // 新增本地 state 维护多选学科
  const [subjectCodes, setSubjectCodes] = useState<string[]>([]);

  // Get tenant ID from auth context (mock for now)
  const tenantId = 'tenant_zhanghan';

  useEffect(() => {
    loadInitialData();
    //ClassesApi.deleteClasses(tenantId, { id: '12' })
    ClassesApi.addHeadTeacher(tenantId, {
      classed_id: tenantId,
      teacher_id: tenantId,
      end_time: new Date().toISOString()
    })
    ClassesApi.removeHeadTeacher(tenantId, {
      classed_id: tenantId,
      teacher_id: tenantId,
    })
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      // 1. 获取行政班列表
      const res1 = await ClassesApi.pageAdministrativeClasses(tenantId, {
        pagination: {
          page: 1,
          page_size: 100,
        }
      });
      //2 .获取教学班列表
      const res2 = await ClassesApi.pageTeachingClasses(tenantId, {
        pagination: {
          page: 1,
          page_size: 100,
        }
      });
      // 合并数据并设置状态
      const combinedClasses = [
        ...(Array.isArray(res1.data) ? res1.data : []),
        ...(Array.isArray(res2.data) ? res2.data : []),
      ];
      setClasses(combinedClasses);
      //科目列表
      const subjectsData = await subjectApi.pageSubject({
        page: 1,
        page_size: 1000,
        order_direction: 'asc',
      });
      // 2. 获取统计数据
      const stats = (await ClassesApi.getStatistics(tenantId));
      setTotalClasses(stats.data?.total_classes || 0);
      setTotalTeachers(stats.data?.total_teacher || 0);
      setTotalStudents(stats.data?.total_students || 0);
      setSubjects(Array.isArray(subjectsData.data) ? subjectsData.data : []);
      setError(null);
    } catch (err) {
      setError('加载数据失败');
      setClasses([]);
    } finally {
      setLoading(false);
    }
  };

  // 编辑班级
  const handleUpdateClass = async () => {
    if (!selectedClass) return;
    try {
      await ClassesApi.updateClasses(tenantId, {
        id: selectedClass.id,
        ...classForm,
        subject_code: subjectCodes.join(','), // 多选用逗号拼接
      } as UpdateClassParams);
      setIsEditDialogOpen(false);
      setSelectedClass(null);
      resetForm();
      setSubjectCodes([]);
      loadInitialData();
    } catch (err) {
      setError('编辑班级失败');
    }
  };

  // 删除班级
  const handleDeleteClass = async () => {
    if (!selectedClass) return;
    try {
      await fakeDeleteClass(String(selectedClass.id));
      setIsDeleteDialogOpen(false);
      setSelectedClass(null);
      loadInitialData();
    } catch (err) {
      setError('删除班级失败');
    }
  };

  //  删除班级
  const fakeDeleteClass = async (id: string) => {
    await ClassesApi.deleteClasses(tenantId, { id: id });
    return Promise.resolve();
  };

  // 批量导入班级（需补全接口）
  const handleBatchImport = async () => {
    // TODO: 实现批量导入功能
    alert('批量导入功能待实现');
  };

  const resetForm = () => {
    setClassForm({
      name: '',
      code: '',
      grade_level_code: '',
      subject_code: '', // 仍为 string
      class_type: ClassType.AdministrativeClass,
      school_year: new Date().getFullYear(),
    });
    setSubjectCodes([]);
  };

  const openViewDialog = (cls: ClassesDetail) => {
    // TODO: 查看班级详细信息
    setIsViewDialogOpen(true);
  };

  const openEditDialog = (cls: ClassesDetail) => {
    setSelectedClass(cls);
    setClassForm({
      name: String(cls.name),
      code: String(cls.code),
      grade_level_code: String(cls.grade_level_code),
      subject_code: cls.subject_code ? String(cls.subject_code) : '',
      class_type: cls.class_type,
      school_year: Number(cls.school_year),
    });
    setSubjectCodes(cls.subject_code ? String(cls.subject_code).split(',') : []);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (cls: ClassesDetail) => {
    setSelectedClass(cls);
    setIsDeleteDialogOpen(true);
  };


  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }
  const getOperationButtons = (cls: ClassesDetail) => {
    return (
      <div className="flex gap-1">
        <Button variant="ghost" size="sm" onClick={() => openViewDialog(cls)}>
          <ListCollapse className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => openEditDialog(cls)}>
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(cls)}>
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    );
  }
  //------------------------------------------------------------------组件部分
  function RefreshButton() {
    return <Button variant="outline" onClick={() => {
      loadInitialData()
    }}>
      <RefreshCw className="h-4 w-4 mr-2" />
      刷新
    </Button>
  }

  return (
    <div className="space-y-6">
      {error && (
        <AlertDialog>
          <AlertDialogDescription>{error}</AlertDialogDescription>
        </AlertDialog>
      )}
      {/* 顶部按钮栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">班级管理</h1>
          <p className="text-muted-foreground">创建、管理和监控班级</p>
        </div>
        <div className="flex gap-2">
          <RefreshButton />
          {/* 创建班级对话框 */}
          <CreateClassDialog
            open={isCreateDialogOpen}
            onOpenChange={setIsCreateDialogOpen}
            subjects={subjects}
            onCreate={async (form, subjectCodes) => {
              await ClassesApi.createClasses(tenantId, {
                ...form,
                subject_code: subjectCodes.join(','),
              });
              loadInitialData();
            }}
          />
          <Button variant="outline" onClick={handleBatchImport}>
            <Upload className="h-4 w-4 mr-2" />
            批量导入
          </Button>
        </div>
      </div>
      {/* 统计卡片 */}
      <ClassStatisticsCards
        totalClasses={totalClasses}
        totalTeachers={totalTeachers}
        totalStudents={totalStudents}
      />
      {/* 班级列表 */}
      <Card>
        <CardHeader>
          <CardTitle>行政班列表</CardTitle>
          <CardDescription>当前共有 {classes.filter(cls => cls.class_type === ClassType.AdministrativeClass).length} 个行政班</CardDescription>
        </CardHeader>
        <CardContent>
          <Table className="w-full table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead>班级名称</TableHead>
                <TableHead>班级编号</TableHead>
                <TableHead>年级</TableHead>
                {/* <TableHead>班级类型</TableHead> */}
                <TableHead>班主任</TableHead>
                <TableHead>班级人数</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
          </Table>
          <ScrollArea className="h-[300px] border-none mt-4">
            <Table className="w-full table-fixed ">
              <TableBody>
                {classes.filter(cls => cls.class_type === ClassType.AdministrativeClass).map(cls => (
                  <TableRow key={cls.id as string}>
                    <TableCell className="font-medium">{cls.name}</TableCell>
                    <TableCell>{cls.code}</TableCell>
                    <TableCell>{cls.grade_level_name || cls.grade_level_code}</TableCell>
                    {/* <TableCell>{getClassTypeBadge(cls.class_type)}</TableCell> */}
                    <TableCell>{cls.teacher_name || '-'}</TableCell>
                    <TableCell>{cls.student_count || '-'}</TableCell>
                    <TableCell>{getOperationButtons(cls)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>教学班列表</CardTitle>
          <CardDescription>当前共有 {classes.filter(cls => cls.class_type === ClassType.TeachingClass).length} 个教学班</CardDescription>
        </CardHeader>
        <CardContent>
          <Table className="w-full table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead>班级名称</TableHead>
                <TableHead>班级编号</TableHead>
                <TableHead>年级</TableHead>
                {/* <TableHead>班级类型</TableHead> */}
                <TableHead>任课老师</TableHead>
                <TableHead>学科组</TableHead>
                <TableHead>班级人数</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
          </Table>
          <ScrollArea className="h-[300px] border-none mt-4">
            <Table className="w-full table-fixed ">
              <TableBody>
                {classes.filter(cls => cls.class_type === ClassType.TeachingClass).map(cls => (
                  <TableRow key={cls.id as string}>
                    <TableCell className="font-medium">{cls.name}</TableCell>
                    <TableCell>{cls.code}</TableCell>
                    <TableCell>{cls.grade_level_name || cls.grade_level_code}</TableCell>
                    {/* <TableCell>{getClassTypeBadge(cls.class_type)}</TableCell> */}
                    <TableCell>{cls.teacher_name || '-'}</TableCell>
                    <TableCell>{cls.subject_name || cls.subject_code || '-'}</TableCell>
                    <TableCell>{cls.student_count || '-'}</TableCell>
                    <TableCell>{getOperationButtons(cls)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
      {/* TODO查看学生列表对话框*/}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>展示学生列表</DialogTitle>
            <DialogDescription>查看班级学生列表</DialogDescription>
          </DialogHeader>
          <div className="my-6 w-full overflow-y-auto">
            <table className="w-full">
              <tbody>
                <tr className="even:bg-muted m-0 border-t p-0">
                  <td className="border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right">
                    Empty
                  </td>
                  <td className="border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right">
                    Overflowing
                  </td>
                  <td className="border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right">
                    Overflowing
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </DialogContent>
      </Dialog>
      {/* 编辑班级对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>编辑班级</DialogTitle>
            <DialogDescription>修改班级信息</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit_name">班级名称</Label>
              <Input id="edit_name" value={String(classForm.name)} onChange={e => setClassForm({ ...classForm, name: e.target.value })} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_code">班级编号</Label>
              <Input id="edit_code" value={String(classForm.code)} onChange={e => setClassForm({ ...classForm, code: e.target.value })} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_grade_level_code">年级</Label>
              <Select value={classForm.grade_level_code ? String(classForm.grade_level_code) : undefined} onValueChange={value => setClassForm({ ...classForm, grade_level_code: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="选择年级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CHN">语文</SelectItem>
                  <SelectItem value="MATH">数学</SelectItem>
                  <SelectItem value="ENG">英语</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit_class_type">班级类型</Label>
              <Select value={classForm.class_type} onValueChange={value => setClassForm({ ...classForm, class_type: value as ClassType })}>
                <SelectTrigger>
                  <SelectValue placeholder="选择班级类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ClassType.TeachingClass}>教学班</SelectItem>
                  <SelectItem value={ClassType.AdministrativeClass}>行政班</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {classForm.class_type === ClassType.TeachingClass && (
              <div className="space-y-2">
                <Label htmlFor="edit_subject_code">学科</Label>
                <div className="flex flex-wrap gap-2">
                  {subjects.map(subject => (
                    <label key={subject.id} className="flex items-center gap-1">
                      <input
                        type="checkbox"
                        checked={subjectCodes.includes(subject.name)}
                        onChange={e => {
                          if (e.target.checked) {
                            setSubjectCodes([...subjectCodes, subject.name]);
                          } else {
                            setSubjectCodes(subjectCodes.filter(s => s !== subject.name));
                          }
                        }}
                      />
                      {subject.name}
                    </label>
                  ))}
                </div>
                <div>
                  已选学科：{subjectCodes.join(',')}
                </div>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="edit_school_year">学年</Label>
              <Input id="edit_school_year" type="number" value={String(classForm.school_year)} onChange={e => setClassForm({ ...classForm, school_year: Number(e.target.value) })} />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
            <Button onClick={handleUpdateClass}>保存</Button>
          </div>
        </DialogContent>
      </Dialog>
      {/* 删除班级对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>你确定要删除班级 {selectedClass?.name} 吗？此操作无法撤销。</DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>取消</Button>
            <Button variant="destructive" onClick={handleDeleteClass}>删除</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TeachingClassesManagementPage;