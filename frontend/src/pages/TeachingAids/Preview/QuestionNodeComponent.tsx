import React from 'react';
import { Node } from './tiptap-types';

interface QuestionNodeProps {
  node: Node;
  children: React.ReactNode;
}

const QuestionNodeComponent: React.FC<QuestionNodeProps> = ({ node, children }) => {
  // The node.attrs are now encapsulated here, ready for future interactions
  // like an "Add to Cart" button.
  // For example: const handleAddToCart = () => { console.log('Adding question:', node.attrs); };

  return (
    <div className="border rounded-lg p-4 my-4 bg-slate-50 relative group">
      {/* Future "Add to Cart" button can be positioned here */}
      {/* 
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button size="sm" onClick={handleAddToCart}>Add to Cart</Button>
      </div>
      */}
      {children}
    </div>
  );
};

export default QuestionNodeComponent;
