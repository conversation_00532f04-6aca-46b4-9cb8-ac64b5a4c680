import { TeachingAidChapter2 } from '@/services/teachingAidsApi';

// Define the recursive type for the tree structure
export interface ChapterWithChildren extends TeachingAidChapter2 {
  children: ChapterWithChildren[];
}

/**
 * Sorts a chapter and its descendants recursively by the 'sequence' property.
 * @param nodes - The array of chapter nodes to sort.
 */
function sortChapterTree(nodes: ChapterWithChildren[]): void {
  // Sort the current level
  nodes.sort((a, b) => a.chapter_number - b.chapter_number);

  // Recursively sort children
  nodes.forEach(node => {
    if (node.children && node.children.length > 0) {
      sortChapterTree(node.children);
    }
  });
}

/**
 * Builds a nested and sorted chapter tree from a flat list of chapters.
 * @param chapters - A flat array of chapter objects.
 * @returns A nested array of root-level chapter objects, sorted by sequence.
 */
export function buildChapterTree(chapters: TeachingAidChapter2[]): ChapterWithChildren[] {
  const nodeMap = new Map<string, ChapterWithChildren>();
  const tree: ChapterWithChildren[] = [];

  // 1. First pass: create a map of all nodes and initialize children array
  chapters.forEach(chapter => {
    nodeMap.set(chapter.id, { ...chapter, children: [] });
  });

  // 2. Second pass: build the tree by linking children to their parents
  chapters.forEach(chapter => {
    const node = nodeMap.get(chapter.id);
    if (!node) return; // Should not happen

    if (chapter.parent_id) {
      const parent = nodeMap.get(chapter.parent_id);
      if (parent) {
        parent.children.push(node);
      } else {
        // If parent is not in the map, treat it as a root node
        tree.push(node);
      }
    } else {
      // No parent_id means it's a root node
      tree.push(node);
    }
  });

  // 3. Final pass: sort the entire tree recursively
  sortChapterTree(tree);

  return tree;
}
