import { AlertDialog, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { chapterApi, TeachingAid, teachingAidApi, TeachingAidChapter2 } from '@/services/teachingAidsApi';
import { AlertCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import TeachingAidViewer from './TeachingAidTextbookPreviewPage';

const TeachingAidTextbookPreviewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [teachingAid, setTeachingAid] = useState<TeachingAid | null>(null);
  const [chapters, setChapters] = useState<TeachingAidChapter2[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setError('Teaching aid ID is missing.');
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        const [aidData, chaptersData] = await Promise.all([
          teachingAidApi.getTeachingAid(id),
          chapterApi.getChapters(id),
        ]);
        setTeachingAid(aidData);
        setChapters(chaptersData);
        setError(null);
      } catch (err) {
        setError('Failed to load teaching aid data.');
        console.error('Error fetching data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  if (loading) {
    return (
      <div className="p-6 space-y-4">
        <Skeleton className="h-8 w-1/2" />
        <Skeleton className="h-4 w-1/3" />
        <div className="flex gap-4 h-[70vh]">
          <Skeleton className="w-1/4 rounded-lg" />
          <Skeleton className="w-3/4 rounded-lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <AlertDialog defaultOpen>
          <div className='flex items-center gap-2'>
            <AlertCircle className="h-4 w-4" />
            <AlertDialogDescription>{error}</AlertDialogDescription>
          </div>
        </AlertDialog>
      </div>
    );
  }

  if (!teachingAid) {
    return null; // Or a 'not found' component
  }

  return (
    <div className="p-1">
      <TeachingAidViewer teachingAid={teachingAid} chapters={chapters} />
    </div>
  );
};

export default TeachingAidTextbookPreviewPage;
