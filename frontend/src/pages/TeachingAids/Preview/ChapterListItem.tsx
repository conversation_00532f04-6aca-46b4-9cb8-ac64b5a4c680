import React from 'react';
import { TeachingAidChapter2 } from '@/services/teachingAidsApi';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Button } from '@/components/ui/button';
import { ChevronRight, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ChapterWithChildren } from './treeUtils';

interface ChapterListItemProps {
  chapter: ChapterWithChildren;
  selectedChapter: TeachingAidChapter2 | null;
  onSelectChapter: (chapter: TeachingAidChapter2) => void;
  level: number;
}

const ChapterListItem: React.FC<ChapterListItemProps> = ({ chapter, selectedChapter, onSelectChapter, level }) => {
  const hasChildren = chapter.children && chapter.children.length > 0;
  const [isOpen, setIsOpen] = React.useState(level === 0); // Automatically open the first level

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className="w-full">
      <div className={cn("flex items-center space-x-1", `pl-${level * 2}`)}>
        {hasChildren && (
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
        )}
        <Button
          variant={selectedChapter?.id === chapter.id ? 'secondary' : 'ghost'}
          className={cn(
            'w-full justify-start text-left h-auto py-2 px-3',
            selectedChapter?.id === chapter.id && 'font-bold',
            !hasChildren && 'ml-9' // Add margin if no children to align with parent
          )}
          onClick={() => onSelectChapter(chapter)}
        >
          <div>
            <div className='text-base'>{chapter.title}</div>
            {chapter.description && <div className='text-xs text-muted-foreground font-normal'>{chapter.description}</div>}
          </div>
        </Button>
      </div>
      {hasChildren && (
        <CollapsibleContent className="py-1">
          {chapter.children?.map((child) => (
            <ChapterListItem
              key={child.id}
              chapter={child}
              selectedChapter={selectedChapter}
              onSelectChapter={onSelectChapter}
              level={level + 1}
            />
          ))}
        </CollapsibleContent>
      )}
    </Collapsible>
  );
};

export default ChapterListItem;
