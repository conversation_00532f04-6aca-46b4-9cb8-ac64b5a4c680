import React, { useMemo, useState } from 'react';
import { TeachingAid, TeachingAidChapter2 } from '@/services/teachingAidsApi';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { ScrollArea } from '@/components/ui/scroll-area';
import TiptapContentRenderer from './TiptapContentRenderer';
import ChapterListItem from './ChapterListItem';
import { buildChapterTree } from './treeUtils';
import { useIsMobile } from '@/hooks/use-mobile';
import { Drawer, DrawerContent, DrawerTrigger } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Menu } from 'lucide-react';

interface TeachingAidViewerProps {
  teachingAid: TeachingAid;
  chapters: TeachingAidChapter2[];
}

const TeachingAidViewer: React.FC<TeachingAidViewerProps> = ({ teachingAid, chapters }) => {
  const isMobile = useIsMobile();
  const chapterTree = useMemo(() => buildChapterTree(chapters), [chapters]);
  const [selectedChapter, setSelectedChapter] = useState<TeachingAidChapter2 | null>(chapterTree[0] || null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'complete' | 'questionOnly'>('complete');

  const handleSelectChapter = (chapter: TeachingAidChapter2) => {
    setSelectedChapter(chapter);
    if (isMobile) {
      setIsDrawerOpen(false); // Close drawer on selection in mobile view
    }
  };

  const contentToRender = useMemo(() => {
    if (!selectedChapter || !selectedChapter.content) {
      return null;
    }
    if (viewMode === 'questionOnly') {
      const originalContent = selectedChapter.content as { type: 'doc'; content: any[] };
      return {
        type: 'doc',
        content: originalContent.content.filter(node => node.type === 'question-node'),
      };
    }
    return selectedChapter.content;
  }, [selectedChapter, viewMode]);

  const chapterListView = (
    <ScrollArea className="h-full p-2">
      <div className="space-y-1">
        <h2 className="text-lg font-semibold p-2">章节列表</h2>
        {chapterTree.map((chapter) => (
          <ChapterListItem
            key={chapter.id}
            chapter={chapter}
            selectedChapter={selectedChapter}
            onSelectChapter={handleSelectChapter}
            level={0}
          />
        ))}
      </div>
    </ScrollArea>
  );

  const contentDisplayView = (
    <ScrollArea className="h-full p-6">
      {contentToRender ? (
        <TiptapContentRenderer content={contentToRender as any} />
      ) : (
        <div className="flex items-center justify-center h-full text-muted-foreground">
          <p>请从左侧选择一个章节开始阅读</p>
        </div>
      )}
    </ScrollArea>
  );

  const headerView = (
    <div className='p-4 border-b flex items-center justify-between'>
      <div>
        <h1 className={`font-bold ${isMobile ? 'text-xl' : 'text-2xl'}`}>{teachingAid.title}</h1>
        <p className={`text-muted-foreground ${isMobile ? 'text-xs' : 'text-sm'}`}>
          {teachingAid.subject} - {teachingAid.grade_level}
        </p>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" onClick={() => setViewMode(viewMode === 'complete' ? 'questionOnly' : 'complete')}>
          {viewMode === 'complete' ? '预览试题' : '完整预览'}
        </Button>
        {isMobile && (
          <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
            <DrawerTrigger asChild>
              <Button variant="outline" size="icon">
                <Menu className="h-4 w-4" />
              </Button>
            </DrawerTrigger>
            <DrawerContent>
              <div className="h-[80vh]">
                {chapterListView}
              </div>
            </DrawerContent>
          </Drawer>
        )}
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <div className="h-full border rounded-lg flex flex-col">
        {headerView}
        <div className="flex-grow overflow-y-auto">
          {contentDisplayView}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full border rounded-lg">
      {headerView}
      <ResizablePanelGroup direction="horizontal" className="h-full w-full">
        <ResizablePanel defaultSize={20} minSize={15}>
          {chapterListView}
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={80}>
          {contentDisplayView}
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};

export default TeachingAidViewer;
