import React from 'react';
import { Node } from './tiptap-types';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tooltip } from '@/components/ui/tooltip';

interface QuestionCardProps {
  node: Node;
}

const QuestionCardComponent: React.FC<QuestionCardProps> = ({ node }) => {
  // The node.attrs (e.g., paperTitle, id) are encapsulated here,
  // ready for future functionalities like fetching dynamic paper content.

  return (
    <div className="flex justify-center my-4">
      <Dialog>
        <Card className="w-full max-w-lg">
          <CardHeader>
            <CardTitle>答题卡</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center p-6 pt-0">
            <p className="text-center text-lg font-semibold">{node.attrs?.paperTitle}</p>
          </CardContent>
          <CardFooter className="flex justify-center">
            <DialogTrigger asChild>
              <Button>点击预览</Button>
            </DialogTrigger>
          </CardFooter>
        </Card>
        <DialogContent className="max-w-4xl">
          {/* In the future, this could fetch and render a full component based on node.attrs.id */}
          <Tooltip>这是答题卡</Tooltip>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default QuestionCardComponent;
