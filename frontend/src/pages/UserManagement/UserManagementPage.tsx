import React, { useState, useEffect } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {PlusCircle, Pencil, Eye, EyeOff, Trash2} from 'lucide-react';
import { toast } from "sonner";
import { User } from '@/types/user';
import { Switch } from "@/components/ui/switch";

import { getUsers, createUser, updateUser} from '@/services/userApi';

const UserManagementPage: React.FC = () => {
    const [users, setUsers] = useState<User[]>([]);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<Partial<User> | null>(null);
    const [password, setPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);

    useEffect(() => {
        fetchUsers();
    }, []);

    const fetchUsers = async () => {
        try {
            const data = await getUsers();
            setUsers(data);
        } catch (error) {
            console.error("获取用户列表失败", error);
        }
    };

    const handleEdit = (user: User) => {
        setSelectedUser(user);
        setPassword(''); // 编辑时清空密码输入框
        setIsDialogOpen(true);
    };

    const handleSave = async () => {
        if (!selectedUser) return;
        try {
            if (selectedUser.user_id) {
                await updateUser(
                  {
                    user_id:  selectedUser.user_id,
                    password: password ? password : undefined,
                    phone: selectedUser.phone_number,
                    is_active: selectedUser.is_active,
                  }
                );
                toast.info("更新成功");
            } else {
                await createUser({
                  username: selectedUser.username || '',
                  phone: selectedUser.phone_number || '',
                  password: password,
                });
                toast.info("创建成功");
            }
            await fetchUsers();
            setIsDialogOpen(false);
            setSelectedUser(null);
            setPassword('');
            setShowPassword(false);
        } catch (err: any) {
            toast.error("保存用户失败 " + err.response?.data?.message);
            console.error("保存用户失败", err);
        }
    };

    return (
        <div className="space-y-4">
            <Card>
                <CardHeader>
                    <div className="flex justify-between items-center">
                        <div>
                            <CardTitle>用户管理</CardTitle>
                            <CardDescription>管理系统中的所有用户账号。</CardDescription>
                        </div>
                        <Button onClick={() => {
                            setSelectedUser({});
                            setIsDialogOpen(true);
                        }}>
                            <PlusCircle className="mr-2 h-4 w-4" />
                            创建用户
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>用户名</TableHead>
                                <TableHead>手机号</TableHead>
                                <TableHead>角色</TableHead>
                                <TableHead>管理员</TableHead>
                                <TableHead>教师</TableHead>
                                <TableHead>学生</TableHead>
                                <TableHead>激活</TableHead>
                                <TableHead>手机号验证</TableHead>
                                <TableHead>创建时间</TableHead>
                                <TableHead className="text-right"><p className="mr-12">操作</p></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {users.map((user) => (
                                <TableRow key={user.user_id}>
                                    <TableCell>{user.username}</TableCell>
                                    <TableCell>{user.phone_number}</TableCell>
                                    <TableCell>{user.roles?.join(', ')}</TableCell>
                                    <TableCell>{user.is_admin ? '是' : '否'}</TableCell>
                                    <TableCell>{user.is_teacher ? '是' : '否'}</TableCell>
                                    <TableCell>{user.is_student ? '是' : '否'}</TableCell>
                                    <TableCell>{user.is_active ? '是' : '否'}</TableCell>
                                    <TableCell>{user.phone_verified ? '是' : '否'}</TableCell>
                                    <TableCell>{user.created_at}</TableCell>
                                    <TableCell className="text-right">
                                        <Button variant="ghost" size="icon" onClick={() => handleEdit(user)}>
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            className="ml-2 text-red-500 hover:text-red-600"
                                            onClick={() => toast.info('功能正在开发中')}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>{selectedUser?.user_id ? '编辑用户' : '创建用户'}</DialogTitle>
                        <DialogDescription>
                            {selectedUser?.user_id ? '编辑用户的详细信息。' : '创建一个新的用户。'}
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="username" className="text-right">
                                用户名
                            </Label>
                            <Input
                                id="username"
                                value={selectedUser?.username || ''}
                                onChange={(e) => setSelectedUser({ ...selectedUser, username: e.target.value })}
                                className="col-span-3"
                                disabled={!!selectedUser?.user_id}
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="phone_number" className="text-right">
                                手机号
                            </Label>
                            <Input
                                id="phone_number"
                                value={selectedUser?.phone_number || ''}
                                onChange={(e) => setSelectedUser({ ...selectedUser, phone_number: e.target.value })}
                                className="col-span-3"
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="password" className="text-right">
                                密码
                            </Label>
                            <div className="col-span-3 relative flex items-center">
                              <Input
                                id="password"
                                type={showPassword ? 'text' : 'password'}
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                className="w-full"
                                placeholder={selectedUser?.user_id ? '如需修改请输入新密码' : '请输入密码'}
                              />
                              <button
                                type="button"
                                className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                                onClick={() => setShowPassword((v) => !v)}
                                tabIndex={-1}
                              >
                                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                              </button>
                            </div>
                        </div>
                        {selectedUser?.user_id && (
                          <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="is_active" className="text-right">
                              激活状态
                            </Label>
                            <div className="col-span-3 flex items-center">
                              <Switch
                                id="is_active"
                                checked={!!selectedUser.is_active}
                                onCheckedChange={checked => setSelectedUser({ ...selectedUser, is_active: checked })}
                              />
                              <span className="ml-2 text-sm">{selectedUser.is_active ? '激活' : '禁用'}</span>
                            </div>
                          </div>
                        )}
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDialogOpen(false)}>取消</Button>
                        <Button onClick={handleSave}>保存</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default UserManagementPage;
