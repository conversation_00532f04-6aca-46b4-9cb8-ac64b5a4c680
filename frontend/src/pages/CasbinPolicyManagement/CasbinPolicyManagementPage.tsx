/**
 * <PERSON>asbin策略管理页面
 * 提供Casbin策略的CRUD操作、权限测试和批量管理功能
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious, PaginationEllipsis } from '@/components/ui/pagination';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import {
  Plus, 
  Trash2,
  Search,
  RefreshCw,
  Shield,
  TestTube,
  Download,
  Upload,
  BarChart3,
  FileText,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';

import { 
  policyApi,
  CasbinPolicy,
  PolicyStats,
  CreatePolicyRequest,
  DeletePolicyRequest,
  PolicyTestRequest,
  PolicyTestResponse
} from '@/services/policyApi';
import { useAuth } from '@/contexts/AuthContext';

// Helper for pagination
const generatePagination = (currentPage: number, totalPages: number): (number | string)[] => {
  if (totalPages <= 7) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  if (currentPage <= 4) {
    return [1, 2, 3, 4, 5, '...', totalPages];
  }

  if (currentPage >= totalPages - 3) {
    return [1, '...', totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
  }

  return [
    1,
    '...',
    currentPage - 1,
    currentPage,
    currentPage + 1,
    '...',
    totalPages,
  ];
};

// 表单验证 Schema
const policyFormSchema = z.object({
  policy_type: z.enum(['permission', 'role'], { 
    errorMap: () => ({ message: '请选择策略类型' }) 
  }),
  subject: z.string().min(1, '主体不能为空'),
  object: z.string().optional(),
  action: z.string().optional(),
  effect: z.string().optional(),
  role: z.string().optional(),
});

const testFormSchema = z.object({
  subject: z.string().min(1, '主体不能为空'),
  object: z.string().min(1, '对象不能为空'),
  action: z.string().min(1, '动作不能为空'),
});

type PolicyFormValues = z.infer<typeof policyFormSchema>;
type TestFormValues = z.infer<typeof testFormSchema>;

const CasbinPolicyManagementPage: React.FC = () => {
  const { identity } = useAuth();
  
  // 状态管理
  const [policies, setPolicies] = useState<CasbinPolicy[]>([]);
  const [stats, setStats] = useState<PolicyStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 分页和筛选
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [policyTypeFilter, setPolicyTypeFilter] = useState<string>('all');
  const [subjectFilter, setSubjectFilter] = useState('');
  const [objectFilter, setObjectFilter] = useState('');
  
  // 对话框状态
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<CasbinPolicy | null>(null);
  const [testResult, setTestResult] = useState<PolicyTestResponse | null>(null);
  
  // 文件上传
  const [importFile, setImportFile] = useState<File | null>(null);
  
  // 表单
  const policyForm = useForm<PolicyFormValues>({
    resolver: zodResolver(policyFormSchema),
    defaultValues: {
      policy_type: 'permission',
      subject: '',
      object: '',
      action: '',
      effect: 'allow',
      role: '',
    }
  });

  const testForm = useForm<TestFormValues>({
    resolver: zodResolver(testFormSchema),
    defaultValues: {
      subject: '',
      object: '',
      action: '',
    }
  });

  // 当前租户ID
  const currentTenantId = identity?.tenant_id || '';

  // 加载数据
  const loadPolicies = async () => {
    try {
      setLoading(true);
      const params: any = {
        tenant_id: currentTenantId,
        page: currentPage,
        page_size: 20,
        subject: subjectFilter || undefined,
        object: objectFilter || undefined,
      };
      if (policyTypeFilter !== 'all') {
        params.policy_type = policyTypeFilter;
      }
      
      const response = await policyApi.getPolicies(params);
      if (response.success && response.data) {
        setPolicies(response.data);
        if (response.pagination) {
          setTotalPages(response.pagination.total_pages || 1);
          setTotalCount(response.pagination.total || 0);
        }
      }
      setError(null);
    } catch (err: any) {
      setError(err.message || '加载策略列表失败');
      toast.error('加载策略列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await policyApi.getPolicyStats(currentTenantId);
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (err: any) {
      console.error('加载统计信息失败:', err);
    }
  };

  // 生命周期
  useEffect(() => {
    if (currentTenantId) {
      loadPolicies();
      // loadStats();
    }
  }, [currentTenantId, currentPage, subjectFilter, objectFilter, policyTypeFilter]);

  // 表单处理
  const handleCreatePolicy = async (data: PolicyFormValues) => {
    try {
      const createData: CreatePolicyRequest = {
        tenant_id: currentTenantId,
        policy_type: data.policy_type,
        subject: data.subject,
        object: data.object,
        action: data.action,
        effect: data.effect || 'allow',
        role: data.role,
      };
      
      const response = await policyApi.createPolicy(createData);
      if (response.success) {
        toast.success('策略创建成功');
        setIsCreateDialogOpen(false);
        policyForm.reset();
        loadPolicies();
        loadStats();
      }
    } catch (err: any) {
      toast.error(err.message || '创建策略失败');
    }
  };

  const handleDeletePolicy = async () => {
    if (!selectedPolicy) return;
    
    try {
      const deleteData: DeletePolicyRequest = {
        tenant_id: selectedPolicy.domain,
        policy_type: selectedPolicy.policy_type,
        subject: selectedPolicy.subject,
        object: selectedPolicy.object,
        action: selectedPolicy.action,
        effect: selectedPolicy.effect,
        role: selectedPolicy.policy_type === 'role' ? selectedPolicy.object : undefined,
      };
      
      const response = await policyApi.deletePolicy(deleteData);
      if (response.success) {
        toast.success('策略删除成功');
        setIsDeleteDialogOpen(false);
        setSelectedPolicy(null);
        loadPolicies();
        loadStats();
      }
    } catch (err: any) {
      toast.error(err.message || '删除策略失败');
    }
  };

  const handleTestPermission = async (data: TestFormValues) => {
    try {
      const testData: PolicyTestRequest = {
        tenant_id: currentTenantId,
        subject: data.subject,
        object: data.object,
        action: data.action,
      };
      
      const response = await policyApi.testPermission(testData);
      if (response.success && response.data) {
        setTestResult(response.data);
        toast.success('权限测试完成');
      }
    } catch (err: any) {
      toast.error(err.message || '权限测试失败');
    }
  };

  const handleExportPolicies = async () => {
    try {
      const response = await policyApi.exportPolicies(currentTenantId);
      if (response.success && response.data) {
        const dataStr = JSON.stringify(response.data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `casbin-policies-${currentTenantId}-${Date.now()}.json`;
        link.click();
        URL.revokeObjectURL(url);
        toast.success('策略导出成功');
      }
    } catch (err: any) {
      toast.error(err.message || '导出策略失败');
    }
  };

  const handleImportPolicies = async () => {
    if (!importFile) {
      toast.error('请选择要导入的文件');
      return;
    }

    try {
      const response = await policyApi.importPolicies(importFile, currentTenantId);
      if (response.success) {
        toast.success(`导入成功：${response.data.imported_policies} 条策略`);
        setIsImportDialogOpen(false);
        setImportFile(null);
        loadPolicies();
        loadStats();
      }
    } catch (err: any) {
      toast.error(err.message || '导入策略失败');
    }
  };

  const handleClearPolicies = async () => {
    if (!confirm('确定要清除所有策略吗？此操作不可撤销！')) {
      return;
    }

    try {
      const response = await policyApi.clearTenantPolicies(currentTenantId);
      if (response.success) {
        toast.success('策略清除成功');
        loadPolicies();
        loadStats();
      }
    } catch (err: any) {
      toast.error(err.message || '清除策略失败');
    }
  };

  const handleSyncPolicies = async () => {
    try {
      const response = await policyApi.syncTenantPolicies(currentTenantId);
      if (response.success) {
        toast.success('策略同步成功');
        loadPolicies();
        loadStats();
      }
    } catch (err: any) {
      toast.error(err.message || '同步策略失败');
    }
  };

  // 对话框处理
  const openCreateDialog = () => {
    policyForm.reset();
    setIsCreateDialogOpen(true);
  };

  const openDeleteDialog = (policy: CasbinPolicy) => {
    setSelectedPolicy(policy);
    setIsDeleteDialogOpen(true);
  };

  const openTestDialog = () => {
    testForm.reset();
    setTestResult(null);
    setIsTestDialogOpen(true);
  };

  // 渲染函数
  const renderPolicyType = (type: string) => (
    <Badge variant={type === 'permission' ? 'default' : 'secondary'}>
      {type === 'permission' ? '权限策略' : '角色策略'}
    </Badge>
  );

  const renderEffect = (effect?: string) => {
    if (!effect) return '-';
    return (
      <Badge variant={effect === 'allow' ? 'default' : 'destructive'}>
        {effect === 'allow' ? '允许' : '拒绝'}
      </Badge>
    );
  };

  const renderTableLoading = () => (
    <div className="space-y-3">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4">
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[150px]" />
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-4 w-[80px]" />
        </div>
      ))}
    </div>
  );

  const renderTestResult = () => {
    if (!testResult) return null;

    return (
      <div className="mt-4 p-4 border rounded-md">
        <div className="flex items-center gap-2 mb-3">
          {testResult.allowed ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <XCircle className="h-5 w-5 text-red-500" />
          )}
          <span className="font-medium">
            测试结果: {testResult.allowed ? '允许访问' : '拒绝访问'}
          </span>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <Label>主体</Label>
            <p className="text-slate-600">{testResult.subject}</p>
          </div>
          <div>
            <Label>对象</Label>
            <p className="text-slate-600">{testResult.object}</p>
          </div>
          <div>
            <Label>动作</Label>
            <p className="text-slate-600">{testResult.action}</p>
          </div>
          <div>
            <Label>测试时间</Label>
            <p className="text-slate-600">
              {new Date(testResult.test_timestamp).toLocaleString()}
            </p>
          </div>
        </div>

        {testResult.matched_policies.length > 0 && (
          <div className="mt-4">
            <Label>匹配的策略</Label>
            <div className="mt-2 space-y-2">
              {testResult.matched_policies.map((policy, index) => (
                <div key={index} className="p-2 bg-slate-50 rounded text-sm">
                  {policy.subject} → {policy.object} | {policy.action} ({policy.effect})
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 页头 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Casbin策略管理</h1>
          <p className="text-slate-600 mt-2">
            管理Casbin权限策略和角色关系，支持策略测试和批量操作
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={openTestDialog}>
            <TestTube className="h-4 w-4 mr-2" />
            权限测试
          </Button>
          <Button onClick={openCreateDialog}>
            <Plus className="h-4 w-4 mr-2" />
            新建策略
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总策略数</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_policies}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">权限策略</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.permission_policies}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">角色策略</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.role_policies}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 主要内容 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">策略列表</CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleExportPolicies}>
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
              <Button variant="outline" size="sm" onClick={() => setIsImportDialogOpen(true)}>
                <Upload className="h-4 w-4 mr-2" />
                导入
              </Button>
              <Button variant="outline" size="sm" onClick={handleSyncPolicies}>
                <RefreshCw className="h-4 w-4 mr-2" />
                同步
              </Button>
              <Button variant="destructive" size="sm" onClick={handleClearPolicies}>
                <Trash2 className="h-4 w-4 mr-2" />
                清空
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 筛选栏 */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="搜索主体..."
                value={subjectFilter}
                onChange={(e) => setSubjectFilter(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Input
              placeholder="搜索对象..."
              value={objectFilter}
              onChange={(e) => setObjectFilter(e.target.value)}
              className="max-w-sm"
            />
            
            <Select value={policyTypeFilter} onValueChange={setPolicyTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="策略类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="permission">权限策略</SelectItem>
                <SelectItem value="role">角色策略</SelectItem>
              </SelectContent>
            </Select>
            
            <Button variant="outline" size="sm" onClick={loadPolicies}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>

          {/* 策略表格 */}
          {loading ? (
            renderTableLoading()
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              {error}
            </div>
          ) : policies.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              暂无策略数据
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>策略类型</TableHead>
                  <TableHead>主体</TableHead>
                  <TableHead>对象</TableHead>
                  <TableHead>动作</TableHead>
                  <TableHead>效果</TableHead>
                  <TableHead>租户</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {policies.map((policy) => (
                  <TableRow key={policy.id || `${policy.subject}-${policy.object}-${policy.action}`}>
                    <TableCell>{renderPolicyType(policy.policy_type)}</TableCell>
                    <TableCell>
                      <code className="text-sm bg-slate-100 px-2 py-1 rounded">
                        {policy.subject}
                      </code>
                    </TableCell>
                    <TableCell>
                      <code className="text-sm bg-slate-100 px-2 py-1 rounded">
                        {policy.object || '-'}
                      </code>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{policy.action || '-'}</span>
                    </TableCell>
                    <TableCell>{renderEffect(policy.effect)}</TableCell>
                    <TableCell>
                      <span className="text-sm text-slate-600">{policy.domain}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => openDeleteDialog(policy)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationPrevious
                    onClick={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
                    className={currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  />
                  {generatePagination(currentPage, totalPages).map((page, index) => (
                    <PaginationItem key={index}>
                      {typeof page === 'string' ? (
                        <PaginationEllipsis />
                      ) : (
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                          className="cursor-pointer"
                        >
                          {page}
                        </PaginationLink>
                      )}
                    </PaginationItem>
                  ))}
                  <PaginationNext
                    onClick={() => currentPage < totalPages && setCurrentPage(currentPage + 1)}
                    className={currentPage >= totalPages ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                  />
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 创建策略对话框 */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>新建策略</DialogTitle>
            <DialogDescription>
              创建新的Casbin权限策略或角色关系
            </DialogDescription>
          </DialogHeader>
          <Form {...policyForm}>
            <form onSubmit={policyForm.handleSubmit(handleCreatePolicy)} className="space-y-4">
              <FormField
                control={policyForm.control}
                name="policy_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>策略类型</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择策略类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="permission">权限策略</SelectItem>
                        <SelectItem value="role">角色策略</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={policyForm.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>主体</FormLabel>
                    <FormControl>
                      <Input placeholder="用户ID或角色" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {policyForm.watch('policy_type') === 'permission' ? (
                <>
                  <FormField
                    control={policyForm.control}
                    name="object"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>对象</FormLabel>
                        <FormControl>
                          <Input placeholder="资源对象" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={policyForm.control}
                    name="action"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>动作</FormLabel>
                        <FormControl>
                          <Input placeholder="read, write, create, delete" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={policyForm.control}
                    name="effect"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>效果</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择效果" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="allow">允许</SelectItem>
                            <SelectItem value="deny">拒绝</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              ) : (
                <FormField
                  control={policyForm.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>角色</FormLabel>
                      <FormControl>
                        <Input placeholder="角色名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button type="submit">
                  创建
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 删除策略确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这条策略吗？此操作无法撤销。
              <br />
              策略详情: {selectedPolicy?.subject} → {selectedPolicy?.object} | {selectedPolicy?.action}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeletePolicy}>
              删除
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 权限测试对话框 */}
      <Dialog open={isTestDialogOpen} onOpenChange={setIsTestDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>权限测试</DialogTitle>
            <DialogDescription>
              测试指定主体对资源的访问权限
            </DialogDescription>
          </DialogHeader>
          <Form {...testForm}>
            <form onSubmit={testForm.handleSubmit(handleTestPermission)} className="space-y-4">
              <FormField
                control={testForm.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>主体</FormLabel>
                    <FormControl>
                      <Input placeholder="用户ID或角色" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={testForm.control}
                name="object"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>对象</FormLabel>
                    <FormControl>
                      <Input placeholder="资源对象" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={testForm.control}
                name="action"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>动作</FormLabel>
                    <FormControl>
                      <Input placeholder="read, write, create, delete" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {renderTestResult()}

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsTestDialogOpen(false)}>
                  关闭
                </Button>
                <Button type="submit">
                  <TestTube className="h-4 w-4 mr-2" />
                  开始测试
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 导入策略对话框 */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>导入策略</DialogTitle>
            <DialogDescription>
              从JSON文件导入Casbin策略配置
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="import-file">选择文件</Label>
              <Input
                id="import-file"
                type="file"
                accept=".json"
                onChange={(e) => setImportFile(e.target.files?.[0] || null)}
                className="mt-1"
              />
            </div>
            
            {importFile && (
              <div className="p-3 bg-slate-50 rounded">
                <p className="text-sm">
                  <strong>文件名:</strong> {importFile.name}
                </p>
                <p className="text-sm">
                  <strong>大小:</strong> {(importFile.size / 1024).toFixed(2)} KB
                </p>
              </div>
            )}

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleImportPolicies} disabled={!importFile}>
                <Upload className="h-4 w-4 mr-2" />
                导入
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CasbinPolicyManagementPage;
