import {<PERSON>, CardContent, CardHeader, CardTitle} from "@/components/ui/card.tsx";
import WorkflowForm from "@/pages/Workflow/components/WorkflowForm.tsx";
import WorkflowTable from "@/pages/Workflow/components/WorkflowTable.tsx";
import WorkflowTableHeader from "@/pages/Workflow/components/WorkflowTableHeader.tsx";
import {useEffect, useState} from "react";
import {questionTypeApi} from "@/services/questionApi.ts";
import subjectApi from "@/services/subjectApi.ts";
import gradeApi from "@/services/gradeApi.ts";
import {toast} from "sonner";
import {
    DEFAULT_WORKFLOW_SETTING_QUERY,
    GradeLevelSummary,
    QuestionTypeSummary,
    SubjectSummary, WorkflowSettingQueryParams, WorkflowSetting, Tenant,
} from "@/types";
import {getTenants} from "@/services/tenantApi.ts";
import {workflowSettingApi} from "@/services/workflowApi.ts";

const WorkflowPage = () => {
    
    const [loading, setLoading] = useState(false);
    const [workflowSettings, setWorkflowSettings] = useState<WorkflowSetting[]>([])
    const [queryWorkflowSettingParams, setQueryWorkflowSettingParams] = useState<WorkflowSettingQueryParams>(DEFAULT_WORKFLOW_SETTING_QUERY);
    const [workflowSettingsPagination, setWorkflowSettingsPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0,
    });

    const [questionTypeSummaries, setQuestionTypeSummaries] = useState<QuestionTypeSummary[]>([])
    const [subjectSummaries, setSubjectSummaries] = useState<SubjectSummary[]>([])
    const [gradeLevelSummaries, setGradeLevelSummaries] = useState<GradeLevelSummary[]>([])
    const [tenants, setTenants] = useState<Tenant[]>([]);
    const [formOpen, setFormOpen] = useState(false);

    const [editingWorkflowSetting, setEditingWorkflowSetting] = useState<WorkflowSetting>();

    const loadWorkflowSetting = async (params?: Partial<WorkflowSettingQueryParams>)=>{
        try {
            setLoading(true)
            const finalParams = {...queryWorkflowSettingParams, ...params};
            let response = await workflowSettingApi.getWorkflowSetting(finalParams)
            setWorkflowSettings(response.data)
            setWorkflowSettingsPagination({
                current: response.pagination.page,
                pageSize: response.pagination.page_size,
                total: response.pagination.total,
                totalPages: response.pagination.total_pages,
            });
            setQueryWorkflowSettingParams(finalParams);
        } catch (error) {
            console.error('Failed to load settings:', error);
            toast.error('加载数据失败');
        }
        finally {
            setLoading(false)
        }
    }

    const loadComposeSelection = async () => {
        try {
            setLoading(true)
            const [questionTypesRes, subjectsRes, gradesRes, tenantsRes] = await Promise.all([
                questionTypeApi.getQuestionTypeSummaries(),
                subjectApi.getSubjectSummaries(),
                gradeApi.getGradeSummaries(),
                getTenants()
            ]);

            setQuestionTypeSummaries(questionTypesRes.data ?? [])
            setSubjectSummaries(subjectsRes.data ?? [])
            setGradeLevelSummaries(gradesRes.data ?? [])
            setTenants(tenantsRes)
        } catch (error) {
            console.error('Failed to load summaries:', error);
            toast.error('加载数据失败');
        } finally {
            setLoading(false);
        }

    }

    const handleCreateWorkflowSetting = () => {
        setEditingWorkflowSetting(undefined);
        setFormOpen(true);
    };

    const handleWorkflowPageChange = (page: number, pageSize: number) => {
        loadWorkflowSetting({...queryWorkflowSettingParams, page, page_size: pageSize});
    };

    useEffect(() => {
        loadWorkflowSetting()
        loadComposeSelection();
    }, []);

    return (
        <div className="space-y-6 p-6">
            {/* Page Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">工作流管理</h1>
                    <p className="text-gray-600 mt-1">管理系统中的用以评分的工作流信息</p>
                </div>
            </div>
        {/*    todo 表头一个新增功能，筛选功能。表格展示所有工作流，筛选中增加：满足学科年级题型（多选），满足哪个工作流（单？多？），删除、修改操作。带分页。*/}
            <Card>
                <CardHeader className="pb-2">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                        <CardTitle className="text-lg">工作流配置列表</CardTitle>

                        <WorkflowTableHeader
                            questionTypeSummaries={questionTypeSummaries}
                            subjectSummaries={subjectSummaries}
                            gradeLevelSummaries={gradeLevelSummaries}
                            tenants={tenants}
                            onFilterChange={(v)=>{
                                loadWorkflowSetting(v)
                            }}
                            onCreateWorkflowSetting={()=>{
                                handleCreateWorkflowSetting()
                            }}

                        />
                    </div>
                </CardHeader>
                <CardContent>
                    <WorkflowTable 
                        workflowSettings={workflowSettings}
                        questionTypeSummaries={questionTypeSummaries}
                        subjectSummaries={subjectSummaries}
                        gradeLevelSummaries={gradeLevelSummaries}
                        tenants={tenants}
                        loading={loading} 
                        pagination={{
                        current: workflowSettingsPagination.current,
                        pageSize: workflowSettingsPagination.pageSize,
                        total: workflowSettingsPagination.total,
                        onChange: handleWorkflowPageChange,
                    }}/>
                </CardContent>
                <WorkflowForm
                    questionTypeSummaries={questionTypeSummaries}
                    subjectSummaries={subjectSummaries}
                    gradeLevelSummaries={gradeLevelSummaries}
                    tenants={tenants}
                    open={formOpen}
                    onClose={() => setFormOpen(false)}
                />
            </Card>
        </div>
    );
};

export default WorkflowPage;
