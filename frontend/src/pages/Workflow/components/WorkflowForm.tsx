import {<PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Footer, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/components/ui/dialog.tsx";
import {MultiSelect, Option} from "@/components/ui/multi-select.tsx";
import {FC, useEffect, useState} from "react";
import {
    CreateWorkflowSettingRequest,
    GradeLevelSummary,
    QuestionTypeSummary,
    SubjectSummary,
    Tenant, WorkflowCategory, workflowCategoryOptions,
    WorkflowSetting, WorkflowSummary
} from "@/types";
import {Button} from "@/components/ui/button.tsx";
import {Loader2} from "lucide-react";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {workflowSettingApi} from "@/services/workflowApi.ts";
import {toast} from "sonner";

interface WorkflowFormProps {
    workflowSetting?: WorkflowSetting;
    open: boolean;
    onClose: () => void;
    onSubmit: (data: CreateWorkflowSettingRequest) => Promise<void>;
    loading?: boolean;
    questionTypeSummaries: QuestionTypeSummary[]
    subjectSummaries: SubjectSummary[]
    gradeLevelSummaries: GradeLevelSummary[]
    tenants: Tenant[]
}

const WorkflowForm: FC<WorkflowFormProps> = ({
                                                 workflowSetting,
                                                 open,
                                                 onClose,
                                                 loading,
                                                 onSubmit,
                                                 questionTypeSummaries,
                                                 subjectSummaries,
                                                 gradeLevelSummaries,
                                                 tenants,
                                             }) => {
    const [queryWorkflowLoading,setQueryWorkflowLoading] = useState(false)
    const [workflowSummary, setWorkflowSummary] = useState<WorkflowSummary[]>([]);

    // 转换成 Option[]
    const questionTypeOptions: Option[] = questionTypeSummaries.map((q) => ({
        value: q.code,
        label: q.type_name,
    }))
    const subjectOptions: Option[] = subjectSummaries.map((s) => ({
        value: s.code,
        label: s.name,
    }))
    const gradeOptions: Option[] = gradeLevelSummaries.map((g) => ({
        value: g.code,
        label: g.name,
    }))
    const tenantOptions: Option[] = tenants.map((t) => ({
        value: t.schemaName,
        label: t.name,
    }))

    // 状态
    const [workflowCategoryValue, setWorkflowCategoryValue] = useState<WorkflowCategory|undefined>(undefined); // 默认值可自行设置
    const [selectedQuestionTypes, setSelectedQuestionTypes] = useState<Option[]>([])
    const [selectedSubjects, setSelectedSubjects] = useState<Option[]>([])
    const [selectedGrades, setSelectedGrades] = useState<Option[]>([])
    const [selectedTenants, setSelectedTenants] = useState<Option[]>([])
    const [selectedWorkflow, setSelectedWorkflow] = useState<string|undefined>(undefined)

    const initialData = async () => {
        if (workflowSetting) {
            setWorkflowCategoryValue(workflowSetting.workflow_type)
            await loadWorkflow(workflowSetting.workflow_type)
            // setSelectedQuestionTypes(questionTypeOptions.filter(q => workflowSetting.question_type_codes.includes(q.value)))
            // setSelectedSubjects(subjectOptions.filter(s => workflowSetting.subject_codes.includes(s.value)))
            // setSelectedGrades(gradeOptions.filter(g => workflowSetting.grade_level_codes.includes(g.value)))
            // setSelectedTenants(tenantOptions.filter(t => workflowSetting.schema_names.includes(t.value)))
        } else {
            setWorkflowCategoryValue(undefined)
            setSelectedWorkflow(undefined)
            setSelectedQuestionTypes([])
            setSelectedSubjects([])
            setSelectedGrades([])
            setSelectedTenants([])
        }
    }



    useEffect(() => {
        if (open) {
            initialData()
        }
    }, [open, workflowSetting]);

    const loadWorkflow = async (value:WorkflowCategory)=>{
        setWorkflowCategoryValue(value)
        try {
            setQueryWorkflowLoading(true)
            let response = await workflowSettingApi.getWorkflowSummary(value)
            if (response.success && response.data) {
                setWorkflowSummary(response.data)
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
            toast.error('加载数据失败');
        }
        finally {
            setQueryWorkflowLoading(false)
        }
    }

    const handleCategorySelect = async (value:WorkflowCategory) => {
        setSelectedWorkflow('')
        await loadWorkflow(value)
    }

    const handleFormSubmit = async (data:CreateWorkflowSettingRequest) => {
        // todo submit
    };

    const isEditMode = !!workflowSetting;

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>
                        {isEditMode ? '编辑工作流配置' : '新增工作流配置'}
                    </DialogTitle>
                </DialogHeader>
                {/*todo 暂定功能，选择一个workflow类型，后端查询，储存入库并返回。*/}
                {/* 选择workflow，多选 学科、年级、题型、租户。*/}
                {/* save 保存*/}
                todo
                <div className="space-y-6">
                    <Select value={workflowCategoryValue} onValueChange={handleCategorySelect}>
                        <SelectTrigger className="w-[200px]">
                            <SelectValue placeholder="请选择流程类型" />
                        </SelectTrigger>
                        <SelectContent>
                            {workflowCategoryOptions.map(option => (
                                <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>

                    {workflowSummary.length > 0 && (
                        <div className="flex flex-wrap gap-4">
                            <Select value={selectedWorkflow} onValueChange={setSelectedWorkflow}>
                                <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="请选择工作流" />
                                </SelectTrigger>
                                <SelectContent>
                                    {workflowSummary.map(option => (
                                        <SelectItem key={option.workflow_id} value={option.workflow_id}>
                                            {option.workflow_name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    )}


                    {selectedWorkflow && (<div className="flex flex-wrap gap-4">
                        <div className="max-w-80">
                            <MultiSelect
                                value={selectedQuestionTypes}
                                onChange={setSelectedQuestionTypes}
                                options={questionTypeOptions}
                                placeholder="筛选题型"
                            />
                        </div>
                        <div className="max-w-80">
                            <MultiSelect
                                value={selectedSubjects}
                                onChange={setSelectedSubjects}
                                options={subjectOptions}
                                placeholder="筛选学科"
                            />
                        </div>
                        <div className="max-w-80">
                            <MultiSelect
                                value={selectedGrades}
                                onChange={setSelectedGrades}
                                options={gradeOptions}
                                placeholder="筛选年级"
                            />
                        </div>
                        <div className="max-w-80">
                            <MultiSelect
                                value={selectedTenants}
                                onChange={setSelectedTenants}
                                options={tenantOptions}
                                placeholder="筛选租户"
                            />
                        </div>
                    </div>)}

                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                            disabled={loading}
                        >
                            取消 todo click
                        </Button>
                        <Button
                            type="submit"
                            disabled={loading}
                        >
                            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                            {isEditMode ? '更新' : '创建'} todo click
                        </Button>
                    </DialogFooter>
                </div>
            </DialogContent>
        </Dialog>
    )
};

export default WorkflowForm;
