import {GradeLevelSummary, QuestionTypeSummary, SubjectSummary, Tenant, WorkflowSettingQueryParams} from "@/types";
import {FC, useEffect, useRef, useState} from "react"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select.tsx";
import {Button} from "@/components/ui/button.tsx";
import {Plus} from "lucide-react";

interface WorkflowTableHeaderProps {
    questionTypeSummaries: QuestionTypeSummary[]
    subjectSummaries: SubjectSummary[]
    gradeLevelSummaries: GradeLevelSummary[]
    tenants: Tenant[]
    onFilterChange: (filter: WorkflowSettingQueryParams) => void;
    onCreateWorkflowSetting: () => void;
}

const WorkflowTableHeader: FC<WorkflowTableHeaderProps> = ({
                                                               questionTypeSummaries,
                                                               subjectSummaries,
                                                               gradeLevelSummaries,
                                                               tenants,
                                                               onFilterChange,
                                                               onCreateWorkflowSetting
                                                           }) => {

    const [questionTypeSelect, setQuestionTypeSelect] = useState<string|undefined>(undefined);
    const [subjectSelect, setSubjectSelect] = useState<string|undefined>(undefined);
    const [gradeLevelSelect, setGradeLevelSelect] = useState<string|undefined>(undefined);
    const [tenantSelect, setTenantSelect] = useState<string|undefined>(undefined);


    const firstRender = useRef(true);
    useEffect(() => {
        if (firstRender.current) {
            firstRender.current = false;
            return;
        }
        onFilterChange({
            question_type_code: questionTypeSelect,
            subject_code: subjectSelect,
            grade_level_code: gradeLevelSelect,
            schema_name: tenantSelect,
        });
    }, [questionTypeSelect, subjectSelect, gradeLevelSelect, tenantSelect]);

    return (
        <div className="flex flex-wrap gap-4 items-center">
            <div className="flex gap-2">
                <Select
                    value={tenantSelect}
                    onValueChange={(value) =>
                        setTenantSelect(value === 'none'?'':value)
                    }
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="选择租户"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem key={''} value={'none'}>未选择</SelectItem>
                        {tenants.map((tenant) => (
                            <SelectItem key={String(tenant.schemaName)} value={String(tenant.schemaName)}>
                                {tenant.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                <Select
                    value={questionTypeSelect}
                    onValueChange={(value) =>
                        setQuestionTypeSelect(value === 'none'?'':value)
                    }
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="选择题型"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem key={''} value={'none'}>未选择</SelectItem>
                        {questionTypeSummaries.map((questionType) => (
                            <SelectItem key={String(questionType.code)} value={String(questionType.code)}>
                                {questionType.type_name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                <Select
                    value={subjectSelect}
                    onValueChange={(value) =>
                        setSubjectSelect(value === 'none'?'':value)
                    }
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="选择学科"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem key={''} value={'none'}>未选择</SelectItem>
                        {subjectSummaries.map((subject) => (
                            <SelectItem key={String(subject.code)} value={String(subject.code)}>
                                {subject.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>

                <Select
                    value={gradeLevelSelect}
                    onValueChange={(value) =>
                        setGradeLevelSelect(value === 'none'?'':value)
                    }
                >
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="选择年级"/>
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem key={''} value={'none'}>未选择</SelectItem>
                        {gradeLevelSummaries.map((gradeLevel) => (
                            <SelectItem key={String(gradeLevel.code)} value={String(gradeLevel.code)}>
                                {gradeLevel.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            <div className="flex space-x-2">
                <Button onClick={onCreateWorkflowSetting}>
                    <Plus className="w-4 h-4 mr-2"/>
                    新增
                </Button>
            </div>

        </div>
    )
}

export default WorkflowTableHeader
