import {FC} from "react";
import {GradeLevelSummary, QuestionTypeSummary, SubjectSummary, Tenant, WorkflowSetting} from "@/types";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table.tsx";
import {Badge} from "@/components/ui/badge.tsx";
import {
    Pagination,
    PaginationContent,
    PaginationItem,
    PaginationLink, PaginationNext,
    PaginationPrevious
} from "@/components/ui/pagination.tsx";

interface WorkflowTableProps {
    workflowSettings: WorkflowSetting[];
    questionTypeSummaries: QuestionTypeSummary[]
    subjectSummaries: SubjectSummary[]
    gradeLevelSummaries: GradeLevelSummary[]
    tenants: Tenant[]
    loading?: boolean;
    // onEdit: (workflowSetting: WorkflowSetting) => void;
    pagination?: {
        current: number;
        pageSize: number;
        total: number;
        onChange: (page: number, pageSize: number) => void;
    };
}

const WorkflowTable:FC<WorkflowTableProps> = ({workflowSettings, loading, pagination, subjectSummaries, gradeLevelSummaries, questionTypeSummaries, tenants }) => {
    // 建立 code → name 映射
    const subjectMap = Object.fromEntries(subjectSummaries.map(s => [s.code, s.name]));
    const gradeMap = Object.fromEntries(gradeLevelSummaries.map(g => [g.code, g.name]));
    const questionTypeMap = Object.fromEntries(questionTypeSummaries.map(q => [q.code, q.type_name]));
    const schemaMap = Object.fromEntries(tenants.map(t => [t.schemaName, t.name])); // ✅ 新增：schema 映射

    const renderCodeBadges = (codes: string[], map: Record<string, string>) => {
        if (!codes || codes.length === 0) {
            return <Badge variant="secondary" className="text-xs">未限制</Badge>;
        }
        return codes.map(code => (
            <Badge key={code} variant="outline" className="text-xs mr-1">
                {map[code] ?? code}
            </Badge>
        ));
    };
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('zh-CN');
    };

    if (loading) {
        return (
            <div className="space-y-4">
                <div className="rounded-lg border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>工作流名称</TableHead>
                                <TableHead>类型</TableHead>
                                <TableHead>描述</TableHead>
                                <TableHead>学科</TableHead>
                                <TableHead>年级</TableHead>
                                <TableHead>题型</TableHead>
                                <TableHead>Schema 名称</TableHead>
                                <TableHead>更新时间</TableHead>
                                <TableHead>操作</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {Array.from({length: 5}).map((_, index) => (
                                <TableRow key={index}>
                                    {Array.from({length: 9}).map((_, cellIndex) => (
                                        <TableCell key={cellIndex}>
                                            <div className="h-4 bg-gray-200 rounded animate-pulse"/>
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </div>
        );
    }
    if (!Array.isArray(workflowSettings) || workflowSettings.length === 0) {
        return (
            <div className="text-center py-8">
                <div className="text-gray-500 text-lg mb-2">暂无数据</div>
                {/*<div className="text-gray-400 text-sm">点击&#34;新增学科&#34;创建第一个学科</div>*/}
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="rounded-lg border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[160px]">工作流名称</TableHead>
                            <TableHead className="w-[100px]">类型</TableHead>
                            <TableHead className="w-[200px]">描述</TableHead>
                            <TableHead className="w-[200px]">学科</TableHead>
                            <TableHead className="w-[200px]">年级</TableHead>
                            <TableHead className="w-[200px]">题型</TableHead>
                            <TableHead className="w-[200px]">Schema 名称</TableHead>
                            <TableHead className="w-[180px]">更新时间</TableHead>
                            <TableHead>操作</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {workflowSettings.map(ws => (
                            <TableRow key={ws.workflow_id} className="hover:bg-gray-50">
                                <TableCell>{ws.workflow_name}</TableCell>
                                <TableCell>{ws.workflow_type}</TableCell>
                                <TableCell>{ws.description}</TableCell>

                                <TableCell>
                                    {renderCodeBadges(ws.subject_codes, subjectMap)}
                                </TableCell>

                                <TableCell>
                                    {renderCodeBadges(ws.grade_level_codes, gradeMap)}
                                </TableCell>

                                <TableCell>
                                    {renderCodeBadges(ws.question_type_codes, questionTypeMap)}
                                </TableCell>

                                <TableCell>
                                    {renderCodeBadges(ws.schema_names, schemaMap)}
                                </TableCell>

                                <TableCell>
                                    {formatDate(ws.updated_at)}
                                </TableCell>

                                <TableCell>
                                    todo 编辑、删除
                                </TableCell>

                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            {/* 分页 */}
            {pagination && pagination.total > 0 && (
                <div className="flex flex-wrap items-center gap-4 mt-2">
                    <div className="text-sm text-gray-500 whitespace-nowrap">
                        显示第 {((pagination.current - 1) * pagination.pageSize) + 1} -{' '}
                        {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共{' '}
                        {pagination.total} 条记录
                    </div>
                    {/* 分页大小选择器和跳页 */}
                    <div className="flex flex-wrap gap-3 items-center">
                        <select
                            className="border rounded px-2 py-1 text-sm min-w-[80px]"
                            value={pagination.pageSize}
                            onChange={e => pagination.onChange(1, Number(e.target.value))}
                        >
                            {[10, 20, 50, 100].map(size => (
                                <option key={size} value={size}>{size} 条/页</option>
                            ))}
                        </select>
                        <span className="text-sm">跳转到</span>
                        <input
                            type="number"
                            min={1}
                            max={Math.ceil(pagination.total / pagination.pageSize)}
                            defaultValue={pagination.current}
                            onBlur={e => {
                                let page = Number(e.target.value);
                                if (page < 1) page = 1;
                                if (page > Math.ceil(pagination.total / pagination.pageSize)) page = Math.ceil(pagination.total / pagination.pageSize);
                                if (page !== pagination.current) {
                                    pagination.onChange(page, pagination.pageSize);
                                }
                            }}
                            className="border rounded px-2 py-1 w-16 text-sm text-center"
                        />
                        <span className="text-sm">页</span>
                    </div>
                    {/* 分页按钮 */}
                    <div className="flex items-center">
                        <Pagination>
                            <PaginationContent>
                                <PaginationItem>
                                    <PaginationPrevious
                                        onClick={() => {
                                            if (pagination.current > 1) {
                                                pagination.onChange(pagination.current - 1, pagination.pageSize);
                                            }
                                        }}
                                        className={pagination.current === 1 ? 'pointer-events-none opacity-50' : ''}
                                    />
                                </PaginationItem>
                                {Array.from({length: Math.ceil(pagination.total / pagination.pageSize)}, (_, i) => i + 1).map((page) => (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            isActive={page === pagination.current}
                                            onClick={() => pagination.onChange(page, pagination.pageSize)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}
                                <PaginationItem>
                                    <PaginationNext
                                        onClick={() => {
                                            if (pagination.current < Math.ceil(pagination.total / pagination.pageSize)) {
                                                pagination.onChange(pagination.current + 1, pagination.pageSize);
                                            }
                                        }}
                                        className={
                                            pagination.current === Math.ceil(pagination.total / pagination.pageSize)
                                                ? 'pointer-events-none opacity-50'
                                                : ''
                                        }
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            )}
        </div>
    );
};

export default WorkflowTable;
