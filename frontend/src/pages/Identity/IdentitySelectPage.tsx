import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext.tsx";
import { toast } from "sonner";
import { getIdentities } from "@/services/identityApi.ts";
import { IdentityInfo } from "@/types/identity.ts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle2 } from "lucide-react";

const IdentitySelectPage = () => {
    const navigate = useNavigate();
    const { token, selectIdentity, clearIdentity } = useAuth();
    const [identities, setIdentities] = useState<IdentityInfo[]>([]);
    const [selectedIdentityId, setSelectedIdentityId] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        if (!token) {
            navigate("/login");
        } else {
            initIdentity();
        }
    }, [token]);

    const initIdentity = async () => {
        try {
            setIsLoading(true);
            clearIdentity();
            console.log("🔍 开始获取身份信息，当前token:", !!token);
            const identitiesData = await getIdentities();
            console.log("✅ 获取到的身份信息:", identitiesData);

            if (identitiesData.length === 0) {
                console.log("⚠️ 未找到身份信息，跳转到绑定页面");
                toast.info("未检测到可选身份，请前往绑定");
                navigate("/identityBinding");
            } else if (identitiesData.length === 1) {
                console.log("🎯 找到唯一身份，自动选择:", identitiesData[0]);
                toast.info("欢迎您：" + identitiesData[0].display_name);
                selectIdentity(identitiesData[0]);
                navigate("/");
            } else {
                console.log("📋 找到多个身份，需要用户选择:", identitiesData.length);
                setIdentities(identitiesData);
            }
        } catch (error) {
            console.error("❌ 获取身份信息失败:", error);
            if (error instanceof Error) {
                toast.error("获取身份信息失败: " + error.message);
            } else {
                toast.error("获取身份信息失败");
            }
            // 如果是认证失败，跳转到登录页面
            if ((error as any)?.response?.status === 401) {
                console.log("🔄 认证失败，跳转到登录页面");
                navigate("/login");
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleSelectIdentity = (identityId: string) => {
        setSelectedIdentityId(identityId);
    };

    const handleConfirmSelection = () => {
        if (!selectedIdentityId) {
            toast.warning("请先选择一个身份");
            return;
        }

        const selectedIdentity = identities.find(
            (identity) => identity.identity_id === selectedIdentityId
        );

        if (selectedIdentity) {
            selectIdentity(selectedIdentity);
            toast.success("身份选择成功");
            navigate("/");
        }
    };

    const renderIdentityCard = (identity: IdentityInfo) => {
        const isSelected = selectedIdentityId === identity.identity_id;

        return (
            <Card
                key={identity.identity_id}
                className={`cursor-pointer transition-all duration-200 ${
                    isSelected
                        ? "border-primary ring-2 ring-primary"
                        : "hover:border-gray-300"
                }`}
                onClick={() => handleSelectIdentity(identity.identity_id)}
            >
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-2">
                                <div className="bg-gray-300 border-2 border-dashed rounded-xl w-10 h-10" />
                            </div>
                            <div>
                                <h3 className="text-lg font-medium">{identity.display_name}</h3>
                                <p className="text-sm text-muted-foreground">
                                    {identity.tenant_name}
                                </p>
                            </div>
                        </div>
                        {isSelected && (
                            <CheckCircle2 className="h-6 w-6 text-primary" />
                        )}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <p className="text-muted-foreground">身份类型</p>
                            <p className="capitalize">{identity.identity_type}</p>
                        </div>
                        <div>
                            <p className="text-muted-foreground">身份ID</p>
                            <p className="truncate text-xs">{identity.identity_id}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
        );
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-4">
            <div className="w-full max-w-md">
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        选择您的身份
                    </h1>
                    <p className="text-muted-foreground">
                        请从以下身份中选择一个继续操作
                    </p>
                </div>

                <div className="space-y-4 mb-6">
                    {identities.map(renderIdentityCard)}
                </div>

                <Button
                    className="w-full py-6 text-lg"
                    onClick={handleConfirmSelection}
                    disabled={!selectedIdentityId}
                >
                    确认选择
                </Button>

                <div className="mt-4 text-center">
                    <Button
                        variant="link"
                        className="text-muted-foreground"
                        onClick={() => navigate("/identityBinding")}
                    >
                        绑定新身份
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default IdentitySelectPage;