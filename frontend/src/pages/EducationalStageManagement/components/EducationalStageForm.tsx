import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

import { educationalStageApi } from '@/services/educationalStageApi';
import { 
  EducationalStage, 
  EducationalStageFormData, 
  CreateEducationalStageRequest 
} from '@/types/educationalStage';

interface EducationalStageFormProps {
  stage?: EducationalStage;
  open: boolean;
  onClose: () => void;
  onSubmit: (data: CreateEducationalStageRequest) => Promise<void>;
  loading?: boolean;
}

export const EducationalStageForm: React.FC<EducationalStageFormProps> = ({
  stage,
  open,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [codeChecking, setCodeChecking] = useState(false);
  const [codeAvailable, setCodeAvailable] = useState<boolean | null>(null);
  
  const isEditMode = !!stage;
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<EducationalStageFormData>({
    defaultValues: {
      code: '',
      name: '',
      short_name: '',
      description: '',
      order_level: 1,
      duration_years: 6,
      age_range: '6-12',
      is_standard: true,
    },
  });

  // Reset form when opening/closing or when stage changes
  useEffect(() => {
    if (open) {
      if (stage) {
        reset({
          code: stage.code,
          name: stage.name,
          short_name: stage.short_name || '',
          description: stage.description || '',
          order_level: stage.order_level,
          duration_years: stage.duration_years || 6,
          age_range: stage.age_range || '6-12',
          is_standard: stage.is_standard ?? true,
        });
      } else {
        reset({
          code: '',
          name: '',
          short_name: '',
          description: '',
          order_level: 1,
          duration_years: 6,
          age_range: '6-12',
          is_standard: true,
        });
      }
      setCodeAvailable(null);
    }
  }, [open, stage, reset]);

  // Check if code is available
  const watchedCode = watch('code');
  useEffect(() => {
    const checkCodeAvailability = async () => {
      if (!watchedCode || (isEditMode && stage?.code === watchedCode)) {
        setCodeAvailable(null);
        return;
      }

      setCodeChecking(true);
      try {
        const isAvailable = await educationalStageApi.checkCodeAvailable(watchedCode);
        setCodeAvailable(isAvailable);
      } catch (error) {
        console.error('Failed to check code availability:', error);
        setCodeAvailable(false);
      } finally {
        setCodeChecking(false);
      }
    };

    const timer = setTimeout(checkCodeAvailability, 500);
    return () => clearTimeout(timer);
  }, [watchedCode, isEditMode, stage]);

  const handleFormSubmit = async (data: EducationalStageFormData) => {
    // Validate code availability for new stages
    if (!isEditMode && codeAvailable === false) {
      toast.error('学段代码已存在，请使用其他代码');
      return;
    }

    try {
      await onSubmit(data);
    } catch (error: any) {
      // Handle validation errors
      if (error.response?.data?.errors) {
        // Handle form level errors if needed
        console.error('Form validation errors:', error.response.data.errors);
      }
      // Error toast will be shown by the parent component
      throw error;
    }
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? '编辑学段' : '添加新学段'}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="code">学段代码 *</Label>
            <div className="relative">
              <Input
                id="code"
                placeholder="例如: PRIMARY, JUNIOR, SENIOR"
                className={cn(
                  'pr-10',
                  codeAvailable === false && 'border-red-500 focus-visible:ring-red-500'
                )}
                disabled={isEditMode || loading}
                {...register('code', {
                  required: '请输入学段代码',
                  pattern: {
                    value: /^[A-Z0-9_]+$/,
                    message: '只能包含大写字母、数字和下划线',
                  },
                })}
              />
              {!isEditMode && watchedCode && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {codeChecking ? (
                    <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
                  ) : codeAvailable === true ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : codeAvailable === false ? (
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  ) : null}
                </div>
              )}
            </div>
            {errors.code && (
              <p className="text-sm text-red-500">{errors.code.message}</p>
            )}
            {codeAvailable === false && (
              <p className="text-sm text-red-500">该学段代码已被使用</p>
            )}
            <p className="text-xs text-muted-foreground">
              学段代码应为大写英文字母，例如: PRIMARY, JUNIOR, SENIOR
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">学段名称 *</Label>
            <Input
              id="name"
              placeholder="例如: 小学, 初中, 高中"
              disabled={loading}
              {...register('name', {
                required: '请输入学段名称',
              })}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="short_name">简称</Label>
              <Input
                id="short_name"
                placeholder="例如: 小, 初, 高"
                disabled={loading}
                {...register('short_name')}
              />
              <p className="text-xs text-muted-foreground">
                学段简称，用于简略显示
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="order_level">排序级别 *</Label>
              <Input
                id="order_level"
                type="number"
                min="1"
                disabled={loading}
                {...register('order_level', {
                  required: '请输入排序级别',
                  valueAsNumber: true,
                  min: {
                    value: 1,
                    message: '排序级别必须大于0',
                  },
                })}
              />
              {errors.order_level && (
                <p className="text-sm text-red-500">{errors.order_level.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="duration_years">学制(年)</Label>
              <Input
                id="duration_years"
                type="number"
                min="1"
                max="12"
                disabled={loading}
                placeholder="请输入学制年数"
                {...register('duration_years', {
                  valueAsNumber: true,
                  min: {
                    value: 1,
                    message: '学制年数必须大于0',
                  },
                  max: {
                    value: 12,
                    message: '学制年数不能超过12年',
                  },
                })}
              />
              {errors.duration_years && (
                <p className="text-sm text-red-500">{errors.duration_years.message}</p>
              )}
              <p className="text-xs text-muted-foreground">
                可选，学制年数范围1-12年
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="age_range">适龄范围</Label>
              <Input
                id="age_range"
                placeholder="例如: 6-12"
                disabled={loading}
                {...register('age_range', {
                  validate: (value) => {
                    if (!value) return true; // Optional field
                    const pattern = /^(0?[1-9]|[1-9][0-9])-(0?[1-9]|[1-9][0-9])$/;
                    if (!pattern.test(value)) {
                      return '格式不正确，请使用"起始-结束"格式，例如: 6-12';
                    }
                    const [start, end] = value.split('-').map(Number);
                    if (start >= end) {
                      return '结束年龄必须大于起始年龄';
                    }
                    return true;
                  },
                })}
              />
              {errors.age_range && (
                <p className="text-sm text-red-500">{errors.age_range.message}</p>
              )}
              <p className="text-xs text-muted-foreground">
                可选，格式如: 6-12
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="is_standard"
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                {...register('is_standard')}
              />
              <Label htmlFor="is_standard" className="text-sm font-medium">
                是否标准学段
              </Label>
            </div>
            <p className="text-xs text-muted-foreground">
              标准学段将作为系统默认学段选项
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              placeholder="请输入学段描述信息"
              className="min-h-[100px]"
              disabled={loading}
              {...register('description')}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditMode ? '保存更改' : '创建学段'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EducationalStageForm;
