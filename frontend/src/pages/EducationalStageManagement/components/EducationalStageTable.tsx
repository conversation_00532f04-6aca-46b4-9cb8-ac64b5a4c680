import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
// Removed unused Select imports
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2,
  Power, 
  PowerOff,
  BookOpen,
} from 'lucide-react';
import { EducationalStage } from '@/types/educationalStage';
import {cn} from "@/lib/utils.ts";

interface EducationalStageTableProps {
  stages: EducationalStage[];
  loading: boolean;
  onEdit: (stage: EducationalStage) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (id: string, isActive: boolean) => void;
  onViewGrades?: (stage: EducationalStage) => void;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  onPageChange: (page: number, pageSize?: number) => void;
}

const EducationalStageTable: React.FC<EducationalStageTableProps> = ({
  stages,
  loading,
  onEdit,
  onDelete,
  onToggleStatus,
  onViewGrades,
  pagination,
  onPageChange,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const handleToggleStatus = (stage: EducationalStage) => {
    onToggleStatus(stage.id, !stage.is_active);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-lg border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>学段代码</TableHead>
                <TableHead>学段名称</TableHead>
                <TableHead>排序级别</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="w-[200px]">描述</TableHead>
                <TableHead className="w-[150px]">创建时间</TableHead>
                <TableHead className="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[1, 2, 3, 5].map((i) => (
                <TableRow key={i}>
                  <TableCell colSpan={10}>
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (stages.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">暂无学段数据</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">学段代码</TableHead>
              <TableHead className="w-[100px]">学段名称</TableHead>
              <TableHead className="w-[60px]">简称</TableHead>
              <TableHead className="w-[60px]">排序</TableHead>
              <TableHead className="w-[80px]">学制(年)</TableHead>
              <TableHead className="w-[100px]">年龄范围</TableHead>
              <TableHead className="w-[200px]">描述</TableHead>
              <TableHead className="w-[80px]">标准学段</TableHead>
              <TableHead className="w-[70px]">状态</TableHead>
              <TableHead className="w-[150px]">创建时间</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {stages.map((stage) => (
              <TableRow key={stage.id}>
                <TableCell className="font-medium">{stage.code}</TableCell>
                <TableCell>{stage.name}</TableCell>
                <TableCell>{stage.short_name}</TableCell>
                <TableCell>{stage.order_level}</TableCell>
                <TableCell>{stage.duration_years}年</TableCell>
                <TableCell>{stage.age_range}</TableCell>
                <TableCell className="line-clamp-2" title={stage.description}>
                  {stage.description || '-'}
                </TableCell>

                <TableCell>
                  <Badge variant={'outline'}>
                    {stage.is_standard ? '是' : '否'}
                  </Badge>
                </TableCell>

                <TableCell className="text-center">
                  <Badge
                      variant={stage.is_active ? "default" : "secondary"}
                      className={cn(
                          "text-xs",
                          stage.is_active
                              ? "bg-green-100 text-green-800 hover:bg-green-200"
                              : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                      )}
                  >
                    {stage.is_active ? '启用' : '禁用'}
                  </Badge>
                </TableCell>

                <TableCell>{formatDate(stage.created_at)}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">打开菜单</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => onViewGrades?.(stage)}
                      >
                        <BookOpen className="mr-2 h-4 w-4" />
                        查看年级
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => onEdit(stage)}
                        className="cursor-pointer"
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        <span>编辑</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleToggleStatus(stage)}
                        className="cursor-pointer"
                      >
                        {stage.is_active ? (
                          <>
                            <PowerOff className="mr-2 h-4 w-4" />
                            <span>禁用</span>
                          </>
                        ) : (
                          <>
                            <Power className="mr-2 h-4 w-4" />
                            <span>启用</span>
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => onDelete(stage.id)}
                        className="cursor-pointer text-red-600 hover:!text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        <span>删除</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {pagination.total > 0 && (
        <div className="flex flex-wrap items-center gap-4 mt-2">
          <div className="text-sm text-gray-500 whitespace-nowrap">
            显示第 {((pagination.current - 1) * pagination.pageSize) + 1} -{' '}
            {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共{' '}
            {pagination.total} 条记录
          </div>
          {/* 分页大小选择器和跳页 */}
          <div className="flex flex-wrap gap-3 items-center">
            <select
              className="border rounded px-2 py-1 text-sm min-w-[80px]"
              value={pagination.pageSize}
              onChange={e => onPageChange(1, Number(e.target.value))}
            >
              {[10, 20, 50, 100].map(size => (
                <option key={size} value={size}>{size} 条/页</option>
              ))}
            </select>
            <span className="text-sm">跳转到</span>
            <input
              type="number"
              min={1}
              max={Math.ceil(pagination.total / pagination.pageSize)}
              defaultValue={pagination.current}
              onBlur={e => {
                let page = Number(e.target.value);
                if (page < 1) page = 1;
                if (page > Math.ceil(pagination.total / pagination.pageSize)) page = Math.ceil(pagination.total / pagination.pageSize);
                if (page !== pagination.current) {
                  onPageChange(page, pagination.pageSize);
                }
              }}
              className="border rounded px-2 py-1 w-16 text-sm text-center"
            />
            <span className="text-sm">页</span>
          </div>
          {/* 分页按钮 */}
          <div className="flex items-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => {
                      if (pagination.current > 1) {
                        onPageChange(pagination.current - 1, pagination.pageSize);
                      }
                    }}
                    className={pagination.current === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
                {Array.from({ length: Math.ceil(pagination.total / pagination.pageSize) }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      isActive={page === pagination.current}
                      onClick={() => onPageChange(page, pagination.pageSize)}
                      className="cursor-pointer"
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                <PaginationItem>
                  <PaginationNext
                    onClick={() => {
                      if (pagination.current < Math.ceil(pagination.total / pagination.pageSize)) {
                        onPageChange(pagination.current + 1, pagination.pageSize);
                      }
                    }}
                    className={
                      pagination.current === Math.ceil(pagination.total / pagination.pageSize)
                        ? 'pointer-events-none opacity-50'
                        : 'cursor-pointer'
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
};

export default EducationalStageTable;
