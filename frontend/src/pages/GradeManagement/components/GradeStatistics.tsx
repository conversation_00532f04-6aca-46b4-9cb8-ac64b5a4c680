import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Users, BookOpen, FileText, TrendingUp } from 'lucide-react';
import { gradeApi } from '@/services/gradeApi';
import { GradeLevelStatistics } from '@/types/grade';
import { toast } from 'sonner';

const GradeStatistics: React.FC = () => {
  const [statistics, setStatistics] = useState<GradeLevelStatistics | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStatistics();
  }, []);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const response = await gradeApi.getGradeStatistics();
      if (response.success && response.data) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('Failed to load statistics:', error);
      toast.error('加载统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!statistics) {
    return (
      <div className="text-center text-gray-500 py-8">
        暂无统计数据
      </div>
    );
  }

  const statCards = [
    {
      title: '总年级数',
      value: statistics.total_grades,
      icon: BookOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: '启用年级',
      value: statistics.active_grades,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: '禁用年级',
      value: statistics.inactive_grades,
      icon: FileText,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {statCards.map((card, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg ${card.bgColor}`}>
                  <card.icon className={`h-6 w-6 ${card.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Usage Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>年级使用情况</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>年级名称</TableHead>
                <TableHead>学生数量</TableHead>
                <TableHead>班级数量</TableHead>
                <TableHead>考试数量</TableHead>
                <TableHead>使用率</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {statistics.usage_stats.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center text-gray-500 py-8">
                    暂无使用数据
                  </TableCell>
                </TableRow>
              ) : (
                statistics.usage_stats.map((stat) => {
                  const totalUsage = stat.student_count + stat.class_count + (stat.exam_count || 0);
                  const usageLevel = totalUsage > 50 ? 'high' : totalUsage > 20 ? 'medium' : 'low';
                  
                  return (
                    <TableRow key={stat.grade_id}>
                      <TableCell className="font-medium">{stat.grade_name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          <Users className="w-3 h-3 mr-1" />
                          {stat.student_count}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          <BookOpen className="w-3 h-3 mr-1" />
                          {stat.class_count}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          <FileText className="w-3 h-3 mr-1" />
                          {stat.exam_count || 0}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={
                            usageLevel === 'high' ? 'default' : 
                            usageLevel === 'medium' ? 'secondary' : 
                            'outline'
                          }
                        >
                          {usageLevel === 'high' ? '高' : 
                           usageLevel === 'medium' ? '中' : '低'}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>年级分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">启用率</span>
                <span className="text-sm font-medium">
                  {statistics.total_grades > 0 
                    ? Math.round((statistics.active_grades / statistics.total_grades) * 100)
                    : 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full" 
                  style={{ 
                    width: statistics.total_grades > 0 
                      ? `${(statistics.active_grades / statistics.total_grades) * 100}%`
                      : '0%'
                  }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>使用概览</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">总学生数</span>
                <span className="text-sm font-medium">
                  {statistics.usage_stats.reduce((sum, stat) => sum + stat.student_count, 0)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">总班级数</span>
                <span className="text-sm font-medium">
                  {statistics.usage_stats.reduce((sum, stat) => sum + stat.class_count, 0)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">总考试数</span>
                <span className="text-sm font-medium">
                  {statistics.usage_stats.reduce((sum, stat) => sum + (stat.exam_count || 0), 0)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default GradeStatistics;