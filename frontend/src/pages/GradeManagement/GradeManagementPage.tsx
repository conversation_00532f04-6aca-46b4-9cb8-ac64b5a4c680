import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, Download, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';

import GradeTable from './components/GradeTable';
import GradeForm from './components/GradeForm';
import GradeStatistics from './components/GradeStatistics';
import { gradeApi } from '@/services/gradeApi';
import { 
  GradeLevel, 
  GradeLevelQueryParams, 
  GradeLevelFormData,
  DEFAULT_GRADE_QUERY,
  GRADE_SORT_OPTIONS,
  GRADE_STATUS_OPTIONS
} from '@/types/grade';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

const GradeManagementPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const stageCode = searchParams.get('stage_code');
  
  // State management
  const [grades, setGrades] = useState<GradeLevel[]>([]);
  const [loading, setLoading] = useState(false);
  const [queryParams, setQueryParams] = useState<GradeLevelQueryParams>(() => ({
    ...DEFAULT_GRADE_QUERY,
    ...(stageCode ? { stage_code: stageCode } : {})
  }));
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });
  
  // Form state
  const [formOpen, setFormOpen] = useState(false);
  const [editingGrade, setEditingGrade] = useState<GradeLevel | undefined>();
  const [formLoading, setFormLoading] = useState(false);
  const [searchInput, setSearchInput] = useState('');

  // Load grades data
  const loadGrades = async (params?: Partial<GradeLevelQueryParams>) => {
    try {
      setLoading(true);
      const finalParams = { 
        ...queryParams, 
        ...params,
        ...(stageCode ? { stage_code: stageCode } : {})
      };
      
      const response = await gradeApi.getGrades(finalParams);
      
      if (response.success && response.data) {
        setGrades(response.data);
        setPagination({
          current: response.pagination.page,
          pageSize: response.pagination.page_size,
          total: response.pagination.total,
          totalPages: response.pagination.total_pages,
        });
        setQueryParams(finalParams);
      }
    } catch (error) {
      console.error('Failed to load grades:', error);
      toast.error('加载年级列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadGrades().then();
  }, []);

  // Handle search
  const handleSearch = () => {
    loadGrades({...queryParams, search: searchInput, page: 1}).then();
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof GradeLevelQueryParams, value: any) => {
    const newParams = { ...queryParams, [key]: value, page: 1 };
    loadGrades(newParams).then();
  };

  // Handle pagination
  const handlePageChange = (page: number, pageSize: number) => {
    loadGrades({...queryParams, page, page_size: pageSize}).then();
  };

  // Handle create grade
  const handleCreateGrade = () => {
    setEditingGrade(undefined);
    setFormOpen(true);
  };

  // Handle edit grade
  const handleEditGrade = (grade: GradeLevel) => {
    setEditingGrade(grade);
    setFormOpen(true);
  };

  // Handle form submit
  const handleFormSubmit = async (data: GradeLevelFormData) => {
    try {
      setFormLoading(true);
      
      if (editingGrade) {
        // Update grade
        const response = await gradeApi.updateGrade(editingGrade.id, data);
        if (response.success) {
          toast.success('年级更新成功');
          setFormOpen(false);
          await loadGrades();
        }
      } else {
        // Create grade
        const response = await gradeApi.createGrade(data);
        if (response.success) {
          toast.success('年级创建成功');
          setFormOpen(false);
          await loadGrades();
        }
      }
    } catch (error: any) {
      console.error('Failed to save grade:', error);
      toast.error(error.response?.data?.message || '保存失败');
    } finally {
      setFormLoading(false);
    }
  };

  // 删除确认框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [gradeToDelete, setGradeToDelete] = useState<string | null>(null);

  // 打开删除确认框
  const handleDeleteGrade = (id: string) => {
    setGradeToDelete(id);
    setDeleteDialogOpen(true);
  };

  // 确认删除年级
  const confirmDeleteGrade = async () => {
    if (!gradeToDelete) return;

    try {
      const response = await gradeApi.deleteGrade(gradeToDelete);
      if (response.success) {
        toast.success('年级删除成功');
        await loadGrades();
      }
    } catch (error: any) {
      console.error('Failed to delete grade:', error);
      toast.error(error.response?.data?.message || '删除失败');
    } finally {
      setDeleteDialogOpen(false);
      setGradeToDelete(null);
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await gradeApi.toggleGradeStatus(id, isActive);
      if (response.success) {
        toast.success(`年级已${isActive ? '启用' : '禁用'}`);
        await loadGrades();
      }
    } catch (error: any) {
      console.error('Failed to toggle grade status:', error);
      toast.error(error.response?.data?.message || '状态切换失败');
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      const blob = await gradeApi.exportGrades(queryParams);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `grades-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('导出成功');
    } catch (error) {
      console.error('Failed to export grades:', error);
      toast.error('导出接口开发中');
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          {stageCode && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => window.history.back()}
              className="px-0 text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              返回学段管理
            </Button>
          )}
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold">
              {stageCode ? `${stageCode} - 年级管理` : '年级管理'}
            </h1>
            {stageCode && (
              <span className="text-sm text-muted-foreground">
                当前学段: {stageCode}
              </span>
            )}
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            导出
          </Button>
          <Button onClick={handleCreateGrade}>
            <Plus className="w-4 h-4 mr-2" />
            新增年级
          </Button>
        </div>
      </div>

      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">年级列表</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <CardTitle className="text-lg">年级列表</CardTitle>
                
                <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                  <div className="relative w-full md:w-64">
                    <button 
                      type="button" 
                      onClick={handleSearch}
                      className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
                    >
                      <Search className="h-4 w-4" />
                    </button>
                    <Input
                      placeholder="搜索年级名称..."
                      className="w-full pl-8"
                      value={searchInput}
                      onChange={(e) => setSearchInput(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Select
                      value={queryParams.is_active === undefined ? 'all' : String(queryParams.is_active)}
                      onValueChange={(value) => 
                        handleFilterChange('is_active', value === 'all' ? undefined : value === 'true')
                      }
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="全部状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        {GRADE_STATUS_OPTIONS.map((option) => (
                          <SelectItem key={String(option.value)} value={String(option.value)}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Select
                      value={queryParams.order_by ? `${queryParams.order_by}-${queryParams.order_direction}` : ''}
                      onValueChange={(value) => {
                        const [orderBy, orderDirection] = value.split('-');
                        handleFilterChange('order_by', orderBy);
                        handleFilterChange('order_direction', orderDirection);
                      }}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="排序方式" />
                      </SelectTrigger>
                      <SelectContent>
                        {GRADE_SORT_OPTIONS.map((option) => (
                          <React.Fragment key={option.value}>
                            <SelectItem value={`${option.value}-asc`}>
                              {option.label} ↑
                            </SelectItem>
                            <SelectItem value={`${option.value}-desc`}>
                              {option.label} ↓
                            </SelectItem>
                          </React.Fragment>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <GradeTable
                grades={grades}
                loading={loading}
                onEdit={handleEditGrade}
                onDelete={handleDeleteGrade}
                onToggleStatus={handleToggleStatus}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  onChange: handlePageChange,
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <GradeStatistics />
        </TabsContent>
      </Tabs>

      {/* 删除确认框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除该年级吗？此操作不可恢复。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteGrade}>
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Grade Form Modal */}
      <GradeForm
        grade={editingGrade}
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />
    </div>
  );
};

export default GradeManagementPage;