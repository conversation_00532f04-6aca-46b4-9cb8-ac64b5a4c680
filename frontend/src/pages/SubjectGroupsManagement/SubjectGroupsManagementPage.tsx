import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Users } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { subjectGroupApi } from '@/services/subjectGroupApi';
import { SubjectGroupsApi } from '@/services/subjectGroupsApi';
import SubjectGroupForm from './components/SubjectGroupForm';
import SubjectGroupStatistics from './components/SubjectGroupStatistics';
import SubjectGroupTable from './components/SubjectGroupTable';
import {CreateSubjectGroupsParams, SubjectGroupsDetail} from "@/types/subjectGroups.ts";

const SubjectGroupsManagementPage: React.FC = () => {
  // State management
  const [subjectGroups, setSubjectGroups] = useState<SubjectGroupsDetail[]>([]);
  const [loading, setLoading] = useState(false);
  const [formOpen, setFormOpen] = useState(false);
  const [editingSubjectGroup, setEditingSubjectGroup] = useState<any | undefined>();
  const [formLoading, setFormLoading] = useState(false);

  // 只查全部学科组
  const loadSubjectGroups = async () => {
    try {
      setLoading(true);
      const response = await SubjectGroupsApi.findAll('tenant_zhanghan');
      if (response.success && response.data) {
        setSubjectGroups(response.data);
      }
    } catch (error) {
      console.error('Failed to load subject groups:', error);
      toast.error('加载学科组列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSubjectGroups().then();
  }, []);

  // Handle search
  // const handleSearch = (search: string) => {
  //   loadSubjectGroups();
  // };
  //
  // // Handle filter changes
  // const handleFilterChange = (key: keyof SubjectGroupQueryParams, value: any) => {
  //   loadSubjectGroups();
  // };

  // Handle create subject group
  const handleCreateSubjectGroup = () => {
    setEditingSubjectGroup(undefined);
    setFormOpen(true);
  };

  // Handle edit subject group
  const handleEditSubjectGroup = (subjectGroup: SubjectGroupsDetail) => {
    // Map the data to match the form's expected structure
    const formData = {
      id: subjectGroup.id,
      name: subjectGroup.group_name,
      subject: subjectGroup.subject_code,
      description: subjectGroup.description || '',
      leader_id: subjectGroup.leader_user_id || ''
    };
    setEditingSubjectGroup(formData);
    setFormOpen(true);
  };

  // 处理表单提交
  const handleFormSubmit = async (data: CreateSubjectGroupsParams) => {
    try {
      setFormLoading(true);

      if (editingSubjectGroup) {
        // 更新学科组
        const updateParams = {
          id: editingSubjectGroup.id,
          group_name: data.group_name,
          subject_code: data.subject_code,
          description: data.description || null,
          // 确保 leader_user_id 被正确传递，即使为空
          leader_user_id: data.leader_user_id
        };

        console.log('更新学科组参数:', updateParams); // 添加日志
        const response = await SubjectGroupsApi.updateSubjectGroups('tenant_zhanghan', updateParams);
        if (response.success) {
          toast.success('学科组更新成功');
          setFormOpen(false);
          setEditingSubjectGroup(undefined); // 清除编辑状态
          await loadSubjectGroups();
        }
      } else {
        // 创建新学科组
        console.log('创建学科组参数:', data); // 添加日志
        const response = await SubjectGroupsApi.createSubjectGroups('tenant_zhanghan', data);
        if (response.success) {
          toast.success('学科组创建成功');
          setFormOpen(false);
          await loadSubjectGroups();
        }
      }
    } catch (error: any) {
      console.error('Failed to save subject group:', error);
      toast.error(error.response?.data?.message || '保存失败');
    } finally {
      setFormLoading(false);
    }
  };

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [subjectGroupToDelete, setSubjectGroupToDelete] = useState<string | null>(null);

  const handleDeleteSubjectGroup = (id: string) => {
    setSubjectGroupToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteSubjectGroup = async () => {
    if (!subjectGroupToDelete) return;

    try {
        const response = await SubjectGroupsApi.deleteSubjectGroup('tenant_zhanghan', subjectGroupToDelete);
        if (response.success) {
            toast.success('学科组删除成功');
            await loadSubjectGroups();
        }
    } catch (error: any) {
        console.error('Failed to delete subject group:', error);
        toast.error(error.response?.data?.message || '删除失败');
    } finally {
        setDeleteDialogOpen(false);
        setSubjectGroupToDelete(null);
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await subjectGroupApi.toggleSubjectGroupStatus(id, isActive);
      if (response.success) {
        toast.success(`学科组已${isActive ? '启用' : '禁用'}`);
        await loadSubjectGroups();
      }
    } catch (error: any) {
      console.error('Failed to toggle subject group status:', error);
      toast.error(error.response?.data?.message || '状态切换失败');
    }
  };

  // // Handle export
  // const handleExport = async () => {
  //   try {
  //     const blob = await subjectGroupApi.exportSubjectGroups(queryParams);
  //     const url = window.URL.createObjectURL(blob);
  //     const a = document.createElement('a');
  //     a.href = url;
  //     a.download = `subject-groups-${new Date().toISOString().split('T')[0]}.xlsx`;
  //     document.body.appendChild(a);
  //     a.click();
  //     window.URL.revokeObjectURL(url);
  //     document.body.removeChild(a);
  //     toast.success('导出成功');
  //   } catch (error) {
  //     console.error('Failed to export subject groups:', error);
  //     toast.error('导出失败');
  //   }
  // };

  return (
    <div className="space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">学科组管理</h1>
          <p className="text-gray-600 mt-1">管理学校的学科组织架构，包括学科组的创建、成员管理和权限分配</p>
        </div>
        <div className="flex space-x-2">
          {/*<Button variant="outline" onClick={handleExport}>*/}
          {/*  <Download className="w-4 h-4 mr-2" />*/}
          {/*  导出*/}
          {/*</Button>*/}
          <Button onClick={handleCreateSubjectGroup}>
            <Plus className="w-4 h-4 mr-2" />
            新增学科组
          </Button>
        </div>
      </div>

      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">学科组列表</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                学科组列表
              </CardTitle>

              <div className="flex flex-wrap gap-4 items-center">
                <div className="flex-1 min-w-[300px] max-w-md">
                  <div className="relative">
                    {/*<Search*/}
                    {/*  onClick={() => handleSearch(searchInput)}*/}
                    {/*  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 cursor-pointer"*/}
                    {/*/>*/}
                    {/*<Input*/}
                    {/*  placeholder="搜索学科组名称..."*/}
                    {/*  className="pl-10"*/}
                    {/*  value={searchInput}*/}
                    {/*  onChange={(e) => setSearchInput(e.target.value)}*/}
                    {/*  onKeyDown={(e) => {*/}
                    {/*    if (e.key === 'Enter') {*/}
                    {/*      handleSearch(searchInput);*/}
                    {/*    }*/}
                    {/*  }}*/}
                    {/*/>*/}
                  </div>
                </div>

                {/*<Select*/}
                {/*  value={queryParams.subject || 'all'}*/}
                {/*  onValueChange={(value) => handleFilterChange('subject', value === 'all' ? '' : value)}*/}
                {/*>*/}
                {/*  <SelectTrigger className="w-32">*/}
                {/*    <SelectValue placeholder="选择学科" />*/}
                {/*  </SelectTrigger>*/}
                {/*  <SelectContent>*/}
                {/*    <SelectItem value="all">全部学科</SelectItem>*/}
                {/*    {subjects.map(subject => (*/}
                {/*      <SelectItem key={subject.code} value={subject.code}>*/}
                {/*        {subject.name}*/}
                {/*      </SelectItem>*/}
                {/*    ))}*/}
                {/*  </SelectContent>*/}
                {/*</Select>*/}

                {/*<Select*/}
                {/*  value={queryParams.is_active?.toString() || 'all'}*/}
                {/*  onValueChange={(value) => handleFilterChange('is_active', value === 'all' ? undefined : value === 'true')}*/}
                {/*>*/}
                {/*  <SelectTrigger className="w-32">*/}
                {/*    <SelectValue />*/}
                {/*  </SelectTrigger>*/}
                {/*  <SelectContent>*/}
                {/*    <SelectItem value="all">全部状态</SelectItem>*/}
                {/*    {SUBJECT_GROUP_STATUS_OPTIONS.map(option => (*/}
                {/*      <SelectItem key={option.value.toString()} value={option.value.toString()}>*/}
                {/*        {option.label}*/}
                {/*      </SelectItem>*/}
                {/*    ))}*/}
                {/*  </SelectContent>*/}
                {/*</Select>*/}

                {/*<Select*/}
                {/*  value={`${queryParams.order_by}-${queryParams.order_direction}`}*/}
                {/*  onValueChange={(value) => {*/}
                {/*    const [orderBy, orderDirection] = value.split('-');*/}
                {/*    handleFilterChange('order_by', orderBy);*/}
                {/*    handleFilterChange('order_direction', orderDirection);*/}
                {/*  }}*/}
                {/*>*/}
                {/*  <SelectTrigger className="w-36">*/}
                {/*    <SelectValue />*/}
                {/*  </SelectTrigger>*/}
                {/*  <SelectContent>*/}
                {/*    {SUBJECT_GROUP_SORT_OPTIONS.map(option => (*/}
                {/*      <React.Fragment key={option.value}>*/}
                {/*        <SelectItem value={`${option.value}-asc`}>*/}
                {/*          {option.label} ↑*/}
                {/*        </SelectItem>*/}
                {/*        <SelectItem value={`${option.value}-desc`}>*/}
                {/*          {option.label} ↓*/}
                {/*        </SelectItem>*/}
                {/*      </React.Fragment>*/}
                {/*    ))}*/}
                {/*  </SelectContent>*/}
                {/*</Select>*/}
              </div>
            </CardHeader>

            <CardContent>
              <SubjectGroupTable
                subjectGroups={subjectGroups}
                loading={loading}
                onEdit={handleEditSubjectGroup}
                onDelete={handleDeleteSubjectGroup}
                onToggleStatus={handleToggleStatus}
                pagination={undefined}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <SubjectGroupStatistics />
        </TabsContent>
      </Tabs>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除该学科组吗？此操作不可恢复。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteSubjectGroup}>
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Subject Group Form Modal */}
      <SubjectGroupForm
        subjectGroup={editingSubjectGroup}
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        loading={formLoading}
      />

    </div>
  );
};

export default SubjectGroupsManagementPage;

