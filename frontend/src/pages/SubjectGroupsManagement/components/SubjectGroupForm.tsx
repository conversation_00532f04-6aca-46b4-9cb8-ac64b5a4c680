import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

import { SubjectGroupFormProps, SubjectGroupFormData, SubjectGroupFormErrors } from '@/types/subjectGroup';
import { SubjectSummary } from '@/types/subject';
import { subjectApi } from '@/services/subjectApi';
import teachersApi from "@/services/teacherApi.ts";

const SubjectGroupForm: React.FC<SubjectGroupFormProps> = ({
  subjectGroup,
  open,
  onClose,
  onSubmit,
  loading = false,
}) => {
  const [formData, setFormData] = useState<SubjectGroupFormData>({
    group_name: '',
    subject_code: '',
    description: '',
    leader_user_id: '',
  });

  const [errors, setErrors] = useState<SubjectGroupFormErrors>({});
  const [subjects, setSubjects] = useState<SubjectSummary[]>([]);
  const [teachers, setTeachers] = useState<any[]>([]);

  // Load form data
  useEffect(() => {
    if (open) {
      loadFormData().then( );
      if (subjectGroup) {
        setFormData({
          group_name: subjectGroup.name,
          subject_code: subjectGroup.subject,
          description: subjectGroup.description || '',
          leader_user_id: subjectGroup.leader_id || '',
        });
      } else {
        setFormData({
          group_name: '',
          subject_code: '',
          description: '',
          leader_user_id: '',
        });
      }
      setErrors({});
    }
  }, [open, subjectGroup]);

  const loadFormData = async () => {
    try {
      const subjectsResponse = await subjectApi.getSubjectSummaries();
      if (subjectsResponse.success && subjectsResponse.data) {
        setSubjects(subjectsResponse.data);
      } else {
        setSubjects([]);
      }
      // 调用教师简要信息接口
      const teachersResponse = await teachersApi.getTeacherSummaries('tenant_zhanghan', true);
      if (teachersResponse.success && teachersResponse.data) {
        setTeachers(teachersResponse.data);
      } else {
        setTeachers([]);
      }
    } catch (error) {
      console.error('Failed to load form data:', error);
      toast.error('加载数据失败');
    }
  };

  const handleInputChange = (field: keyof SubjectGroupFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (field === 'group_name' && errors.name) {
      setErrors(prev => ({ ...prev, name: '' }));
    } else if (field === 'subject_code' && errors.subject) {
      setErrors(prev => ({ ...prev, subject: '' }));
    } else if (field === 'description' && errors.description) {
      setErrors(prev => ({ ...prev, description: '' }));
    } else if (field === 'leader_user_id' && errors.leader_user_id) {
      setErrors(prev => ({ ...prev, leader_user_id: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: SubjectGroupFormErrors = {};

    if (!formData.group_name.trim()) {
      newErrors.name = '请输入学科组名称';
    } else if (formData.group_name.trim().length < 2) {
      newErrors.name = '学科组名称至少2个字符';
    }

    if (!formData.subject_code) {
      newErrors.subject = '请选择学科';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      // 字段映射，确保与后端一致
      await onSubmit({
        group_name: formData.group_name.trim(),
        subject_code: formData.subject_code,
        description: formData.description?.trim() || undefined,
        leader_user_id: formData.leader_user_id && formData.leader_user_id !== 'all' ? formData.leader_user_id : undefined,
      });
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  const isEditing = !!subjectGroup;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? '编辑学科组' : '新增学科组'}
          </DialogTitle>
          <DialogDescription>
            {isEditing ? '修改学科组的基本信息' : '创建一个新的学科组，管理特定学科的教学工作'}
          </DialogDescription>
        </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              {/* 学科组名称 */}
              <div className="space-y-2">
                <Label htmlFor="name">学科组名称 *</Label>
                <Input
                  id="name"
                  value={formData.group_name}
                  onChange={(e) => handleInputChange('group_name', e.target.value)}
                  placeholder="请输入学科组名称"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              {/* 学科 */}
              <div className="space-y-2">
                <Label htmlFor="subject">学科 *</Label>
                <Select
                  value={formData.subject_code}
                  onValueChange={(value) => handleInputChange('subject_code', value)}
                >
                  <SelectTrigger className={errors.subject ? 'border-red-500' : ''}>
                    <SelectValue placeholder="请选择学科" />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.filter(s => s.is_active).map((subject) => (
                      <SelectItem key={subject.code} value={subject.code}>
                        {subject.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.subject && (
                  <p className="text-sm text-red-500">{errors.subject}</p>
                )}
              </div>
            </div>

            {/* 学科组组长 */}
            <div className="space-y-2">
              <Label htmlFor="leader_id">学科组组长</Label>
              <Select
                value={formData.leader_user_id}
                onValueChange={(value) => handleInputChange('leader_user_id', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择组长（可选）" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">不指定组长</SelectItem>
                  {teachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      {teacher.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 描述 */}
            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="请输入学科组描述（可选）"
                rows={3}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? '保存' : '创建'}
              </Button>
            </DialogFooter>
          </form>
      </DialogContent>
    </Dialog>
  );
};

export default SubjectGroupForm;