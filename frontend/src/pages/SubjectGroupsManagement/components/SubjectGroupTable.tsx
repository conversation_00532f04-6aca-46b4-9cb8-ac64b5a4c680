import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Edit, Trash2, Users, BookOpen } from 'lucide-react';
import { SubjectGroupTableProps } from '@/types/subjectGroup';
import {cn} from "@/lib/utils.ts";

const SubjectGroupTable: React.FC<SubjectGroupTableProps> = ({
  subjectGroups,
  loading = false,
  onEdit,
  onDelete
}) => {
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex space-x-4">
              <Skeleton className="h-12 w-full" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (subjectGroups.length === 0) {
    return (
      <div className="text-center py-12">
        <Users className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-semibold text-gray-900">暂无学科组</h3>
        <p className="mt-1 text-sm text-gray-500">开始创建第一个学科组吧</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>学科组名称</TableHead>
            <TableHead>学科</TableHead>
            <TableHead>组长</TableHead>
            <TableHead>状态</TableHead>
            <TableHead>描述</TableHead>
            <TableHead>创建时间</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {subjectGroups.map((subjectGroup) => (
            <TableRow key={String(subjectGroup.id)}>
              <TableCell className="font-medium">
                <div className="flex items-center space-x-2">
                  <BookOpen className="h-4 w-4 text-blue-500" />
                  <span>{subjectGroup.group_name}</span>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant="outline">{subjectGroup.subject_name || subjectGroup.subject_code}</Badge>
              </TableCell>
              <TableCell>
                {subjectGroup.teacher_name ? (
                  <span className="text-sm">{subjectGroup.teacher_name}</span>
                ) : (
                  <span className="text-sm text-gray-500">未指定</span>
                )}
              </TableCell>
              <TableCell>
                <Badge
                    variant={subjectGroup.is_active ? "default" : "secondary"}
                    className={cn(
                        "text-xs",
                        subjectGroup.is_active
                            ? "bg-green-100 text-green-800 hover:bg-green-200"
                            : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    )}
                >
                  {subjectGroup.is_active ? '启用' : '禁用'}
                </Badge>
              </TableCell>
              <TableCell>
                {subjectGroup.description ? (
                    <span className="text-sm">{subjectGroup.description}</span>
                ) : (
                    <span className="text-sm text-gray-500">暂无</span>
                )}
              </TableCell>
              <TableCell>
                <span className="text-sm text-gray-500">
                  {subjectGroup.created_at ? new Date(subjectGroup.created_at as string).toLocaleDateString('zh-CN') : '-'}
                </span>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(subjectGroup)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDelete(String(subjectGroup.id))}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

    </div>
  );
};

export default SubjectGroupTable;