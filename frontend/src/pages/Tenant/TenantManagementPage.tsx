import React, {useState, useEffect} from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {Button} from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {getTenants, createTenant, updateTenant, deleteTenant} from '@/services/tenantApi.ts';
import {Tenant} from '@/types/Tenant';
import {PlusCircle, Pencil, Trash2} from 'lucide-react';
import {toast} from "sonner";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"

const TenantManagementPage: React.FC = () => {
    const [tenants, setTenants] = useState<Tenant[]>([]);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [selectedTenant, setSelectedTenant] = useState<Partial<Tenant> | null>(null);
    const [codeError, setCodeError] = useState(false);

    const statusMap: Record<string, string> = {
        active: "启用",
        deleted: "已删除",
    };

    useEffect(() => {
        fetchTenants();
    }, []);

    const fetchTenants = async () => {
        try {
            const data = await getTenants();
            setTenants(data);
        } catch (error) {
            console.error("获取租户列表失败", error);
        }
    };

    const handleEdit = (tenant: Tenant) => {
        setSelectedTenant(tenant);
        setIsDialogOpen(true);
    };

    const handleDelete = async (tenantId: number) => {
        try {
            await deleteTenant(tenantId);
            console.log("删除租户", tenantId);
            fetchTenants();
        } catch (error) {
            console.error("删除租户失败", error);
        }
    };

    // 新增校验函数（放在组件外部）
    const isValidSchemaName = (name: string) => {
        if (!name) return false;
        return /^[a-z0-9_]+$/.test(name);
    };

    const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        if (isValidSchemaName(value)) {
            setCodeError(false);
        } else {
            setCodeError(true);
        }

        if (value.length===0)
            setCodeError(false);
        setSelectedTenant({ ...selectedTenant, schemaName: value });
    };

    const handleSave = async () => {
        if (!selectedTenant) return;

        try {
            if (selectedTenant.id) {
                await updateTenant(selectedTenant.id, selectedTenant);
                console.log("更新租户", selectedTenant);
                toast.info("更新成功")
            } else {
                await createTenant(selectedTenant as Omit<Tenant, 'id'>);
                console.log("创建租户", selectedTenant);
                toast.info("创建成功")
            }
            fetchTenants();
            setIsDialogOpen(false);
            setSelectedTenant(null);
        } catch (err: any) {
            toast.error("保存租户失败 " + err.response?.data?.message);
            console.error("保存租户失败", err);
        }
    };

    return (
        <div className="space-y-4">
            <Card>
                <CardHeader>
                    <div className="flex justify-between items-center">
                        <div>
                            <CardTitle>租户管理</CardTitle>
                            <CardDescription>管理系统中的所有租户机构。</CardDescription>
                        </div>
                        <Button onClick={() => {
                            setSelectedTenant({tenantType:'unknown'});
                            setIsDialogOpen(true);
                        }}>
                            <PlusCircle className="mr-2 h-4 w-4"/>
                            创建租户
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>名称</TableHead>
                                <TableHead>代码</TableHead>
                                <TableHead>schemaName</TableHead>
                                <TableHead>类型</TableHead>
                                <TableHead>状态</TableHead>
                                <TableHead className="text-right"><p className="mr-12">操作</p></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {tenants.map((tenant) => (
                                <TableRow key={tenant.id}>
                                    <TableCell>{tenant.name}</TableCell>
                                    <TableCell>{tenant.schemaName.replace(tenant.schemaName.split('_')[0]+'_','')}</TableCell>
                                    <TableCell>{tenant.schemaName}</TableCell>
                                    <TableCell>{tenant.tenantType}</TableCell>
                                    <TableCell>{statusMap[tenant.status] ?? "未知状态"}</TableCell>
                                    <TableCell className="text-right">
                                        <Button variant="ghost" size="icon" onClick={() => handleEdit(tenant)}>
                                            <Pencil className="h-4 w-4"/>
                                        </Button>
                                        <Button variant="ghost" size="icon"
                                                className="ml-2 text-red-500 hover:text-red-600"
                                                onClick={() => handleDelete(tenant.id)}>
                                            <Trash2 className="h-4 w-4"/>
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>{selectedTenant?.id ? '编辑租户' : '创建租户'}</DialogTitle>
                        <DialogDescription>
                            {selectedTenant?.id ? '编辑租户的详细信息。' : '创建一个新的租户。'}
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="name" className="text-right">
                                名称
                            </Label>
                            <Input
                                id="name"
                                value={selectedTenant?.name || ''}
                                onChange={(e) => setSelectedTenant({...selectedTenant, name: e.target.value})}
                                className="col-span-3"
                            />
                        </div>
                        {!selectedTenant?.id && <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="code" className="text-right">
                                代码
                            </Label>
                            <Input
                                id="code"
                                value={selectedTenant?.schemaName || ''}
                                onChange={handleCodeChange}
                                className="col-span-3"
                            />
                            {codeError && (
                                <p className="col-span-4 text-red-500 text-sm mt-1 ml-[25%]">
                                    代码只能包含小写字母、数字和下划线
                                </p>
                            )}
                        </div>}
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="tenantType" className="text-right">
                                类型
                            </Label>
                            <div className="col-span-3">
                                <Select
                                    value={selectedTenant?.tenantType || 'unknown'}
                                    onValueChange={(value) =>
                                        setSelectedTenant({ ...selectedTenant, tenantType: value as 'school' | 'unknown' })
                                    }
                                >
                                    <SelectTrigger id="tenantType">
                                        <SelectValue placeholder="请选择租户类型" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="standard">standard</SelectItem>
                                        <SelectItem value="school">学校</SelectItem>
                                        <SelectItem value="unknown">未知</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDialogOpen(false)}>取消</Button>
                        <Button onClick={handleSave}>保存</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default TenantManagementPage;
