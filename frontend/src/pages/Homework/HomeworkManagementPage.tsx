import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import {
  Plus,
  Edit,
  Trash2,
  Search,
  Calendar,
  Clock,
  Users,
  FileText,
  CheckCircle2,
  AlertCircle,
  Play,
  Square,
  Upload,
  RefreshCw,
  Eye,
  HelpCircle
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

import {
  homeworkApi
} from '@/services/homeworkApi';
import { getIdentityInfoFromLocalStorage } from '@/lib/apiUtils';
import { SubjectGroupsApi } from '@/services/subjectGroupsApi';
import { SubjectGroupsDetail } from '@/types/subjectGroups';
import { CreateHomeworkParams, Homework, HomeworkStatistics, HomeworkStatusEnum, PageHomeworkParams, UpdateHomeworkParams } from '@/types/homework';
import { GradeLevel } from '@/types/grade';
import EditHomework from './components/EditHomework';

/**
 * 作者：张瀚
 * 说明：作业管理页面（管理员版本）
 */
export const HomeworkManagementPage: React.FC = () => {
  const [statistics, setStatistics] = useState<HomeworkStatistics | undefined>(undefined);

  useEffect(() => {
    loadInitialData();
    loadSubjectGroups();
  }, []);

  /**
   * 作者：张瀚
   * 说明：加载初始数据
   */
  const loadInitialData = async () => {
    const [statsDataRes] = await Promise.all([
      homeworkApi.getStatistics(tenantId,tenantName),
      loadHomeworks()
    ]);
    console.log(statsDataRes);
    setStatistics(statsDataRes.data);

  };
  // State management
  const [homeworks, setHomeworks] = useState<Homework[]>([
    {
      id: '',
      homework_name: '',
      homework_status: HomeworkStatusEnum.Draft,
      subject_group_id: '',
      description: '',
      created_at: '',
      updated_at: '',
    }
  ]);

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [subjectGroupFilter, setSubjectGroupFilter] = useState('all');

  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedHomework, setSelectedHomework] = useState<Homework | null>(null);
  const [isDescriptionDialogOpen, setIsDescriptionDialogOpen] = useState(false);
  const [currentDescription, setCurrentDescription] = useState<string>('');

  // Form states
  const [subjects, setSubjects] = useState<any[]>([]);
  const [classes, setClasses] = useState<any[]>([]);
  const [subjectGroups, setSubjectGroups] = useState<SubjectGroupsDetail[]>([]);
  const [gradeLevels, setGradeLevels] = useState<GradeLevel[]>([]);

  //
  const [error, setError] = useState<string | null>(null);

  // Create homework form
  const [homeworkForm, setHomeworkForm] = useState<CreateHomeworkParams>({
    homework_name: '',
    homework_status: HomeworkStatusEnum.Draft,
    subject_group_id: undefined,
    description: undefined,
  });

  // Get tenant ID from auth context (mock for now)
  const identityInfo = getIdentityInfoFromLocalStorage();
  const tenantId = identityInfo?.tenant_id || '';
  const tenantName = identityInfo?.tenant_name || '';
  const navigate = useNavigate();



  //Load homeworks when filters change
  useEffect(() => {
    loadHomeworks();
  }, [currentPage, searchTerm, statusFilter, typeFilter, subjectGroupFilter]);



  const loadHomeworks = async () => {
    try {
      console.log("12312321",homeworks);
      
      const params: PageHomeworkParams = {
        page_params: {
          page: currentPage,
          page_size: 6,
        }
        //name: searchTerm || undefined,
        //status: statusFilter === 'all' ? undefined : statusFilter,
        //subject_group_id: subjectGroupFilter === 'all' ? undefined : subjectGroupFilter
      };
      homeworkApi.pageHomework(tenantId, tenantName,params).then(res=>{
        const { success, data, message, pagination } = res
        if (!success) {
          return setError(message)
        }
        setHomeworks(Array.isArray(data) ? data : []);
        setTotalPages(pagination.total_pages);
      })
    } catch (err) {
      console.error('Error loading homeworks:', err);
    }
  };
  const loadSubjectGroups = async () => {
    SubjectGroupsApi.findAll(tenantName).then(res => {
      const { success, data, message } = res
      if (!success) {
        return setError(message)
      }
      setSubjectGroups(Array.isArray(data) ? data : []);
    })
  }

  const handleCreateHomework = async () => {
    try {
      console.log("12312312");

      if (!homeworkForm.homework_name || !homeworkForm.homework_status) {
        console.error('Please fill in all required fields');
        return;
      }
      homeworkApi.createHomework(tenantId, tenantName, homeworkForm).then(res => {
        const { success, data, message } = res
        if (!success) {
          return setError(message)
        }
        setHomeworks(Array.isArray(data) ? data : []);
      })
      setIsCreateDialogOpen(false);
      resetForm();
      loadHomeworks();
      loadInitialData(); // Refresh statistics
    } catch (err) {
      console.error('Failed to create homework');
      console.error('Error creating homework:', err);
    }
  };

  const handleUpdateHomework = async () => {
    if (!selectedHomework) return;
    try {
      await homeworkApi.updateHomework(tenantId, tenantName, {
        id: selectedHomework.id,
        homework_name: homeworkForm.homework_name,
        homework_status: homeworkForm.homework_status,
        subject_group_id: homeworkForm.subject_group_id,
        description: homeworkForm.description,
      });
      setIsEditDialogOpen(false);
      setSelectedHomework(null);
      resetForm();
      loadHomeworks();
    } catch (err) {
      console.error('Failed to update homework');
      console.error('Error updating homework:', err);
    }
  };

  const handleDeleteHomework = async () => {
    if (!selectedHomework) return;

    try {
      await homeworkApi.deleteHomework(tenantId, selectedHomework.id);
      setIsDeleteDialogOpen(false);
      setSelectedHomework(null);
      loadHomeworks();
      loadInitialData(); // Refresh statistics
    } catch (err) {
      console.error('Failed to delete homework');
      console.error('Error deleting homework:', err);
    }
  };

  const handlePublishHomework = async (homeworkId: string) => {
    try {
      await homeworkApi.publishHomework(tenantId, homeworkId);
      loadHomeworks();
    } catch (err) {
      console.error('Failed to publish homework');
      console.error('Error publishing homework:', err);
    }
  };

  const handleStartHomework = async (homeworkId: string) => {
    try {
      await homeworkApi.startHomework(tenantId, homeworkId);
      loadHomeworks();
    } catch (err) {
      console.error('Failed to start homework');
      console.error('Error starting homework:', err);
    }
  };

  const handleCompleteHomework = async (homeworkId: string) => {
    try {
      await homeworkApi.completeHomework(tenantId, homeworkId);
      loadHomeworks();
    } catch (err) {
      console.error('Failed to complete homework');
      console.error('Error completing homework:', err);
    }
  };

  const resetForm = () => {
    setHomeworkForm({
      homework_name: '',
      homework_status: HomeworkStatusEnum.Draft,
      subject_group_id: undefined,
      description: '',
    });
  };

  const openEditDialog = (homework: Homework) => {
    setSelectedHomework(homework);
    setHomeworkForm({
      homework_name: homework.homework_name,
      homework_status: homework.homework_status as HomeworkStatusEnum,
      subject_group_id: homework.subject_group_id,
      description: homework.description || '',

    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (homework: Homework) => {
    setSelectedHomework(homework);
    setIsDeleteDialogOpen(true);
  };

  const getStatusBadge = (status: HomeworkStatusEnum) => {
    const statusConfig = {
      [HomeworkStatusEnum.Draft]: { variant: 'secondary' as const, label: '草稿', icon: FileText },
      [HomeworkStatusEnum.Doing]: { variant: 'default' as const, label: '处理中', icon: CheckCircle2 },
      [HomeworkStatusEnum.Done]: { variant: 'destructive' as const, label: '已完成', icon: Square },
      unknown: { variant: 'outline' as const, label: '未知状态', icon: HelpCircle }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.unknown;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getActionButtons = (homework: Homework) => {
    return (
      <div className="flex gap-1">
        <Button variant="ghost" size="sm" onClick={() => openEditDialog(homework)}>
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => navigate(`/homeworksDetail/${homework.id}`)}>
          <Eye className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => openDeleteDialog(homework)}>
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  //------------------------------------------------------------------组件部分
  function RefreshButton() {
    return <Button variant="outline" onClick={() => {
      loadInitialData()
      loadHomeworks()
    }}>
      <RefreshCw className="h-4 w-4 mr-2" />
      刷新
    </Button>
  }

  return (
    <div className="space-y-6 h-full flex flex-col">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">作业管理</h1>
          <p className="text-muted-foreground">创建、管理和监控作业</p>
        </div>
        <div className="flex gap-2">
          <RefreshButton />
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新建作业
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>新建作业</DialogTitle>
                <DialogDescription>
                  请填写作业的基本信息
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">作业名称 *</Label>
                    <Input
                      id="name"
                      value={homeworkForm.homework_name.toString()}
                      onChange={(e) => setHomeworkForm({ ...homeworkForm, homework_name: e.target.value })}
                      placeholder="请输入作业名称"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="homework_status">作业类型 *</Label>
                    <Select value={homeworkForm.homework_status.toString()} onValueChange={(value) => setHomeworkForm({ ...homeworkForm, homework_status: value as HomeworkStatusEnum })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择作业类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={HomeworkStatusEnum.Draft}>草稿</SelectItem>
                        <SelectItem value={HomeworkStatusEnum.Doing}>处理中</SelectItem>
                        <SelectItem value={HomeworkStatusEnum.Done}>已完成</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="subject_group_id">学科组</Label>
                    <Select value={homeworkForm.subject_group_id?.toString()} onValueChange={(value) => setHomeworkForm({ ...homeworkForm, subject_group_id: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择学科" />
                      </SelectTrigger>
                      <SelectContent>
                        {subjectGroups.map(subjectGroup => (
                          <SelectItem key={subjectGroup.id.toString()} value={subjectGroup.id.toString()}>{subjectGroup.group_name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">作业说明</Label>
                  <Textarea
                    id="description"
                    value={homeworkForm.description?.toString() || ''}
                    onChange={(e) => setHomeworkForm({ ...homeworkForm, description: e.target.value })}
                    placeholder="请输入作业说明"
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateHomework}>
                  创建作业
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总作业数</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.total_homeworks || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">进行中作业</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.in_progress_homeworks || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成作业</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.completed_homeworks || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总参与学生</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics?.total_students || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索作业..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="draft">草稿</SelectItem>
            <SelectItem value="Started">已发布</SelectItem>
            <SelectItem value="Reviewing">进行中</SelectItem>
            <SelectItem value="Done">已完成</SelectItem>
          </SelectContent>
        </Select>
        <Select value={subjectGroupFilter} onValueChange={setSubjectGroupFilter}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部学科组</SelectItem>
            {subjectGroups.map(subjectGroup => (
              <SelectItem key={subjectGroup.id.toString()} value={subjectGroup.id.toString()}>{subjectGroup.group_name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Homework List */}
      <Card className="flex flex-col flex-grow">
        <CardHeader>
          <CardTitle>作业列表</CardTitle>
          <CardDescription>
            当前共有 {homeworks.length} 份作业
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-grow min-h-[200px]">
          <div className="h-full">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>作业名称</TableHead>
                  <TableHead>学科组</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>参与学生</TableHead>
                  <TableHead>描述</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {homeworks.map((homework) => (
                  <TableRow key={homework.id.toString()}>
                    <TableCell className="font-medium">{homework.homework_name}</TableCell>
                    <TableCell>{homework.subject_group_id}</TableCell>
                    <TableCell>{getStatusBadge(homework.homework_status)}</TableCell>
                    <TableCell>{homework.created_at ? new Date(homework.created_at.toString()).toLocaleDateString() : '-'}</TableCell>
                    <TableCell>0人</TableCell>
                    <TableCell>
                      <Button variant="link" className="p-0 h-auto min-h-0 text-blue-600 font-bold" onClick={() => {
                        setCurrentDescription(typeof homework.description === 'string' ? homework.description : (homework.description ? homework.description.toString() : ''));
                        setIsDescriptionDialogOpen(true);
                      }}>
                        查看
                      </Button>
                    </TableCell>
                    <TableCell>{getActionButtons(homework)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setCurrentPage(currentPage > 1 ? currentPage - 1 : 1);
                  }}
                />
              </PaginationItem>
              {[...Array(totalPages)].map((_, i) => (
                <PaginationItem key={i}>
                  <PaginationLink
                    href="#"
                    isActive={currentPage === i + 1}
                    onClick={(e) => {
                      e.preventDefault();
                      setCurrentPage(i + 1);
                    }}
                  >
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}
              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setCurrentPage(currentPage < totalPages ? currentPage + 1 : totalPages);
                  }}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
      {/* Edit Dialog */}
      <EditHomework
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        homeworkForm={homeworkForm as UpdateHomeworkParams}
        onHomeworkFormChange={setHomeworkForm}
        subjectGroups={subjectGroups}
        onSave={handleUpdateHomework}
        onCancel={() => setIsEditDialogOpen(false)}
      />

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              你确定要删除作业 {selectedHomework?.homework_name} 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteHomework}>
              删除
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Description Dialog */}
      <Dialog open={isDescriptionDialogOpen} onOpenChange={setIsDescriptionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>作业描述</DialogTitle>
          </DialogHeader>
          <div style={{ whiteSpace: 'pre-wrap' }}>{currentDescription || ''}</div>
          <div className="flex justify-end mt-4">
            <Button variant="outline" onClick={() => setIsDescriptionDialogOpen(false)}>
              关闭
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
