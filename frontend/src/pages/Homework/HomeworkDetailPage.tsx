import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Upload,
  <PERSON>an,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Eye,
  Play,
  Pause,
  RefreshCw,
  FileText,
  Settings,
  BarChart3,
  TrendingUp,
  Zap,
  FileImage,
  CircleChevronLeft,
  ChevronRight
} from 'lucide-react';

import { 
  paperScanApi, 
  gradingTaskApi, 
  exceptionApi, 
  monitoringApi, 
  gradingControlApi,
  PaperScan,
  PaperScanStats,
  GradingStats,
  GraderStats,
  StudentException,
  ExceptionStats,
  QualityAlert,
  AIGradingStats
} from '@/services/gradingApi';
import { useParams, useNavigate } from 'react-router-dom';
import { getHomeworkById } from '../../services/homeworkService';
import { Homework, HomeworkStatusEnum } from '../../types/homework';
import HomeworkScan from './HomeworkScan'
import HomeworkReview from './HomeworkReview'
import HomeworkReport from './HomeworkReport'

const HomeworkDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [homework, setHomework] = useState<Homework | null>({
    id: 'mock-1',
    homework_name: '期中作业1',
    homework_status: HomeworkStatusEnum.Draft,
    description: '这是一个mock的期中作业',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  });
  // State management
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Data states
  const [scanStats, setScanStats] = useState<PaperScanStats | null>(null);
  const [gradingStats, setGradingStats] = useState<GradingStats | null>(null);
  const [graderStats, setGraderStats] = useState<GraderStats[]>([]);
  const [exceptionStats, setExceptionStats] = useState<ExceptionStats | null>(null);
  const [qualityAlerts, setQualityAlerts] = useState<QualityAlert[]>([]);
  const [aiGradingStats, setAIGradingStats] = useState<AIGradingStats | null>(null);
  
  // Paper scan states
  const [paperScans, setPaperScans] = useState<PaperScan[]>([]);

  // Exception management states
  const [studentExceptions, setStudentExceptions] = useState<StudentException[]>([]);
  const [isExceptionDialogOpen, setIsExceptionDialogOpen] = useState(false);
  const [selectedException, setSelectedException] = useState<StudentException | null>(null);
  const [manualStudentNumber, setManualStudentNumber] = useState('');
  
  // Grading control states
  const [isControlDialogOpen, setIsControlDialogOpen] = useState(false);
  
  // Upload states
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [uploadFiles, setUploadFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Mock exam ID - in real app, this would come from route params or context
  const examId = '00000000-0000-0000-0000-000000000001';

  // useEffect(() => {
  //   const fetchHomework = async () => {
  //     if (!id) return;
  //     try {
  //       const data = await getHomeworkById(id);
  //       setHomework(data);
  //     } catch (err) {
  //       setError('Failed to fetch Homework details');
  //     } finally {
  //       setLoading(false);
  //     }
  //   }
  //   fetchHomework();
  // }, [id]);


  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [
        scanStatsData,
        gradingStatsData,
        graderStatsData,
        exceptionStatsData,
        qualityAlertsData,
        aiGradingStatsData
      ] = await Promise.all([
        paperScanApi.getScanStats(examId),
        gradingTaskApi.getGradingStats(examId),
        gradingTaskApi.getGraderStats(examId),
        exceptionApi.getExceptionStats(examId),
        monitoringApi.getQualityAlerts(examId),
        gradingTaskApi.getAIGradingStats(examId)
      ]);
      
      setScanStats(scanStatsData);
      setGradingStats(gradingStatsData);
      setGraderStats(graderStatsData);
      setExceptionStats(exceptionStatsData);
      setQualityAlerts(qualityAlertsData);
      setAIGradingStats(aiGradingStatsData);
      setError(null);
    } catch (err) {
      setError('Failed to load grading center data');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadPaperScans = async () => {
    try {
      const scans = await paperScanApi.getPaperScans(examId);
      setPaperScans(scans);
    } catch (err) {
      setError('Failed to load paper scans');
      console.error('Error loading paper scans:', err);
    }
  };

  const loadStudentExceptions = async () => {
    try {
      const exceptions = await exceptionApi.getStudentExceptions(examId);
      setStudentExceptions(exceptions);
    } catch (err) {
      setError('Failed to load student exceptions');
      console.error('Error loading student exceptions:', err);
    }
  };

  const handleStartGrading = async () => {
    try {
      await gradingControlApi.startGrading(examId);
      await loadInitialData();
      setIsControlDialogOpen(false);
    } catch (err) {
      setError('Failed to start grading');
      console.error('Error starting grading:', err);
    }
  };

  const handlePauseGrading = async () => {
    try {
      await gradingControlApi.pauseGrading(examId, '用户手动暂停');
      await loadInitialData();
      setIsControlDialogOpen(false);
    } catch (err) {
      setError('Failed to pause grading');
      console.error('Error pausing grading:', err);
    }
  };

  const handleResumeGrading = async () => {
    try {
      await gradingControlApi.resumeGrading(examId);
      await loadInitialData();
      setIsControlDialogOpen(false);
    } catch (err) {
      setError('Failed to resume grading');
      console.error('Error resuming grading:', err);
    }
  };

  const handleStartAIGrading = async () => {
    try {
      await gradingTaskApi.startAIGrading(examId);
      await loadInitialData();
    } catch (err) {
      setError('Failed to start AI grading');
      console.error('Error starting AI grading:', err);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      setUploadFiles(Array.from(files));
    }
  };

  const handleUploadConfirm = async () => {
    if (uploadFiles.length === 0) return;
    
    try {
      setUploadProgress(0);
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);
      
      // Mock upload - in real app, this would upload to MinIO
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      setIsUploadDialogOpen(false);
      setUploadFiles([]);
      setUploadProgress(0);
      loadInitialData();
    } catch (err) {
      setError('Failed to upload files');
      console.error('Error uploading files:', err);
    }
  };

  const handleManualResolve = () => {
    if (selectedException && manualStudentNumber) {
      handleResolveException(selectedException.id, {
        resolution_method: 'manual_input',
        correct_student_id: manualStudentNumber,
        resolution_notes: `手动输入学号：${manualStudentNumber}`
      });
    }
  };

  const handleResolveException = async (exceptionId: string, resolution: any) => {
    try {
      await exceptionApi.resolveStudentException(exceptionId, resolution);
      await loadStudentExceptions();
      setIsExceptionDialogOpen(false);
      setSelectedException(null);
    } catch (err) {
      setError('Failed to resolve exception');
      console.error('Error resolving exception:', err);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, label: '待处理', icon: Clock },
      processing: { variant: 'outline' as const, label: '处理中', icon: RefreshCw },
      completed: { variant: 'default' as const, label: '已完成', icon: CheckCircle2 },
      exception: { variant: 'destructive' as const, label: '异常', icon: AlertTriangle }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getQualityBadge = (quality: number) => {
    if (quality >= 0.9) return <Badge variant="default">优秀</Badge>;
    if (quality >= 0.7) return <Badge variant="secondary">良好</Badge>;
    if (quality >= 0.5) return <Badge variant="outline">一般</Badge>;
    return <Badge variant="destructive">差</Badge>;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-4 w-48" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          {/* 面包屑导航 */}
          <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-auto font-normal text-muted-foreground hover:text-foreground"
              onClick={() => navigate('/homework-management')}
            >
              作业管理
            </Button>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground font-medium">{homework?.homework_name}</span>
          </nav>
          <h1 className="text-3xl font-bold">{homework?.homework_name}</h1>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadInitialData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Dialog open={isControlDialogOpen} onOpenChange={setIsControlDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Settings className="h-4 w-4 mr-2" />
                阅卷控制
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>阅卷控制</DialogTitle>
                <DialogDescription>
                  管理阅卷流程的启动、暂停和恢复
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Button onClick={handleStartGrading} disabled={loading}>
                    <Play className="h-4 w-4 mr-2" />
                    {loading ? 'Starting...' : '开始阅卷'}
                  </Button>
                  <Button onClick={handlePauseGrading} disabled={loading}>
                    <Pause className="h-4 w-4 mr-2" />
                    {loading ? 'Pausing...' : '暂停阅卷'}
                  </Button>
                  <Button onClick={handleResumeGrading} disabled={loading}>
                    <Play className="h-4 w-4 mr-2" />
                    {loading ? 'Resuming...' : '恢复阅卷'}
                  </Button>
                  <Button onClick={handleStartAIGrading} disabled={loading}>
                    <Zap className="h-4 w-4 mr-2" />
                    {loading ? 'Starting AI...' : '启动AI阅卷'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="scanning">试卷扫描</TabsTrigger>
          <TabsTrigger value="grading">智能阅卷</TabsTrigger>
          <TabsTrigger value="monitoring">教学报告</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">扫描进度</CardTitle>
                <Scan className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{scanStats?.total_scanned || 0}</div>
                <div className="text-xs text-muted-foreground">
                  异常: {scanStats?.exception_count || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">阅卷进度</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{gradingStats?.completed_papers || 0}</div>
                <div className="text-xs text-muted-foreground">
                  总计: {gradingStats?.total_papers || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">AI阅卷率</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {((gradingStats?.ai_grading_ratio || 0) * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">
                  置信度: {((aiGradingStats?.average_confidence || 0) * 100).toFixed(1)}%
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">异常处理</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{exceptionStats?.pending_exceptions || 0}</div>
                <div className="text-xs text-muted-foreground">
                  总计: {exceptionStats?.total_exceptions || 0}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Progress Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>阅卷进度</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>总体进度</span>
                      <span>{((gradingStats?.grading_progress || 0) * 100).toFixed(1)}%</span>
                    </div>
                    <Progress value={(gradingStats?.grading_progress || 0) * 100} />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>人工复核</span>
                      <span>{((gradingStats?.manual_review_ratio || 0) * 100).toFixed(1)}%</span>
                    </div>
                    <Progress value={(gradingStats?.manual_review_ratio || 0) * 100} />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>质量告警</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {qualityAlerts.slice(0, 3).map((alert) => (
                    <div key={alert.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm">{alert.message}</span>
                      </div>
                      <Badge variant={alert.severity === 'high' ? 'destructive' : 'secondary'}>
                        {alert.severity}
                      </Badge>
                    </div>
                  ))}
                  {qualityAlerts.length === 0 && (
                    <div className="text-center py-4 text-muted-foreground">
                      暂无质量告警
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="scanning" className="space-y-6">
          <HomeworkScan></HomeworkScan>
        </TabsContent>

        <TabsContent value="grading" className="space-y-6">
         <HomeworkReview></HomeworkReview>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-6">
          <HomeworkReport></HomeworkReport>
        </TabsContent>
      </Tabs>

      {/* Exception Resolution Dialog */}
      <Dialog open={isExceptionDialogOpen} onOpenChange={setIsExceptionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>处理学号异常</DialogTitle>
            <DialogDescription>
              为学号 {selectedException?.student_number} 选择正确的学生
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>可能的学生</Label>
              <div className="max-h-48 overflow-y-auto space-y-2">
                {selectedException?.possible_students.map((student, index) => (
                  <div key={index} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <div className="font-medium">{student.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {student.student_number} · {student.class_name}
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleResolveException(selectedException.id, {
                        resolution_method: 'manual_bind',
                        correct_student_id: student.id,
                        resolution_notes: `手动绑定到学生：${student.name}`
                      })}
                    >
                      选择
                    </Button>
                  </div>
                ))}
              </div>
            </div>
            <Separator />
            <div className="space-y-2">
              <Label>手动输入学号</Label>
              <Input placeholder="请输入正确的学号" value={manualStudentNumber} onChange={(e) => setManualStudentNumber(e.target.value)} />
              <Button variant="outline" size="sm" onClick={handleManualResolve}>
                确认绑定
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default HomeworkDetailPage;
