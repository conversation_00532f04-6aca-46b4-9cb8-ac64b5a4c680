import React, { useState, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Users,
  UserCheck,
  UserX,
  TrendingUp,
  Eye,
  ArrowUpDown,
  FileText,
} from "lucide-react";

// 类型定义
interface ClassOverview {
  participantCount: number;
  recognizedPapers: number;
  absentCount: number;
  averageScore: number;
  totalScore: number;
  totalStudents: number;
}

interface StudentDetail {
  id: string;
  studentNumber: string;
  studentName: string;
  status: "completed" | "absent";
  totalScore: number;
  questionScores: number[];
  originalPapers: string[];
}

// Mock数据
const mockClassOverview: ClassOverview = {
  participantCount: 48,
  recognizedPapers: 46,
  absentCount: 2,
  averageScore: 82.3,
  totalScore: 100,
  totalStudents: 50,
};

const mockStudentDetails: StudentDetail[] = [
  {
    id: "1",
    studentNumber: "2023001",
    studentName: "张三",
    status: "completed",
    totalScore: 85,
    questionScores: [4, 3, 8, 18, 13, 9, 28, 6],
    originalPapers: [
      "http://*************:8173/static/image/1753692963655/score_area/2320511/68883b77eab0ec114ea9eff7.jpg",
      "http://*************:8173/static/image/1753692963655/score_area/2320535/68883b76eab0ec114ea9efa2.jpg",
    ],
  },
  {
    id: "2",
    studentNumber: "2023002",
    studentName: "李四",
    status: "completed",
    totalScore: 78,
    questionScores: [5, 2, 7, 16, 12, 8, 26, 5],
    originalPapers: [
      "http://*************:8173/static/image/1753692963655/score_area/2320511/68883b77eab0ec114ea9eff7.jpg",
    ],
  },
  {
    id: "3",
    studentNumber: "2023003",
    studentName: "王五",
    status: "absent",
    totalScore: 0,
    questionScores: [0, 0, 0, 0, 0, 0, 0, 0],
    originalPapers: [],
  },
];

const questionTitles = [
  "第1题",
  "第2题",
  "第3题",
  "第4题",
  "第5题",
  "第6题",
  "第7题",
  "第8题",
];

const ClassReport: React.FC = () => {
  const { classId } = useParams<{ classId: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const className = location.state?.className || "班级报告";

  const [classOverview] = useState<ClassOverview>(mockClassOverview);
  const [studentDetails, setStudentDetails] =
    useState<StudentDetail[]>(mockStudentDetails);
  const [sortConfig, setSortConfig] = useState<{
    key: keyof StudentDetail | "totalScore";
    direction: "asc" | "desc";
  } | null>(null);

  useEffect(() => {
    // 这里可以调用API获取班级报告数据
    console.log("加载班级报告数据:", classId);
  }, [classId]);

  // 排序功能
  const handleSort = (key: keyof StudentDetail | "totalScore") => {
    let direction: "asc" | "desc" = "asc";
    if (
      sortConfig &&
      sortConfig.key === key &&
      sortConfig.direction === "asc"
    ) {
      direction = "desc";
    }
    setSortConfig({ key, direction });

    const sortedData = [...studentDetails].sort((a, b) => {
      if (key === "totalScore") {
        return direction === "asc"
          ? a.totalScore - b.totalScore
          : b.totalScore - a.totalScore;
      }
      if (key === "studentNumber") {
        return direction === "asc"
          ? a.studentNumber.localeCompare(b.studentNumber)
          : b.studentNumber.localeCompare(a.studentNumber);
      }
      if (key === "status") {
        return direction === "asc"
          ? a.status.localeCompare(b.status)
          : b.status.localeCompare(a.status);
      }
      return 0;
    });
    setStudentDetails(sortedData);
  };

  // 返回教学报告
  const handleBackToReport = () => {
    navigate("/homework/report");
  };

  return (
    <div className="space-y-6">
      {/* 面包屑导航 */}
      <div className="flex items-center space-x-2 text-sm">
        <button
          className="text-primary hover:underline"
          onClick={handleBackToReport}
        >
          教学报告
        </button>
        <span className="text-muted-foreground">/</span>
        <span className="text-muted-foreground">{className}</span>
      </div>

      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold">{className}班级报告</h1>
      </div>

      {/* 数据看板 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="text-sm text-muted-foreground">参与人数</div>
              <Users className="h-4 w-4 text-green-600" />
            </div>
            <div className="text-2xl font-bold">
              {classOverview.participantCount}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="text-sm text-muted-foreground">已识别答题卡</div>
              <FileText className="h-4 w-4 text-purple-600" />
            </div>
            <div className="text-2xl font-bold">
              {classOverview.recognizedPapers}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="text-sm text-muted-foreground">班级平均分</div>
              <TrendingUp className="h-4 w-4 text-orange-600" />
            </div>
            <div className="text-2xl font-bold">
              {classOverview.averageScore}/{classOverview.totalScore}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="text-sm text-muted-foreground">缺考人数</div>
              <UserX className="h-4 w-4 text-red-600" />
            </div>
            <div className="text-2xl font-bold">
              {classOverview.absentCount}
            </div>
          </CardContent>
        </Card>

      </div>

      {/* 学生详情 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <div>学生详情</div>
            <Button size='sm'>导出班级报告</Button>
          </CardTitle>
          
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">序号</TableHead>
                  <TableHead className="min-w-24">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort("studentNumber")}
                      className="h-auto p-0 font-medium"
                    >
                      学号
                      <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-20">学生姓名</TableHead>
                  <TableHead className="min-w-20">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort("status")}
                      className="h-auto p-0 font-medium"
                    >
                      状态
                      <ArrowUpDown className="ml-1 h-3 w-3" />
                    </Button>
                  </TableHead>
                  <TableHead className="min-w-32">
                    <div className="text-center">
                      <div>得分明细</div>
                      <div className="flex border-t mt-1 pt-1">
                        <div className="min-w-16 border-r pr-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSort("totalScore")}
                            className="h-auto p-0 font-medium text-xs"
                          >
                            得分
                            <ArrowUpDown className="ml-1 h-2 w-2" />
                          </Button>
                        </div>
                        <div className="flex-1 overflow-x-auto">
                          <div className="flex min-w-max">
                            {questionTitles.map((title, index) => (
                              <div
                                key={index}
                                className="min-w-12 text-xs text-center px-1"
                              >
                                {title}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </TableHead>
                  <TableHead className="min-w-20">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {studentDetails.map((student, index) => (
                  <TableRow key={student.id}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{student.studentNumber}</TableCell>
                    <TableCell>{student.studentName}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          student.status === "completed"
                            ? "default"
                            : "destructive"
                        }
                      >
                        {student.status === "completed" ? "阅卷完成" : "缺考"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex">
                        <div className="min-w-16 border-r pr-2 font-medium">
                          {student.totalScore}
                        </div>
                        <div className="flex-1 overflow-x-auto">
                          <div className="flex min-w-max">
                            {student.questionScores.map((score, qIndex) => (
                              <div
                                key={qIndex}
                                className="min-w-12 text-center px-1 text-sm"
                              >
                                {score}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {student.status === "completed" &&
                        student.originalPapers.length > 0 && (
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                size="sm"
                                variant="outline"
                                className="gap-1"
                              >
                                <Eye className="h-3 w-3" />
                                查看原卷
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-[50vw] max-h-[80vh]">
                              <DialogHeader>
                                <DialogTitle>
                                  {student.studentName} - 原卷
                                </DialogTitle>
                              </DialogHeader>
                              <ScrollArea className="h-[60vh]">
                                <div className="space-y-4">
                                  {student.originalPapers.map(
                                    (paperUrl, paperIndex) => (
                                      <img
                                        key={paperIndex}
                                        src={paperUrl}
                                        alt={`${student.studentName}答题卡${
                                          paperIndex + 1
                                        }`}
                                        className="w-full h-auto"
                                      />
                                    )
                                  )}
                                </div>
                              </ScrollArea>
                            </DialogContent>
                          </Dialog>
                        )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ClassReport;
