import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FileText,
  Users,
  UserCheck,
  UserX,
  TrendingUp,
  Download,
  Eye,
  BookOpen,
} from "lucide-react";

// 类型定义
interface OverallStats {
  classCount: number;
  participantCount: number;
  recognizedPapers: number;
  absentCount: number;
  averageScore: number;
  totalScore: number;
  totalStudents: number;
}

interface QuestionStats {
  id: string;
  questionNumber: string;
  questionType: "objective" | "subjective";
  averageScore: number;
  totalScore: number;
}

interface ClassStats {
  id: string;
  className: string;
  averageScore: number;
  studentCount: number;
  absentCount: number;
}

// Mock数据
const mockOverallStats: OverallStats = {
  classCount: 3,
  participantCount: 142,
  recognizedPapers: 138,
  absentCount: 8,
  averageScore: 78.5,
  totalScore: 100,
  totalStudents: 150,
};

const mockQuestionStats: QuestionStats[] = [
  {
    id: "1",
    questionNumber: "第1题",
    questionType: "objective",
    averageScore: 4.2,
    totalScore: 5,
  },
  {
    id: "2",
    questionNumber: "第2题",
    questionType: "objective",
    averageScore: 2.8,
    totalScore: 3,
  },
  {
    id: "3",
    questionNumber: "第3题",
    questionType: "subjective",
    averageScore: 7.5,
    totalScore: 10,
  },
  {
    id: "4",
    questionNumber: "第4题",
    questionType: "subjective",
    averageScore: 16.2,
    totalScore: 20,
  },
  {
    id: "5",
    questionNumber: "第5题",
    questionType: "subjective",
    averageScore: 12.8,
    totalScore: 15,
  },
  {
    id: "6",
    questionNumber: "第6题",
    questionType: "objective",
    averageScore: 8.9,
    totalScore: 10,
  },
  {
    id: "7",
    questionNumber: "第7题",
    questionType: "subjective",
    averageScore: 25.6,
    totalScore: 30,
  },
  {
    id: "8",
    questionNumber: "第8题",
    questionType: "objective",
    averageScore: 6.3,
    totalScore: 7,
  },
];

const mockClassStats: ClassStats[] = [
  {
    id: "1",
    className: "高三(1)班",
    averageScore: 82.3,
    studentCount: 48,
    absentCount: 2,
  },
  {
    id: "2",
    className: "高三(2)班",
    averageScore: 76.8,
    studentCount: 50,
    absentCount: 3,
  },
  {
    id: "3",
    className: "高三(3)班",
    averageScore: 76.4,
    studentCount: 52,
    absentCount: 3,
  },
];

const HomeworkReport: React.FC = () => {
  const navigate = useNavigate();
  const [overallStats] = useState<OverallStats>(mockOverallStats);
  const [questionStats] = useState<QuestionStats[]>(mockQuestionStats);
  const [classStats] = useState<ClassStats[]>(mockClassStats);

  useEffect(() => {
    // 这里可以调用API获取数据
    console.log("加载教学报告数据");
  }, []);

  // 导出整体报告
  const handleExportOverallReport = async () => {
    try {
      console.log("导出整体报告");
      // 调用API
      // const response = await reportApi.exportOverallReport();
      alert("整体报告导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      alert("导出失败");
    }
  };

  // 查看班级报告
  const handleViewClassReport = (classId: string, className: string) => {
    navigate(`/homeworks/class-report/${classId}`, {
      state: { className },
    });
  };

  // 导出班级报告
  const handleExportClassReport = async (
    classId: string,
    className: string
  ) => {
    try {
      console.log("导出班级报告:", classId, className);
      // 调用API
      // const response = await reportApi.exportClassReport(classId);
      alert(`${className}报告导出成功`);
    } catch (error) {
      console.error("导出失败:", error);
      alert("导出失败");
    }
  };

  // 导出整体班级报告
  const handleExportAllClassReports = async () => {
    try {
      console.log("导出整体班级报告");
      // 调用API
      // const response = await reportApi.exportAllClassReports();
      alert("整体班级报告导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      alert("导出失败");
    }
  };

  return (
    <div className="space-y-6">
      {/* 整体统计 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div>
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">班级数量</div>
                <BookOpen className="h-4 w-4 text-blue-600" />
              </div>
              <div className="text-2xl font-bold">
                {overallStats.classCount}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div>
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">参与人数</div>
                <Users className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {overallStats.participantCount}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="text-sm text-muted-foreground">已识别答题卡</div>
              <FileText className="h-4 w-4 text-purple-600" />
            </div>
            <div className="text-2xl font-bold">
              {overallStats.recognizedPapers}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="text-sm text-muted-foreground">缺考人数</div>
              <UserX className="h-4 w-4 text-red-600" />
            </div>
            <div className="text-2xl font-bold">{overallStats.absentCount}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="text-sm text-muted-foreground">平均分</div>
              <TrendingUp className="h-4 w-4 text-orange-600" />
            </div>
            <div className="text-2xl font-bold">
              {overallStats.averageScore}/{overallStats.totalScore}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 试题详情 */}
      <Card>
        <CardHeader>
          <CardTitle>试题详情</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {questionStats.map((question) => (
              <Card key={question.id} className="border shadow-sm">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold">
                        {question.questionNumber}
                      </h3>
                      <Badge
                        variant={
                          question.questionType === "objective"
                            ? "secondary"
                            : "default"
                        }
                      >
                        {question.questionType === "objective"
                          ? "客观题"
                          : "主观题"}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      平均分:{" "}
                      <span className="font-medium text-foreground">
                        {question.averageScore}/{question.totalScore}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 班级详情 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>班级详情</CardTitle>
            <Button
              onClick={handleExportAllClassReports}
              variant="outline"
              className="gap-2"
            >
              <Download className="h-4 w-4" />
              导出整体报告
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>班级名称</TableHead>
                <TableHead>平均分</TableHead>
                <TableHead>学生人数</TableHead>
                <TableHead>缺考人数</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {classStats.map((classItem) => (
                <TableRow key={classItem.id}>
                  <TableCell className="font-medium">
                    {classItem.className}
                  </TableCell>
                  <TableCell>{classItem.averageScore}</TableCell>
                  <TableCell>{classItem.studentCount}</TableCell>
                  <TableCell>{classItem.absentCount}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          handleViewClassReport(
                            classItem.id,
                            classItem.className
                          )
                        }
                        className="gap-1"
                      >
                        <Eye className="h-3 w-3" />
                        查看班级报告
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          handleExportClassReport(
                            classItem.id,
                            classItem.className
                          )
                        }
                        className="gap-1"
                      >
                        <Download className="h-3 w-3" />
                        导出班级报告
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default HomeworkReport;
