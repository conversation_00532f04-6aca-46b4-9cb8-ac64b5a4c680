import { RouterProvider } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { PermissionProvider } from './contexts/PermissionContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { routerWithPermissions } from './router/indexWithPermissions';
import { Toaster } from "sonner";

function App() {
    return (
        <ThemeProvider>
            <AuthProvider>
                <PermissionProvider>
                    <RouterProvider router={routerWithPermissions} />
                    <Toaster position="top-center" />
                </PermissionProvider>
            </AuthProvider>
        </ThemeProvider>
    );
}

export default App;