/**
 * 菜单角色权限 API 服务
 * 提供菜单与角色权限关系管理的 API 调用接口
 */

import apiClient from './apiClient';
import { ApiResponse } from '@/types';

// 角色信息
export interface RoleInfo {
  id: string;
  name: string;
  code: string;
  category: string;
  level: number;
}

// 菜单角色权限响应
export interface MenuRolePermissionResponse {
  menu_id: string;
  roles: RoleInfo[];
}

// 设置菜单角色权限请求
export interface SetMenuRolePermissionRequest {
  role_ids: string[];
}

// 菜单角色权限查询参数
export interface MenuRoleQueryParams {
  tenant_id?: string;
}

/**
 * 菜单角色权限 API
 */
export const menuRoleApi = {
  /**
   * 获取菜单的角色权限配置
   */
  async getMenuRolePermissions(
    menuId: string, 
    params?: MenuRoleQueryParams
  ): Promise<ApiResponse<MenuRolePermissionResponse>> {
    const response = await apiClient.get(`/api/v1/permissions/menus/${menuId}/roles`, { 
      params 
    });
    return response;
  },

  /**
   * 设置菜单的角色权限配置
   */
  async setMenuRolePermissions(
    menuId: string, 
    request: SetMenuRolePermissionRequest
  ): Promise<ApiResponse<string>> {
    const response = await apiClient.put(`/api/v1/permissions/menus/${menuId}/roles`, request);
    return response;
  },

  /**
   * 添加单个角色的菜单权限
   */
  async addRoleMenuPermission(
    menuId: string, 
    roleId: string
  ): Promise<ApiResponse<string>> {
    const response = await apiClient.post(`/api/v1/permissions/menus/${menuId}/roles/${roleId}`);
    return response;
  },

  /**
   * 移除单个角色的菜单权限
   */
  async removeRoleMenuPermission(
    menuId: string, 
    roleId: string
  ): Promise<ApiResponse<string>> {
    const response = await apiClient.delete(`/api/v1/permissions/menus/${menuId}/roles/${roleId}`);
    return response;
  },

  /**
   * 检查角色是否有菜单访问权限
   */
  async checkRoleMenuPermission(
    menuId: string, 
    roleId: string
  ): Promise<ApiResponse<boolean>> {
    const response = await apiClient.get(`/api/v1/permissions/menus/${menuId}/roles/${roleId}/check`);
    return response;
  },

  /**
   * 获取角色的所有菜单权限
   */
  async getRoleMenuPermissions(roleId: string): Promise<ApiResponse<string[]>> {
    const response = await apiClient.get(`/api/v1/permissions/roles/${roleId}/menus`);
    return response;
  },

  /**
   * 批量设置多个菜单的角色权限
   */
  async batchSetMenuRolePermissions(
    requests: Array<{ menuId: string; roleIds: string[] }>
  ): Promise<ApiResponse<Record<string, { success: boolean; message?: string }>>> {
    const batchRequests = requests.map(req => ({
      menu_id: req.menuId,
      role_ids: req.roleIds,
    }));

    const response = await apiClient.post('/api/v1/permissions/menus/batch-roles', {
      requests: batchRequests
    });
    return response;
  },

  /**
   * 获取所有菜单的角色权限配置（用于权限矩阵显示）
   */
  async getAllMenuRolePermissions(
    params?: MenuRoleQueryParams
  ): Promise<ApiResponse<MenuRolePermissionResponse[]>> {
    const response = await apiClient.get('/api/v1/permissions/menus/roles', { 
      params 
    });
    return response;
  },

  /**
   * 复制菜单的角色权限配置到其他菜单
   */
  async copyMenuRolePermissions(
    sourceMenuId: string,
    targetMenuIds: string[]
  ): Promise<ApiResponse<Record<string, { success: boolean; message?: string }>>> {
    const response = await apiClient.post(`/api/v1/permissions/menus/${sourceMenuId}/copy-roles`, {
      target_menu_ids: targetMenuIds
    });
    return response;
  },

  /**
   * 清除菜单的所有角色权限
   */
  async clearMenuRolePermissions(menuId: string): Promise<ApiResponse<string>> {
    const response = await apiClient.delete(`/api/v1/permissions/menus/${menuId}/roles`);
    return response;
  },

  /**
   * 获取菜单权限统计信息
   */
  async getMenuPermissionStats(
    menuId?: string
  ): Promise<ApiResponse<{
    total_menus: number;
    menus_with_roles: number;
    total_role_assignments: number;
    most_assigned_roles: Array<{ role_id: string; role_name: string; assignment_count: number }>;
  }>> {
    const params = menuId ? { menu_id: menuId } : {};
    const response = await apiClient.get('/api/v1/permissions/menus/stats', { params });
    return response;
  },

  /**
   * 验证菜单角色权限配置
   */
  async validateMenuRolePermissions(
    menuId: string
  ): Promise<ApiResponse<{
    is_valid: boolean;
    issues: string[];
    suggestions: string[];
  }>> {
    const response = await apiClient.get(`/api/v1/permissions/menus/${menuId}/validate`);
    return response;
  },

  /**
   * 导出菜单角色权限配置
   */
  async exportMenuRolePermissions(
    format: 'json' | 'csv' | 'excel' = 'json',
    menuIds?: string[]
  ): Promise<ApiResponse<string>> {
    const params = { 
      format,
      ...(menuIds && { menu_ids: menuIds.join(',') })
    };
    const response = await apiClient.get('/api/v1/permissions/menus/export', { params });
    return response;
  },

  /**
   * 导入菜单角色权限配置
   */
  async importMenuRolePermissions(
    configData: any,
    options?: {
      overwrite?: boolean;
      validate_only?: boolean;
    }
  ): Promise<ApiResponse<{
    imported_count: number;
    skipped_count: number;
    error_count: number;
    errors: string[];
  }>> {
    const response = await apiClient.post('/api/v1/permissions/menus/import', {
      config_data: configData,
      options: options || {}
    });
    return response;
  }
};

// 导出默认菜单角色权限API
export default menuRoleApi;
