import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse, PaginatedApiResponse } from '@/types';
import { CreateHomeworkParams, Homework, HomeworkStatistics, PageHomeworkParams, UpdateHomeworkParams } from '@/types/homework';
import apiClient from './apiClient';

/**
 * 作者：张瀚
 * 说明：作业API
 */
export const homeworkApi = {
  /**
   * 作者：张瀚
   * 说明：获取作业统计数据
   */
  getStatistics: async ( tenant_id: string, tenant_name: string): Promise<ApiResponse<HomeworkStatistics>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homework/getStatistics`, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：创建作业
   */
  createHomework: async (tenant_id: string, tenant_name: string, params: CreateHomeworkParams): Promise<ApiResponse<Homework>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homework/createHomework`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：更新作业
   */
  updateHomework: async (tenant_id: string, tenant_name: string, params: UpdateHomeworkParams): Promise<ApiResponse<Homework>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homework/updateHomework`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：分页查询作业
   */
  pageHomework: async (tenant_id: string, tenant_name: string, params: PageHomeworkParams): Promise<PaginatedApiResponse<Homework>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homework/pageHomework`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },

};
