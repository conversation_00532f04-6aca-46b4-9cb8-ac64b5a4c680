import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse } from '@/types';
import { BatchBindStudentsToHomeworkParams, BatchUnbindStudentsFromHomeworkParams, FindAllByHomeworkIdParams, HomeworkStudents, HomeworkStudentsWithStudentBaseInfo } from '@/types/homeworkStudents';
import apiClient from './apiClient';

/**
 * 作者：张瀚
 * 说明：作业和学生关联的API
 */
export const homeworkStudentsApi = {
  /**
   * 作者：张瀚
   * 说明：批量绑定学生到作业中
   */
  batchBindStudentsToHomework: async (tenant_id: string, tenant_name: string, params: BatchBindStudentsToHomeworkParams): Promise<ApiResponse<HomeworkStudents[]>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homeworkStudents/batchBindStudentsToHomework`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：查询作业涉及的学生列表
   */
  findAllByHomeworkId: async (tenant_id: string, tenant_name: string, params: FindAllByHomeworkIdParams): Promise<ApiResponse<HomeworkStudentsWithStudentBaseInfo[]>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homeworkStudents/findAllByHomeworkId`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：批量解绑学生
   */
  batchUnbindStudentsFromHomework: async (tenant_id: string, tenant_name: string, params: BatchUnbindStudentsFromHomeworkParams): Promise<ApiResponse<undefined>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/homeworkStudents/batchUnbindStudentsFromHomework`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
};
