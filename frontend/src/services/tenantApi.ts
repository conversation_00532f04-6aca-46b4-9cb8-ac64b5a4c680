import apiClient from './apiClient';
import { Tenant } from '@/types/Tenant';

export const getTenants = async (): Promise<Tenant[]> => {
  const response = await apiClient.get('/api/v1/tenants');
  return response.data;
};

export const createTenant = async (tenantData: Omit<Tenant, 'id'>): Promise<Tenant> => {
  const response = await apiClient.post('/api/v1/tenants', tenantData);
  return response.data;
};

export const updateTenant = async (id: number, tenantData: Partial<Tenant>): Promise<Tenant> => {
  const response = await apiClient.put(`/api/v1/tenants/${id}`, tenantData);
  return response.data;
};

export const deleteTenant = async (id: number): Promise<void> => {
  await apiClient.delete(`/api/v1/tenants/${id}`);
};