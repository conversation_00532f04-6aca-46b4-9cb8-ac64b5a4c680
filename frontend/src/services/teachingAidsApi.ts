import apiClient from './apiClient';

const API_BASE_URL = (import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000') + '/api/v1';

// Types
export interface TeachingAid {
  id: string;
  title: string;
  description: string;
  author: string;
  publisher: string;
  subject: string;
  grade_level: string;
  content_type: string;
  version: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface TeachingAidChapter {
  id: string;
  teaching_aid_id: string;
  title: string;
  description: string;
  chapter_number: number;
  difficulty_level: string;
  created_at: string;
}

export interface TeachingAidChapter2 {
  id: string,
  textbook_id: string,
  parent_id: string,
  chapter_number: number,
  title: string,
  description: string,
  content: any,
  metadata: any,
  creator_id: string,
  created_at: string,
  updated_at: string,
}

export interface TeachingAidExercise {
  id: string;
  teaching_aid_id: string;
  chapter_id?: string;
  question_type: string;
  question_content: string;
  difficulty_level: string;
  points: number;
  created_at: string;
}

export interface TeachingAidStats {
  total_textbooks: number;
  total_chapters: number;
  total_exercises: number;
  total_authorized_tenants: number;
  popular_subjects: Array<{
    subject: string;
    count: number;
  }>;
  recent_imports: Array<{
    id: string;
    title: string;
    status: string;
    count: number;
    imported_at: string;
  }>;
}

export interface ImportProgress {
  id: string;
  filename: string;
  total_records: number;
  processed_records: number;
  success_count: number;
  error_count: number;
  status: string;
  started_at: string;
  completed_at?: string;
}

export interface TeachingAidAuthorization {
  id: string;
  teaching_aid_id: string;
  tenant_id: string;
  tenant_name: string;
  access_level: string;
  status: string;
  authorized_at: string;
  expires_at?: string;
}

export interface TeachingAidVersion {
  id: string;
  teaching_aid_id: string;
  version: string;
  changes: string;
  created_at: string;
}

export interface CreateTeachingAidRequest {
  title: string;
  description: string;
  author: string;
  publisher: string;
  subject: string;
  grade_level: string;
  content_type: string;
}

export interface ImportTeachingAidRequest {
  file: File;
  metadata: {
    title: string;
    description: string;
    author: string;
    publisher: string;
    subject: string;
    grade_level: string;
    content_type: string;
    import_exercises: boolean;
  }
}

export interface ImportTeachingAidFormParams {
  file: File;
  title: string;
  description: string;
  author: string;
  publisher: string;
  subject: string;
  grade_level: string;
  content_type: string;
  import_exercises: boolean;
}

export interface UpdateAuthorizationRequest {
  tenant_id: string;
  access_level: string;
  expires_at?: string;
}

// API Services
export const teachingAidApi = {
  async getTeachingAids(): Promise<TeachingAid[]> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/textbook/list`);
    return response.data;
  },

  async getTeachingAid(id: string): Promise<TeachingAid> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/textbook/${id}`);
    return response.data;
  },

  async createTeachingAid(data: CreateTeachingAidRequest): Promise<TeachingAid> {
    const response = await apiClient.post(`${API_BASE_URL}/teaching-aids/textbook`, data);
    return response.data;
  },

  async updateTeachingAid(id: string, data: Partial<CreateTeachingAidRequest>): Promise<TeachingAid> {
    const response = await apiClient.put(`${API_BASE_URL}/teaching-aids/textbook/${id}`, data);
    return response.data;
  },

  async deleteTeachingAid(id: string): Promise<void> {
    await apiClient.delete(`${API_BASE_URL}/teaching-aids/textbook/${id}`);
  },

  async getTeachingAidStats(): Promise<TeachingAidStats> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/stats`);
    return response.data;
  }
};

export const chapterApi = {
  async getChapters(teachingAidId: string): Promise<TeachingAidChapter2[]> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/textbook/${teachingAidId}/chapters`);
    return response.data;
  },

  async createChapter(teachingAidId: string, data: Omit<TeachingAidChapter, 'id' | 'teaching_aid_id' | 'created_at'>): Promise<TeachingAidChapter> {
    const response = await apiClient.post(`${API_BASE_URL}/teaching-aids/${teachingAidId}/chapters`, data);
    return response.data;
  }
};

export const exerciseApi = {
  async getExercises(params: { teaching_aid_id?: string; chapter_id?: string }): Promise<TeachingAidExercise[]> {
    const response = await apiClient.get(`${API_BASE_URL}/exercises`, { params });
    return response.data;
  }
};

export const importApi = {
  async importTeachingAid(data: ImportTeachingAidRequest): Promise<ImportProgress> {
    const formData = new FormData();
    formData.append('file', data.file);
    formData.append('metadata', JSON.stringify(data.metadata));

    const response = await apiClient.post(`${API_BASE_URL}/teaching-aids/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  },

  async getImportProgress(id: string): Promise<ImportProgress> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/import/${id}`);
    return response.data;
  },

  async getImportHistory(): Promise<ImportProgress[]> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/import/history`);
    return response.data;
  }
};

export const authorizationApi = {
  async getTeachingAidAuthorizations(teachingAidId: string): Promise<TeachingAidAuthorization[]> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/${teachingAidId}/authorizations`);
    return response.data;
  },

  async updateAuthorization(teachingAidId: string, data: UpdateAuthorizationRequest): Promise<TeachingAidAuthorization> {
    const response = await apiClient.post(`${API_BASE_URL}/teaching-aids/${teachingAidId}/authorizations`, data);
    return response.data;
  }
};

export const versionApi = {
  async getTeachingAidVersions(teachingAidId: string): Promise<TeachingAidVersion[]> {
    const response = await apiClient.get(`${API_BASE_URL}/teaching-aids/${teachingAidId}/versions`);
    return response.data;
  }
};