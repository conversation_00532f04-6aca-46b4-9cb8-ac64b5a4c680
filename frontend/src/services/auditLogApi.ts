import apiClient from './apiClient';

// API 响应接口
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

interface PaginatedApiResponse<T> {
  success: boolean;
  data: T[];
  message?: string;
  code?: number;
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
}

// 审计日志查询参数
export interface AuditQueryParams {
  operation?: string;
  menu_id?: string;
  operator_id?: string;
  tenant_id?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
  sort_by?: string;
  sort_order?: string;
  page?: number;
  page_size?: number;
}

// 审计统计查询参数
export interface AuditStatsQueryParams {
  tenant_id?: string;
  time_range?: string; // "day", "week", "month", "year"
  group_by?: string;   // "operation", "operator", "menu", "tenant"
  start_date?: string;
  end_date?: string;
}

// 审计日志响应
export interface AuditLogResponse {
  id: string;
  menu_id: string;
  operation: string;
  old_config?: any;
  new_config?: any;
  changes_summary: any;
  affected_users_count: number;
  operator_id: string;
  operator_identity: string;
  tenant_id?: string;
  reason?: string;
  impact_analysis: any;
  rollback_data?: any;
  is_rolled_back: boolean;
  rollback_at?: string;
  created_at: string;
  menu_name?: string;
  operator_name?: string;
  change_magnitude: string; // "minor", "moderate", "major", "critical"
}

// 审计统计响应
export interface AuditStatisticsResponse {
  time_range: string;
  total_operations: number;
  operations_by_type: OperationTypeStats[];
  operations_by_operator: OperatorStats[];
  operations_by_menu: MenuStats[];
  operations_by_tenant: TenantStats[];
  timeline: TimelinePoint[];
  risk_analysis: RiskAnalysis;
}

export interface OperationTypeStats {
  operation: string;
  count: number;
  percentage: number;
  trend: string; // "increasing", "decreasing", "stable"
}

export interface OperatorStats {
  operator_id: string;
  operator_identity: string;
  operator_name?: string;
  operation_count: number;
  most_common_operation: string;
  risk_score: number; // 0.0 to 1.0
}

export interface MenuStats {
  menu_id: string;
  menu_name: string;
  modification_count: number;
  last_modified: string;
  stability_score: number; // 0.0 to 1.0, higher is more stable
}

export interface TenantStats {
  tenant_id: string;
  operation_count: number;
  unique_operators: number;
  most_active_menu: string;
  activity_score: number;
}

export interface TimelinePoint {
  date: string;
  operation_count: number;
  unique_operators: number;
  high_risk_operations: number;
}

export interface RiskAnalysis {
  overall_risk_level: string; // "low", "medium", "high", "critical"
  risk_factors: RiskFactor[];
  recommendations: string[];
  anomalies_detected: number;
}

export interface RiskFactor {
  factor_type: string;
  description: string;
  severity: string;
  affected_count: number;
}

// 审计摘要响应
export interface AuditSummaryResponse {
  total_logs: number;
  recent_activity: RecentActivity[];
  top_operators: TopOperator[];
  frequent_operations: FrequentOperation[];
  system_health: SystemHealthIndicator;
  alerts: AuditAlert[];
}

export interface RecentActivity {
  operation: string;
  menu_id: string;
  menu_name: string;
  operator_identity: string;
  created_at: string;
  impact_level: string;
}

export interface TopOperator {
  operator_id: string;
  operator_identity: string;
  operation_count: number;
  last_activity: string;
}

export interface FrequentOperation {
  operation: string;
  count: number;
  avg_impact: number;
}

export interface SystemHealthIndicator {
  health_score: number; // 0.0 to 1.0
  status: string;    // "healthy", "warning", "critical"
  indicators: HealthMetric[];
}

export interface HealthMetric {
  metric_name: string;
  current_value: number;
  threshold: number;
  status: string;
}

export interface AuditAlert {
  alert_type: string;
  severity: string;
  message: string;
  affected_count: number;
  recommendation: string;
}

// 回滚请求
export interface RollbackRequest {
  audit_log_id: string;
  reason: string;
  confirm_rollback: boolean;
}

// 回滚响应
export interface RollbackResponse {
  audit_log_id: string;
  rollback_successful: boolean;
  rollback_details: any;
  new_audit_log_id?: string;
  message: string;
}

// 权限审计 API 服务
export const auditLogApi = {
  /**
   * 获取审计日志列表
   */
  async getAuditLogs(params: AuditQueryParams = {}): Promise<PaginatedApiResponse<AuditLogResponse>> {
    const response = await apiClient.get('/api/v1/admin/audit/logs', { params });
    return response.data;
  },

  /**
   * 获取审计统计信息
   */
  async getAuditStatistics(params: AuditStatsQueryParams = {}): Promise<ApiResponse<AuditStatisticsResponse>> {
    const response = await apiClient.get('/api/v1/admin/audit/statistics', { params });
    return response.data;
  },

  /**
   * 获取审计摘要
   */
  async getAuditSummary(): Promise<ApiResponse<AuditSummaryResponse>> {
    const response = await apiClient.get('/api/v1/admin/audit/summary');
    return response.data;
  },

  /**
   * 回滚权限变更
   */
  async rollbackChange(request: RollbackRequest): Promise<ApiResponse<RollbackResponse>> {
    const response = await apiClient.post('/api/v1/admin/audit/rollback', request);
    return response.data;
  },

  /**
   * 获取单个审计日志详情
   */
  async getAuditLogDetail(auditLogId: string): Promise<ApiResponse<AuditLogResponse>> {
    const response = await apiClient.get(`/api/v1/admin/audit/logs/${auditLogId}`);
    return response.data;
  },

  /**
   * 导出审计日志
   */
  async exportAuditLogs(params: AuditQueryParams = {}, format: 'json' | 'csv' | 'excel' = 'json'): Promise<ApiResponse<any>> {
    const response = await apiClient.get('/api/v1/admin/audit/logs/export', {
      params: { ...params, format }
    });
    return response.data;
  },

  /**
   * 获取审计日志趋势
   */
  async getAuditTrends(timeRange: string = 'month', groupBy: string = 'day'): Promise<ApiResponse<any>> {
    const response = await apiClient.get('/api/v1/admin/audit/trends', {
      params: { time_range: timeRange, group_by: groupBy }
    });
    return response.data;
  },

  /**
   * 获取风险警报
   */
  async getRiskAlerts(severity?: string): Promise<ApiResponse<AuditAlert[]>> {
    const params = severity ? { severity } : {};
    const response = await apiClient.get('/api/v1/admin/audit/alerts', { params });
    return response.data;
  },

  /**
   * 标记警报为已读
   */
  async markAlertAsRead(alertId: string): Promise<ApiResponse<string>> {
    const response = await apiClient.put(`/api/v1/admin/audit/alerts/${alertId}/read`);
    return response.data;
  },

  /**
   * 批量标记警报为已读
   */
  async batchMarkAlertsAsRead(alertIds: string[]): Promise<ApiResponse<string>> {
    const response = await apiClient.put('/api/v1/admin/audit/alerts/batch-read', {
      alert_ids: alertIds
    });
    return response.data;
  },

  /**
   * 获取操作员活动统计
   */
  async getOperatorActivity(operatorId?: string, timeRange?: string): Promise<ApiResponse<any>> {
    const params: any = {};
    if (operatorId) params.operator_id = operatorId;
    if (timeRange) params.time_range = timeRange;
    
    const response = await apiClient.get('/api/v1/admin/audit/operator-activity', { params });
    return response.data;
  },

  /**
   * 获取菜单变更统计
   */
  async getMenuChangeStats(menuId?: string, timeRange?: string): Promise<ApiResponse<any>> {
    const params: any = {};
    if (menuId) params.menu_id = menuId;
    if (timeRange) params.time_range = timeRange;
    
    const response = await apiClient.get('/api/v1/admin/audit/menu-changes', { params });
    return response.data;
  },

  /**
   * 获取系统健康报告
   */
  async getSystemHealthReport(): Promise<ApiResponse<SystemHealthIndicator>> {
    const response = await apiClient.get('/api/v1/admin/audit/system-health');
    return response.data;
  },

  /**
   * 生成审计报告
   */
  async generateAuditReport(startDate: string, endDate: string, reportType: string = 'comprehensive'): Promise<ApiResponse<any>> {
    const response = await apiClient.post('/api/v1/admin/audit/generate-report', {
      start_date: startDate,
      end_date: endDate,
      report_type: reportType
    });
    return response.data;
  },

  /**
   * 获取可回滚的操作列表
   */
  async getRollbackableOperations(menuId?: string): Promise<ApiResponse<AuditLogResponse[]>> {
    const params = menuId ? { menu_id: menuId } : {};
    const response = await apiClient.get('/api/v1/admin/audit/rollbackable', { params });
    return response.data;
  },

  /**
   * 预览回滚影响
   */
  async previewRollbackImpact(auditLogId: string): Promise<ApiResponse<any>> {
    const response = await apiClient.get(`/api/v1/admin/audit/rollback/${auditLogId}/preview`);
    return response.data;
  }
};