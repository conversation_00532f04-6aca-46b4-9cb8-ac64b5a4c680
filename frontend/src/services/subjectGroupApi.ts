// Subject group API service for the Deep-Mate frontend application

import  apiClient from './apiClient';
import {
  SubjectGroup,
  SubjectGroupSummary,
  SubjectGroupStatistics,
  UpdateSubjectGroupRequest,
  SubjectGroupQueryParams,
} from '@/types/subjectGroup';

export interface SubjectGroupResponse {
  success: boolean;
  code: number;
  message: string;
  data: SubjectGroup;
}

export interface SubjectGroupListResponse {
  success: boolean;
  code: number;
  message: string;
  data: SubjectGroup[];
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
}

export interface SubjectGroupSummaryResponse {
  success: boolean;
  code: number;
  message: string;
  data: SubjectGroupSummary[];
}

export interface SubjectGroupStatisticsResponse {
  success: boolean;
  code: number;
  message: string;
  data: SubjectGroupStatistics;
}

export const subjectGroupApi = {
  /**
   * 获取学科组列表（分页）
   */
  async getSubjectGroups(params: SubjectGroupQueryParams = {}): Promise<SubjectGroupListResponse> {
    const response = await apiClient.get('/subject-groups', { params });
    return response.data;
  },

  /**
   * 获取学科组详情
   */
  async getSubjectGroup(id: string): Promise<SubjectGroupResponse> {
    const response = await apiClient.get(`/subject-groups/${id}`);
    return response.data;
  },


  /**
   * 更新学科组信息
   */
  async updateSubjectGroup(id: string, data: UpdateSubjectGroupRequest): Promise<SubjectGroupResponse> {
    const response = await apiClient.put(`/subject-groups/${id}`, data);
    return response.data;
  },

  /**
   * 删除学科组（软删除）
   */
  async deleteSubjectGroup(id: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete(`/subject-groups/${id}`);
    return response.data;
  },

  /**
   * 切换学科组状态
   */
  async toggleSubjectGroupStatus(id: string, isActive: boolean): Promise<SubjectGroupResponse> {
    const response = await apiClient.patch(`/subject-groups/${id}/status`, { is_active: isActive });
    return response.data;
  },

  /**
   * 获取学科组简要信息（用于下拉选择）
   */
  async getSubjectGroupSummaries(subject?: string, showInactive?: boolean): Promise<SubjectGroupSummaryResponse> {
    const params: any = {};
    if (subject) params.subject = subject;
    if (showInactive) params.show_inactive = showInactive;
    
    const response = await apiClient.get('/subject-groups/summaries', { params });
    return response.data;
  },

  /**
   * 获取学科组统计信息
   */
  async getSubjectGroupStatistics(): Promise<SubjectGroupStatisticsResponse> {
    const response = await apiClient.get('/subject-groups/statistics');
    return response.data;
  },

  /**
   * 导出学科组数据
   */
  async exportSubjectGroups(params: SubjectGroupQueryParams = {}): Promise<Blob> {
    const response = await apiClient.get('/subject-groups/export', {
      params,
      responseType: 'blob',
    });
    return response.data;
  },

  /**
   * 批量更新学科组状态
   */
  async batchUpdateStatus(ids: string[], isActive: boolean): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.patch('/subject-groups/batch-status', {
      ids,
      is_active: isActive,
    });
    return response.data;
  },

  /**
   * 检查学科组名称可用性
   */
  async checkNameAvailability(name: string, excludeId?: string): Promise<{ is_available: boolean }> {
    const params: any = { name };
    if (excludeId) params.exclude_id = excludeId;
    
    const response = await apiClient.get('/subject-groups/check-name', { params });
    return response.data;
  },

  /**
   * 获取学科组成员（教师）列表
   */
  async getSubjectGroupMembers(id: string): Promise<{
    success: boolean;
    data: Array<{
      id: string;
      name: string;
      employee_id: string;
      title?: string;
      is_leader: boolean;
    }>;
  }> {
    const response = await apiClient.get(`/subject-groups/${id}/members`);
    return response.data;
  },

  /**
   * 添加学科组成员
   */
  async addSubjectGroupMember(id: string, teacherId: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.post(`/subject-groups/${id}/members`, {
      teacher_id: teacherId,
    });
    return response.data;
  },

  /**
   * 移除学科组成员
   */
  async removeSubjectGroupMember(id: string, teacherId: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete(`/subject-groups/${id}/members/${teacherId}`);
    return response.data;
  },

  /**
   * 设置学科组组长
   */
  async setSubjectGroupLeader(id: string, teacherId: string): Promise<SubjectGroupResponse> {
    const response = await apiClient.patch(`/subject-groups/${id}/leader`, {
      leader_id: teacherId,
    });
    return response.data;
  },
};