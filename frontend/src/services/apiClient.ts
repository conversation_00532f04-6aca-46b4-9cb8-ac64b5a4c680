
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

// API client configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    console.error("🔥 API请求错误:", {
      url: error.config?.url,
      status: error.response?.status,
      message: error.response?.data?.message || error.message
    });
    
    if (error.response?.status === 401) {
      // 只有当用户不在身份选择页面时才自动登出
      const currentPath = window.location.pathname;
      if (currentPath !== '/identitySelect' && currentPath !== '/identityBinding') {
        console.log("🔄 401错误，执行自动登出，当前路径:", currentPath);
        localStorage.removeItem('token');
        localStorage.removeItem('identity');
        window.location.href = '/login';
      } else {
        console.log("⚠️ 身份选择页面401错误，不执行自动登出");
      }
    }
    return Promise.reject(error);
  }
);

export default apiClient;
