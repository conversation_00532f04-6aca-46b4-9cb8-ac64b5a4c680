import apiClient from './apiClient';

// 权限模板管理API接口定义
export interface PermissionTemplate {
  id: string;
  template_name: string;
  template_type: string;
  template_category?: string;
  permissions: string[];
  data_scopes?: string[];
  permission_mode: string;
  description?: string;
  usage_count: number;
  is_system_template: boolean;
  is_active: boolean;
  metadata?: any;
  created_by: string;
  created_at: string;
  updated_at: string;
  last_used_at?: string;
  compatible_menus?: string[];
}

export interface TemplateCreateRequest {
  template_name: string;
  template_type: string;
  template_category?: string;
  permissions: string[];
  data_scopes?: string[];
  permission_mode?: string;
  description?: string;
  metadata?: any;
}

export interface TemplateUpdateRequest {
  template_name?: string;
  template_type?: string;
  template_category?: string;
  permissions?: string[];
  data_scopes?: string[];
  permission_mode?: string;
  description?: string;
  is_active?: boolean;
  metadata?: any;
}

export interface ApplyTemplateRequest {
  template_id: string;
  menu_ids: string[];
  override_existing?: boolean;
  reason?: string;
}

export interface BatchTemplateRequest {
  operation: string; // "activate", "deactivate", "delete", "duplicate"
  template_ids: string[];
  reason?: string;
}

export interface TemplateQueryParams {
  template_type?: string;
  template_category?: string;
  is_active?: boolean;
  is_system_template?: boolean;
  search?: string;
  sort_by?: string;
  sort_order?: string;
  page?: number;
  page_size?: number;
}

export interface TemplateApplicationResult {
  template_id: string;
  template_name: string;
  applied_to_menus: Array<{
    menu_id: string;
    success: boolean;
    message?: string;
    previous_permissions?: string[];
    new_permissions: string[];
  }>;
  total_requested: number;
  successful_count: number;
  failed_count: number;
  execution_time_ms: number;
}

export interface TemplateCompatibilityCheck {
  template_id: string;
  menu_id: string;
  compatible: boolean;
  compatibility_score: number; // 0.0 to 1.0
  reasons: string[];
  suggestions: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
}

// 权限模板API服务
export const permissionTemplateApi = {
  /**
   * 获取权限模板列表
   */
  async getTemplates(params?: TemplateQueryParams): Promise<PaginatedResponse<PermissionTemplate>> {
    const response = await apiClient.get('/api/v1/admin/permission-templates', { params });
    return response.data;
  },

  /**
   * 创建权限模板
   */
  async createTemplate(request: TemplateCreateRequest): Promise<{ data: PermissionTemplate }> {
    const response = await apiClient.post('/api/v1/admin/permission-templates', request);
    return response.data;
  },

  /**
   * 更新权限模板
   */
  async updateTemplate(templateId: string, request: TemplateUpdateRequest): Promise<{ data: PermissionTemplate }> {
    const response = await apiClient.put(`/api/v1/admin/permission-templates/${templateId}`, request);
    return response.data;
  },

  /**
   * 删除权限模板
   */
  async deleteTemplate(templateId: string): Promise<{ data: string }> {
    const response = await apiClient.delete(`/api/v1/admin/permission-templates/${templateId}`);
    return response.data;
  },

  /**
   * 获取模板详情
   */
  async getTemplateById(templateId: string): Promise<{ data: PermissionTemplate }> {
    const response = await apiClient.get(`/api/v1/admin/permission-templates/${templateId}`);
    return response.data;
  },

  /**
   * 应用权限模板到菜单
   */
  async applyTemplate(request: ApplyTemplateRequest): Promise<{ data: TemplateApplicationResult }> {
    const response = await apiClient.post('/api/v1/admin/permission-templates/apply', request);
    return response.data;
  },

  /**
   * 检查模板与菜单的兼容性
   */
  async checkTemplateCompatibility(templateId: string, menuId: string): Promise<{ data: TemplateCompatibilityCheck }> {
    const response = await apiClient.get(`/api/v1/admin/permission-templates/${templateId}/compatibility/${menuId}`);
    return response.data;
  },

  /**
   * 批量检查模板与多个菜单的兼容性
   */
  async batchCheckCompatibility(templateId: string, menuIds: string[]): Promise<{ 
    data: {
      template_id: string;
      checks: TemplateCompatibilityCheck[];
      summary: {
        total_checked: number;
        compatible_count: number;
        incompatible_count: number;
        avg_compatibility_score: number;
      };
    }
  }> {
    const response = await apiClient.post(`/api/v1/admin/permission-templates/${templateId}/compatibility/batch`, {
      menu_ids: menuIds
    });
    return response.data;
  },

  /**
   * 复制权限模板
   */
  async duplicateTemplate(templateId: string, newName: string): Promise<{ data: PermissionTemplate }> {
    const response = await apiClient.post(`/api/v1/admin/permission-templates/${templateId}/duplicate`, {
      template_name: newName
    });
    return response.data;
  },

  /**
   * 批量操作权限模板
   */
  async batchOperateTemplates(request: BatchTemplateRequest): Promise<{ 
    data: {
      operation: string;
      total_requested: number;
      successful_count: number;
      failed_count: number;
      results: Array<{
        template_id: string;
        success: boolean;
        message?: string;
      }>;
      execution_time_ms: number;
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/permission-templates/batch', request);
    return response.data;
  },

  /**
   * 获取模板使用统计
   */
  async getTemplateUsageStats(templateId: string): Promise<{ 
    data: {
      template_id: string;
      template_name: string;
      usage_count: number;
      applied_menus: Array<{
        menu_id: string;
        menu_name: string;
        applied_at: string;
        applied_by: string;
      }>;
      usage_timeline: Array<{
        date: string;
        application_count: number;
      }>;
      popular_permissions: Array<{
        permission: string;
        frequency: number;
      }>;
    }
  }> {
    const response = await apiClient.get(`/api/v1/admin/permission-templates/${templateId}/stats`);
    return response.data;
  },

  /**
   * 获取推荐的权限模板
   */
  async getRecommendedTemplates(menuId: string): Promise<{ 
    data: Array<{
      template: PermissionTemplate;
      compatibility_score: number;
      recommendation_reason: string;
      confidence: number;
    }>
  }> {
    const response = await apiClient.get(`/api/v1/admin/permission-templates/recommendations/${menuId}`);
    return response.data;
  },

  /**
   * 导出权限模板
   */
  async exportTemplates(templateIds?: string[]): Promise<{ 
    data: {
      templates: PermissionTemplate[];
      export_info: {
        exported_count: number;
        exported_at: string;
        exported_by: string;
      };
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/permission-templates/export', {
      template_ids: templateIds
    });
    return response.data;
  },

  /**
   * 导入权限模板
   */
  async importTemplates(templatesData: PermissionTemplate[]): Promise<{ 
    data: {
      imported_count: number;
      skipped_count: number;
      failed_count: number;
      results: Array<{
        template_name: string;
        status: 'imported' | 'skipped' | 'failed';
        message?: string;
      }>;
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/permission-templates/import', {
      templates: templatesData
    });
    return response.data;
  },

  /**
   * 验证权限模板
   */
  async validateTemplate(templateData: Partial<PermissionTemplate>): Promise<{ 
    data: {
      is_valid: boolean;
      issues: Array<{
        field: string;
        type: string;
        message: string;
        severity: 'error' | 'warning' | 'info';
      }>;
      suggestions: string[];
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/permission-templates/validate', templateData);
    return response.data;
  },

  /**
   * 获取模板分类列表
   */
  async getTemplateCategories(): Promise<{ 
    data: Array<{
      category: string;
      template_count: number;
      description?: string;
    }>
  }> {
    const response = await apiClient.get('/api/v1/admin/permission-templates/categories');
    return response.data;
  },

  /**
   * 获取权限列表（用于模板创建）
   */
  async getAvailablePermissions(): Promise<{ 
    data: Array<{
      permission: string;
      description?: string;
      category: string;
      usage_count: number;
    }>
  }> {
    const response = await apiClient.get('/api/v1/admin/permission-templates/permissions');
    return response.data;
  }
};

export default permissionTemplateApi;