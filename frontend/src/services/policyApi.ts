import apiClient from './apiClient';
import {PaginatedApiResponse} from "@/types";

// 策略相关类型定义
export interface CasbinPolicy {
  id?: string;
  policy_type: string;  // "permission" or "role"
  subject: string;
  domain: string;
  object?: string;
  action?: string;
  effect?: string;
  created_at?: string;
}

export interface PolicyStats {
  total_policies: number;
  permission_policies: number;
  role_policies: number;
  tenants: string[];
}

export interface PolicyTestRequest {
  tenant_id: string;
  subject: string;
  object: string;
  action: string;
}

export interface PolicyTestResponse {
  allowed: boolean;
  subject: string;
  object: string;
  action: string;
  tenant_id: string;
  matched_policies: CasbinPolicy[];
  test_timestamp: string;
}

export interface CreatePolicyRequest {
  tenant_id: string;
  policy_type: string;
  subject: string;
  object?: string;
  action?: string;
  effect?: string;
  role?: string;
}

export interface DeletePolicyRequest {
  tenant_id: string;
  policy_type: string;
  subject: string;
  object?: string;
  action?: string;
  effect?: string;
  role?: string;
}

export interface BatchPolicyRequest {
  tenant_id: string;
  policies: CreatePolicyRequest[];
}

export interface PolicyExportResponse {
  tenant_id?: string;
  policies: CasbinPolicy[];
  total_count: number;
  export_timestamp: string;
}

// API 响应接口
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
  meta?: any;
}

// Policy API 服务
export const policyApi = {
  /**
   * 获取策略列表
   */
  async getPolicies(params?: {
    tenant_id?: string;
    subject?: string;
    object?: string;
    action?: string;
    page?: number;
    page_size?: number;
  }): Promise<PaginatedApiResponse<CasbinPolicy[]>> {
    return await apiClient.get('/api/v1/casbin/policies', { params });
  },

  /**
   * 创建策略
   */
  async createPolicy(policy: CreatePolicyRequest): Promise<ApiResponse<string>> {
    const response = await apiClient.post('/api/v1/casbin/policies', policy);
    return response.data;
  },

  /**
   * 删除策略
   */
  async deletePolicy(policy: DeletePolicyRequest): Promise<ApiResponse<string>> {
    const response = await apiClient.delete('/api/v1/casbin/policies', { data: policy });
    return response.data;
  },

  /**
   * 测试权限
   */
  async testPermission(request: PolicyTestRequest): Promise<ApiResponse<PolicyTestResponse>> {
    const response = await apiClient.post('/api/v1/casbin/policies/test', request);
    return response.data;
  },

  /**
   * 获取策略统计
   */
  async getPolicyStats(tenant_id?: string): Promise<ApiResponse<PolicyStats>> {
    const params = tenant_id ? { tenant_id } : undefined;
    const response = await apiClient.get('/api/v1/casbin/policies/stats', { params });
    return response.data;
  },

  /**
   * 导出策略
   */
  async exportPolicies(tenant_id?: string): Promise<ApiResponse<PolicyExportResponse>> {
    const params = tenant_id ? { tenant_id } : undefined;
    const response = await apiClient.get('/api/v1/casbin/policies/export', { params });
    return response.data;
  },

  /**
   * 批量创建策略
   */
  async batchCreatePolicies(request: BatchPolicyRequest): Promise<ApiResponse<string>> {
    const response = await apiClient.post('/api/v1/casbin/policies/batch', request);
    return response.data;
  },

  /**
   * 清除租户所有策略
   */
  async clearTenantPolicies(tenant_id: string): Promise<ApiResponse<string>> {
    const response = await apiClient.delete(`/api/v1/casbin/policies/tenant/${tenant_id}/clear`);
    return response.data;
  },

  /**
   * 同步租户策略
   */
  async syncTenantPolicies(tenant_id: string): Promise<ApiResponse<string>> {
    const response = await apiClient.post(`/api/v1/casbin/policies/tenant/${tenant_id}/sync`);
    return response.data;
  },

  /**
   * 导入策略配置（文件上传）
   */
  async importPolicies(file: File, tenant_id?: string): Promise<ApiResponse<{
    imported_policies: number;
    message: string;
  }>> {
    const formData = new FormData();
    formData.append('file', file);
    if (tenant_id) {
      formData.append('tenant_id', tenant_id);
    }

    const response = await apiClient.post('/api/v1/casbin/policies/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }
};