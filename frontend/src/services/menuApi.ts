/**
 * 菜单管理 API 服务
 * 提供菜单和权限模板相关的 API 调用接口
 */

import apiClient from './apiClient';
import { ApiResponse, PaginatedApiResponse } from '@/types';
import {
  MenuItem,
  PermissionTemplate,
  MenuQueryParams,
  MenuCreateRequest,
  MenuUpdateRequest,
  BatchMenuOperationRequest,
  MenuReorderRequest,
  BatchOperationResponse,
  PermissionTemplateQueryParams,
  DetailedMenuUsageStats,
  MenuCleanupResult,
  MenuValidationResult,
} from '@/types/menu';

// 基础API路径
const MENUS_API = '/api/v1/menus';
const PERMISSION_TEMPLATES_API = '/api/v1/admin/permission-templates';

/**
 * 菜单管理 API
 */
export const menuApi = {
  /**
   * 获取菜单树结构
   */
  async getMenuTree(params?: MenuQueryParams): Promise<ApiResponse<MenuItem[]>> {
    const response = await apiClient.get(MENUS_API, { params }) as ApiResponse<MenuItem[]>;
    return response;
  },

  /**
   * 创建新菜单
   */
  async createMenu(request: MenuCreateRequest): Promise<ApiResponse<MenuItem>> {
    const response = await apiClient.post(MENUS_API, request) as ApiResponse<MenuItem>;
    return response;
  },

  /**
   * 更新菜单
   */
  async updateMenu(menuId: string, request: MenuUpdateRequest): Promise<ApiResponse<MenuItem>> {
    const response = await apiClient.put(`${MENUS_API}/${menuId}`, request) as ApiResponse<MenuItem>;
    return response;
  },

  /**
   * 删除菜单
   */
  async deleteMenu(menuId: string, force: boolean = false): Promise<ApiResponse<string>> {
    const params = force ? { search: 'force' } : {};
    const response = await apiClient.delete(`${MENUS_API}/${menuId}`, { params }) as ApiResponse<string>;
    return response;
  },

  /**
   * 获取菜单详情
   */
  async getMenuById(menuId: string): Promise<ApiResponse<MenuItem>> {
    const response = await apiClient.get(`${MENUS_API}/${menuId}`) as ApiResponse<MenuItem>;
    return response;
  },

  /**
   * 批量操作菜单
   */
  async batchOperateMenus(request: BatchMenuOperationRequest): Promise<ApiResponse<BatchOperationResponse>> {
    const response = await apiClient.post(`${MENUS_API}/batch`, request) as ApiResponse<BatchOperationResponse>;
    return response;
  },

  /**
   * 重排序菜单
   */
  async reorderMenus(request: MenuReorderRequest): Promise<ApiResponse<BatchOperationResponse>> {
    const response = await apiClient.post(`${MENUS_API}/reorder`, request) as ApiResponse<BatchOperationResponse>;
    return response;
  },

  /**
   * 复制菜单
   */
  async duplicateMenu(menuId: string, newMenuId: string, newName: string): Promise<ApiResponse<MenuItem>> {
    const response = await apiClient.post(`${MENUS_API}/${menuId}/duplicate`, {
      new_menu_id: newMenuId,
      new_name: newName
    }) as ApiResponse<MenuItem>;
    return response;
  },

  /**
   * 导出菜单配置
   */
  async exportMenus(menuIds?: string[]): Promise<ApiResponse<any>> {
    const response = await apiClient.post(`${MENUS_API}/export`, {
      menu_ids: menuIds
    }) as ApiResponse<any>;
    return response;
  },

  /**
   * 导入菜单配置
   */
  async importMenus(configData: any): Promise<ApiResponse<BatchOperationResponse>> {
    const response = await apiClient.post(`${MENUS_API}/import`, configData) as ApiResponse<BatchOperationResponse>;
    return response;
  },

  /**
   * 获取菜单使用统计
   */
  async getMenuUsageStats(menuId: string, timeRange?: string): Promise<ApiResponse<DetailedMenuUsageStats>> {
    const params = timeRange ? { time_range: timeRange } : {};
    const response = await apiClient.get(`${MENUS_API}/${menuId}/stats`, { params }) as ApiResponse<DetailedMenuUsageStats>;
    return response;
  },

  /**
   * 清理无效菜单引用
   */
  async cleanupInvalidMenus(): Promise<ApiResponse<MenuCleanupResult>> {
    const response = await apiClient.post(`${MENUS_API}/cleanup`) as ApiResponse<MenuCleanupResult>;
    return response;
  },

  /**
   * 验证菜单结构
   */
  async validateMenuStructure(): Promise<ApiResponse<MenuValidationResult>> {
    const response = await apiClient.get(`${MENUS_API}/validate`) as ApiResponse<MenuValidationResult>;
    return response;
  },

  /**
   * 创建测试数据（开发用）
   */
  async createTestData(): Promise<ApiResponse<string>> {
    const response = await apiClient.post(`${MENUS_API}/test-data`) as ApiResponse<string>;
    return response;
  },

  /**
   * 获取菜单树（测试用，无权限验证）
   */
  async getTestMenuTree(params?: MenuQueryParams): Promise<ApiResponse<MenuItem[]>> {
    const response = await apiClient.get(`${MENUS_API}/test`, { params }) as ApiResponse<MenuItem[]>;
    return response;
  }
};

/**
 * 权限模板管理 API
 */
export const permissionTemplateApi = {
  /**
   * 获取权限模板列表
   */
  async getTemplates(params?: PermissionTemplateQueryParams): Promise<PaginatedApiResponse<PermissionTemplate>> {
    const response = await apiClient.get(PERMISSION_TEMPLATES_API, { params }) as PaginatedApiResponse<PermissionTemplate>;
    return response;
  },

  /**
   * 获取权限模板详情
   */
  async getTemplate(id: string): Promise<ApiResponse<PermissionTemplate>> {
    const response = await apiClient.get(`${PERMISSION_TEMPLATES_API}/${id}`) as ApiResponse<PermissionTemplate>;
    return response;
  },

  /**
   * 创建权限模板
   */
  async createTemplate(data: Partial<PermissionTemplate>): Promise<ApiResponse<PermissionTemplate>> {
    const response = await apiClient.post(PERMISSION_TEMPLATES_API, data) as ApiResponse<PermissionTemplate>;
    return response;
  },

  /**
   * 更新权限模板
   */
  async updateTemplate(id: string, data: Partial<PermissionTemplate>): Promise<ApiResponse<PermissionTemplate>> {
    const response = await apiClient.put(`${PERMISSION_TEMPLATES_API}/${id}`, data) as ApiResponse<PermissionTemplate>;
    return response;
  },

  /**
   * 删除权限模板
   */
  async deleteTemplate(id: string): Promise<ApiResponse<void>> {
    const response = await apiClient.delete(`${PERMISSION_TEMPLATES_API}/${id}`) as ApiResponse<void>;
    return response;
  },

  /**
   * 应用权限模板到菜单
   */
  async applyTemplateToMenu(templateId: string, menuId: string): Promise<ApiResponse<MenuItem>> {
    const response = await apiClient.post(`${PERMISSION_TEMPLATES_API}/${templateId}/apply`, {
      menu_id: menuId
    }) as ApiResponse<MenuItem>;
    return response;
  },

  /**
   * 批量应用权限模板
   */
  async batchApplyTemplate(templateId: string, menuIds: string[]): Promise<ApiResponse<BatchOperationResponse>> {
    const response = await apiClient.post(`${PERMISSION_TEMPLATES_API}/${templateId}/batch-apply`, {
      menu_ids: menuIds
    }) as ApiResponse<BatchOperationResponse>;
    return response;
  }
};

// 导出默认菜单API
export default menuApi;
