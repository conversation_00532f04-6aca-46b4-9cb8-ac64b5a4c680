/**
 * 年级管理 API 服务
 * 提供年级相关的 API 调用接口
 */

import apiClient from './apiClient';
import {ApiResponse, PaginatedApiResponse} from '@/types';
import {
  CheckCodeResponse,
  CreateGradeLevelRequest,
  GradeLevel,
  GradeLevelQueryParams,
  GradeLevelStatistics,
  GradeLevelSummary,
  UpdateGradeLevelRequest
} from '@/types/grade';

// 基础API路径
const GRADES_API = '/api/v1/grades';

/**
 * 年级管理 API
 */
export const gradeApi = {
  /**
   * 获取年级列表（支持分页和查询）
   * @param params 查询参数，可包含分页、搜索、筛选条件
   * @param params.stage_code 可选，学段代码，用于筛选指定学段下的年级
   */
  async getGrades(params?: GradeLevelQueryParams): Promise<PaginatedApiResponse<GradeLevel>> {
    // 确保参数是对象
    const queryParams = { ...(params || {}) };
    
    // 如果stage_code是空字符串，则从查询参数中删除
    if (queryParams.stage_code === '') {
      delete queryParams.stage_code;
    }
    
    return await apiClient.get(GRADES_API, { params: queryParams });
  },

  /**
   * 获取年级简要信息列表（用于下拉选择）
   */
  async getGradeSummaries(isActive?: boolean): Promise<ApiResponse<GradeLevelSummary[]>> {
    return await apiClient.get(`${GRADES_API}/summaries`, {
      params: { is_active: isActive ?? true }
    });
  },

  /**
   * 获取单个年级详情
   */
  async getGrade(id: string): Promise<ApiResponse<GradeLevel>> {
    return await apiClient.get(`${GRADES_API}/${id}`);
  },

  /**
   * 创建年级
   */
  async createGrade(data: CreateGradeLevelRequest): Promise<ApiResponse<GradeLevel>> {
    return await apiClient.post(GRADES_API+'/create', data);
  },

  /**
   * 更新年级
   */
  async updateGrade(id: string, data: UpdateGradeLevelRequest): Promise<ApiResponse<GradeLevel>> {
    return await apiClient.put(`${GRADES_API}/${id}`, data);
  },

  /**
   * 删除年级（软删除）
   */
  async deleteGrade(id: string): Promise<ApiResponse<void>> {
    return await apiClient.delete(`${GRADES_API}/${id}`);
  },

  /**
   * 获取年级统计信息
   */
  async getGradeStatistics(): Promise<ApiResponse<GradeLevelStatistics>> {
    return await apiClient.get(`${GRADES_API}/statistics`);
  },

  /**
   * 批量更新年级排序
   */
  async updateGradeOrders(orders: Array<[string, number]>): Promise<ApiResponse<void>> {
    return await apiClient.patch(`${GRADES_API}/orders`, {orders});
  },

  /**
   * 检查年级代码是否可用
   */
  async checkCodeAvailability(code: string, excludeId?: string): Promise<ApiResponse<CheckCodeResponse>> {
    const params: any = { code };
    if (excludeId) {
      params.exclude_id = excludeId;
    }
    return await apiClient.get(`${GRADES_API}/check-code`, {params});
  },

  /**
   * 启用/禁用年级
   */
  async toggleGradeStatus(id: string, isActive: boolean): Promise<ApiResponse<GradeLevel>> {
    return await apiClient.put(`${GRADES_API}/${id}`, {
      is_active: isActive
    });
  },

  /**
   * 批量删除年级
   */
  async batchDeleteGrades(ids: string[]): Promise<ApiResponse<void>> {
    return await apiClient.delete(`${GRADES_API}/batch`, {
      data: {ids}
    });
  },

  /**
   * 复制年级
   */
  async cloneGrade(id: string, name: string, code: string): Promise<ApiResponse<GradeLevel>> {
    return await apiClient.post(`${GRADES_API}/${id}/clone`, {
      name,
      code
    });
  },

  /**
   * 导入年级数据
   */
  async importGrades(data: CreateGradeLevelRequest[]): Promise<ApiResponse<{
    success_count: number;
    error_count: number;
    errors: Array<{ index: number; error: string }>;
  }>> {
    return await apiClient.post(`${GRADES_API}/import`, {grades: data});
  },

  /**
   * 导出年级数据
   */
  async exportGrades(params?: GradeLevelQueryParams): Promise<Blob> {
    return await apiClient.get(`${GRADES_API}/export`, {
      params,
      responseType: 'blob'
    });
  },

  /**
   * 重置年级排序（按名称排序）
   */
  async resetGradeOrders(): Promise<ApiResponse<void>> {
    return await apiClient.post(`${GRADES_API}/reset-orders`);
  },

  /**
   * 获取年级使用情况详情
   */
  async getGradeUsage(id: string): Promise<ApiResponse<{
    student_count: number;
    class_count: number;
    exam_count: number;
    recent_students: any[];
    recent_classes: any[];
    recent_exams: any[];
  }>> {
    return await apiClient.get(`${GRADES_API}/${id}/usage`);
  }
};

/**
 * 年级查询 API（便捷方法）
 */
export const gradeQueryApi = {
  /**
   * 按代码获取年级
   */
  async getGradeByCode(code: string): Promise<ApiResponse<GradeLevel | null>> {
    const response = await gradeApi.getGrades({ 
      search: code,
      page_size: 1
    });
    
    const grades = response.data || [];
    const grade = grades[0]?.code === code ? grades[0] : null;
    
    return {
      success: response.success,
      message: response.message,
      timestamp: response.timestamp,
      data: grade,
      error_code: response.error_code
    };
  },

  /**
   * 获取激活的年级列表
   */
  async getActiveGrades(): Promise<ApiResponse<GradeLevel[]>> {
    const response = await gradeApi.getGrades({ 
      is_active: true,
      page_size: 1000,
      order_by: 'order_level',
      order_direction: 'asc'
    });
    
    return {
      success: response.success,
      message: response.message,
      timestamp: response.timestamp,
      data: response.data || [],
      error_code: response.error_code
    };
  },

  /**
   * 搜索年级
   */
  async searchGrades(keyword: string): Promise<ApiResponse<GradeLevel[]>> {
    const response = await gradeApi.getGrades({ 
      search: keyword,
      is_active: true,
      page_size: 100
    });
    
    return {
      success: response.success,
      message: response.message,
      timestamp: response.timestamp,
      data: response.data || [],
      error_code: response.error_code
    };
  }
};

// 导出默认年级API
export default gradeApi;