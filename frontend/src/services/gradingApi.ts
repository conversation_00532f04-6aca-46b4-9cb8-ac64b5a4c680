// API service for grading center
import apiClient from '../services/examApi';

// Paper Scan Types
export interface PaperScan {
  id: string;
  exam_id: string;
  student_id: string;
  paper_images: string[];
  scan_status: string;
  scan_quality: number;
  scan_time: string;
  scanner_device: string;
  scanner_operator: string;
  created_at: string;
  updated_at: string;
}

export interface PaperScanStats {
  total_scanned: number;
  pending_processing: number;
  processing_complete: number;
  exception_count: number;
  quality_issues: number;
}

export interface ScanExceptionRecord {
  id: string;
  scan_id: string;
  exception_type: string;
  exception_reason: string;
  auto_resolution: boolean;
  manual_resolution: boolean;
  resolver_id?: string;
  resolution_notes?: string;
  created_at: string;
  resolved_at?: string;
}

// Grading Types
export interface GradingTask {
  id: string;
  exam_id: string;
  question_id: string;
  paper_block_id: string;
  grader_id?: string;
  grading_method: string;
  ai_confidence?: number;
  status: string;
  score?: number;
  original_score?: number;
  review_required: boolean;
  assigned_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface GradingStats {
  total_papers: number;
  completed_papers: number;
  pending_papers: number;
  exception_papers: number;
  average_score: number;
  grading_progress: number;
  ai_grading_ratio: number;
  manual_review_ratio: number;
}

export interface GraderStats {
  grader_id: string;
  grader_name: string;
  assigned_count: number;
  completed_count: number;
  pending_count: number;
  average_score: number;
  consistency_score: number;
  efficiency_score: number;
}

// AI Grading Types
export interface AIGradingRecord {
  id: string;
  task_id: string;
  ai_agent_id: string;
  ai_model_version: string;
  input_data: any;
  output_result: any;
  confidence_score: number;
  processing_time: number;
  status: string;
  error_message?: string;
  created_at: string;
}

export interface AIGradingStats {
  total_processed: number;
  success_rate: number;
  average_confidence: number;
  average_processing_time: number;
  error_rate: number;
  model_performance: any;
}

// Exception Management Types
export interface StudentException {
  id: string;
  exam_id: string;
  student_number: string;
  exception_type: string;
  exception_reason: string;
  possible_students: any[];
  resolved: boolean;
  resolver_id?: string;
  resolution_method?: string;
  correct_student_id?: string;
  created_at: string;
  resolved_at?: string;
}

export interface ExceptionStats {
  total_exceptions: number;
  resolved_exceptions: number;
  pending_exceptions: number;
  auto_resolved: number;
  manual_resolved: number;
  resolution_rate: number;
}

// Monitoring Types
export interface GradingMonitor {
  exam_id: string;
  question_id: string;
  grader_stats: GraderStats[];
  score_distribution: any;
  consistency_metrics: any;
  quality_indicators: any;
  alert_status: string;
  last_updated: string;
}

export interface QualityAlert {
  id: string;
  exam_id: string;
  alert_type: string;
  severity: string;
  message: string;
  affected_items: string[];
  status: string;
  created_at: string;
  resolved_at?: string;
}

// Request/Response Types
export interface CreateScanRequest {
  exam_id: string;
  student_id: string;
  paper_images: string[];
  scanner_device: string;
}

export interface GradingTaskRequest {
  exam_id: string;
  question_id: string;
  grader_id?: string;
  grading_method: string;
  priority: string;
}

export interface ExceptionResolutionRequest {
  resolution_method: string;
  correct_student_id?: string;
  resolution_notes?: string;
}

export interface GradingControlRequest {
  action: string;
  scope: string;
  target_id?: string;
  reason?: string;
}

// Paper Scan API
export const paperScanApi = {
  // Upload and scan papers
  uploadPaperScan: async (scanData: CreateScanRequest): Promise<PaperScan> => {
    return apiClient.post('/api/v1/grading/scans', scanData);
  },

  // Get scan statistics
  getScanStats: async (examId: string): Promise<PaperScanStats> => {
    return apiClient.get(`/api/v1/grading/scans/${examId}/stats`);
  },

  // Get paper scans
  getPaperScans: async (examId: string, params?: any): Promise<PaperScan[]> => {
    return apiClient.get(`/api/v1/grading/scans/${examId}`, { params });
  },

  // Get scan by ID
  getScanById: async (scanId: string): Promise<PaperScan> => {
    return apiClient.get(`/api/v1/grading/scans/detail/${scanId}`);
  },

  // Get scan exceptions
  getScanExceptions: async (examId: string): Promise<ScanExceptionRecord[]> => {
    return apiClient.get(`/api/v1/grading/scans/${examId}/exceptions`);
  },

  // Resolve scan exception
  resolveScanException: async (exceptionId: string, resolution: ExceptionResolutionRequest): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/scans/exceptions/${exceptionId}/resolve`, resolution);
  },

  // Batch upload scans
  batchUploadScans: async (examId: string, scans: CreateScanRequest[]): Promise<PaperScan[]> => {
    return apiClient.post(`/api/v1/grading/scans/${examId}/batch`, { scans });
  },

  // Check scan quality
  checkScanQuality: async (scanId: string): Promise<any> => {
    return apiClient.get(`/api/v1/grading/scans/${scanId}/quality`);
  },
};

// Grading Task API
export const gradingTaskApi = {
  // Create grading task
  createGradingTask: async (taskData: GradingTaskRequest): Promise<GradingTask> => {
    return apiClient.post('/api/v1/grading/tasks', taskData);
  },

  // Get grading tasks
  getGradingTasks: async (examId: string, params?: any): Promise<GradingTask[]> => {
    return apiClient.get(`/api/v1/grading/tasks/${examId}`, { params });
  },

  // Get grading statistics
  getGradingStats: async (examId: string): Promise<GradingStats> => {
    return apiClient.get(`/api/v1/grading/tasks/${examId}/stats`);
  },

  // Assign grading task
  assignGradingTask: async (taskId: string, graderId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/tasks/${taskId}/assign`, { grader_id: graderId });
  },

  // Complete grading task
  completeGradingTask: async (taskId: string, score: number, notes?: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/tasks/${taskId}/complete`, { score, notes });
  },

  // Get grader statistics
  getGraderStats: async (examId: string): Promise<GraderStats[]> => {
    return apiClient.get(`/api/v1/grading/tasks/${examId}/grader-stats`);
  },

  // Start AI grading
  startAIGrading: async (examId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/tasks/${examId}/ai-grading/start`);
  },

  // Get AI grading records
  getAIGradingRecords: async (examId: string): Promise<AIGradingRecord[]> => {
    return apiClient.get(`/api/v1/grading/tasks/${examId}/ai-grading/records`);
  },

  // Get AI grading statistics
  getAIGradingStats: async (examId: string): Promise<AIGradingStats> => {
    return apiClient.get(`/api/v1/grading/tasks/${examId}/ai-grading/stats`);
  },
};

// Exception Management API
export const exceptionApi = {
  // Get student exceptions
  getStudentExceptions: async (examId: string): Promise<StudentException[]> => {
    return apiClient.get(`/api/v1/grading/exceptions/${examId}/students`);
  },

  // Resolve student exception
  resolveStudentException: async (exceptionId: string, resolution: ExceptionResolutionRequest): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/exceptions/students/${exceptionId}/resolve`, resolution);
  },

  // Get exception statistics
  getExceptionStats: async (examId: string): Promise<ExceptionStats> => {
    return apiClient.get(`/api/v1/grading/exceptions/${examId}/stats`);
  },

  // Batch resolve exceptions
  batchResolveExceptions: async (examId: string, resolutions: any[]): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/exceptions/${examId}/batch-resolve`, { resolutions });
  },

  // Get possible students for exception
  getPossibleStudents: async (exceptionId: string): Promise<any[]> => {
    return apiClient.get(`/api/v1/grading/exceptions/students/${exceptionId}/possible`);
  },
};

// Monitoring API
export const monitoringApi = {
  // Get grading monitor data
  getGradingMonitor: async (examId: string): Promise<GradingMonitor> => {
    return apiClient.get(`/api/v1/grading/monitoring/${examId}`);
  },

  // Get quality alerts
  getQualityAlerts: async (examId: string): Promise<QualityAlert[]> => {
    return apiClient.get(`/api/v1/grading/monitoring/${examId}/alerts`);
  },

  // Resolve quality alert
  resolveQualityAlert: async (alertId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/monitoring/alerts/${alertId}/resolve`);
  },

  // Get score distribution
  getScoreDistribution: async (examId: string, questionId?: string): Promise<any> => {
    return apiClient.get(`/api/v1/grading/monitoring/${examId}/score-distribution`, {
      params: { question_id: questionId }
    });
  },

  // Get consistency metrics
  getConsistencyMetrics: async (examId: string): Promise<any> => {
    return apiClient.get(`/api/v1/grading/monitoring/${examId}/consistency`);
  },

  // Get grading progress
  getGradingProgress: async (examId: string): Promise<any> => {
    return apiClient.get(`/api/v1/grading/monitoring/${examId}/progress`);
  },
};

// Control API
export const gradingControlApi = {
  // Start grading
  startGrading: async (examId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/control/${examId}/start`);
  },

  // Pause grading
  pauseGrading: async (examId: string, reason?: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/control/${examId}/pause`, { reason });
  },

  // Resume grading
  resumeGrading: async (examId: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/control/${examId}/resume`);
  },

  // Stop grading
  stopGrading: async (examId: string, reason?: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/control/${examId}/stop`, { reason });
  },

  // Control question grading
  controlQuestionGrading: async (examId: string, questionId: string, action: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/control/${examId}/questions/${questionId}/${action}`);
  },

  // Control grader
  controlGrader: async (examId: string, graderId: string, action: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/control/${examId}/graders/${graderId}/${action}`);
  },

  // Get grading status
  getGradingStatus: async (examId: string): Promise<any> => {
    return apiClient.get(`/api/v1/grading/control/${examId}/status`);
  },

  // Emergency stop
  emergencyStop: async (examId: string, reason: string): Promise<boolean> => {
    return apiClient.post(`/api/v1/grading/control/${examId}/emergency-stop`, { reason });
  },
};