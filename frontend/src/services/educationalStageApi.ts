/**
 * 学段管理 API 服务
 * 提供学段相关的 API 调用接口
 */

import apiClient from './apiClient';
import {ApiResponse, PaginatedApiResponse} from '@/types';
import {
  EducationalStage,
  CreateEducationalStageRequest,
  UpdateEducationalStageRequest,
  EducationalStageQueryParams, EducationStageSummary,
} from '@/types/educationalStage';

// 基础API路径
const EDUCATIONAL_STAGES_API = '/api/v1/education-stages';

/**
 * 学段管理 API
 */
export const educationalStageApi = {
  /**
   * 获取学段列表（分页）
   */
  async getEducationalStages(
    params?: EducationalStageQueryParams
  ): Promise<PaginatedApiResponse<EducationalStage>> {
    return await apiClient.get(EDUCATIONAL_STAGES_API, { params });
  },

  /**
   * 获取学段简要信息列表（用于下拉选择）
   */
  async getEducationStageSummaries(isActive?: boolean): Promise<ApiResponse<EducationStageSummary[]>> {
    return await apiClient.get(`${EDUCATIONAL_STAGES_API}/summaries`, {
      params: isActive !== undefined ? {is_active: isActive} : undefined
    });
  },

  /**
   * 获取单个学段详情
   */
  async getEducationalStage(id: string): Promise<ApiResponse<EducationalStage>> {
    return await apiClient.get(`${EDUCATIONAL_STAGES_API}/${id}`);
  },

  /**
   * 创建学段
   */
  async createEducationalStage(
    data: CreateEducationalStageRequest
  ): Promise<ApiResponse<EducationalStage>> {
    return await apiClient.post(`${EDUCATIONAL_STAGES_API}/create`, data);
  },

  /**
   * 更新学段
   */
  async updateEducationalStage(
    id: string,
    data: UpdateEducationalStageRequest
  ): Promise<ApiResponse<EducationalStage>> {
    return await apiClient.put(`${EDUCATIONAL_STAGES_API}/${id}`, data);
  },

  /**
   * 删除学段（软删除）
   */
  async deleteEducationalStage(id: string): Promise<ApiResponse<void>> {
    return await apiClient.delete(`${EDUCATIONAL_STAGES_API}/${id}`);
  },

  /**
   * 切换学段状态
   */
  async toggleEducationalStageStatus(
    id: string,
    isActive: boolean
  ): Promise<ApiResponse<EducationalStage>> {
    // 使用 PUT 方法更新学段状态
    return await apiClient.put(`${EDUCATIONAL_STAGES_API}/${id}`, {
      is_active: isActive,
    });
  },

  /**
   * 检查学段代码是否可用
   */
  async checkCodeAvailable(code: string): Promise<boolean> {
    try {
      const response = await educationalStageApi.getEducationalStages({
        search: code,
        page_size: 1,
      });
      
      const stages = response.data || [];
      return !stages.some((stage) => stage.code === code);
    } catch (error) {
      console.error('Failed to check code availability:', error);
      return false;
    }
  },

  /**
   * 导出学段数据
   */
  async exportEducationalStages(
    params?: EducationalStageQueryParams
  ): Promise<Blob> {
    const response = await apiClient.get(`${EDUCATIONAL_STAGES_API}/export`, {
      params,
      responseType: 'blob',
    });
    return response.data;
  },
};

// 导出默认学段API
export default educationalStageApi;
