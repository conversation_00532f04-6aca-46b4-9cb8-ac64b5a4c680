import { ApiResponse } from '@/types';
import { CreateSubjectGroupsParams, SubjectGroups, SubjectGroupsDetail, UpdateSubjectGroupsParams } from '@/types/subjectGroups';
import apiClient from './examApi';



// 基础API路径
const SUBJECTGRUOPS_API = '/api/v1/tenants';
/**
 * 作者：张瀚
 * 说明：学科组管理API
 */
export const SubjectGroupsApi = {
  /**
   * 作者：张瀚
   * 说明：创建学科组
   */
  createSubjectGroups: async (tenantName: string, params: CreateSubjectGroupsParams): Promise<ApiResponse<SubjectGroups>> => {
    console.log('createSubjectGroups', params)
    return apiClient.post(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/createSubjectGroups`, params);
  },
  /**
   * 作者：张瀚
   * 说明：查询所有学科组信息
   */
  findAll: async (tenantName: string): Promise<ApiResponse<SubjectGroupsDetail[]>> => {
    return apiClient.post(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/findAll`);
  },
  /**
   * 说明：删除学科组
   */
  deleteSubjectGroup: async (tenantName: string, id: string): Promise<ApiResponse<void>> => {
    return apiClient.post(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/deleteSubjectGroup`, { id });
  },
  /**
   * 说明：更新学科组信息
   */
  updateSubjectGroups: async (tenantName: string, params: UpdateSubjectGroupsParams): Promise<ApiResponse<SubjectGroups>> => {
    return apiClient.post(`${SUBJECTGRUOPS_API}/${tenantName}/subjectGroups/updateSubjectGroups`, params);
  },
};

