import apiClient from "@/services/apiClient.ts";
import {BindIdentityRequest, IdentityInfo} from "@/types/identity.ts";

export async function getIdentities():Promise<IdentityInfo[]> {
    const response = await apiClient.get('/api/v1/identity/list');
    return response.data;
}

export async function bindIdentities(request:BindIdentityRequest) {
    const response = await apiClient.post('/api/v1/identity/bind', request);
    return response.data;
}