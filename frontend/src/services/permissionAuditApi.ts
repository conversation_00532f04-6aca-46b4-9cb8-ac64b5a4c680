import apiClient from './apiClient';

// 权限审计API接口定义
export interface AuditQueryParams {
  operation?: string;
  menu_id?: string;
  operator_id?: string;
  tenant_id?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
  sort_by?: string;
  sort_order?: string;
  page?: number;
  page_size?: number;
}

export interface AuditStatsQueryParams {
  tenant_id?: string;
  time_range?: string; // "day", "week", "month", "year"
  group_by?: string;   // "operation", "operator", "menu", "tenant"
  start_date?: string;
  end_date?: string;
}

export interface AuditLogResponse {
  id: string;
  menu_id: string;
  operation: string;
  old_config?: any;
  new_config?: any;
  changes_summary: any;
  affected_users_count: number;
  operator_id: string;
  operator_identity: string;
  tenant_id?: string;
  reason?: string;
  impact_analysis: any;
  rollback_data?: any;
  is_rolled_back: boolean;
  rollback_at?: string;
  created_at: string;
  menu_name?: string;
  operator_name?: string;
  change_magnitude: string; // "minor", "moderate", "major", "critical"
}

export interface AuditStatisticsResponse {
  time_range: string;
  total_operations: number;
  operations_by_type: Array<{
    operation: string;
    count: number;
    percentage: number;
    trend: string; // "increasing", "decreasing", "stable"
  }>;
  operations_by_operator: Array<{
    operator_id: string;
    operator_identity: string;
    operator_name?: string;
    operation_count: number;
    most_common_operation: string;
    risk_score: number; // 0.0 to 1.0
  }>;
  operations_by_menu: Array<{
    menu_id: string;
    menu_name: string;
    modification_count: number;
    last_modified: string;
    stability_score: number; // 0.0 to 1.0, higher is more stable
  }>;
  operations_by_tenant: Array<{
    tenant_id: string;
    operation_count: number;
    unique_operators: number;
    most_active_menu: string;
    activity_score: number;
  }>;
  timeline: Array<{
    date: string;
    operation_count: number;
    unique_operators: number;
    high_risk_operations: number;
  }>;
  risk_analysis: {
    overall_risk_level: string; // "low", "medium", "high", "critical"
    risk_factors: Array<{
      factor_type: string;
      description: string;
      severity: string;
      affected_count: number;
    }>;
    recommendations: string[];
    anomalies_detected: number;
  };
}

export interface AuditSummaryResponse {
  total_logs: number;
  recent_activity: Array<{
    operation: string;
    menu_id: string;
    menu_name: string;
    operator_identity: string;
    created_at: string;
    impact_level: string;
  }>;
  top_operators: Array<{
    operator_id: string;
    operator_identity: string;
    operation_count: number;
    last_activity: string;
  }>;
  frequent_operations: Array<{
    operation: string;
    count: number;
    avg_impact: number;
  }>;
  system_health: {
    health_score: number; // 0.0 to 1.0
    status: string;    // "healthy", "warning", "critical"
    indicators: Array<{
      metric_name: string;
      current_value: number;
      threshold: number;
      status: string;
    }>;
  };
  alerts: Array<{
    alert_type: string;
    severity: string;
    message: string;
    affected_count: number;
    recommendation: string;
  }>;
}

export interface RollbackRequest {
  audit_log_id: string;
  reason: string;
  confirm_rollback: boolean;
}

export interface RollbackResponse {
  audit_log_id: string;
  rollback_successful: boolean;
  rollback_details: any;
  new_audit_log_id?: string;
  message: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
}

// 权限审计API服务
export const permissionAuditApi = {
  /**
   * 获取审计日志列表
   */
  async getAuditLogs(params?: AuditQueryParams): Promise<PaginatedResponse<AuditLogResponse>> {
    const response = await apiClient.get('/api/v1/admin/audit/logs', { params });
    return response.data;
  },

  /**
   * 获取审计日志详情
   */
  async getAuditLogById(logId: string): Promise<{ data: AuditLogResponse }> {
    const response = await apiClient.get(`/api/v1/admin/audit/logs/${logId}`);
    return response.data;
  },

  /**
   * 获取审计统计信息
   */
  async getAuditStatistics(params?: AuditStatsQueryParams): Promise<{ data: AuditStatisticsResponse }> {
    const response = await apiClient.get('/api/v1/admin/audit/statistics', { params });
    return response.data;
  },

  /**
   * 获取审计摘要
   */
  async getAuditSummary(): Promise<{ data: AuditSummaryResponse }> {
    const response = await apiClient.get('/api/v1/admin/audit/summary');
    return response.data;
  },

  /**
   * 回滚权限变更
   */
  async rollbackChange(request: RollbackRequest): Promise<{ data: RollbackResponse }> {
    const response = await apiClient.post('/api/v1/admin/audit/rollback', request);
    return response.data;
  },

  /**
   * 批量回滚权限变更
   */
  async batchRollbackChanges(requests: RollbackRequest[]): Promise<{ 
    data: {
      total_requested: number;
      successful_count: number;
      failed_count: number;
      results: Array<{
        audit_log_id: string;
        success: boolean;
        message: string;
        rollback_details?: any;
      }>;
      execution_time_ms: number;
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/audit/batch-rollback', {
      rollback_requests: requests
    });
    return response.data;
  },

  /**
   * 预览回滚操作
   */
  async previewRollback(auditLogId: string): Promise<{ 
    data: {
      audit_log_id: string;
      can_rollback: boolean;
      rollback_preview: {
        menu_id: string;
        menu_name: string;
        current_config: any;
        rollback_config: any;
        changes_to_revert: any;
        affected_users_estimate: number;
        potential_conflicts: string[];
      };
      warnings: string[];
      recommendations: string[];
    }
  }> {
    const response = await apiClient.get(`/api/v1/admin/audit/${auditLogId}/rollback-preview`);
    return response.data;
  },

  /**
   * 导出审计日志
   */
  async exportAuditLogs(params?: AuditQueryParams & { 
    format?: 'json' | 'csv' | 'xlsx';
    include_details?: boolean;
  }): Promise<{ 
    data: {
      export_url: string;
      download_token: string;
      expires_at: string;
      export_summary: {
        total_logs: number;
        date_range: string;
        file_size_mb: number;
      };
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/audit/export', params);
    return response.data;
  },

  /**
   * 清理旧审计日志
   */
  async cleanupOldLogs(config: {
    retention_days: number;
    archive_before_delete?: boolean;
    dry_run?: boolean;
  }): Promise<{ 
    data: {
      dry_run: boolean;
      logs_to_delete: number;
      logs_to_archive: number;
      estimated_space_freed_mb: number;
      deletion_details: Array<{
        date_range: string;
        log_count: number;
        operations_included: string[];
      }>;
      warnings: string[];
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/audit/cleanup', config);
    return response.data;
  },

  /**
   * 获取操作者活动分析
   */
  async getOperatorActivity(operatorId: string, timeRange?: string): Promise<{ 
    data: {
      operator_id: string;
      operator_identity: string;
      operator_name?: string;
      activity_summary: {
        total_operations: number;
        operation_types: { [key: string]: number };
        affected_menus: number;
        first_activity: string;
        last_activity: string;
        most_active_day: string;
        avg_operations_per_day: number;
      };
      timeline: Array<{
        date: string;
        operation_count: number;
        high_impact_operations: number;
        menu_types_modified: string[];
      }>;
      risk_assessment: {
        risk_score: number;
        risk_level: string;
        risk_factors: string[];
        recommendations: string[];
      };
      recent_operations: Array<{
        operation: string;
        menu_id: string;
        menu_name: string;
        impact_level: string;
        created_at: string;
      }>;
    }
  }> {
    const params = timeRange ? { time_range: timeRange } : {};
    const response = await apiClient.get(`/api/v1/admin/audit/operators/${operatorId}/activity`, { params });
    return response.data;
  },

  /**
   * 获取菜单变更历史
   */
  async getMenuChangeHistory(menuId: string): Promise<{ 
    data: {
      menu_id: string;
      menu_name: string;
      change_summary: {
        total_changes: number;
        first_created: string;
        last_modified: string;
        change_frequency: number; // changes per month
        stability_score: number;
      };
      change_timeline: Array<{
        audit_log_id: string;
        operation: string;
        operator_identity: string;
        changes_summary: any;
        impact_level: string;
        created_at: string;
        is_rolled_back: boolean;
      }>;
      impact_analysis: {
        total_users_affected: number;
        permission_evolution: Array<{
          date: string;
          permissions_added: string[];
          permissions_removed: string[];
          permissions_modified: string[];
        }>;
        access_pattern_changes: Array<{
          date: string;
          access_level_change: number;
          activation_status_changed: boolean;
        }>;
      };
    }
  }> {
    const response = await apiClient.get(`/api/v1/admin/audit/menus/${menuId}/history`);
    return response.data;
  },

  /**
   * 检测异常活动
   */
  async detectAnomalies(config?: {
    time_range?: string;
    sensitivity?: 'low' | 'medium' | 'high';
    check_types?: string[];
  }): Promise<{ 
    data: {
      detection_summary: {
        total_anomalies: number;
        high_severity: number;
        medium_severity: number;
        low_severity: number;
        detection_time_range: string;
      };
      anomalies: Array<{
        type: string;
        severity: string;
        description: string;
        affected_entities: string[];
        detection_confidence: number;
        first_detected: string;
        last_seen: string;
        suggested_actions: string[];
        related_logs: string[];
      }>;
      patterns: Array<{
        pattern_type: string;
        description: string;
        frequency: number;
        examples: string[];
      }>;
      recommendations: string[];
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/audit/detect-anomalies', config);
    return response.data;
  },

  /**
   * 生成合规报告
   */
  async generateComplianceReport(config: {
    report_type: 'access_control' | 'change_management' | 'user_activity' | 'full_compliance';
    time_range: string;
    include_recommendations?: boolean;
    compliance_standards?: string[]; // e.g., ["ISO27001", "SOX", "GDPR"]
  }): Promise<{ 
    data: {
      report_id: string;
      report_type: string;
      report_url: string;
      download_token: string;
      expires_at: string;
      report_summary: {
        compliance_score: number;
        total_checks: number;
        passed_checks: number;
        failed_checks: number;
        warnings: number;
        critical_issues: number;
      };
      key_findings: Array<{
        category: string;
        finding: string;
        severity: string;
        recommendation: string;
      }>;
      next_review_date: string;
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/audit/compliance-report', config);
    return response.data;
  },

  /**
   * 设置审计告警规则
   */
  async setAlertRules(rules: Array<{
    rule_name: string;
    trigger_conditions: {
      operations?: string[];
      operators?: string[];
      menus?: string[];
      impact_level?: string;
      frequency_threshold?: number;
      time_window_minutes?: number;
    };
    alert_channels: string[]; // ["email", "webhook", "slack"]
    is_active: boolean;
  }>): Promise<{ 
    data: {
      created_rules: number;
      updated_rules: number;
      failed_rules: number;
      rule_results: Array<{
        rule_name: string;
        status: 'created' | 'updated' | 'failed';
        message?: string;
      }>;
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/audit/alert-rules', { rules });
    return response.data;
  },

  /**
   * 获取审计告警规则
   */
  async getAlertRules(): Promise<{ 
    data: Array<{
      rule_id: string;
      rule_name: string;
      trigger_conditions: any;
      alert_channels: string[];
      is_active: boolean;
      created_at: string;
      last_triggered?: string;
      trigger_count: number;
    }>
  }> {
    const response = await apiClient.get('/api/v1/admin/audit/alert-rules');
    return response.data;
  },

  /**
   * 获取实时监控仪表板数据
   */
  async getDashboardData(): Promise<{ 
    data: {
      current_activity: {
        active_operators: number;
        operations_last_hour: number;
        high_risk_operations_today: number;
        system_health_score: number;
      };
      recent_operations: Array<{
        operation: string;
        menu_name: string;
        operator_identity: string;
        impact_level: string;
        created_at: string;
      }>;
      trending_metrics: {
        operations_trend: Array<{ time: string; count: number }>;
        risk_trend: Array<{ time: string; risk_score: number }>;
        operator_activity: Array<{ operator: string; count: number }>;
        menu_modifications: Array<{ menu: string; count: number }>;
      };
      alerts: Array<{
        alert_type: string;
        severity: string;
        message: string;
        created_at: string;
        is_acknowledged: boolean;
      }>;
    }
  }> {
    const response = await apiClient.get('/api/v1/admin/audit/dashboard');
    return response.data;
  }
};

export default permissionAuditApi;