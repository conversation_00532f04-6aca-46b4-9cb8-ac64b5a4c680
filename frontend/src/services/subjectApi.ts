/**
 * 学科管理 API 服务
 * 提供学科相关的 API 调用接口
 */

import apiClient from './apiClient';
import {ApiResponse, PaginatedApiResponse} from '@/types';
import {
  CheckCodeResponse,
  CreateSubjectRequest,
  Subject,
  SubjectQueryParams,
  SubjectStatistics,
  SubjectSummary,
  UpdateSubjectRequest
} from '@/types/subject';

// 基础API路径
const SUBJECTS_API = '/api/v1/subjects';

/**
 * 学科管理 API
 */
export const subjectApi = {

  /**
   * 作者：张瀚
   * 说明：学科列表分页条件查询
   */
  async pageSubject(params: PageSubjectParams): Promise<PaginatedApiResponse<Subject>> {
    return apiClient.post(`/api/v1/subjects/pageSubject`, params);
  },

  /**
   * 获取学科列表（支持分页和查询）
   */
  async getSubjects(params?: SubjectQueryParams): Promise<PaginatedApiResponse<Subject>> {
    return await apiClient.get(SUBJECTS_API, {params});
  },

  /**
   * 创建学科
   */
  async createSubject(data: CreateSubjectRequest): Promise<ApiResponse<Subject>> {
    return await apiClient.post(SUBJECTS_API + '/create', data);
  },

  /**
   * 更新学科
   */
  async updateSubject(id: string, data: UpdateSubjectRequest): Promise<ApiResponse<Subject>> {
    return  await apiClient.put(`${SUBJECTS_API}/${id}`, data);
  },

  /**
   * 删除学科（软删除）
   */
  async deleteSubject(id: string): Promise<ApiResponse<void>> {
    return await apiClient.delete(`${SUBJECTS_API}/${id}`);
  },

  /**
   * 获取学科简要信息列表（用于下拉选择）
   */
  async getSubjectSummaries(isActive?: boolean): Promise<ApiResponse<SubjectSummary[]>> {
    return await apiClient.get(`${SUBJECTS_API}/summaries`, {
      params: { is_active: isActive ?? true }
    });
  },

  /**
   * 获取单个学科详情
   */
  async getSubject(id: string): Promise<ApiResponse<Subject>> {
    return await apiClient.get(`${SUBJECTS_API}/${id}`);
  },

  /**
   * 获取学科统计信息
   */
  async getSubjectStatistics(): Promise<ApiResponse<SubjectStatistics>> {
    return await apiClient.get(`${SUBJECTS_API}/statistics`)
  },

  /**
   * 批量更新学科排序
   */
  async updateSubjectOrders(orders: Array<[string, number]>): Promise<ApiResponse<void>> {
    return await apiClient.patch(`${SUBJECTS_API}/orders`, { orders });
  },

  /**
   * 检查学科代码是否可用
   */
  async checkCodeAvailability(code: string, excludeId?: string): Promise<ApiResponse<CheckCodeResponse>> {
    const params: any = { code };
    if (excludeId) {
      params.exclude_id = excludeId;
    }
    return await apiClient.get(`${SUBJECTS_API}/check-code`, { params });

  },

  /**
   * 启用/禁用学科
   */
  async toggleSubjectStatus(id: string, isActive: boolean): Promise<ApiResponse<Subject>> {
    return await apiClient.put(`${SUBJECTS_API}/${id}`, {
      is_active: isActive
    });

  },

  /**
   * 批量删除学科
   */
  async batchDeleteSubjects(ids: string[]): Promise<ApiResponse<void>> {
    return await apiClient.delete(`${SUBJECTS_API}/batch`, {
      data: { ids }
    });

  },

  /**
   * 复制学科
   */
  async cloneSubject(id: string, name: string, code: string): Promise<ApiResponse<Subject>> {
    return await apiClient.post(`${SUBJECTS_API}/${id}/clone`, {
      name,
      code
    });
  },

  /**
   * 导入学科数据
   */
  async importSubjects(data: CreateSubjectRequest[]): Promise<ApiResponse<{
    success_count: number;
    error_count: number;
    errors: Array<{ index: number; error: string }>;
  }>> {
    return await apiClient.post(`${SUBJECTS_API}/import`, { subjects: data });
  },

  /**
   * 导出学科数据
   */
  async exportSubjects(params?: SubjectQueryParams): Promise<Blob> {
    return await apiClient.get(`${SUBJECTS_API}/export`, {
      params,
      responseType: 'blob'
    });
  },

  /**
   * 重置学科排序（按名称排序）
   */
  async resetSubjectOrders(): Promise<ApiResponse<void>> {
    return await apiClient.post(`${SUBJECTS_API}/reset-orders`);
  },

  /**
   * 获取学科使用情况详情
   */
  async getSubjectUsage(id: string): Promise<ApiResponse<{
    question_count: number;
    paper_count: number;
    exam_count: number;
    recent_questions: any[];
    recent_papers: any[];
    recent_exams: any[];
  }>> {
    return await apiClient.get(`${SUBJECTS_API}/${id}/usage`);
  }
};

/**
 * 学科查询 API（便捷方法）
 */
export const subjectQueryApi = {
  /**
   * 按代码获取学科
   */
  async getSubjectByCode(code: string): Promise<ApiResponse<Subject | null>> {
    const response = await subjectApi.getSubjects({
      search: code,
      page_size: 1
    });

    const subjects = response.data || [];
    const subject = subjects.find((s: Subject) => s.code === code);

    return {
      ...response,
      data: subject || null
    };
  },

  /**
   * 获取激活的学科列表
   */
  async getActiveSubjects(): Promise<ApiResponse<Subject[]>> {
    const response = await subjectApi.getSubjects({
      is_active: true,
      page_size: 1000,
      order_by: 'order_level',
      order_direction: 'asc'
    });

    return {
      ...response,
      data: response.data || []
    };
  },

  /**
   * 搜索学科
   */
  async searchSubjects(keyword: string): Promise<ApiResponse<Subject[]>> {
    const response = await subjectApi.getSubjects({
      search: keyword,
      is_active: true,
      page_size: 100
    });

    return {
      ...response,
      data: response.data || []
    };
  }
};

// 导出默认学科API
export default subjectApi;

export interface PageSubjectParams {
  page: Number,
  page_size: Number,
  name_or_code_ilike?: String,
  is_active?: boolean,
  order_by?: String, // 排序字段：name, code, order_level, created_at
  order_direction?: String, // 排序方向：asc, desc
}
