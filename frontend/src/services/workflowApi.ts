import apiClient from './examApi';
import {
    PaginatedApiResponse,
    WorkflowSettingQueryParams,
    WorkflowSummary,
    WorkflowSetting, WorkflowCategory, ApiResponse
} from "@/types";
export const workflowSettingApi = {
    getWorkflowSetting: async (params?: WorkflowSettingQueryParams): Promise<PaginatedApiResponse<WorkflowSetting>> => {
        return await apiClient.get('/api/v1/workflow/setting', {params});
    },

    getWorkflowSummaryInSetting: async (params?: WorkflowSettingQueryParams): Promise<ApiResponse<WorkflowSummary[]>> => {
        return await apiClient.get('/api/v1/workflow/setting/summary', {params});
    },
    
    /**
     * 请求后端，后端再向ai查询可用工作流。
     */
    getWorkflowSummary: async (value: WorkflowCategory): Promise<ApiResponse<WorkflowSummary[]>> => {
        return await apiClient.get('/api/v1/workflow/summary', {params:{'workflow_type':value}});
    },
}