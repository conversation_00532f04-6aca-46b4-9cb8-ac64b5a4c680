import axios from 'axios';
import { Homework, CreateHomeworkPayload } from '../types/homework';

const API_URL = '/api/v1/homeworks'; // Adjust if your API endpoint is different

// Fetch all Homework
export const getHomeworks = async (): Promise<Homework[]> => {
  const response = await axios.get<Homework[]>(API_URL);
  return response.data;
};

// Fetch a single Homework by its ID
export const getHomeworkById = async (id: string): Promise<Homework> => {
  const response = await axios.get<Homework>(`${API_URL}/${id}`);
  return response.data;
};

// Create a new Homework
export const createHomework = async (payload: CreateHomeworkPayload): Promise<Homework> => {
  const response = await axios.post<Homework>(API_URL, payload);
  return response.data;
};

// Update an existing Homework
export const updateHomework = async (id: string, payload: Partial<CreateHomeworkPayload>): Promise<Homework> => {
  const response = await axios.put<Homework>(`${API_URL}/${id}`, payload);
  return response.data;
};

// Delete a Homework by its ID
export const deleteHomework = async (id: string): Promise<void> => {
  await axios.delete(`${API_URL}/${id}`);
};
