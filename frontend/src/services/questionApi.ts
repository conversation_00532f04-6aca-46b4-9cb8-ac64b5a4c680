// API service for paper and question management
import apiClient from './examApi';
import {
    ComposeQuestionType,
    ComposeQuestionTypeQueryParams, ComposeBindRequest,
    CreateQuestionTypeRequest,
    QuestionType,
    QuestionTypeQueryParams, QuestionTypeSummary,
    UpdateQuestionTypeRequest
} from "@/types/question.ts";
import {ApiResponse, CheckCodeResponse, PaginatedApiResponse, Subject} from "@/types";

// Question Bank Types
export interface QuestionBank {
    id: string;
    source_type: string;
    source_id?: string;
    question_type: string;
    question_content: string;
    answer_content?: string;
    difficulty_level: number;
    knowledge_points: any;
    subject: string;
    grade_level: number;
    creator_id: string;
    version: number;
    status: string;
    created_at: string;
    updated_at: string;
    usage_count: number;
    average_score?: number;
}

export interface CreateQuestionRequest {
    source_type: string;
    source_id?: string;
    question_type: string;
    question_content: string;
    answer_content?: string;
    difficulty_level: number;
    knowledge_points: any;
    subject: string;
    grade_level: number;
}

export interface UpdateQuestionRequest {
    question_content?: string;
    answer_content?: string;
    difficulty_level?: number;
    knowledge_points?: any;
    status?: string;
}

export interface QuestionQueryParams {
    page?: number;
    page_size?: number;
    subject?: string;
    grade_level?: number;
    difficulty_level?: number;
    question_type?: string;
    status?: string;
    keyword?: string;
}

export interface QuestionResponse extends QuestionBank {
}

export interface QuestionListResponse {
    questions: QuestionResponse[];
    total: number;
    page: number;
    page_size: number;
}

export interface QuestionStatistics {
    total_questions: number;
    published_questions: number;
    draft_questions: number;
    by_subject: any;
    by_difficulty: any;
    by_type: any;
}

// Paper Template Types
export interface ExamPaper {
    id: string;
    title: string;
    subject: string;
    grade_level: number;
    total_score: number;
    description?: string;
    structure: any;
    creator_id: string;
    version: number;
    status: string;
    created_at: string;
    updated_at: string;
    questions: ExamPaperQuestion[];
    usage_count: number;
}

export interface ExamPaperQuestion {
    id: string;
    exam_paper_id: string;
    question_id: string;
    question_number: string;
    score: number;
    section_name?: string;
    display_order: number;
    created_at: string;
}

export interface CreatePaperRequest {
    title: string;
    subject: string;
    grade_level: number;
    total_score: number;
    description?: string;
    structure: any;
    questions: CreatePaperQuestionRequest[];
}

export interface CreatePaperQuestionRequest {
    question_id: string;
    question_number: string;
    score: number;
    section_name?: string;
    display_order: number;
}

export interface PaperResponse extends ExamPaper {
}

export interface PaperQueryParams {
    page?: number;
    page_size?: number;
    subject?: string;
    grade_level?: number;
    status?: string;
    keyword?: string;
}

export interface PaperListResponse {
    papers: PaperResponse[];
    total: number;
    page: number;
    page_size: number;
}

// Textbook Types
export interface Textbook {
    id: string;
    title: string;
    subject_id: string;
    grade_level_id: string;
    publisher?: string;
    publication_year?: number;
    isbn?: string;
    version?: string;
    status: string;
    creator_id: string;
    created_at: string;
    updated_at: string;
}

export interface CreateTextbookRequest {
    title: string;
    subject_id: string;
    grade_level_id: string;
    publisher?: string;
    publication_year?: number;
    isbn?: string;
    version?: string;
    chapters: CreateChapterRequest[];
}

export interface CreateChapterRequest {
    chapter_number: number;
    title: string;
    content?: string;
    knowledge_points?: any;
    exercises: CreateExerciseRequest[];
}

export interface CreateExerciseRequest {
    question_content: string;
    answer_content?: string;
    difficulty_level?: number;
    knowledge_points?: any;
}

export interface TextbookResponse extends Textbook {
    chapter_count: number;
    exercise_count: number;
    access_tenants: string[];
}

// Question Bank API
export const questionBankApi = {
    // Get question statistics
    getStatistics: async (): Promise<QuestionStatistics> => {
        return apiClient.get('/api/v1/question-bank/statistics');
    },

    // Create question
    createQuestion: async (questionData: CreateQuestionRequest): Promise<QuestionResponse> => {
        return apiClient.post('/api/v1/question-bank', questionData);
    },

    // Get question list
    getQuestions: async (params?: QuestionQueryParams): Promise<QuestionListResponse> => {
        return apiClient.get('/api/v1/question-bank', {params});
    },

    // Get question by ID
    getQuestion: async (questionId: string): Promise<QuestionResponse> => {
        return apiClient.get(`/api/v1/question-bank/${questionId}`);
    },

    // Update question
    updateQuestion: async (questionId: string, questionData: UpdateQuestionRequest): Promise<QuestionResponse> => {
        return apiClient.put(`/api/v1/question-bank/${questionId}`, questionData);
    },

    // Delete question
    deleteQuestion: async (questionId: string): Promise<boolean> => {
        return apiClient.delete(`/api/v1/question-bank/${questionId}`);
    },

    // Search questions
    searchQuestions: async (params: QuestionQueryParams): Promise<QuestionListResponse> => {
        return apiClient.get('/api/v1/question-bank/search', {params});
    },
};

// Paper Template API
export const paperTemplateApi = {
    // Create paper template
    createPaper: async (paperData: CreatePaperRequest): Promise<PaperResponse> => {
        return apiClient.post('/api/v1/paper-templates', paperData);
    },

    // Get paper list
    getPapers: async (params?: PaperQueryParams): Promise<PaperListResponse> => {
        return apiClient.get('/api/v1/paper-templates', {params});
    },

    // Get paper by ID
    getPaper: async (paperId: string): Promise<PaperResponse> => {
        return apiClient.get(`/api/v1/paper-templates/${paperId}`);
    },

    // Update paper
    updatePaper: async (paperId: string, paperData: Partial<CreatePaperRequest>): Promise<PaperResponse> => {
        return apiClient.put(`/api/v1/paper-templates/${paperId}`, paperData);
    },

    // Delete paper
    deletePaper: async (paperId: string): Promise<boolean> => {
        return apiClient.delete(`/api/v1/paper-templates/${paperId}`);
    },

    // Publish paper
    publishPaper: async (paperId: string): Promise<boolean> => {
        return apiClient.post(`/api/v1/paper-templates/${paperId}/publish`);
    },

    // Get paper statistics
    getPaperStatistics: async (): Promise<any> => {
        return apiClient.get('/api/v1/paper-templates/statistics');
    },
};

// Textbook API
export const textbookApi = {
    // Create textbook
    createTextbook: async (textbookData: CreateTextbookRequest): Promise<TextbookResponse> => {
        return apiClient.post('/api/v1/textbooks', textbookData);
    },

    // Get textbook list
    getTextbooks: async (params?: any): Promise<TextbookResponse[]> => {
        return apiClient.get('/api/v1/textbooks', {params});
    },

    // Get textbook by ID
    getTextbook: async (textbookId: string): Promise<TextbookResponse> => {
        return apiClient.get(`/api/v1/textbooks/${textbookId}`);
    },

    // Import textbook exercises to question bank
    importTextbookExercises: async (textbookId: string): Promise<number> => {
        return apiClient.post(`/api/v1/textbooks/${textbookId}/import-exercises`);
    },

    // Grant textbook access to tenants
    grantTextbookAccess: async (textbookId: string, tenantIds: string[]): Promise<number> => {
        return apiClient.post(`/api/v1/textbooks/${textbookId}/grant-access`, {tenant_ids: tenantIds});
    },

    // Revoke textbook access
    revokeTextbookAccess: async (textbookId: string, tenantId: string): Promise<boolean> => {
        return apiClient.delete(`/api/v1/textbooks/${textbookId}/access/${tenantId}`);
    },

    // Get authorized textbooks for tenant
    getAuthorizedTextbooks: async (tenantId: string): Promise<TextbookResponse[]> => {
        return apiClient.get(`/api/v1/textbooks/authorized/${tenantId}`);
    },
};

export const questionTypeApi = {
    getQuestionTypes: async (params?: QuestionTypeQueryParams): Promise<PaginatedApiResponse<QuestionType>> => {
        return apiClient.get('/api/v1/question-type', {params});
    },

    getQuestionTypeSummaries: async (isActive?: boolean): Promise<ApiResponse<QuestionTypeSummary[]>> => {
        return apiClient.get('/api/v1/question-type/summaries', {params: { is_active: isActive ?? true }});
    },

    createQuestionType:async (data: CreateQuestionTypeRequest): Promise<ApiResponse<Subject>> =>{
        return apiClient.post(`/api/v1/question-type/create`, {...data});
    },
    updateQuestionType:async (data: UpdateQuestionTypeRequest): Promise<ApiResponse<Subject>> =>{
        return apiClient.put(`/api/v1/question-type/update`, {...data});
    },
    toggleQuestionTypeStatus: async (code: string, isActive: boolean): Promise<ApiResponse<null>> => {
        return apiClient.put(`/api/v1/question-type/update`, {code, is_active: isActive});
    },
    checkCodeAvailability: async (code: string, excludeId?: string): Promise<ApiResponse<CheckCodeResponse>> => {
        const params: any = {code};
        if (excludeId) {
            params.exclude_id = excludeId;
        }
        return await apiClient.get('/api/v1/question-type/check-code', {params});
    },

    getCompose: async (params?: ComposeQuestionTypeQueryParams): Promise<PaginatedApiResponse<ComposeQuestionType>> => {
        return apiClient.get('/api/v1/question-type/compose', {params});
    },

    bindCompose: async (data: ComposeBindRequest): Promise<ApiResponse<Subject>> =>{
        return apiClient.post(`/api/v1/question-type/compose/bind`, {...data});
    },
    unbindCompose: async (data: ComposeBindRequest): Promise<ApiResponse<Subject>> =>{
        return apiClient.post(`/api/v1/question-type/compose/unbind`, {...data});
    },
}