import apiClient from './apiClient';

// 权限测试API接口定义
export interface SingleUserTestRequest {
  user_id: string;
  tenant_id: string;
  role_type?: string;
  menu_ids: string[];
  test_name?: string;
  save_result?: boolean;
}

export interface BatchUserTestRequest {
  user_tests: Array<{
    user_id: string;
    tenant_id: string;
    role_type?: string;
    menu_ids: string[];
  }>;
  test_name?: string;
  parallel_execution?: boolean;
  save_result?: boolean;
}

export interface RoleSimulationTestRequest {
  role_type: string;
  tenant_id: string;
  menu_ids?: string[]; // 如果为空，测试所有菜单
  test_name?: string;
  include_inheritance?: boolean;
  save_result?: boolean;
}

export interface PermissionMatrixTestRequest {
  tenant_id: string;
  role_types?: string[]; // 如果为空，测试所有角色
  menu_types?: string[]; // 如果为空，测试所有菜单类型
  test_name?: string;
  generate_report?: boolean;
  save_result?: boolean;
}

export interface ConflictDetectionRequest {
  tenant_id?: string;
  scope: string; // "global", "tenant", "role", "menu"
  target_id?: string; // 具体的租户ID、角色类型或菜单ID
  check_inheritance?: boolean;
  save_result?: boolean;
}

export interface TestHistoryQueryParams {
  test_type?: string;
  tester_id?: string;
  tenant_id?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  page_size?: number;
}

export interface PermissionTestResult {
  user_id?: string;
  menu_id: string;
  menu_name: string;
  accessible: boolean;
  reason?: string;
  required_permissions: string[];
  user_permissions: string[];
  matched_permissions: string[];
  missing_permissions: string[];
  data_scopes_valid: boolean;
  decision_path: string[];
  execution_time_ms: number;
}

export interface BatchTestResponse {
  test_id?: string;
  test_name?: string;
  test_type: string;
  total_tests: number;
  passed_tests: number;
  failed_tests: number;
  execution_time_ms: number;
  results: Array<{
    user_id: string;
    tenant_id: string;
    role_type?: string;
    total_menus_tested: number;
    accessible_menus: number;
    denied_menus: number;
    menu_results: PermissionTestResult[];
  }>;
  summary: {
    total_users: number;
    total_menus: number;
    overall_pass_rate: number;
    most_accessible_menu?: string;
    least_accessible_menu?: string;
    role_performance: Array<{
      role_type: string;
      total_tests: number;
      pass_rate: number;
      avg_accessible_menus: number;
    }>;
  };
}

export interface PermissionMatrixResponse {
  test_id?: string;
  test_name?: string;
  tenant_id: string;
  matrix: Array<{
    role_type: string;
    role_display_name: string;
    menu_permissions: Array<{
      menu_id: string;
      menu_name: string;
      menu_type: string;
      accessible: boolean;
      permission_source: string; // "direct", "inherited", "denied"
    }>;
  }>;
  statistics: {
    total_roles: number;
    total_menus: number;
    total_permissions: number;
    direct_permissions: number;
    inherited_permissions: number;
    denied_permissions: number;
    coverage_percentage: number;
  };
  execution_time_ms: number;
}

export interface ConflictDetectionResponse {
  scope: string;
  target_id?: string;
  conflicts_found: boolean;
  total_conflicts: number;
  conflicts: Array<{
    conflict_type: string; // "role_conflict", "permission_overlap", "data_scope_conflict"
    severity: string;      // "high", "medium", "low"
    description: string;
    affected_entities: string[];
    suggested_resolution: string;
  }>;
  recommendations: string[];
  execution_time_ms: number;
}

export interface TestHistoryRecord {
  id: string;
  test_name?: string;
  test_type: string;
  total_tests: number;
  passed_tests: number;
  failed_tests: number;
  execution_time_ms: number;
  tester_id: string;
  tester_identity: string;
  tenant_id?: string;
  created_at: string;
  test_parameters: any;
  test_results: any;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
  };
}

// 权限测试API服务
export const permissionTestApi = {
  /**
   * 单用户权限测试
   */
  async testSingleUser(request: SingleUserTestRequest): Promise<{ data: PermissionTestResult[] }> {
    const response = await apiClient.post('/api/v1/admin/permission-tests/single-user', request);
    return response.data;
  },

  /**
   * 批量用户权限测试
   */
  async testBatchUsers(request: BatchUserTestRequest): Promise<{ data: BatchTestResponse }> {
    const response = await apiClient.post('/api/v1/admin/permission-tests/batch-users', request);
    return response.data;
  },

  /**
   * 角色模拟测试
   */
  async testRoleSimulation(request: RoleSimulationTestRequest): Promise<{ data: PermissionTestResult[] }> {
    const response = await apiClient.post('/api/v1/admin/permission-tests/role-simulation', request);
    return response.data;
  },

  /**
   * 权限矩阵测试
   */
  async testPermissionMatrix(request: PermissionMatrixTestRequest): Promise<{ data: PermissionMatrixResponse }> {
    const response = await apiClient.post('/api/v1/admin/permission-tests/permission-matrix', request);
    return response.data;
  },

  /**
   * 权限冲突检测
   */
  async detectConflicts(request: ConflictDetectionRequest): Promise<{ data: ConflictDetectionResponse }> {
    const response = await apiClient.post('/api/v1/admin/permission-tests/conflict-detection', request);
    return response.data;
  },

  /**
   * 获取测试历史
   */
  async getTestHistory(params?: TestHistoryQueryParams): Promise<PaginatedResponse<TestHistoryRecord>> {
    const response = await apiClient.get('/api/v1/admin/permission-tests/history', { params });
    return response.data;
  },

  /**
   * 获取测试历史详情
   */
  async getTestHistoryDetail(testId: string): Promise<{ data: TestHistoryRecord }> {
    const response = await apiClient.get(`/api/v1/admin/permission-tests/history/${testId}`);
    return response.data;
  },

  /**
   * 删除测试历史
   */
  async deleteTestHistory(testId: string): Promise<{ data: string }> {
    const response = await apiClient.delete(`/api/v1/admin/permission-tests/history/${testId}`);
    return response.data;
  },

  /**
   * 批量删除测试历史
   */
  async batchDeleteTestHistory(testIds: string[]): Promise<{ 
    data: {
      deleted_count: number;
      failed_count: number;
      results: Array<{
        test_id: string;
        success: boolean;
        message?: string;
      }>;
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/permission-tests/history/batch-delete', {
      test_ids: testIds
    });
    return response.data;
  },

  /**
   * 导出测试结果
   */
  async exportTestResults(testId: string, format: 'json' | 'csv' | 'xlsx' = 'json'): Promise<{ 
    data: {
      export_url: string;
      download_token: string;
      expires_at: string;
    }
  }> {
    const response = await apiClient.post(`/api/v1/admin/permission-tests/history/${testId}/export`, {
      format
    });
    return response.data;
  },

  /**
   * 批量测试预设场景
   */
  async testPresetScenarios(scenarioType: 'common_roles' | 'edge_cases' | 'security_checks' | 'performance_test', tenantId: string): Promise<{ 
    data: {
      scenario_type: string;
      tenant_id: string;
      total_scenarios: number;
      completed_scenarios: number;
      failed_scenarios: number;
      results: Array<{
        scenario_name: string;
        success: boolean;
        execution_time_ms: number;
        details: any;
      }>;
      summary: {
        overall_success_rate: number;
        avg_execution_time: number;
        critical_issues: string[];
        recommendations: string[];
      };
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/permission-tests/preset-scenarios', {
      scenario_type: scenarioType,
      tenant_id: tenantId
    });
    return response.data;
  },

  /**
   * 权限压力测试
   */
  async performStressTest(config: {
    tenant_id: string;
    concurrent_users: number;
    test_duration_minutes: number;
    menu_ids?: string[];
    user_roles?: string[];
  }): Promise<{ 
    data: {
      test_id: string;
      status: 'running' | 'completed' | 'failed';
      progress_percentage: number;
      start_time: string;
      end_time?: string;
      metrics: {
        total_requests: number;
        successful_requests: number;
        failed_requests: number;
        avg_response_time_ms: number;
        max_response_time_ms: number;
        requests_per_second: number;
        error_rate: number;
      };
      performance_issues?: Array<{
        type: string;
        description: string;
        severity: string;
        recommendations: string[];
      }>;
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/permission-tests/stress-test', config);
    return response.data;
  },

  /**
   * 获取压力测试状态
   */
  async getStressTestStatus(testId: string): Promise<{ 
    data: {
      test_id: string;
      status: 'running' | 'completed' | 'failed';
      progress_percentage: number;
      current_metrics: any;
      estimated_completion_time?: string;
    }
  }> {
    const response = await apiClient.get(`/api/v1/admin/permission-tests/stress-test/${testId}/status`);
    return response.data;
  },

  /**
   * 停止压力测试
   */
  async stopStressTest(testId: string): Promise<{ data: string }> {
    const response = await apiClient.post(`/api/v1/admin/permission-tests/stress-test/${testId}/stop`);
    return response.data;
  },

  /**
   * 生成权限测试报告
   */
  async generateTestReport(testIds: string[], reportType: 'summary' | 'detailed' | 'comparison'): Promise<{ 
    data: {
      report_id: string;
      report_type: string;
      report_url: string;
      download_token: string;
      expires_at: string;
      report_summary: {
        tests_included: number;
        total_test_cases: number;
        overall_pass_rate: number;
        critical_issues: number;
        recommendations_count: number;
      };
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/permission-tests/generate-report', {
      test_ids: testIds,
      report_type: reportType
    });
    return response.data;
  },

  /**
   * 权限覆盖率分析
   */
  async analyzeCoverage(tenantId: string): Promise<{ 
    data: {
      tenant_id: string;
      coverage_summary: {
        total_menus: number;
        tested_menus: number;
        coverage_percentage: number;
        untested_menus: string[];
      };
      role_coverage: Array<{
        role_type: string;
        tested_menus: number;
        total_applicable_menus: number;
        coverage_percentage: number;
      }>;
      permission_coverage: Array<{
        permission: string;
        tested_count: number;
        usage_count: number;
        coverage_percentage: number;
      }>;
      recommendations: string[];
    }
  }> {
    const response = await apiClient.get(`/api/v1/admin/permission-tests/coverage-analysis/${tenantId}`);
    return response.data;
  }
};

export default permissionTestApi;