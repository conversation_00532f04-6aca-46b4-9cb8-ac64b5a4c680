import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse, PaginatedApiResponse } from '@/types';
import {
  CreateStudentParams,
  FindAllStudentParams,
  Student,
  UpdateStudentParams
} from '@/types/student';
import apiClient from './apiClient';

//学生的接口，大部分需要管理权限，主要用于学生管理页面
export const studentsApi = {
  /**
     * 作者：张瀚
     * 说明：分页查询所有学生
     */
  pageAllStudent: async (tenant_id: string, tenant_name: string, params: FindAllStudentParams): Promise<PaginatedApiResponse<Student>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/students/pageAllStudent`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
     * 作者：张瀚
     * 说明：新建学生
     */
  createStudent: async (tenant_id: string, tenant_name: string, params: CreateStudentParams): Promise<ApiResponse<Student>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/students/createStudent`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
     * 作者：张瀚
     * 说明：更新学生
     */
  updateStudent: async (tenant_id: string, tenant_name: string, params: UpdateStudentParams): Promise<ApiResponse<Student>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/students/updateStudent`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },

  /**
   * 获取学生详情
   */
  getStudentDetail: async (tenant_id: string, tenant_name: string, student_id: string): Promise<ApiResponse<Student>> => {
    return apiClient.get(`/api/v1/tenants/${tenant_name}/students/${student_id}`, {
      headers: createApiHeaders(tenant_id)
    });
  },

  /**
   * 删除学生
   */
  deleteStudent: async (tenant_id: string, tenant_name: string, student_id: string): Promise<ApiResponse<boolean>> => {
    return apiClient.delete(`/api/v1/tenants/${tenant_name}/students/${student_id}`, {
      headers: createApiHeaders(tenant_id)
    });
  }
};

// 导出默认学生API
export default studentsApi;