import apiClient from './apiClient';

// 管理员菜单管理API接口定义
export interface MenuItem {
  id?: string;
  menu_id: string;
  name: string;
  path: string;
  icon?: string;
  parent_id?: string;
  menu_type: string;
  description?: string;
  component_path?: string;
  external_link?: string;
  required_roles?: string[];
  access_level: number;
  sort_order: number;
  is_active: boolean;
  cache_enabled: boolean;
  metadata?: any;
  version: number;
  last_modified_by?: string;
  last_modified_at?: string;
  created_at: string;
  updated_at: string;
  children?: MenuItem[];
  children_count: number;
  depth_level: number;
  usage_stats?: {
    total_access_count: number;
    unique_user_count: number;
    denied_access_count: number;
    last_accessed_at?: string;
    avg_daily_access: number;
  };
}

export interface MenuQueryParams {
  menu_type?: string;
  parent_id?: string;
  is_active?: boolean;
  include_children?: boolean;
  include_metadata?: boolean;
  search?: string;
  page?: number;
  page_size?: number;
}

export interface MenuCreateRequest {
  menu_id: string;
  name: string;
  path: string;
  icon?: string;
  parent_id?: string;
  menu_type?: string;
  description?: string;
  component_path?: string;
  external_link?: string;
  required_roles?: string[];
  access_level?: number;
  sort_order?: number;
  is_active?: boolean;
  metadata?: any;
}

export interface MenuUpdateRequest {
  name?: string;
  path?: string;
  icon?: string;
  parent_id?: string;
  menu_type?: string;
  description?: string;
  component_path?: string;
  external_link?: string;
  required_roles?: string[];
  access_level?: number;
  sort_order?: number;
  is_active?: boolean;
  metadata?: any;
  reason?: string;
}

export interface BatchMenuOperationRequest {
  operation: string; // "create", "update", "delete", "reorder", "status_change"
  menu_ids: string[];
  operation_data?: any;
  reason?: string;
}

export interface MenuReorderRequest {
  menu_orders: Array<{
    menu_id: string;
    new_parent_id?: string;
    new_sort_order: number;
  }>;
  reason?: string;
}

export interface BatchOperationResponse {
  operation: string;
  total_requested: number;
  successful_count: number;
  failed_count: number;
  results: Array<{
    menu_id: string;
    success: boolean;
    message?: string;
    error_code?: string;
  }>;
  execution_time_ms: number;
}

// 管理员菜单API服务
export const adminMenuApi = {
  /**
   * 获取菜单树结构（管理员专用）
   */
  async getMenuTree(params?: MenuQueryParams): Promise<{ data: MenuItem[] }> {
    const response = await apiClient.get('/api/v1/admin/menus', { params });
    return response.data;
  },

  /**
   * 创建新菜单
   */
  async createMenu(request: MenuCreateRequest): Promise<{ data: MenuItem }> {
    const response = await apiClient.post('/api/v1/admin/menus', request);
    return response.data;
  },

  /**
   * 更新菜单
   */
  async updateMenu(menuId: string, request: MenuUpdateRequest): Promise<{ data: MenuItem }> {
    const response = await apiClient.put(`/api/v1/admin/menus/${menuId}`, request);
    return response.data;
  },

  /**
   * 删除菜单
   */
  async deleteMenu(menuId: string, force: boolean = false): Promise<{ data: string }> {
    const params = force ? { search: 'force' } : {};
    const response = await apiClient.delete(`/api/v1/admin/menus/${menuId}`, { params });
    return response.data;
  },

  /**
   * 批量操作菜单
   */
  async batchOperateMenus(request: BatchMenuOperationRequest): Promise<{ data: BatchOperationResponse }> {
    const response = await apiClient.post('/api/v1/admin/menus/batch', request);
    return response.data;
  },

  /**
   * 重排序菜单
   */
  async reorderMenus(request: MenuReorderRequest): Promise<{ data: BatchOperationResponse }> {
    const response = await apiClient.post('/api/v1/admin/menus/reorder', request);
    return response.data;
  },

  /**
   * 获取菜单详情
   */
  async getMenuById(menuId: string): Promise<{ data: MenuItem }> {
    const response = await apiClient.get(`/api/v1/admin/menus/${menuId}`);
    return response.data;
  },

  /**
   * 复制菜单
   */
  async duplicateMenu(menuId: string, newMenuId: string, newName: string): Promise<{ data: MenuItem }> {
    const response = await apiClient.post(`/api/v1/admin/menus/${menuId}/duplicate`, {
      new_menu_id: newMenuId,
      new_name: newName
    });
    return response.data;
  },

  /**
   * 导出菜单配置
   */
  async exportMenus(menuIds?: string[]): Promise<{ data: any }> {
    const response = await apiClient.post('/api/v1/admin/menus/export', {
      menu_ids: menuIds
    });
    return response.data;
  },

  /**
   * 导入菜单配置
   */
  async importMenus(configData: any): Promise<{ data: BatchOperationResponse }> {
    const response = await apiClient.post('/api/v1/admin/menus/import', configData);
    return response.data;
  },

  /**
   * 获取菜单使用统计
   */
  async getMenuUsageStats(menuId: string, timeRange?: string): Promise<{ 
    data: {
      total_access_count: number;
      unique_user_count: number;
      denied_access_count: number;
      last_accessed_at?: string;
      avg_daily_access: number;
      timeline: Array<{
        date: string;
        access_count: number;
        unique_users: number;
        denied_count: number;
      }>;
    }
  }> {
    const params = timeRange ? { time_range: timeRange } : {};
    const response = await apiClient.get(`/api/v1/admin/menus/${menuId}/stats`, { params });
    return response.data;
  },

  /**
   * 清理无效菜单引用
   */
  async cleanupInvalidMenus(): Promise<{ 
    data: {
      cleaned_count: number;
      invalid_references: string[];
      message: string;
    }
  }> {
    const response = await apiClient.post('/api/v1/admin/menus/cleanup');
    return response.data;
  },

  /**
   * 验证菜单结构
   */
  async validateMenuStructure(): Promise<{ 
    data: {
      is_valid: boolean;
      issues: Array<{
        type: string;
        menu_id: string;
        description: string;
        severity: string;
      }>;
      suggestions: string[];
    }
  }> {
    const response = await apiClient.get('/api/v1/admin/menus/validate');
    return response.data;
  }
};

export default adminMenuApi;