import { createApiHeaders } from '@/lib/apiUtils';
import { ApiResponse } from '@/types';
import { AdministrativeClasses, AdministrativeClassesDetail, AdministrativeClassesStatistics, CreateAdministrativeClassesParams, DeleteAdministrativeClassesParams, FindAllStudentInClassParams, MoveStudentToAdministrativeClassesParams, RemoveStudentFromAdministrativeClassesParams, UpdateAdministrativeClassesParams } from '@/types/administrativeClasses';
import { Student } from '@/types/student';
import apiClient from './examApi';

/**
 * 作者：张瀚
 * 说明：行政班级管理API
 */
export const AdministrativeClassesApi = {
  /**
   * 作者：张瀚
   * 说明：统计班级管理信息
   */
  getStatistics: async (tenant_id: string, tenant_name: string): Promise<ApiResponse<AdministrativeClassesStatistics>> => {
    return apiClient.get(`/api/v1/tenants/${tenant_name}/administrativeClasses/getStatistics`, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：获取有权限的行政班列表
   */
  getUserClassList: async (tenant_id: string, tenant_name: string): Promise<ApiResponse<AdministrativeClassesDetail[]>> => {
    return apiClient.get(`/api/v1/tenants/${tenant_name}/administrativeClasses/getUserClassList`, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：创建班级
   */
  createClasses: async (tenant_id: string, tenant_name: string, params: CreateAdministrativeClassesParams): Promise<ApiResponse<AdministrativeClasses>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/administrativeClasses/createClasses`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：查询班级内的学生
   */
  findAllStudentInClass: async (tenant_id: string, tenant_name: string, params: FindAllStudentInClassParams): Promise<ApiResponse<Student>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/administrativeClasses/findAllStudentInClass`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：更新班级信息
   */
  updateClasses: async (tenant_id: string, tenant_name: string, params: UpdateAdministrativeClassesParams): Promise<ApiResponse<undefined>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/administrativeClasses/updateClasses`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：移动学生到行政班内
   */
  moveStudentToAdministrativeClasses: async (tenant_id: string, tenant_name: string, params: MoveStudentToAdministrativeClassesParams): Promise<ApiResponse<undefined>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/administrativeClasses/moveStudentToAdministrativeClasses`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：从行政班内移除学生
   */
  removeStudentFromAdministrativeClasses: async (tenant_id: string, tenant_name: string, params: RemoveStudentFromAdministrativeClassesParams): Promise<ApiResponse<undefined>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/administrativeClasses/removeStudentFromAdministrativeClasses`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
  /**
   * 作者：张瀚
   * 说明：删除行政班
   */
  deleteClass: async (tenant_id: string, tenant_name: string, params: DeleteAdministrativeClassesParams): Promise<ApiResponse<undefined>> => {
    return apiClient.post(`/api/v1/tenants/${tenant_name}/administrativeClasses/deleteClass`, params, {
      headers: createApiHeaders(tenant_id)
    });
  },
};
