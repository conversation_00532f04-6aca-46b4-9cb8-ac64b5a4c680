import React, { useState, useEffect } from "react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

interface PaginationProps {
  total: number;
  current?: number;
  page?: number; 
  pageSize: number;
  onChange?: (page: number, pageSize: number) => void;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  showSizeChanger?: boolean;
  showTotal?: boolean;
  pageSizeOptions?: number[];
  className?: string;
}

const CustomPagination: React.FC<PaginationProps> = ({
  total,
  current,
  page,
  pageSize,
  onChange,
  onPageChange,
  onPageSizeChange,
  showSizeChanger = true,
  showTotal = true,
  pageSizeOptions = [5, 10, 20, 50, 100],
  className = "",
}) => {
  const [jumpPage, setJumpPage] = useState<string>("");

  // 兼容新旧接口，优先使用current，如果没有则使用page
  const currentPage = current ?? page ?? 1;

  // 计算总页数
  const totalPages = Math.ceil(total / pageSize);

  // 统一的页码变化处理函数
  const handlePageChange = (newPage: number) => {
    if (onChange) {
      onChange(newPage, pageSize);
    } else if (onPageChange) {
      onPageChange(newPage);
    }
  };

  // 统一的页面大小变化处理函数
  const handlePageSizeChange = (newPageSize: number) => {
    if (onChange) {
      onChange(1, newPageSize); // 改变页面大小时重置到第一页
    } else if (onPageSizeChange) {
      onPageSizeChange(newPageSize);
      if (onPageChange) {
        onPageChange(1); // 重置到第一页
      }
    }
  };

  // 处理页码跳转
  const handleJumpToPage = (value: string) => {
    const targetPage = parseInt(value);
    if (isNaN(targetPage)) {
      return;
    }

    let validPage = targetPage;
    if (targetPage < 1) {
      validPage = 1;
    } else if (targetPage > totalPages) {
      validPage = totalPages;
    }

    handlePageChange(validPage);
  };

  // 当页码变化时，清空跳转输入框
  useEffect(() => {
    setJumpPage("");
  }, [currentPage]);
  
  if (total === 0) {
    return null;
  }

  // 生成页码数组，智能显示页码
  const generatePageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 7;

    if (totalPages <= maxVisiblePages) {
      // 如果总页数小于等于最大显示页数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 智能显示页码
      if (currentPage <= 4) {
        // 当前页在前面
        for (let i = 1; i <= 5; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 3) {
        // 当前页在后面
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 当前页在中间
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  return (
    <div className={`flex items-center justify-between gap-4 ${className}`}>
      <div className="flex flex-wrap items-center gap-4">
        {/* 总数显示 */}
        {showTotal && (
          <div className="text-sm text-gray-500 whitespace-nowrap">
            共 {total} 条
          </div>
        )}

        {/* 每页条数选择器 */}
        {showSizeChanger && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">每页</span>
            <select
              className="border rounded px-2 py-1 text-sm min-w-[80px]"
              value={pageSize}
              onChange={e => handlePageSizeChange(Number(e.target.value))}
            >
              {pageSizeOptions.map(size => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
            <span className="text-sm text-gray-500">条</span>
          </div>
        )}
      </div>

      <div className="flex items-center gap-4">
        {/* 跳转到指定页 */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">跳至</span>
          <input
            type="number"
            min={1}
            max={totalPages}
            value={jumpPage}
            onChange={(e) => setJumpPage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleJumpToPage(jumpPage);
                setJumpPage("");
              }
            }}
            onBlur={() => {
              if (jumpPage) {
                handleJumpToPage(jumpPage);
                setJumpPage("");
              }
            }}
            className="border rounded px-2 py-1 w-16 text-sm text-center"
            placeholder={currentPage.toString()}
          />
          <span className="text-sm text-gray-500">页</span>
        </div>

        {/* 分页按钮 */}
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => {
                  if (currentPage > 1) {
                    handlePageChange(currentPage - 1);
                  }
                }}
                className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>

            {generatePageNumbers().map((page, index) => (
              <PaginationItem key={index}>
                {page === '...' ? (
                  <span className="px-3 py-2 text-sm text-gray-500">...</span>
                ) : (
                  <PaginationLink
                    isActive={page === currentPage}
                    onClick={() => handlePageChange(page as number)}
                    className="cursor-pointer"
                  >
                    {page}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationNext
                onClick={() => {
                  if (currentPage < totalPages) {
                    handlePageChange(currentPage + 1);
                  }
                }}
                className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};

export default CustomPagination;

