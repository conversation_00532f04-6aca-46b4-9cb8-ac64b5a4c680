import React from 'react';
import { usePermissions, Permission } from '@/contexts/PermissionContext';

// 基础权限组件属性
interface BasePermissionProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loading?: React.ReactNode;
  showError?: boolean;
}

// 权限检查组件属性
interface PermissionCheckProps extends BasePermissionProps {
  resource: string;
  action: string;
  scope?: string;
}

// 多权限检查组件属性
interface MultiPermissionCheckProps extends BasePermissionProps {
  permissions: Permission[];
  requireAll?: boolean; // true: 需要所有权限, false: 需要任意一个权限
}

// 角色检查组件属性
interface RoleCheckProps extends BasePermissionProps {
  role: string | string[];
  requireAll?: boolean;
}

// 菜单权限检查组件属性
interface MenuPermissionProps extends BasePermissionProps {
  menuId: string;
}

// 管理员权限检查组件属性
interface AdminPermissionProps extends BasePermissionProps {
  requireSystemAdmin?: boolean;
}

// 数据权限检查组件属性
interface DataPermissionProps extends BasePermissionProps {
  resource: string;
  scopeType: string;
  scopeValue: string;
}

// 组合权限检查组件属性
interface CombinedPermissionProps extends BasePermissionProps {
  permissions?: Permission[];
  roles?: string[];
  menuId?: string;
  dataAccess?: {
    resource: string;
    scopeType: string;
    scopeValue: string;
  };
  requireSystemAdmin?: boolean;
  requireAll?: boolean;
}

// 默认加载组件
const DefaultLoading: React.FC = () => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
  </div>
);

// 单权限检查组件
export const PermissionCheck: React.FC<PermissionCheckProps> = ({
  resource,
  action,
  scope,
  children,
  fallback = null,
  loading,
  showError = false
}) => {
  const { hasPermission, isLoading, error } = usePermissions();

  if (isLoading) {
    return <>{loading || <DefaultLoading />}</>;
  }

  if (error && showError) {
    console.warn('Permission check error:', error);
  }

  if (!hasPermission(resource, action, scope)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// 多权限检查组件
export const MultiPermissionCheck: React.FC<MultiPermissionCheckProps> = ({
  permissions,
  requireAll = false,
  children,
  fallback = null,
  loading,
  showError = false
}) => {
  const { hasAnyPermission, hasAllPermissions, isLoading, error } = usePermissions();

  if (isLoading) {
    return <>{loading || <DefaultLoading />}</>;
  }

  if (error && showError) {
    console.warn('Permission check error:', error);
  }

  const hasAccess = requireAll 
    ? hasAllPermissions(permissions)
    : hasAnyPermission(permissions);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// 角色检查组件
export const RoleCheck: React.FC<RoleCheckProps> = ({
  role,
  requireAll = false,
  children,
  fallback = null,
  loading,
  showError = false
}) => {
  const { hasRole, hasAnyRole, isLoading, error } = usePermissions();

  if (isLoading) {
    return <>{loading || <DefaultLoading />}</>;
  }

  if (error && showError) {
    console.warn('Permission check error:', error);
  }

  const roles = Array.isArray(role) ? role : [role];
  const hasAccess = requireAll
    ? roles.every(r => hasRole(r))
    : hasAnyRole(roles);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// 菜单权限检查组件
export const MenuPermissionCheck: React.FC<MenuPermissionProps> = ({
  menuId,
  children,
  fallback = null,
  loading,
  showError = false
}) => {
  const { canAccessMenu, isLoading, error } = usePermissions();

  if (isLoading) {
    return <>{loading || <DefaultLoading />}</>;
  }

  if (error && showError) {
    console.warn('Permission check error:', error);
  }

  if (!canAccessMenu(menuId)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// 管理员权限检查组件
export const AdminPermissionCheck: React.FC<AdminPermissionProps> = ({
  requireSystemAdmin = true,
  children,
  fallback = null,
  loading,
  showError = false
}) => {
  const { isSystemAdmin, isLoading, error } = usePermissions();

  if (isLoading) {
    return <>{loading || <DefaultLoading />}</>;
  }

  if (error && showError) {
    console.warn('Permission check error:', error);
  }

  if (requireSystemAdmin && !isSystemAdmin()) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// 数据权限检查组件
export const DataPermissionCheck: React.FC<DataPermissionProps> = ({
  resource,
  scopeType,
  scopeValue,
  children,
  fallback = null,
  loading,
  showError = false
}) => {
  const { canAccessData, isLoading, error } = usePermissions();

  if (isLoading) {
    return <>{loading || <DefaultLoading />}</>;
  }

  if (error && showError) {
    console.warn('Permission check error:', error);
  }

  if (!canAccessData(resource, scopeType, scopeValue)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// 组合权限检查组件
export const CombinedPermissionCheck: React.FC<CombinedPermissionProps> = ({
  permissions,
  roles,
  menuId,
  dataAccess,
  requireSystemAdmin = false,
  requireAll = false,
  children,
  fallback = null,
  loading,
  showError = false
}) => {
  const { 
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    canAccessMenu,
    canAccessData,
    isSystemAdmin,
    isLoading,
    error
  } = usePermissions();

  if (isLoading) {
    return <>{loading || <DefaultLoading />}</>;
  }

  if (error && showError) {
    console.warn('Permission check error:', error);
  }

  // 系统管理员检查（如果需要，直接通过）
  if (requireSystemAdmin && !isSystemAdmin()) {
    return <>{fallback}</>;
  }

  // 如果是系统管理员且不强制其他检查，直接通过
  if (isSystemAdmin() && !requireAll) {
    return <>{children}</>;
  }

  const checks: boolean[] = [];

  // 权限检查
  if (permissions && permissions.length > 0) {
    const hasAccess = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
    checks.push(hasAccess);
  }

  // 角色检查
  if (roles && roles.length > 0) {
    const hasAccess = requireAll
      ? roles.every(role => hasRole(role))
      : hasAnyRole(roles);
    checks.push(hasAccess);
  }

  // 菜单检查
  if (menuId) {
    checks.push(canAccessMenu(menuId));
  }

  // 数据权限检查
  if (dataAccess) {
    checks.push(canAccessData(dataAccess.resource, dataAccess.scopeType, dataAccess.scopeValue));
  }

  // 检查结果
  const hasAccess = requireAll 
    ? checks.every(check => check)
    : checks.some(check => check);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// 便捷的Hook组件包装器

// 创建权限按钮组件
interface PermissionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  resource: string;
  action: string;
  scope?: string;
  children: React.ReactNode;
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  resource,
  action,
  scope,
  children,
  disabled,
  ...props
}) => {
  const { hasPermission } = usePermissions();
  
  const canAccess = hasPermission(resource, action, scope);
  
  return (
    <button 
      {...props} 
      disabled={disabled || !canAccess}
      style={{ 
        ...props.style, 
        opacity: canAccess ? 1 : 0.5,
        cursor: canAccess ? 'pointer' : 'not-allowed'
      }}
    >
      {children}
    </button>
  );
};

// 创建角色按钮组件
interface RoleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  role: string | string[];
  requireAll?: boolean;
  children: React.ReactNode;
}

export const RoleButton: React.FC<RoleButtonProps> = ({
  role,
  requireAll = false,
  children,
  disabled,
  ...props
}) => {
  const { hasRole, hasAnyRole } = usePermissions();
  
  const roles = Array.isArray(role) ? role : [role];
  const canAccess = requireAll
    ? roles.every(r => hasRole(r))
    : hasAnyRole(roles);
  
  return (
    <button 
      {...props} 
      disabled={disabled || !canAccess}
      style={{ 
        ...props.style, 
        opacity: canAccess ? 1 : 0.5,
        cursor: canAccess ? 'pointer' : 'not-allowed'
      }}
    >
      {children}
    </button>
  );
};

// 创建管理员按钮组件
interface AdminButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
}

export const AdminButton: React.FC<AdminButtonProps> = ({
  children,
  disabled,
  ...props
}) => {
  const { isSystemAdmin } = usePermissions();
  
  const canAccess = isSystemAdmin();
  
  return (
    <button 
      {...props} 
      disabled={disabled || !canAccess}
      style={{ 
        ...props.style, 
        opacity: canAccess ? 1 : 0.5,
        cursor: canAccess ? 'pointer' : 'not-allowed'
      }}
    >
      {children}
    </button>
  );
};

// 导出默认权限检查组件
export default PermissionCheck;