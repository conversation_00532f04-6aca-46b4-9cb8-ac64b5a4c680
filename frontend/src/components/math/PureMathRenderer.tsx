import { MathJaxConfig } from '@/lib/mathjax';
import { MathJax, MathJaxContext } from 'better-react-mathjax';
import React from 'react';

interface PureMathRendererProps {
    inline?: boolean;
    content: string;
}

// 支持直接渲染 Latex 公式
const PureMathRenderer: React.FC<PureMathRendererProps> = ({ content, inline }) => {
    if (!content) return null;

    let isInline = inline;
    if (inline === undefined) {
        isInline = true;
    }

    return (
        <MathJaxContext config={MathJaxConfig}>
            <MathJax inline={isInline}>{content}</MathJax>
        </MathJaxContext>
    );
};

export default PureMathRenderer;