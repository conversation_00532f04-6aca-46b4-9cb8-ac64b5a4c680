import React, { useMemo } from 'react';
import { MathJax, MathJaxContext } from 'better-react-mathjax';
import parse from 'html-react-parser';
import { MathJaxConfig } from '@/lib/mathjax';

interface MathRendererProps {
    content: string;
    className?: string;
}

// 预编译正则表达式，避免每次渲染重新创建
const NEWLINE_REGEX = /\\n|\n/g;
const RELATIVE_PATH_REGEX = /src="api\//g;
const MATH_DELIMITERS = /(\$\$[\s\S]*?\$\$|\$[\s\S]*?\$|\\\[[\s\S]*?\\\]|\\\(.*?\\\))/;

// 支持直接传入一大段包含 Latex 公式的文本
const MathRenderer: React.FC<MathRendererProps> = ({ content, className }) => {
    if (!content) return null;

    // 使用 useMemo 缓存处理结果，避免不必要的重新计算
    const { parts } = useMemo(() => {
        let processedContent = content;

        // 1. 处理所有换行符（合并两个替换操作）
        processedContent = processedContent.replace(NEWLINE_REGEX, '<br />');

        // 2. 处理相对路径
        processedContent = processedContent.replace(RELATIVE_PATH_REGEX, 'src="/api/');

        // 3. 分割内容
        const parts = processedContent.split(MATH_DELIMITERS);

        return { parts };
    }, [content]);

    return (
        <MathJaxContext config={MathJaxConfig}>
            <span className={className}>
                {parts.map((part, index) => {
                    if (!part) return null;

                    // 奇数位是数学公式
                    if (index % 2 === 1) {
                        const isInline = (part.startsWith('$') && !part.startsWith('$$')) || part.startsWith('\\(');
                        return <MathJax key={index} inline={isInline}>{part}</MathJax>;
                    }

                    // 偶数位是普通文本/HTML
                    return <React.Fragment key={index}>{parse(part)}</React.Fragment>;
                })}
            </span>
        </MathJaxContext>
    );
};

// 提供全局 MathJax 上下文的组件
export const MathProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return <MathJaxContext config={MathJaxConfig}>{children}</MathJaxContext>;
};

export default MathRenderer;