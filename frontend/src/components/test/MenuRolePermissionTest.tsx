import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { menuRoleApi, MenuRolePermissionResponse } from '@/services/menuRoleApi';
import { roleApi } from '@/services/roleApi';
import { Role } from '@/types/role';
import { toast } from 'sonner';

/**
 * 菜单角色权限测试组件
 * 用于测试菜单角色权限配置功能
 */
const MenuRolePermissionTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<Array<{
    test: string;
    status: 'success' | 'error' | 'pending';
    message: string;
  }>>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [menuPermissions, setMenuPermissions] = useState<MenuRolePermissionResponse | null>(null);

  // 测试菜单ID
  const TEST_MENU_ID = 'student_management';

  // 添加测试结果
  const addTestResult = (test: string, status: 'success' | 'error' | 'pending', message: string) => {
    setTestResults(prev => [...prev, { test, status, message }]);
  };

  // 加载角色列表
  const loadRoles = async () => {
    try {
      const response = await roleApi.getRoles({ page: 1, page_size: 10 });
      if (response.success && response.data) {
        setRoles(response.data.items);
        addTestResult('加载角色列表', 'success', `成功加载 ${response.data.items.length} 个角色`);
      }
    } catch (error) {
      addTestResult('加载角色列表', 'error', '加载角色列表失败');
    }
  };

  // 测试获取菜单角色权限
  const testGetMenuRolePermissions = async () => {
    try {
      const response = await menuRoleApi.getMenuRolePermissions(TEST_MENU_ID);
      if (response.success && response.data) {
        setMenuPermissions(response.data);
        addTestResult(
          '获取菜单角色权限', 
          'success', 
          `成功获取菜单 ${TEST_MENU_ID} 的 ${response.data.roles.length} 个角色权限`
        );
      }
    } catch (error) {
      addTestResult('获取菜单角色权限', 'error', '获取菜单角色权限失败');
    }
  };

  // 测试设置菜单角色权限
  const testSetMenuRolePermissions = async () => {
    if (roles.length === 0) {
      addTestResult('设置菜单角色权限', 'error', '没有可用的角色');
      return;
    }

    try {
      // 选择前两个角色进行测试
      const testRoleIds = roles.slice(0, 2).map(role => role.id);
      
      const response = await menuRoleApi.setMenuRolePermissions(TEST_MENU_ID, {
        role_ids: testRoleIds
      });
      
      if (response.success) {
        addTestResult(
          '设置菜单角色权限', 
          'success', 
          `成功为菜单 ${TEST_MENU_ID} 设置 ${testRoleIds.length} 个角色权限`
        );
        
        // 重新获取权限验证设置是否成功
        await testGetMenuRolePermissions();
      }
    } catch (error) {
      addTestResult('设置菜单角色权限', 'error', '设置菜单角色权限失败');
    }
  };

  // 测试添加单个角色权限
  const testAddRoleMenuPermission = async () => {
    if (roles.length === 0) {
      addTestResult('添加角色权限', 'error', '没有可用的角色');
      return;
    }

    try {
      const testRoleId = roles[0].id;
      const response = await menuRoleApi.addRoleMenuPermission(TEST_MENU_ID, testRoleId);
      
      if (response.success) {
        addTestResult(
          '添加角色权限', 
          'success', 
          `成功为角色 ${roles[0].name} 添加菜单 ${TEST_MENU_ID} 的访问权限`
        );
      }
    } catch (error) {
      addTestResult('添加角色权限', 'error', '添加角色权限失败');
    }
  };

  // 测试检查角色权限
  const testCheckRoleMenuPermission = async () => {
    if (roles.length === 0) {
      addTestResult('检查角色权限', 'error', '没有可用的角色');
      return;
    }

    try {
      const testRoleId = roles[0].id;
      const response = await menuRoleApi.checkRoleMenuPermission(TEST_MENU_ID, testRoleId);
      
      if (response.success) {
        const hasPermission = response.data;
        addTestResult(
          '检查角色权限', 
          'success', 
          `角色 ${roles[0].name} ${hasPermission ? '有' : '没有'} 菜单 ${TEST_MENU_ID} 的访问权限`
        );
      }
    } catch (error) {
      addTestResult('检查角色权限', 'error', '检查角色权限失败');
    }
  };

  // 运行所有测试
  const runAllTests = async () => {
    setLoading(true);
    setTestResults([]);
    
    try {
      await loadRoles();
      await testGetMenuRolePermissions();
      await testSetMenuRolePermissions();
      await testAddRoleMenuPermission();
      await testCheckRoleMenuPermission();
      
      toast.success('所有测试完成');
    } catch (error) {
      toast.error('测试过程中发生错误');
    } finally {
      setLoading(false);
    }
  };

  // 清除测试结果
  const clearResults = () => {
    setTestResults([]);
    setMenuPermissions(null);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>菜单角色权限功能测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Button 
              onClick={runAllTests} 
              disabled={loading}
              className="flex items-center space-x-2"
            >
              {loading && <Loader2 className="w-4 h-4 animate-spin" />}
              <span>运行所有测试</span>
            </Button>
            <Button variant="outline" onClick={clearResults}>
              清除结果
            </Button>
          </div>

          {/* 测试结果 */}
          {testResults.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-lg font-medium">测试结果</h3>
              {testResults.map((result, index) => (
                <Alert key={index} className={
                  result.status === 'success' ? 'border-green-200 bg-green-50' :
                  result.status === 'error' ? 'border-red-200 bg-red-50' :
                  'border-yellow-200 bg-yellow-50'
                }>
                  <div className="flex items-center space-x-2">
                    {result.status === 'success' && <CheckCircle className="w-4 h-4 text-green-600" />}
                    {result.status === 'error' && <XCircle className="w-4 h-4 text-red-600" />}
                    {result.status === 'pending' && <Loader2 className="w-4 h-4 animate-spin text-yellow-600" />}
                    <div>
                      <div className="font-medium">{result.test}</div>
                      <AlertDescription>{result.message}</AlertDescription>
                    </div>
                  </div>
                </Alert>
              ))}
            </div>
          )}

          {/* 当前菜单权限配置 */}
          {menuPermissions && (
            <div className="space-y-2">
              <h3 className="text-lg font-medium">当前菜单权限配置</h3>
              <div className="p-4 border rounded-lg bg-gray-50">
                <div className="mb-2">
                  <strong>菜单ID:</strong> {menuPermissions.menu_id}
                </div>
                <div>
                  <strong>配置的角色:</strong>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {menuPermissions.roles.map(role => (
                      <Badge key={role.id} variant="secondary">
                        {role.name} ({role.code})
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 可用角色列表 */}
          {roles.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-lg font-medium">可用角色</h3>
              <div className="flex flex-wrap gap-2">
                {roles.map(role => (
                  <Badge key={role.id} variant="outline">
                    {role.name} ({role.code})
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MenuRolePermissionTest;
