import React, { useMemo } from 'react';
import { NavLink } from 'react-router-dom';
import { 
  SidebarGroup, 
  SidebarGroupContent, 
  SidebarGroupLabel, 
  SidebarMenu, 
  SidebarMenuButton, 
  SidebarMenuItem 
} from '@/components/ui/sidebar';
import { usePermissions, MenuPermission } from '@/contexts/PermissionContext';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronRight, ChevronDown } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

// 图标映射
import {
  Book, FileText, BookCheck, School, Users2, ClipboardCheck, BarChart2, 
  Building2, Shield, BookOpen, GraduationCap, UserCheck, Users, User, 
  UsersRound, Layers, Home, Settings, Trophy, Calendar, PieChart,
  List, Plus, Upload, Edit, Shuffle, Bell, Lock, Search, Filter,
  Download, Share, Eye, EyeOff, CheckCircle, XCircle, AlertCircle,
  Info, HelpCircle, Star, Heart, Bookmark, Flag, Tag, Hash,
  Grid, Menu, MoreHorizontal, MoreVertical, Zap, Target, Compass,
  Map, Navigation, Anchor, Briefcase, Award, Gift, Crown, Diamond
} from 'lucide-react';

const iconMap: Record<string, any> = {
  // 基础图标
  'home': Home,
  'settings': Settings,
  'users': Users,
  'user': User,
  'shield': Shield,
  'book': Book,
  'book-open': BookOpen,
  'file-text': FileText,
  'book-check': BookCheck,
  'school': School,
  'users-2': Users2,
  'clipboard-check': ClipboardCheck,
  'bar-chart-2': BarChart2,
  'building-2': Building2,
  'graduation-cap': GraduationCap,
  'user-check': UserCheck,
  'users-round': UsersRound,
  'layers': Layers,
  
  // 功能图标
  'trophy': Trophy,
  'calendar': Calendar,
  'pie-chart': PieChart,
  'list': List,
  'plus': Plus,
  'upload': Upload,
  'edit': Edit,
  'shuffle': Shuffle,
  'bell': Bell,
  'lock': Lock,
  'search': Search,
  'filter': Filter,
  'download': Download,
  'share': Share,
  'eye': Eye,
  'eye-off': EyeOff,
  
  // 状态图标
  'check-circle': CheckCircle,
  'x-circle': XCircle,
  'alert-circle': AlertCircle,
  'info': Info,
  'help-circle': HelpCircle,
  'star': Star,
  'heart': Heart,
  'bookmark': Bookmark,
  'flag': Flag,
  'tag': Tag,
  'hash': Hash,
  
  // 布局图标
  'grid': Grid,
  'menu': Menu,
  'more-horizontal': MoreHorizontal,
  'more-vertical': MoreVertical,
  
  // 特殊图标
  'zap': Zap,
  'target': Target,
  'compass': Compass,
  'map': Map,
  'navigation': Navigation,
  'anchor': Anchor,
  'briefcase': Briefcase,
  'award': Award,
  'gift': Gift,
  'crown': Crown,
  'diamond': Diamond,
};

// 获取图标组件
const getIcon = (iconName?: string) => {
  if (!iconName) return Home;
  const IconComponent = iconMap[iconName.toLowerCase()] || iconMap[iconName] || Home;
  return IconComponent;
};

// 动态菜单项组件属性
interface DynamicMenuItemProps {
  menu: MenuPermission;
  level?: number;
  defaultExpanded?: boolean;
}

// 动态菜单项组件
const DynamicMenuItem: React.FC<DynamicMenuItemProps> = ({ 
  menu, 
  level = 0,
  defaultExpanded = false 
}) => {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);
  const { canAccessMenu } = usePermissions();
  
  const IconComponent = getIcon(menu.icon);
  const hasChildren = menu.children && menu.children.length > 0;
  const canAccess = canAccessMenu(menu.menu_id);

  // 如果没有访问权限，不渲染
  if (!canAccess) {
    return null;
  }

  // 过滤可访问的子菜单
  const accessibleChildren = menu.children?.filter(child => 
    canAccessMenu(child.menu_id)
  ) || [];

  // 如果有子菜单，渲染可折叠菜单
  if (hasChildren && accessibleChildren.length > 0) {
    return (
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <SidebarMenuItem>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton
              tooltip={menu.name}
              className={level > 0 ? `ml-${level * 4}` : ''}
            >
              <IconComponent className="h-4 w-4" />
              <span>{menu.name}</span>
              {isExpanded ? (
                <ChevronDown className="ml-auto h-4 w-4" />
              ) : (
                <ChevronRight className="ml-auto h-4 w-4" />
              )}
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenu>
              {accessibleChildren.map((childMenu) => (
                <DynamicMenuItem
                  key={childMenu.menu_id}
                  menu={childMenu}
                  level={level + 1}
                />
              ))}
            </SidebarMenu>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    );
  }

  // 渲染普通菜单项
  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        tooltip={menu.name}
        className={level > 0 ? `ml-${level * 4}` : ''}
      >
        <NavLink to={menu.path}>
          <IconComponent className="h-4 w-4" />
          <span>{menu.name}</span>
          {menu.data_scopes && menu.data_scopes.length > 0 && (
            <Badge variant="secondary" className="ml-auto text-xs">
              {menu.data_scopes.length}
            </Badge>
          )}
        </NavLink>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
};

// 动态菜单组属性
interface DynamicMenuGroupProps {
  title: string;
  menus: MenuPermission[];
  defaultExpanded?: boolean;
  showEmptyGroup?: boolean;
}

// 动态菜单组组件
const DynamicMenuGroup: React.FC<DynamicMenuGroupProps> = ({
  title,
  menus,
  defaultExpanded = true,
  showEmptyGroup = false
}) => {
  const { canAccessMenu } = usePermissions();
  
  // 过滤可访问的菜单
  const accessibleMenus = menus.filter(menu => canAccessMenu(menu.menu_id));
  
  // 如果没有可访问的菜单且不显示空组，不渲染
  if (accessibleMenus.length === 0 && !showEmptyGroup) {
    return null;
  }

  return (
    <SidebarGroup>
      <SidebarGroupLabel>
        {title}
        {accessibleMenus.length > 0 && (
          <Badge variant="outline" className="ml-2 text-xs">
            {accessibleMenus.length}
          </Badge>
        )}
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {accessibleMenus.length > 0 ? (
            accessibleMenus.map((menu) => (
              <DynamicMenuItem
                key={menu.menu_id}
                menu={menu}
                defaultExpanded={defaultExpanded}
              />
            ))
          ) : (
            showEmptyGroup && (
              <div className="px-2 py-1 text-sm text-muted-foreground">
                暂无可用菜单
              </div>
            )
          )}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
};

// 动态导航属性
interface DynamicNavigationProps {
  showLoading?: boolean;
  showError?: boolean;
  groupByCategory?: boolean;
  defaultExpanded?: boolean;
}

// 主导航组件
export const DynamicNavigation: React.FC<DynamicNavigationProps> = ({
  showLoading = true,
  showError = true,
  groupByCategory = true,
  defaultExpanded = true
}) => {
  const { menus, isLoading, error, getAccessibleMenus } = usePermissions();

  // 加载状态
  if (isLoading && showLoading) {
    return (
      <div className="space-y-4 p-4">
        <div className="space-y-2">
          <Skeleton className="h-4 w-20" />
          <div className="space-y-2 ml-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-3/4" />
          </div>
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <div className="space-y-2 ml-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-4/5" />
          </div>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error && showError) {
    return (
      <div className="p-4">
        <div className="relative w-full rounded-lg border border-destructive/50 bg-destructive/5 p-4 text-destructive">
          <AlertCircle className="h-4 w-4 absolute left-4 top-4" />
          <div className="pl-7 text-sm">
            菜单加载失败: {error}
          </div>
        </div>
      </div>
    );
  }

  // 获取可访问的菜单
  const accessibleMenus = getAccessibleMenus();

  if (accessibleMenus.length === 0) {
    return (
      <div className="p-4">
        <div className="relative w-full rounded-lg border bg-blue-50 p-4 text-blue-700">
          <Info className="h-4 w-4 absolute left-4 top-4" />
          <div className="pl-7 text-sm">
            暂无可访问的菜单
          </div>
        </div>
      </div>
    );
  }

  // 如果按类别分组
  if (groupByCategory) {
    // 按菜单类型分组
    const mainMenus = accessibleMenus.filter(menu => 
      !menu.menu_id.startsWith('system_') && 
      !menu.menu_id.startsWith('personal_') &&
      !menu.parent_id
    );
    
    const systemMenus = accessibleMenus.filter(menu => 
      menu.menu_id.startsWith('system_') && 
      !menu.parent_id
    );
    
    const personalMenus = accessibleMenus.filter(menu => 
      menu.menu_id.startsWith('personal_') || 
      menu.menu_id === 'personal_center'
    );

    return (
      <>
        {mainMenus.length > 0 && (
          <DynamicMenuGroup
            title="主要功能"
            menus={mainMenus}
            defaultExpanded={defaultExpanded}
          />
        )}
        
        {systemMenus.length > 0 && (
          <DynamicMenuGroup
            title="系统管理"
            menus={systemMenus}
            defaultExpanded={defaultExpanded}
          />
        )}
        
        {personalMenus.length > 0 && (
          <DynamicMenuGroup
            title="个人中心"
            menus={personalMenus}
            defaultExpanded={defaultExpanded}
          />
        )}
      </>
    );
  }

  // 不分组，渲染所有顶级菜单
  const topLevelMenus = accessibleMenus.filter(menu => !menu.parent_id);
  
  return (
    <DynamicMenuGroup
      title="菜单"
      menus={topLevelMenus}
      defaultExpanded={defaultExpanded}
      showEmptyGroup={true}
    />
  );
};

// 菜单搜索组件
interface MenuSearchProps {
  onMenuSelect?: (menu: MenuPermission) => void;
}

export const MenuSearch: React.FC<MenuSearchProps> = ({ onMenuSelect }) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const { getAccessibleMenus } = usePermissions();
  
  const searchResults = useMemo(() => {
    if (!searchTerm.trim()) return [];
    
    const allMenus = getAccessibleMenus();
    const flatMenus: MenuPermission[] = [];
    
    // 扁平化菜单结构
    const flattenMenus = (menus: MenuPermission[]) => {
      menus.forEach(menu => {
        flatMenus.push(menu);
        if (menu.children) {
          flattenMenus(menu.children);
        }
      });
    };
    
    flattenMenus(allMenus);
    
    return flatMenus.filter(menu =>
      menu.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      menu.path.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm, getAccessibleMenus]);

  return (
    <div className="p-4 border-b">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <input
          type="text"
          placeholder="搜索菜单..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
        />
      </div>
      
      {searchTerm && (
        <div className="mt-2 max-h-60 overflow-y-auto">
          {searchResults.length > 0 ? (
            <div className="space-y-1">
              {searchResults.map((menu) => {
                const IconComponent = getIcon(menu.icon);
                return (
                  <button
                    key={menu.menu_id}
                    onClick={() => onMenuSelect?.(menu)}
                    className="w-full flex items-center gap-2 p-2 text-left text-sm hover:bg-accent rounded-md"
                  >
                    <IconComponent className="h-4 w-4" />
                    <span>{menu.name}</span>
                    <span className="ml-auto text-xs text-muted-foreground">
                      {menu.path}
                    </span>
                  </button>
                );
              })}
            </div>
          ) : (
            <div className="py-2 text-sm text-muted-foreground text-center">
              没有找到匹配的菜单
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// 权限统计组件
export const PermissionStats: React.FC = () => {
  const { menus, roles, permissions, getAccessibleMenus } = usePermissions();
  
  const stats = useMemo(() => {
    const accessibleMenus = getAccessibleMenus();
    return {
      totalMenus: menus.length,
      accessibleMenus: accessibleMenus.length,
      totalRoles: roles.length,
      totalPermissions: permissions.length,
    };
  }, [menus, roles, permissions, getAccessibleMenus]);

  return (
    <div className="p-4 space-y-2">
      <div className="text-sm font-medium">权限统计</div>
      <div className="space-y-1 text-xs">
        <div className="flex justify-between">
          <span>可访问菜单:</span>
          <span>{stats.accessibleMenus} / {stats.totalMenus}</span>
        </div>
        <div className="flex justify-between">
          <span>拥有角色:</span>
          <span>{stats.totalRoles}</span>
        </div>
        <div className="flex justify-between">
          <span>拥有权限:</span>
          <span>{stats.totalPermissions}</span>
        </div>
      </div>
    </div>
  );
};

export default DynamicNavigation;