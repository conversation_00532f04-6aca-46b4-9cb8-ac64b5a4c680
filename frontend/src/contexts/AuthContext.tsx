import {createContext, useState, useContext, ReactNode} from 'react';
import { LoginForm } from "@/pages/LoginPage.tsx";
import apiClient from "@/services/apiClient.ts";
import {isAxiosError} from "axios";
import {IdentityInfo} from "@/types/identity.ts";

// 更新 AuthContextType 接口以包含 request 方法
interface AuthContextType {
  isAuthenticated: boolean;
  token: string | null;
  identity: IdentityInfo | null;
  login: (data: LoginForm) => Promise<void>;
  selectIdentity:(identity:IdentityInfo)=>void;
  clearIdentity:()=>void;
  logout: () => void;
}


const AuthContext = createContext<AuthContextType | undefined>(undefined);
export { AuthContext };

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [token, setToken] = useState<string | null>(() => localStorage.getItem('token'));
  const [identity, setIdentity] = useState<IdentityInfo | null>(() => {
    const stored = localStorage.getItem('identity');
    if (!stored) return null;
    try {
      return JSON.parse(stored);
    } catch {
      return null;
    }
  });
  const [isAuthenticated, setIsAuthenticated] = useState(!!token&&!!identity);

  const login = async (data: LoginForm) => {
    try {
      console.log("🔐 开始登录...");
      const res = await apiClient.post('/api/v1/auth/login', data);
      const { access_token, refresh_token } = res.data;

      // 保存 token 到 state 和 localStorage
      setToken(access_token);
      localStorage.setItem('token', access_token);
      if (refresh_token) {
        localStorage.setItem('refresh_token', refresh_token);
      }
      console.log("✅ 登录成功，token已保存，isAuthenticated:", !!access_token && !!identity);
      setIsAuthenticated(true);
    } catch (err: unknown) {
      console.error("❌ 登录失败:", err);
      if (isAxiosError(err)) {
        const msg = err.response?.data?.message || '请求失败';
        throw new Error(msg);
      } else if (err instanceof Error) {
        throw new Error(err.message);
      } else {
        throw new Error('未知错误');
      }
    }
  };

  const selectIdentity = (identity: IdentityInfo) => {
    console.log("🎯 选择身份:", identity);
    // TODO 发送请求记录，并且将当前设置为is_primary
    localStorage.setItem('identity', JSON.stringify(identity));
    setIdentity(identity);
    setIsAuthenticated(true);
    console.log("✅ 身份设置完成，isAuthenticated:", true);
  }

  const clearIdentity = ()=>{
    localStorage.removeItem('identity');
    setIdentity(null);
    setIsAuthenticated(false)
  }

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('identity');
    setToken(null);
    setIdentity(null);
    setIsAuthenticated(false);
  };


  const contextValue: AuthContextType  = {
    isAuthenticated,
    token,
    identity,
    login,
    selectIdentity,
    clearIdentity,
    logout,
  };

  return (
      <AuthContext.Provider value={contextValue}>
        {children}
      </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
