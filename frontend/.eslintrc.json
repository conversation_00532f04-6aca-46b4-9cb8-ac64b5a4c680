{"env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["react", "react-hooks", "@typescript-eslint"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "warn"}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["dist", "node_modules", "*.config.js", "*.config.ts"]}