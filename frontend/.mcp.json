{"mcpServers": {"typescript-sdk": {"name": "TypeScript SDK", "description": "Official Anthropic SDK for building MCP servers and clients in JS/TS", "command": "node", "args": ["path/to/ts-sdk-server.js"], "env": {}}, "puppeteer": {"name": "Puppeteer MCP", "description": "Browser automation using Google Puppeteer", "command": "node", "args": ["path/to/server-puppeteer"], "env": {}}, "filesystem": {"name": "File System MCP", "description": "Local file management; compatible with any language", "command": "node", "args": ["path/to/server-filesystem"], "env": {}}, "memory-bank": {"name": "Memory Bank MCP", "description": "Centralized memory system for AI agents", "command": "server-memory", "args": [], "env": {}}, "sequential-thinking": {"name": "Sequential Thinking MCP", "description": "Helps LLMs decompose complex tasks into logical steps", "command": "code-reasoning", "args": [], "env": {}}, "deep-graph": {"name": "Deep Graph MCP (Code Graph)", "description": "Transforms source code into semantic graphs via DeepGraph", "command": "mcp-code-graph", "args": [], "env": {}}}}