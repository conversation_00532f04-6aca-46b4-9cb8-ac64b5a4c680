{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "mcp__zen__analyze", "mcp__serena__list_dir", "Bash(find:*)", "Bash(cargo check:*)", "Ba<PERSON>(timeout 15 cargo check)", "Bash(cargo build:*)", "mcp__serena__find_file", "mcp__sequential__sequentialthinking", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "mcp__serena__get_symbols_overview", "mcp__serena__insert_after_symbol", "mcp__serena__replace_regex", "Bash(grep -A 3 -B 3 \"ApiResponse::success\" src/controller/permission/menu_controller.rs)", "Bash(grep -n \"ApiResponse::\" src/controller/permission/casbin_policy_controller.rs)", "Bash(grep:*)", "Bash(RUST_BACKTRACE=1 cargo check --message-format=short)", "Bash(ls:*)", "Bash(rustc:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(chmod:*)", "mcp__zen__chat", "Bash(psql:*)", "Bash(psql postgresql://postgres:160923z@localhost:5432/deep_mate -c \"\\d tenant_gzxxzz.teaching_classes\")", "Bash(curl -s -X GET \"http://localhost:3000/api/v1/permissions/menus?tenant_id=7ff2e111-1ca4-4402-bc02-af69c1a7283c\" -H \"Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMTExMTExMS0xMTExLTExMTEtMTExMS0xMTExMTExMTExMTEiLCJleHAiOjE3NTQ1NjgzMjcsImlhdCI6MTc1NDQ4MTkyNywidXNlcm5hbWUiOm51bGx9.n1A8RIXSdnSgo4Rd2KIEeuQmP6fzTE5SRa5nSW45KJI\" -H \"Content-Type: application/json\")", "Bash(timeout 30 cargo check)", "Bash(cargo run)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["zen"]}